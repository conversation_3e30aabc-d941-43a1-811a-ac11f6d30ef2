/**
 * Database Assistant ID Fix Script
 * Run this in browser console to fix the UUID/Vapi ID confusion
 */

console.log('🔧 DATABASE ASSISTANT ID FIX SCRIPT');
console.log('===================================');

async function fixDatabaseAssistantIds() {
  try {
    // Check if Supabase is available
    if (!window.supabase && !window.supabaseClient) {
      console.error('❌ Supabase client not available');
      return;
    }

    const supabase = window.supabase || window.supabaseClient;
    
    // Get attorney data from localStorage
    const attorneyData = localStorage.getItem('attorney');
    if (!attorneyData) {
      console.error('❌ No attorney data in localStorage');
      return;
    }

    const attorney = JSON.parse(attorneyData);
    const problematicId = '87756a2c-a398-43f2-889a-b8815684df71';
    
    console.log('👤 Attorney ID:', attorney.id);
    console.log('🚨 Problematic UUID to remove:', problematicId);
    
    // Step 1: Get your real Vapi assistant IDs
    console.log('\n📡 Step 1: Finding Real Vapi Assistant IDs');
    
    const realVapiIds = ['50e13a9e-22dd-4fe8-a03e-de627c5206c1']; // From your earlier message
    
    // Try to find more by checking Vapi directly
    try {
      const response = await fetch('https://api.vapi.ai/assistant', {
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564'}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const assistants = await response.json();
        console.log(`✅ Found ${assistants.length} assistants in your Vapi account:`);
        assistants.forEach(assistant => {
          console.log(`- ${assistant.id}: ${assistant.name}`);
          if (!realVapiIds.includes(assistant.id)) {
            realVapiIds.push(assistant.id);
          }
        });
      }
    } catch (vapiError) {
      console.warn('⚠️ Could not fetch from Vapi directly:', vapiError.message);
    }
    
    console.log('🎯 Real Vapi IDs to use:', realVapiIds);
    
    // Step 2: Clean up problematic UUID entries
    console.log('\n🧹 Step 2: Cleaning Up Problematic UUID Entries');
    
    // Delete from assistant_ui_configs
    const { error: deleteConfigsError } = await supabase
      .from('assistant_ui_configs')
      .delete()
      .eq('assistant_id', problematicId)
      .eq('attorney_id', attorney.id);
    
    if (deleteConfigsError) {
      console.error('❌ Error deleting configs:', deleteConfigsError);
    } else {
      console.log('✅ Deleted problematic configs');
    }
    
    // Delete from assistant_subdomains
    const { error: deleteSubdomainsError } = await supabase
      .from('assistant_subdomains')
      .delete()
      .eq('assistant_id', problematicId)
      .eq('attorney_id', attorney.id);
    
    if (deleteSubdomainsError) {
      console.error('❌ Error deleting subdomains:', deleteSubdomainsError);
    } else {
      console.log('✅ Deleted problematic subdomains');
    }
    
    // Step 3: Update attorney record with valid Vapi ID
    console.log('\n👤 Step 3: Updating Attorney Record');
    
    const primaryVapiId = realVapiIds[0]; // Use the first valid ID as primary
    
    const { error: updateAttorneyError } = await supabase
      .from('attorneys')
      .update({
        vapi_assistant_id: primaryVapiId,
        current_assistant_id: primaryVapiId,
        updated_at: new Date().toISOString()
      })
      .eq('id', attorney.id);
    
    if (updateAttorneyError) {
      console.error('❌ Error updating attorney:', updateAttorneyError);
    } else {
      console.log('✅ Updated attorney with valid Vapi ID:', primaryVapiId);
    }
    
    // Step 4: Create proper assistant configs for each real Vapi ID
    console.log('\n🎛️ Step 4: Creating Proper Assistant Configs');
    
    for (let i = 0; i < realVapiIds.length; i++) {
      const vapiId = realVapiIds[i];
      
      // Try to get assistant name from Vapi
      let assistantName = `${attorney.firm_name} Assistant ${i + 1}`;
      try {
        const response = await fetch(`https://api.vapi.ai/assistant/${vapiId}`, {
          headers: {
            'Authorization': `Bearer ${import.meta.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564'}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          const assistantData = await response.json();
          assistantName = assistantData.name || assistantName;
        }
      } catch (nameError) {
        console.warn(`⚠️ Could not get name for ${vapiId}:`, nameError.message);
      }
      
      // Create assistant config
      const { error: configError } = await supabase
        .from('assistant_ui_configs')
        .upsert({
          attorney_id: attorney.id,
          assistant_id: vapiId,
          assistant_name: assistantName,
          firm_name: attorney.firm_name,
          primary_color: '#2563eb',
          secondary_color: '#1e40af',
          button_color: '#3b82f6',
          voice_provider: 'openai',
          voice_id: 'echo',
          ai_model: 'gpt-4o',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'attorney_id,assistant_id'
        });
      
      if (configError) {
        console.error(`❌ Error creating config for ${vapiId}:`, configError);
      } else {
        console.log(`✅ Created config for ${vapiId}: ${assistantName}`);
      }
      
      // Create subdomain mapping (if you have subdomains)
      if (attorney.subdomain) {
        const subdomain = i === 0 ? attorney.subdomain : `${attorney.subdomain}-${i + 1}`;
        
        const { error: subdomainError } = await supabase
          .from('assistant_subdomains')
          .upsert({
            attorney_id: attorney.id,
            assistant_id: vapiId,
            subdomain: subdomain,
            is_primary: i === 0,
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }, {
            onConflict: 'attorney_id,assistant_id'
          });
        
        if (subdomainError) {
          console.error(`❌ Error creating subdomain for ${vapiId}:`, subdomainError);
        } else {
          console.log(`✅ Created subdomain for ${vapiId}: ${subdomain}.legalscout.net`);
        }
      }
    }
    
    // Step 5: Update localStorage
    console.log('\n💾 Step 5: Updating Local Storage');
    
    const updatedAttorney = {
      ...attorney,
      vapi_assistant_id: primaryVapiId,
      current_assistant_id: primaryVapiId
    };
    
    localStorage.setItem('attorney', JSON.stringify(updatedAttorney));
    console.log('✅ Updated localStorage with valid Vapi ID');
    
    // Step 6: Final verification
    console.log('\n✅ Step 6: Final Verification');
    
    const { data: finalConfigs } = await supabase
      .from('assistant_ui_configs')
      .select('assistant_id, assistant_name')
      .eq('attorney_id', attorney.id);
    
    console.log('Final assistant configs:');
    finalConfigs.forEach(config => {
      console.log(`- ${config.assistant_id}: ${config.assistant_name}`);
    });
    
    console.log('\n🎉 DATABASE FIX COMPLETE!');
    console.log('🔄 Please refresh the page to see the changes.');
    
    return {
      success: true,
      realVapiIds,
      primaryVapiId,
      configsCreated: realVapiIds.length
    };
    
  } catch (error) {
    console.error('❌ Fix script error:', error);
    return { success: false, error: error.message };
  }
}

// Auto-run the fix script
fixDatabaseAssistantIds();

// Make available globally
window.fixDatabaseAssistantIds = fixDatabaseAssistantIds;
