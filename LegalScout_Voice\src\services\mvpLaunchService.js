/**
 * MVP Launch Service
 * 
 * Safe integration service for MVP launch that:
 * 1. Fixes production authentication issues
 * 2. Ensures every attorney has a working assistant
 * 3. Maintains compatibility with existing code
 * 4. Prepares for multi-assistant architecture
 */

import { mvpAssistantCompatibilityService } from './mvpAssistantCompatibilityService';
import { getSupabaseClient } from '../lib/supabase';

class MVPLaunchService {
  constructor() {
    this.initialized = false;
    this.supabase = null;
  }

  async initialize() {
    if (this.initialized) return;
    
    try {
      this.supabase = await getSupabaseClient();
      this.initialized = true;
      console.log('✅ [MVPLaunch] Service initialized successfully');
    } catch (error) {
      console.error('❌ [MVPLaunch] Service initialization failed:', error);
      throw error;
    }
  }

  /**
   * MVP Launch: Ensure attorney has working assistant and subdomain
   * This is the main MVP function that should be called for every attorney
   */
  async ensureMVPReadyAttorney(attorneyData) {
    await this.initialize();

    try {
      console.log('🚀 [MVPLaunch] Ensuring MVP-ready attorney:', attorneyData.id);

      // 1. Ensure attorney has a working assistant
      const assistantResult = await mvpAssistantCompatibilityService.ensureWorkingAssistant(attorneyData);
      
      if (!assistantResult.success) {
        throw new Error(`Failed to ensure working assistant: ${assistantResult.message}`);
      }

      // 2. Ensure subdomain is properly assigned
      const subdomainResult = await this.ensureSubdomainAssignment(attorneyData, assistantResult.assistant.id);

      // 3. Validate the complete setup
      const validationResult = await this.validateMVPSetup(attorneyData);

      return {
        success: true,
        attorney: attorneyData,
        assistant: assistantResult.assistant,
        subdomain: subdomainResult.subdomain,
        validation: validationResult,
        message: 'Attorney is MVP-ready',
        mvpStatus: 'ready'
      };

    } catch (error) {
      console.error('❌ [MVPLaunch] Failed to ensure MVP-ready attorney:', error);
      return {
        success: false,
        error: error.message,
        mvpStatus: 'failed'
      };
    }
  }

  /**
   * Ensure subdomain assignment exists
   */
  async ensureSubdomainAssignment(attorneyData, assistantId) {
    try {
      const subdomain = attorneyData.subdomain || this.generateSubdomain(attorneyData);
      
      // Check if subdomain assignment exists
      const { assistantSubdomainService } = await import('./assistantSubdomainService');
      const existingMapping = await assistantSubdomainService.getAssistantBySubdomain(subdomain);
      
      if (!existingMapping || existingMapping.assistant_id !== assistantId) {
        // Create or update subdomain mapping
        await assistantSubdomainService.assignSubdomainToAssistant(
          assistantId,
          subdomain,
          attorneyData.id,
          true // Primary assistant
        );
        console.log('✅ [MVPLaunch] Subdomain assignment created/updated');
      } else {
        console.log('✅ [MVPLaunch] Subdomain assignment already exists');
      }

      return {
        success: true,
        subdomain: subdomain,
        url: `https://${subdomain}.legalscout.net`
      };

    } catch (error) {
      console.error('❌ [MVPLaunch] Subdomain assignment failed:', error);
      return {
        success: false,
        error: error.message,
        subdomain: attorneyData.subdomain
      };
    }
  }

  /**
   * Validate complete MVP setup
   */
  async validateMVPSetup(attorneyData) {
    const validation = {
      hasAssistant: false,
      hasSubdomain: false,
      hasWorkingAuth: false,
      isComplete: false,
      issues: []
    };

    try {
      // Check assistant
      const currentAssistant = await mvpAssistantCompatibilityService.getCurrentAssistant(attorneyData);
      validation.hasAssistant = !!currentAssistant;
      if (!validation.hasAssistant) {
        validation.issues.push('No working assistant found');
      }

      // Check subdomain
      validation.hasSubdomain = !!attorneyData.subdomain;
      if (!validation.hasSubdomain) {
        validation.issues.push('No subdomain assigned');
      }

      // Check auth (basic check)
      validation.hasWorkingAuth = !!attorneyData.id && !!attorneyData.email;
      if (!validation.hasWorkingAuth) {
        validation.issues.push('Authentication issues detected');
      }

      // Overall status
      validation.isComplete = validation.hasAssistant && validation.hasSubdomain && validation.hasWorkingAuth;

      console.log('✅ [MVPLaunch] Validation complete:', validation);
      return validation;

    } catch (error) {
      console.error('❌ [MVPLaunch] Validation failed:', error);
      validation.issues.push(`Validation error: ${error.message}`);
      return validation;
    }
  }

  /**
   * Generate subdomain from attorney data
   */
  generateSubdomain(attorneyData) {
    if (attorneyData.subdomain) return attorneyData.subdomain;
    
    const email = attorneyData.email || attorneyData.email_from_oauth || '';
    const firmName = attorneyData.firm_name || '';
    
    let subdomain = '';
    
    if (email) {
      subdomain = email.split('@')[0].toLowerCase().replace(/[^a-z0-9]/g, '');
    } else if (firmName) {
      subdomain = firmName.toLowerCase().replace(/[^a-z0-9]/g, '').substring(0, 20);
    } else {
      subdomain = `attorney${Date.now()}`;
    }

    return subdomain;
  }

  /**
   * Get MVP status for attorney (for dashboard display)
   */
  async getMVPStatus(attorneyData) {
    try {
      const validation = await this.validateMVPSetup(attorneyData);
      
      return {
        isReady: validation.isComplete,
        status: validation.isComplete ? 'ready' : 'needs-setup',
        issues: validation.issues,
        hasAssistant: validation.hasAssistant,
        hasSubdomain: validation.hasSubdomain,
        hasWorkingAuth: validation.hasWorkingAuth
      };
    } catch (error) {
      return {
        isReady: false,
        status: 'error',
        issues: [error.message],
        hasAssistant: false,
        hasSubdomain: false,
        hasWorkingAuth: false
      };
    }
  }

  /**
   * Quick fix for common MVP issues
   */
  async quickFixMVPIssues(attorneyData) {
    try {
      console.log('🔧 [MVPLaunch] Running quick fix for MVP issues');
      
      const result = await this.ensureMVPReadyAttorney(attorneyData);
      
      if (result.success) {
        console.log('✅ [MVPLaunch] Quick fix completed successfully');
        return {
          success: true,
          message: 'MVP issues resolved',
          result: result
        };
      } else {
        console.error('❌ [MVPLaunch] Quick fix failed:', result.error);
        return {
          success: false,
          message: `Quick fix failed: ${result.error}`,
          result: result
        };
      }
    } catch (error) {
      console.error('❌ [MVPLaunch] Quick fix error:', error);
      return {
        success: false,
        message: `Quick fix error: ${error.message}`
      };
    }
  }
}

// Export singleton instance
export const mvpLaunchService = new MVPLaunchService();
