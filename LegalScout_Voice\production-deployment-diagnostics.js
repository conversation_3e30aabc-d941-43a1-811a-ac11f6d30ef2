/**
 * Production Deployment Diagnostics
 * Identifies specific issues causing black screen in production vs working locally
 */

class ProductionDeploymentDiagnostics {
    constructor() {
        this.results = {
            environment: {},
            buildConfig: {},
            routing: {},
            assets: {},
            apiConnectivity: {},
            errors: []
        };
        this.startTime = performance.now();
    }

    async runDiagnostics() {
        console.log('🔍 [ProdDiag] Starting production deployment diagnostics...');
        
        try {
            await this.checkEnvironment();
            await this.checkBuildConfiguration();
            await this.checkRouting();
            await this.checkAssets();
            await this.checkAPIConnectivity();
            await this.generateReport();
        } catch (error) {
            console.error('❌ [ProdDiag] Diagnostics failed:', error);
            this.results.errors.push({ type: 'fatal', error: error.message });
        }
    }

    async checkEnvironment() {
        console.log('🌍 [ProdDiag] Checking environment configuration...');
        
        const env = {
            // Basic environment detection
            hostname: window.location.hostname,
            isProduction: window.location.hostname.includes('legalscout.net'),
            isLocal: window.location.hostname.includes('localhost'),
            isVercel: window.location.hostname.includes('vercel.app'),
            
            // Build-time variables (from Vite)
            buildTime: window.__BUILD_TIME__ || 'unknown',
            version: window.__VERSION__ || 'unknown',
            isDev: window.__DEV__ || false,
            isProd: window.__PROD__ || false,
            
            // Environment variables availability
            envVars: {
                // Vite environment variables (safe access)
                viteSupabaseUrl: this.safeGetImportMeta('VITE_SUPABASE_URL'),
                viteSupabaseKey: this.safeGetImportMeta('VITE_SUPABASE_KEY'),
                viteVapiPrivate: this.safeGetImportMeta('VITE_VAPI_PRIVATE_KEY'),
                viteVapiPublic: this.safeGetImportMeta('VITE_VAPI_PUBLIC_KEY'),
                
                // Window globals (from production fixes)
                windowSupabaseUrl: window.VITE_SUPABASE_URL || window.SUPABASE_URL,
                windowSupabaseKey: window.VITE_SUPABASE_KEY || window.SUPABASE_ANON_KEY,
                windowVapiPrivate: window.VITE_VAPI_SECRET_KEY || window.VAPI_PRIVATE_KEY,
                windowVapiPublic: window.VITE_VAPI_PUBLIC_KEY || window.VAPI_PUBLIC_KEY,
                
                // Process environment (if available)
                processEnv: typeof process !== 'undefined' ? {
                    nodeEnv: process.env?.NODE_ENV,
                    hasViteVars: !!(process.env?.VITE_SUPABASE_URL)
                } : null
            },
            
            // CSP and security
            csp: this.checkCSP(),
            
            // Browser compatibility
            browser: {
                userAgent: navigator.userAgent,
                supportsModules: 'noModule' in HTMLScriptElement.prototype,
                supportsES6: typeof Symbol !== 'undefined'
            }
        };

        this.results.environment = env;
        console.log('✅ [ProdDiag] Environment check complete');
    }

    safeGetImportMeta(key) {
        try {
            if (typeof window !== 'undefined' && window.import && window.import.meta && window.import.meta.env) {
                return window.import.meta.env[key];
            }
        } catch (e) {
            // import.meta not available
        }
        return null;
    }

    checkCSP() {
        const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
        return {
            exists: !!cspMeta,
            content: cspMeta?.content || null,
            allowsEval: cspMeta?.content?.includes('unsafe-eval') || false,
            allowsInline: cspMeta?.content?.includes('unsafe-inline') || false,
            scriptSrc: cspMeta?.content?.match(/script-src[^;]*/)?.[0] || null
        };
    }

    async checkBuildConfiguration() {
        console.log('🔨 [ProdDiag] Checking build configuration...');
        
        const build = {
            // Script loading
            scripts: Array.from(document.scripts).map(script => ({
                src: script.src,
                type: script.type,
                async: script.async,
                defer: script.defer,
                module: script.type === 'module',
                loaded: script.readyState || 'unknown',
                hasError: script.onerror !== null
            })),
            
            // CSS loading
            stylesheets: Array.from(document.styleSheets).map(sheet => ({
                href: sheet.href,
                disabled: sheet.disabled,
                media: sheet.media?.mediaText,
                rules: sheet.cssRules?.length || 0
            })),
            
            // Module loading (Vite specific)
            moduleSupport: {
                hasModuleScripts: document.querySelectorAll('script[type="module"]').length > 0,
                hasNoModuleScripts: document.querySelectorAll('script[nomodule]').length > 0,
                viteClient: !!document.querySelector('script[src*="@vite/client"]'),
                viteHMR: !!window.__vite_plugin_react_preamble_installed__
            },
            
            // Build artifacts
            buildArtifacts: {
                hasManifest: !!document.querySelector('link[rel="manifest"]'),
                hasServiceWorker: 'serviceWorker' in navigator,
                hasPreloadLinks: document.querySelectorAll('link[rel="preload"]').length,
                hasModulePreload: document.querySelectorAll('link[rel="modulepreload"]').length
            }
        };

        this.results.buildConfig = build;
        console.log('✅ [ProdDiag] Build configuration check complete');
    }

    async checkRouting() {
        console.log('🛣️ [ProdDiag] Checking routing configuration...');
        
        const routing = {
            // Current route info
            currentPath: window.location.pathname,
            currentSearch: window.location.search,
            currentHash: window.location.hash,
            
            // Expected routing behavior
            expectedBehavior: this.getExpectedRoutingBehavior(),
            
            // React Router detection
            reactRouter: {
                exists: !!window.ReactRouter,
                browserRouter: !!document.querySelector('[data-testid="browser-router"]'),
                routes: !!document.querySelector('Routes, route'),
                navigation: !!document.querySelector('nav, [role="navigation"]')
            },
            
            // Route components
            routeComponents: {
                root: !!document.getElementById('root'),
                mainApp: !!document.querySelector('[data-testid="main-app"]'),
                dashboard: !!document.querySelector('[data-testid="dashboard"]'),
                home: !!document.querySelector('[data-testid="home"]')
            },
            
            // Navigation elements
            navigation: {
                links: Array.from(document.querySelectorAll('a[href]')).map(a => ({
                    href: a.href,
                    text: a.textContent?.trim(),
                    internal: a.href.includes(window.location.hostname)
                })),
                buttons: Array.from(document.querySelectorAll('button')).length
            }
        };

        this.results.routing = routing;
        console.log('✅ [ProdDiag] Routing check complete');
    }

    getExpectedRoutingBehavior() {
        const isProduction = this.results.environment?.isProduction;
        const hasAuth = this.checkAuthState();
        
        return {
            environment: isProduction ? 'production' : 'development',
            expectedPath: isProduction ? 
                (hasAuth ? '/dashboard' : '/') : 
                (hasAuth ? '/dashboard' : '/home'),
            currentMatches: window.location.pathname === (isProduction ? 
                (hasAuth ? '/dashboard' : '/') : 
                (hasAuth ? '/dashboard' : '/home')),
            authRequired: hasAuth,
            shouldRedirect: !this.isOnExpectedPath()
        };
    }

    checkAuthState() {
        return !!(
            window.supabaseClient?.auth?.getUser ||
            window.user ||
            localStorage.getItem('supabase.auth.token') ||
            sessionStorage.getItem('supabase.auth.token') ||
            document.cookie.includes('supabase-auth-token')
        );
    }

    isOnExpectedPath() {
        const expected = this.getExpectedRoutingBehavior();
        return window.location.pathname === expected.expectedPath;
    }

    async checkAssets() {
        console.log('📦 [ProdDiag] Checking asset loading...');
        
        const assets = {
            // Critical assets
            criticalAssets: await this.checkCriticalAssets(),
            
            // Resource loading performance
            resourceTiming: this.getResourceTiming(),
            
            // Failed resources
            failedResources: this.getFailedResources(),
            
            // Asset paths
            assetPaths: {
                baseUrl: document.querySelector('base')?.href || window.location.origin,
                publicPath: this.detectPublicPath(),
                cdnUsage: this.detectCDNUsage()
            }
        };

        this.results.assets = assets;
        console.log('✅ [ProdDiag] Asset check complete');
    }

    async checkCriticalAssets() {
        const criticalPaths = [
            '/favicon.ico',
            '/nav_logo.webp',
            '/PRIMARY CLEAR.png'
        ];

        const results = {};
        for (const path of criticalPaths) {
            try {
                const response = await fetch(path, { method: 'HEAD' });
                results[path] = {
                    exists: response.ok,
                    status: response.status,
                    contentType: response.headers.get('content-type')
                };
            } catch (error) {
                results[path] = {
                    exists: false,
                    error: error.message
                };
            }
        }
        return results;
    }

    getResourceTiming() {
        if (!window.performance?.getEntriesByType) {
            return { available: false };
        }

        const resources = window.performance.getEntriesByType('resource');
        return {
            available: true,
            total: resources.length,
            scripts: resources.filter(r => r.name.includes('.js')).length,
            styles: resources.filter(r => r.name.includes('.css')).length,
            images: resources.filter(r => /\.(png|jpg|jpeg|gif|webp|svg)/.test(r.name)).length,
            slow: resources.filter(r => r.duration > 1000).length,
            failed: resources.filter(r => r.transferSize === 0 && r.duration > 0).length
        };
    }

    getFailedResources() {
        // Check for 404 errors in console (if available)
        const failedImages = Array.from(document.images).filter(img => !img.complete || img.naturalWidth === 0);
        const failedScripts = Array.from(document.scripts).filter(script => script.onerror);
        
        return {
            images: failedImages.map(img => img.src),
            scripts: failedScripts.map(script => script.src),
            total: failedImages.length + failedScripts.length
        };
    }

    detectPublicPath() {
        const scripts = Array.from(document.scripts);
        const moduleScript = scripts.find(s => s.type === 'module' && s.src);
        if (moduleScript) {
            const url = new URL(moduleScript.src);
            return url.pathname.replace(/\/[^\/]*$/, '/');
        }
        return '/';
    }

    detectCDNUsage() {
        const externalDomains = new Set();
        Array.from(document.scripts).forEach(script => {
            if (script.src && !script.src.includes(window.location.hostname)) {
                externalDomains.add(new URL(script.src).hostname);
            }
        });
        return Array.from(externalDomains);
    }

    async checkAPIConnectivity() {
        console.log('🌐 [ProdDiag] Checking API connectivity...');
        
        const api = {
            supabase: await this.testSupabaseConnection(),
            vapi: await this.testVapiConnection(),
            cors: await this.testCORSIssues()
        };

        this.results.apiConnectivity = api;
        console.log('✅ [ProdDiag] API connectivity check complete');
    }

    async testSupabaseConnection() {
        const supabaseUrl = this.results.environment?.envVars?.viteSupabaseUrl || 
                           this.results.environment?.envVars?.windowSupabaseUrl;
        
        if (!supabaseUrl) {
            return { available: false, error: 'No Supabase URL found' };
        }

        try {
            const response = await fetch(`${supabaseUrl}/rest/v1/`, {
                method: 'HEAD',
                headers: {
                    'apikey': this.results.environment?.envVars?.viteSupabaseKey || 
                             this.results.environment?.envVars?.windowSupabaseKey || ''
                }
            });
            
            return {
                available: true,
                status: response.status,
                ok: response.ok,
                url: supabaseUrl
            };
        } catch (error) {
            return {
                available: false,
                error: error.message,
                url: supabaseUrl
            };
        }
    }

    async testVapiConnection() {
        try {
            const response = await fetch('https://api.vapi.ai/assistant', {
                method: 'HEAD',
                headers: {
                    'Authorization': `Bearer ${this.results.environment?.envVars?.viteVapiPrivate || 
                                              this.results.environment?.envVars?.windowVapiPrivate || ''}`
                }
            });
            
            return {
                available: true,
                status: response.status,
                ok: response.ok
            };
        } catch (error) {
            return {
                available: false,
                error: error.message
            };
        }
    }

    async testCORSIssues() {
        const corsTests = [];
        
        // Test different origins
        const testOrigins = [
            'https://api.vapi.ai',
            'https://utopqxsvudgrtiwenlzl.supabase.co'
        ];

        for (const origin of testOrigins) {
            try {
                const response = await fetch(origin, { method: 'HEAD' });
                corsTests.push({
                    origin,
                    success: true,
                    status: response.status
                });
            } catch (error) {
                corsTests.push({
                    origin,
                    success: false,
                    error: error.message
                });
            }
        }

        return { tests: corsTests };
    }

    async generateReport() {
        console.log('📊 [ProdDiag] Generating diagnostic report...');
        
        const issues = this.identifyIssues();
        const recommendations = this.generateRecommendations(issues);
        
        const report = {
            summary: {
                totalTime: performance.now() - this.startTime,
                environment: this.results.environment?.isProduction ? 'PRODUCTION' : 'DEVELOPMENT',
                criticalIssues: issues.filter(i => i.severity === 'critical').length,
                warnings: issues.filter(i => i.severity === 'warning').length,
                overallStatus: issues.filter(i => i.severity === 'critical').length === 0 ? 'HEALTHY' : 'ISSUES_FOUND'
            },
            issues,
            recommendations,
            rawResults: this.results,
            timestamp: new Date().toISOString()
        };

        // Store globally
        window.productionDiagnostics = report;
        
        console.log('🎯 [ProdDiag] PRODUCTION DIAGNOSTIC COMPLETE');
        console.log('📊 Summary:', report.summary);
        console.log('🚨 Critical Issues:', report.summary.criticalIssues);
        console.log('⚠️ Warnings:', report.summary.warnings);
        
        // Log critical issues
        issues.filter(i => i.severity === 'critical').forEach(issue => {
            console.error('🚨 CRITICAL:', issue.message);
        });
        
        return report;
    }

    identifyIssues() {
        const issues = [];
        
        // Environment variable issues
        if (!this.results.environment?.envVars?.viteSupabaseUrl && 
            !this.results.environment?.envVars?.windowSupabaseUrl) {
            issues.push({
                severity: 'critical',
                category: 'environment',
                message: 'Supabase URL not available in any environment variable location'
            });
        }
        
        // Build configuration issues
        if (this.results.buildConfig?.moduleSupport?.hasModuleScripts === false) {
            issues.push({
                severity: 'critical',
                category: 'build',
                message: 'No ES6 module scripts found - build may have failed'
            });
        }
        
        // Routing issues
        if (!this.results.routing?.routeComponents?.root) {
            issues.push({
                severity: 'critical',
                category: 'routing',
                message: 'React root element not found'
            });
        }
        
        // Asset loading issues
        if (this.results.assets?.failedResources?.total > 0) {
            issues.push({
                severity: 'warning',
                category: 'assets',
                message: `${this.results.assets.failedResources.total} assets failed to load`
            });
        }
        
        // API connectivity issues
        if (!this.results.apiConnectivity?.supabase?.available) {
            issues.push({
                severity: 'critical',
                category: 'api',
                message: 'Supabase connection failed'
            });
        }
        
        return issues;
    }

    generateRecommendations(issues) {
        const recommendations = [];
        
        issues.forEach(issue => {
            switch (issue.category) {
                case 'environment':
                    recommendations.push('Check Vercel environment variables configuration');
                    break;
                case 'build':
                    recommendations.push('Verify Vite build configuration and module output');
                    break;
                case 'routing':
                    recommendations.push('Ensure React app mounts correctly to #root element');
                    break;
                case 'assets':
                    recommendations.push('Check asset paths and public directory configuration');
                    break;
                case 'api':
                    recommendations.push('Verify API keys and CORS configuration');
                    break;
            }
        });
        
        return [...new Set(recommendations)]; // Remove duplicates
    }
}

// Auto-run diagnostics
console.log('🚀 [ProdDiag] Starting production deployment diagnostics...');
const prodDiagnostics = new ProductionDeploymentDiagnostics();
prodDiagnostics.runDiagnostics();

// Make available globally
window.productionDiagnostics = prodDiagnostics;
