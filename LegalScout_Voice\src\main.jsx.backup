// Backup of original main.jsx
// This file is created by the black screen fix script

// Import React polyfill first to ensure it's available for all dependencies
import './utils/reactPolyfill.js';

// 💀 REMOVED: headers-fix causing duplicate Content-Type headers
// import './utils/headers-fix.js';

import React from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom'
import App from './App.jsx'
import './index.css'
import 'leaflet/dist/leaflet.css'
import './fixes/interactionFix.css' // Import the interaction fix CSS
import ErrorBoundary from './utils/ErrorBoundary.jsx'
import ProductionErrorBoundary from './components/ProductionErrorBoundary.jsx'
import SyncAuthProvider from './components/SyncAuthProvider.jsx'
import { ThemeProvider } from './contexts/ThemeContext.jsx'
import { AttorneyStateProvider } from './contexts/AttorneyStateContext.jsx'

// Import schema generator to make it available globally
import './utils/schemaGenerator.js'

// Import production import resolver for optimized builds
import { preloadCriticalModules } from './utils/productionImportResolver.js'

// Import production-safe environment configuration
import { initializeEnvironment, isProduction } from './config/productionEnvironment.js'

// Import development environment configuration
import { initializeDevelopmentEnvironment, isDevelopment as isDevEnvironment } from './config/developmentEnvironment.js'

// Import and initialize environment verification
import { initEnvironmentVerification } from './utils/environmentVerifier.js'

// Import and initialize attorney profile manager
import './utils/initAttorneyProfileManager.js'

// Import and initialize Vapi debugger
import { initVapiDebugger } from './utils/initVapiDebugger.js'

// Production-safe initialization
async function initializeMainEnvironment() {
  console.log('🚀 [Main] Initializing environment...');

  // Preload critical modules for production optimization
  try {
    await preloadCriticalModules();
  } catch (error) {
    console.warn('⚠️ [Main] Failed to preload critical modules:', error);
  }

  // Check if we're in development using multiple methods
  const isDev = (() => {
    try {
      // Method 1: Check import.meta.env
      if (import.meta && import.meta.env && import.meta.env.DEV === true) {
        return true;
      }
    } catch (e) {}

    // Method 2: Check hostname
    if (typeof window !== 'undefined') {
      const hostname = window.location.hostname;
      if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('localhost')) {
        return true;
      }
    }

    // Method 3: Check NODE_ENV
    try {
      if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'development') {
        return true;
      }
    } catch (e) {}

    return false;
  })();

  if (isDev) {
    console.log('🛠️ [Main] Development environment detected, using development initialization...');

    // Use development-specific initialization
    initializeDevelopmentEnvironment();

    // Also run environment verification for compatibility
    initEnvironmentVerification();
  } else {
    console.log('🏭 [Main] Production environment detected, using production-safe configuration...');
    // Initialize production environment
    initializeEnvironment();
  }
}

// CRITICAL FIX: Initialize React immediately, handle async initialization in background
console.log('🚀 [Main] Starting React app immediately...');

// Initialize Vapi debugger in development mode - safely check environment
try {
  if (import.meta.env.DEV || import.meta.env.MODE === 'development') {
    initVapiDebugger();
  }
} catch (e) {
  // Fallback to checking hostname for development
  if (typeof window !== 'undefined' &&
      (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1')) {
    initVapiDebugger();
  }
}

// Render React immediately - don't wait for async initialization
try {
  ReactDOM.createRoot(document.getElementById('root')).render(
    <React.StrictMode>
      <ProductionErrorBoundary>
        <ErrorBoundary showDetails={true} onReset={() => window.location.reload()}>
          <BrowserRouter>
            <ThemeProvider>
              <AttorneyStateProvider>
                <SyncAuthProvider>
                  <App />
                </SyncAuthProvider>
              </AttorneyStateProvider>
            </ThemeProvider>
          </BrowserRouter>
        </ErrorBoundary>
      </ProductionErrorBoundary>
    </React.StrictMode>
  );

  console.log('✅ [Main] React app rendered successfully');
} catch (error) {
  console.error('❌ [Main] Failed to render React app:', error);

  // Fallback: show error message
  document.getElementById('root').innerHTML = `
    <div style="padding: 20px; background: #ffebee; color: #c62828; font-family: monospace;">
      <h1>❌ React Render Error</h1>
      <p><strong>Error:</strong> ${error.message}</p>
      <p><strong>Stack:</strong></p>
      <pre>${error.stack}</pre>
      <button onclick="location.reload()" style="
        padding: 10px 20px;
        background: #c62828;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin-top: 10px;
      ">Reload Page</button>
    </div>
  `;
}

// Run async initialization in background (don't block React)
initializeMainEnvironment().catch(error => {
  console.error('❌ [Main] Environment initialization failed:', error);
  // Don't let this break the app - it's just optimization
});
