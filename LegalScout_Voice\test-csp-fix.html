<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSP Fix Validation Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status.pass { background: #d4edda; color: #155724; }
        .status.fail { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        
        .csp-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 10px 0;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        iframe {
            width: 100%;
            height: 200px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ CSP Fix Validation Test</h1>
            <p>Testing Content Security Policy fixes for Vercel.live</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h3>🔍 Current CSP Analysis</h3>
                <button class="test-button" onclick="analyzeCsp()">Analyze Current CSP</button>
                <div id="csp-analysis"></div>
            </div>
            
            <div class="test-section">
                <h3>🖼️ Frame Source Tests</h3>
                <p>Testing if various frame sources are allowed by CSP:</p>
                
                <div>
                    <strong>Test 1: Vercel.live Frame</strong>
                    <span id="vercel-live-status" class="status">Testing...</span>
                    <button class="test-button" onclick="testVercelLiveFrame()">Test Vercel.live</button>
                    <div id="vercel-live-frame"></div>
                </div>
                
                <div>
                    <strong>Test 2: Daily.co Frame</strong>
                    <span id="daily-status" class="status">Testing...</span>
                    <button class="test-button" onclick="testDailyFrame()">Test Daily.co</button>
                    <div id="daily-frame"></div>
                </div>
                
                <div>
                    <strong>Test 3: Vercel App Frame</strong>
                    <span id="vercel-app-status" class="status">Testing...</span>
                    <button class="test-button" onclick="testVercelAppFrame()">Test Vercel App</button>
                    <div id="vercel-app-frame"></div>
                </div>
            </div>
            
            <div class="test-section">
                <h3>📊 CSP Violation Monitor</h3>
                <button class="test-button" onclick="startViolationMonitoring()">Start Monitoring</button>
                <button class="test-button" onclick="clearViolations()">Clear Log</button>
                <div id="violation-log"></div>
            </div>
            
            <div class="test-section">
                <h3>🔧 Recommended CSP</h3>
                <p>Based on the test results, here's the recommended CSP configuration:</p>
                <div id="recommended-csp" class="csp-display">
                    Click "Generate Recommendation" to see the optimized CSP
                </div>
                <button class="test-button" onclick="generateRecommendation()">Generate Recommendation</button>
            </div>
        </div>
    </div>

    <script>
        let violationCount = 0;
        let violations = [];

        function analyzeCsp() {
            const analysisDiv = document.getElementById('csp-analysis');
            
            // Try to get CSP from meta tag
            const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
            let cspContent = '';
            
            if (cspMeta) {
                cspContent = cspMeta.getAttribute('content');
                analysisDiv.innerHTML = `
                    <h4>CSP found in meta tag:</h4>
                    <div class="csp-display">${cspContent}</div>
                `;
            } else {
                analysisDiv.innerHTML = `
                    <h4>No CSP meta tag found</h4>
                    <p>CSP is likely set via HTTP headers. Check network tab for CSP headers.</p>
                `;
            }
            
            // Analyze frame-src directive
            if (cspContent) {
                const frameSrcMatch = cspContent.match(/frame-src[^;]+/);
                if (frameSrcMatch) {
                    const frameSrc = frameSrcMatch[0];
                    analysisDiv.innerHTML += `
                        <h4>Frame-src directive:</h4>
                        <div class="csp-display">${frameSrc}</div>
                    `;
                    
                    // Check for vercel.live
                    if (frameSrc.includes('vercel.live')) {
                        analysisDiv.innerHTML += `<p class="status pass">✅ vercel.live found in frame-src</p>`;
                    } else {
                        analysisDiv.innerHTML += `<p class="status fail">❌ vercel.live NOT found in frame-src</p>`;
                    }
                    
                    // Check for *.vercel.live
                    if (frameSrc.includes('*.vercel.live')) {
                        analysisDiv.innerHTML += `<p class="status pass">✅ *.vercel.live found in frame-src</p>`;
                    } else {
                        analysisDiv.innerHTML += `<p class="status fail">❌ *.vercel.live NOT found in frame-src</p>`;
                    }
                }
            }
        }

        function testVercelLiveFrame() {
            const statusSpan = document.getElementById('vercel-live-status');
            const frameDiv = document.getElementById('vercel-live-frame');
            
            statusSpan.textContent = 'Testing...';
            statusSpan.className = 'status warning';
            
            // Create iframe to test vercel.live
            const iframe = document.createElement('iframe');
            iframe.src = 'https://vercel.live';
            iframe.style.display = 'none'; // Hide to avoid visual clutter
            
            iframe.onload = function() {
                statusSpan.textContent = 'PASS - Frame loaded successfully';
                statusSpan.className = 'status pass';
                frameDiv.innerHTML = '<p>✅ Vercel.live frame loaded without CSP violation</p>';
            };
            
            iframe.onerror = function() {
                statusSpan.textContent = 'FAIL - Frame blocked by CSP';
                statusSpan.className = 'status fail';
                frameDiv.innerHTML = '<p>❌ Vercel.live frame blocked by CSP</p>';
            };
            
            // Monitor for CSP violations
            setTimeout(() => {
                if (statusSpan.textContent === 'Testing...') {
                    statusSpan.textContent = 'TIMEOUT - Check CSP violations';
                    statusSpan.className = 'status warning';
                }
            }, 5000);
            
            document.body.appendChild(iframe);
        }

        function testDailyFrame() {
            const statusSpan = document.getElementById('daily-status');
            const frameDiv = document.getElementById('daily-frame');
            
            statusSpan.textContent = 'Testing...';
            statusSpan.className = 'status warning';
            
            const iframe = document.createElement('iframe');
            iframe.src = 'https://c.daily.co';
            iframe.style.display = 'none';
            
            iframe.onload = function() {
                statusSpan.textContent = 'PASS - Daily.co frame allowed';
                statusSpan.className = 'status pass';
                frameDiv.innerHTML = '<p>✅ Daily.co frame loaded successfully</p>';
            };
            
            iframe.onerror = function() {
                statusSpan.textContent = 'FAIL - Daily.co frame blocked';
                statusSpan.className = 'status fail';
                frameDiv.innerHTML = '<p>❌ Daily.co frame blocked</p>';
            };
            
            setTimeout(() => {
                if (statusSpan.textContent === 'Testing...') {
                    statusSpan.textContent = 'TIMEOUT - Check violations';
                    statusSpan.className = 'status warning';
                }
            }, 5000);
            
            document.body.appendChild(iframe);
        }

        function testVercelAppFrame() {
            const statusSpan = document.getElementById('vercel-app-status');
            const frameDiv = document.getElementById('vercel-app-frame');
            
            statusSpan.textContent = 'Testing...';
            statusSpan.className = 'status warning';
            
            // Test with a known Vercel app URL
            const iframe = document.createElement('iframe');
            iframe.src = 'https://legalscout-git-main-damonkosts-projects.vercel.app';
            iframe.style.display = 'none';
            
            iframe.onload = function() {
                statusSpan.textContent = 'PASS - Vercel app frame allowed';
                statusSpan.className = 'status pass';
                frameDiv.innerHTML = '<p>✅ Vercel app frame loaded successfully</p>';
            };
            
            iframe.onerror = function() {
                statusSpan.textContent = 'FAIL - Vercel app frame blocked';
                statusSpan.className = 'status fail';
                frameDiv.innerHTML = '<p>❌ Vercel app frame blocked</p>';
            };
            
            setTimeout(() => {
                if (statusSpan.textContent === 'Testing...') {
                    statusSpan.textContent = 'TIMEOUT - Check violations';
                    statusSpan.className = 'status warning';
                }
            }, 5000);
            
            document.body.appendChild(iframe);
        }

        function startViolationMonitoring() {
            const logDiv = document.getElementById('violation-log');
            
            // Listen for CSP violations
            document.addEventListener('securitypolicyviolation', function(e) {
                violationCount++;
                violations.push({
                    directive: e.violatedDirective,
                    blockedURI: e.blockedURI,
                    originalPolicy: e.originalPolicy,
                    timestamp: new Date().toISOString()
                });
                
                logDiv.innerHTML += `
                    <div style="border: 1px solid #dc3545; padding: 10px; margin: 5px 0; border-radius: 4px; background: #f8d7da;">
                        <strong>CSP Violation #${violationCount}</strong><br>
                        <strong>Directive:</strong> ${e.violatedDirective}<br>
                        <strong>Blocked URI:</strong> ${e.blockedURI}<br>
                        <strong>Time:</strong> ${new Date().toLocaleTimeString()}
                    </div>
                `;
            });
            
            logDiv.innerHTML = '<p>🔍 Monitoring for CSP violations... Try the frame tests above.</p>';
        }

        function clearViolations() {
            document.getElementById('violation-log').innerHTML = '';
            violationCount = 0;
            violations = [];
        }

        function generateRecommendation() {
            const recommendedDiv = document.getElementById('recommended-csp');
            
            const recommendedCsp = `default-src 'self'; 
script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: data: https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io https://vercel.live https://*.vercel.live https://*.vercel.app; 
style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com https://c.daily.co https://*.daily.co; 
font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; 
img-src 'self' data: blob: https:; 
media-src 'self' blob: data: https:; 
connect-src 'self' https://api.vapi.ai https://mcp.vapi.ai https://dashboard.vapi.ai https://cdn.vapi.ai https://vapi.ai https://utopqxsvudgrtiwenlzl.supabase.co https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io https://r.jina.ai https://api.openai.com https://api.firecrawl.dev https://vercel.live https://*.vercel.live https://*.vercel.app wss: ws:; 
frame-src 'self' https://c.daily.co https://*.daily.co https://vercel.live https://*.vercel.live https://*.vercel.app; 
worker-src 'self' blob:; 
child-src 'self' blob:; 
object-src 'none'; 
base-uri 'self';`;
            
            recommendedDiv.innerHTML = recommendedCsp.replace(/; /g, ';<br>');
            
            if (violations.length > 0) {
                recommendedDiv.innerHTML += `
                    <h4>Based on ${violations.length} detected violations:</h4>
                    <ul>
                        ${violations.map(v => `<li>${v.directive}: ${v.blockedURI}</li>`).join('')}
                    </ul>
                `;
            }
        }

        // Auto-start monitoring when page loads
        window.addEventListener('load', function() {
            startViolationMonitoring();
            analyzeCsp();
        });
    </script>
</body>
</html>
