/**
 * Environment Variables Checker
 * 
 * This script checks if all required environment variables are properly set
 * and helps diagnose configuration issues.
 */

window.checkEnvVars = function() {
  console.log('🔧 [Env<PERSON><PERSON><PERSON>] Checking environment variables...');
  
  const requiredVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_KEY', 
    'VITE_VAPI_PUBLIC_KEY',
    'VITE_VAPI_SECRET_KEY'
  ];
  
  const results = {
    timestamp: new Date().toISOString(),
    environment: window.location.hostname,
    variables: {},
    issues: []
  };
  
  // Check each required variable
  requiredVars.forEach(varName => {
    const value = window[varName] || window.process?.env?.[varName] || import.meta?.env?.[varName];
    
    results.variables[varName] = {
      exists: !!value,
      length: value?.length || 0,
      source: value ? (window[varName] ? 'window' : 'process.env') : 'none',
      preview: value ? `${value.substring(0, 10)}...` : 'undefined'
    };
    
    if (!value) {
      results.issues.push(`❌ ${varName} is not defined`);
    } else if (value.length < 10) {
      results.issues.push(`⚠️ ${varName} seems too short (${value.length} chars)`);
    } else {
      console.log(`✅ ${varName}: ${value.substring(0, 10)}... (${value.length} chars)`);
    }
  });
  
  // Check Vapi key types
  const publicKey = results.variables['VITE_VAPI_PUBLIC_KEY'];
  const secretKey = results.variables['VITE_VAPI_SECRET_KEY'];
  
  if (publicKey.exists && secretKey.exists) {
    const pubKeyValue = window.VITE_VAPI_PUBLIC_KEY || window.process?.env?.VITE_VAPI_PUBLIC_KEY;
    const secKeyValue = window.VITE_VAPI_SECRET_KEY || window.process?.env?.VITE_VAPI_SECRET_KEY;
    
    // Check if keys might be swapped (common issue)
    if (pubKeyValue && secKeyValue) {
      if (pubKeyValue.length > secKeyValue.length) {
        results.issues.push('⚠️ Public key is longer than secret key - they might be swapped');
      }
      
      // Vapi public keys typically start with specific patterns
      if (!pubKeyValue.match(/^[a-f0-9-]{36}$/)) {
        results.issues.push('⚠️ Public key format looks unusual');
      }
      
      if (!secKeyValue.match(/^[a-f0-9-]{36}$/)) {
        results.issues.push('⚠️ Secret key format looks unusual');
      }
    }
  }
  
  // Summary
  console.log('🔧 [EnvChecker] Environment Variables Summary:');
  console.table(results.variables);
  
  if (results.issues.length > 0) {
    console.log('⚠️ [EnvChecker] Issues found:');
    results.issues.forEach(issue => console.log(issue));
  } else {
    console.log('✅ [EnvChecker] All environment variables look good!');
  }
  
  return results;
};

// Test Vapi API connection
window.testVapiConnection = async function() {
  console.log('🧪 [VapiTest] Testing Vapi API connection...');
  
  const publicKey = window.VITE_VAPI_PUBLIC_KEY || window.process?.env?.VITE_VAPI_PUBLIC_KEY;
  const secretKey = window.VITE_VAPI_SECRET_KEY || window.process?.env?.VITE_VAPI_SECRET_KEY;
  
  if (!publicKey || !secretKey) {
    console.error('❌ [VapiTest] Vapi keys not found');
    return { success: false, error: 'Vapi keys not configured' };
  }
  
  try {
    // Test with public key (should work for basic operations)
    console.log('🧪 [VapiTest] Testing with public key...');
    const publicResponse = await fetch('https://api.vapi.ai/assistant', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${publicKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('🧪 [VapiTest] Public key response status:', publicResponse.status);
    
    if (publicResponse.status === 401) {
      console.error('❌ [VapiTest] Public key authentication failed');
      return { success: false, error: 'Public key authentication failed' };
    }
    
    // Test with secret key (should work for all operations)
    console.log('🧪 [VapiTest] Testing with secret key...');
    const secretResponse = await fetch('https://api.vapi.ai/assistant', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${secretKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('🧪 [VapiTest] Secret key response status:', secretResponse.status);
    
    if (secretResponse.status === 401) {
      console.error('❌ [VapiTest] Secret key authentication failed');
      return { success: false, error: 'Secret key authentication failed' };
    }
    
    if (secretResponse.ok) {
      const assistants = await secretResponse.json();
      console.log('✅ [VapiTest] Vapi connection successful!');
      console.log('📋 [VapiTest] Found assistants:', assistants.length);
      
      // Look for your specific assistant
      const yourAssistant = assistants.find(a => a.id === 'cd0b44b7-397e-410d-8835-ce9c3ba584b2');
      if (yourAssistant) {
        console.log('✅ [VapiTest] Found your assistant:', yourAssistant.name);
      } else {
        console.log('⚠️ [VapiTest] Your assistant ID not found in list');
      }
      
      return { 
        success: true, 
        assistants: assistants.length,
        yourAssistant: !!yourAssistant
      };
    }
    
    return { success: false, error: 'Unknown API error' };
    
  } catch (error) {
    console.error('❌ [VapiTest] Connection test failed:', error);
    return { success: false, error: error.message };
  }
};

console.log('🔧 [EnvChecker] Environment checker loaded. Run checkEnvVars() or testVapiConnection() in console.');
