import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { enhancedVapiService } from '../services/EnhancedVapiService';
import { createCallControlTokenSync } from '../utils/tokenUtils';
import './CallRecordsTable.css';

/**
 * Call Records Table
 *
 * This component displays a table of call records for an attorney,
 * filtered by the currently selected assistant.
 * It allows viewing call details, sending notifications, and accessing call control.
 */
const CallRecordsTable = ({ attorneyId, assistantData, currentAssistantId }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [calls, setCalls] = useState([]);
  const [selectedCall, setSelectedCall] = useState(null);
  const [sendingNotification, setSendingNotification] = useState(false);

  // Load call records
  useEffect(() => {
    const loadCallRecords = async () => {
      try {
        setLoading(true);
        console.log('🔍 Loading call records for attorney:', attorneyId, 'assistant:', currentAssistantId);

        // Get call records from Supabase, filtered by assistant ID if available
        let query = supabase
          .from('call_records')
          .select('*')
          .eq('attorney_id', attorneyId);

        // Filter by current assistant ID if provided
        if (currentAssistantId) {
          query = query.eq('assistant_id', currentAssistantId);
          console.log('📋 Filtering call records by assistant ID:', currentAssistantId);
        }

        const { data, error } = await query.order('start_time', { ascending: false });

        if (error) {
          throw error;
        }

        console.log(`✅ Loaded ${data?.length || 0} call records for assistant ${currentAssistantId}`);
        setCalls(data || []);
        setLoading(false);
      } catch (error) {
        console.error('Error loading call records:', error);
        setError('Failed to load call records');
        setLoading(false);
      }
    };

    if (attorneyId) {
      loadCallRecords();
    }
  }, [attorneyId, currentAssistantId]);

  // Update calls when assistant data changes
  useEffect(() => {
    if (assistantData?.calls) {
      console.log('📞 [CallRecordsTable] Updating calls from assistant data:', assistantData.calls.length);
      setCalls(assistantData.calls);
      setLoading(false);
    }
  }, [assistantData]);

  // Format duration
  const formatDuration = (seconds) => {
    if (!seconds) return '0:00';

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';

    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Format phone number
  const formatPhoneNumber = (phoneNumber) => {
    if (!phoneNumber) return 'Unknown';

    // Remove non-numeric characters
    const cleaned = phoneNumber.replace(/\D/g, '');

    // Format as (XXX) XXX-XXXX
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }

    // Format as +X XXX XXX XXXX for international
    if (cleaned.length > 10) {
      return `+${cleaned.slice(0, cleaned.length - 10)} ${cleaned.slice(-10, -7)} ${cleaned.slice(-7, -4)} ${cleaned.slice(-4)}`;
    }

    return phoneNumber;
  };

  // Generate call control link
  const generateCallControlLink = (callId) => {
    const token = createCallControlTokenSync(callId, attorneyId);
    return `${window.location.origin}/call-control?token=${token}`;
  };

  // Send notification
  const handleSendNotification = async (callId) => {
    try {
      setSendingNotification(true);

      // Initialize Vapi service
      await enhancedVapiService.initialize();

      // Send notification
      await enhancedVapiService.sendCallNotification(callId, attorneyId);

      alert('Notification sent successfully');
      setSendingNotification(false);
    } catch (error) {
      console.error('Error sending notification:', error);
      alert('Failed to send notification');
      setSendingNotification(false);
    }
  };

  // View call details
  const handleViewCallDetails = (call) => {
    setSelectedCall(call);
  };

  // Close call details
  const handleCloseCallDetails = () => {
    setSelectedCall(null);
  };

  // Render loading state
  if (loading) {
    return (
      <div className="call-records-loading">
        <div className="loading-spinner"></div>
        <p>Loading call records...</p>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="call-records-error">
        <p>{error}</p>
        <button onClick={() => setError(null)}>Retry</button>
      </div>
    );
  }

  // Render empty state
  if (calls.length === 0) {
    return (
      <div className="call-records-empty">
        <p>No call records found</p>
      </div>
    );
  }

  return (
    <div className="call-records-container">
      <h2>Call Records</h2>

      <table className="call-records-table">
        <thead>
          <tr>
            <th>Date</th>
            <th>Customer</th>
            <th>Status</th>
            <th>Duration</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {calls.map((call) => (
            <tr key={call.id} className={`status-${call.status}`}>
              <td>{formatDate(call.start_time)}</td>
              <td>{formatPhoneNumber(call.customer_phone)}</td>
              <td>{call.status}</td>
              <td>{formatDuration(call.duration)}</td>
              <td className="actions">
                <button
                  onClick={() => handleViewCallDetails(call)}
                  className="view-button"
                >
                  View
                </button>

                <a
                  href={generateCallControlLink(call.call_id)}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="control-button"
                >
                  Control
                </a>

                <button
                  onClick={() => handleSendNotification(call.call_id)}
                  className="notify-button"
                  disabled={sendingNotification}
                >
                  Notify
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      {selectedCall && (
        <div className="call-details-modal">
          <div className="call-details-content">
            <button
              className="close-button"
              onClick={handleCloseCallDetails}
            >
              &times;
            </button>

            <h3>Call Details</h3>

            <div className="call-details-info">
              <p><strong>Call ID:</strong> {selectedCall.call_id}</p>
              <p><strong>Customer:</strong> {formatPhoneNumber(selectedCall.customer_phone)}</p>
              <p><strong>Status:</strong> {selectedCall.status}</p>
              <p><strong>Start Time:</strong> {formatDate(selectedCall.start_time)}</p>
              <p><strong>End Time:</strong> {formatDate(selectedCall.end_time)}</p>
              <p><strong>Duration:</strong> {formatDuration(selectedCall.duration)}</p>
            </div>

            <div className="call-details-transcripts">
              <h4>Transcripts</h4>

              {selectedCall.transcripts && selectedCall.transcripts.length > 0 ? (
                <div className="transcripts-list">
                  {selectedCall.transcripts.map((transcript, index) => (
                    <div
                      key={index}
                      className={`transcript-item ${transcript.role}`}
                    >
                      <div className="transcript-header">
                        <span className="transcript-role">
                          {transcript.role === 'assistant' ? 'Assistant' : 'Client'}
                        </span>
                        <span className="transcript-time">
                          {new Date(transcript.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      <div className="transcript-content">{transcript.text}</div>
                    </div>
                  ))}
                </div>
              ) : (
                <p>No transcripts available</p>
              )}
            </div>

            <div className="call-details-actions">
              <a
                href={generateCallControlLink(selectedCall.call_id)}
                target="_blank"
                rel="noopener noreferrer"
                className="control-button"
              >
                Open Call Control
              </a>

              <button
                onClick={() => handleSendNotification(selectedCall.call_id)}
                className="notify-button"
                disabled={sendingNotification}
              >
                Send Notification
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CallRecordsTable;
