/**
 * Environment & Timing Diagnostics
 * Focuses on global variables, timing issues, and environment-specific problems
 */

class EnvironmentTimingDiagnostics {
    constructor() {
        this.startTime = performance.now();
        this.checkpoints = [];
        this.environmentData = {};
        this.timingData = {};
        this.globalVars = {};
    }

    checkpoint(name) {
        const now = performance.now();
        this.checkpoints.push({
            name,
            time: now,
            elapsed: now - this.startTime
        });
        console.log(`⏱️ [EnvTiming] Checkpoint ${name}: ${(now - this.startTime).toFixed(2)}ms`);
    }

    async runDiagnostics() {
        console.log('🔍 [EnvTiming] Starting environment and timing diagnostics...');
        this.checkpoint('diagnostics_start');
        
        try {
            await this.checkEnvironmentVariables();
            this.checkpoint('env_vars_checked');
            
            await this.checkGlobalObjects();
            this.checkpoint('globals_checked');
            
            await this.checkTimingIssues();
            this.checkpoint('timing_checked');
            
            await this.checkProductionSpecificIssues();
            this.checkpoint('production_checked');
            
            await this.generateReport();
            this.checkpoint('report_generated');
            
        } catch (error) {
            console.error('❌ [EnvTiming] Diagnostic failed:', error);
            this.checkpoint(`error_${error.message}`);
        }
    }

    async checkEnvironmentVariables() {
        console.log('🌍 [EnvTiming] Checking environment variables...');
        
        const env = {
            // Process environment (if available)
            processEnv: typeof process !== 'undefined' ? {
                exists: true,
                nodeEnv: process.env?.NODE_ENV,
                keys: Object.keys(process.env || {}),
                supabaseUrl: !!process.env?.VITE_SUPABASE_URL || !!process.env?.SUPABASE_URL,
                supabaseKey: !!process.env?.VITE_SUPABASE_ANON_KEY || !!process.env?.SUPABASE_ANON_KEY,
                vapiKeys: !!process.env?.VITE_VAPI_PRIVATE_KEY || !!process.env?.VAPI_PRIVATE_KEY
            } : { exists: false },
            
            // Window environment variables
            windowEnv: {
                supabaseUrl: window.SUPABASE_URL || window.VITE_SUPABASE_URL,
                supabaseKey: window.SUPABASE_ANON_KEY || window.VITE_SUPABASE_ANON_KEY,
                vapiPrivate: window.VAPI_PRIVATE_KEY || window.VITE_VAPI_PRIVATE_KEY,
                vapiPublic: window.VAPI_PUBLIC_KEY || window.VITE_VAPI_PUBLIC_KEY,
                allWindowVars: Object.keys(window).filter(key => 
                    key.includes('SUPABASE') || 
                    key.includes('VAPI') || 
                    key.includes('VITE_')
                )
            },
            
            // Meta tags with environment info
            metaEnv: Array.from(document.querySelectorAll('meta')).reduce((acc, meta) => {
                if (meta.name && (meta.name.includes('env') || meta.name.includes('config'))) {
                    acc[meta.name] = meta.content;
                }
                return acc;
            }, {}),
            
            // Script tags that might contain env vars
            scriptEnv: Array.from(document.scripts).filter(script => 
                script.textContent?.includes('SUPABASE') || 
                script.textContent?.includes('VAPI') ||
                script.textContent?.includes('process.env')
            ).map(script => ({
                src: script.src,
                hasSupabase: script.textContent?.includes('SUPABASE'),
                hasVapi: script.textContent?.includes('VAPI'),
                hasProcessEnv: script.textContent?.includes('process.env'),
                snippet: script.textContent?.substring(0, 200)
            }))
        };

        this.environmentData = env;
        console.log('✅ [EnvTiming] Environment variables checked');
    }

    async checkGlobalObjects() {
        console.log('🌐 [EnvTiming] Checking global objects...');
        
        const globals = {
            // Core libraries
            react: {
                exists: !!window.React,
                version: window.React?.version,
                devTools: !!window.__REACT_DEVTOOLS_GLOBAL_HOOK__
            },
            
            // Supabase
            supabase: {
                exists: !!window.supabase,
                createClient: !!window.supabase?.createClient,
                client: !!window.supabaseClient,
                auth: !!window.supabaseClient?.auth
            },
            
            // Vapi
            vapi: {
                exists: !!window.Vapi,
                webSDK: !!window.VapiWebSDK,
                client: !!window.vapiClient,
                keys: {
                    private: !!window.VAPI_PRIVATE_KEY,
                    public: !!window.VAPI_PUBLIC_KEY
                }
            },
            
            // Application-specific globals
            app: {
                attorneyManager: !!window.StandaloneAttorneyManager,
                routingDiagnostics: !!window.routingDiagnostics,
                vapiAssistantCleanup: !!window.vapiAssistantCleanup
            },
            
            // Browser APIs
            browser: {
                fetch: !!window.fetch,
                localStorage: !!window.localStorage,
                sessionStorage: !!window.sessionStorage,
                history: !!window.history,
                location: !!window.location
            },
            
            // Development tools
            dev: {
                console: !!window.console,
                performance: !!window.performance,
                devtools: !!window.__REACT_DEVTOOLS_GLOBAL_HOOK__ || !!window.__VUE_DEVTOOLS_GLOBAL_HOOK__
            }
        };

        // Check for missing critical globals
        const critical = [];
        if (!globals.react.exists) critical.push('React');
        if (!globals.supabase.exists && !globals.supabase.client) critical.push('Supabase');
        if (!globals.browser.fetch) critical.push('fetch');
        
        globals.missing = critical;
        this.globalVars = globals;
        console.log('✅ [EnvTiming] Global objects checked');
    }

    async checkTimingIssues() {
        console.log('⏱️ [EnvTiming] Checking timing issues...');
        
        const timing = {
            // Current timing
            elapsed: performance.now() - this.startTime,
            checkpoints: [...this.checkpoints],
            
            // Document ready states
            document: {
                readyState: document.readyState,
                loading: document.readyState === 'loading',
                interactive: document.readyState === 'interactive',
                complete: document.readyState === 'complete'
            },
            
            // Performance timing (if available)
            performance: this.getPerformanceTiming(),
            
            // Resource loading
            resources: this.getResourceTiming(),
            
            // Script execution timing
            scripts: this.getScriptTiming(),
            
            // Async operations
            asyncOps: this.checkAsyncOperations()
        };

        this.timingData = timing;
        console.log('✅ [EnvTiming] Timing issues checked');
    }

    getPerformanceTiming() {
        if (!window.performance || !window.performance.timing) {
            return { available: false };
        }

        const timing = window.performance.timing;
        const navigation = timing.navigationStart;
        
        return {
            available: true,
            navigationStart: navigation,
            domContentLoaded: timing.domContentLoadedEventEnd - navigation,
            loadComplete: timing.loadEventEnd - navigation,
            domInteractive: timing.domInteractive - navigation,
            firstPaint: this.getFirstPaint(),
            
            // Critical timing thresholds
            slowDOMContentLoaded: (timing.domContentLoadedEventEnd - navigation) > 3000,
            slowLoadComplete: (timing.loadEventEnd - navigation) > 5000
        };
    }

    getFirstPaint() {
        if (!window.performance || !window.performance.getEntriesByType) {
            return null;
        }
        
        const paintEntries = window.performance.getEntriesByType('paint');
        const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
        return firstPaint ? firstPaint.startTime : null;
    }

    getResourceTiming() {
        if (!window.performance || !window.performance.getEntriesByType) {
            return { available: false };
        }

        const resources = window.performance.getEntriesByType('resource');
        const critical = resources.filter(r => 
            r.name.includes('react') || 
            r.name.includes('supabase') || 
            r.name.includes('vapi') ||
            r.name.includes('.js') ||
            r.name.includes('.css')
        );

        return {
            available: true,
            total: resources.length,
            critical: critical.length,
            failed: resources.filter(r => r.transferSize === 0).length,
            slow: resources.filter(r => r.duration > 1000).length,
            slowResources: resources.filter(r => r.duration > 1000).map(r => ({
                name: r.name,
                duration: r.duration
            }))
        };
    }

    getScriptTiming() {
        const scripts = Array.from(document.scripts);
        return {
            total: scripts.length,
            external: scripts.filter(s => s.src).length,
            inline: scripts.filter(s => !s.src).length,
            async: scripts.filter(s => s.async).length,
            defer: scripts.filter(s => s.defer).length,
            
            // Check for blocking scripts
            blocking: scripts.filter(s => s.src && !s.async && !s.defer).map(s => s.src)
        };
    }

    checkAsyncOperations() {
        return {
            // Check for pending promises (if possible)
            pendingFetches: this.countPendingFetches(),
            
            // Check for timers
            activeTimers: this.estimateActiveTimers(),
            
            // Check for event listeners
            eventListeners: this.countEventListeners()
        };
    }

    countPendingFetches() {
        // This is a rough estimate - we can't directly count pending fetches
        // but we can look for loading indicators
        const loadingElements = document.querySelectorAll('[class*="loading"], [class*="spinner"]');
        return {
            estimated: loadingElements.length,
            indicators: Array.from(loadingElements).map(el => el.className)
        };
    }

    estimateActiveTimers() {
        // We can't directly count active timers, but we can check for common patterns
        return {
            estimated: 'unknown',
            note: 'Cannot directly count active timers from browser context'
        };
    }

    countEventListeners() {
        // Count elements with event listeners (rough estimate)
        const elementsWithEvents = document.querySelectorAll('[onclick], [onload], [onerror]');
        return {
            estimated: elementsWithEvents.length,
            types: Array.from(elementsWithEvents).map(el => ({
                tag: el.tagName,
                events: Object.keys(el).filter(key => key.startsWith('on'))
            }))
        };
    }

    async checkProductionSpecificIssues() {
        console.log('🏭 [EnvTiming] Checking production-specific issues...');
        
        const isProduction = window.location.hostname.includes('legalscout.net');
        const isLocal = window.location.hostname.includes('localhost');
        
        const prodIssues = {
            environment: { isProduction, isLocal },
            
            // CSP issues
            csp: this.checkCSP(),
            
            // CORS issues
            cors: await this.checkCORS(),
            
            // Environment variable exposure
            envExposure: this.checkEnvExposure(),
            
            // Production vs development differences
            differences: this.checkEnvironmentDifferences()
        };

        this.productionData = prodIssues;
        console.log('✅ [EnvTiming] Production-specific issues checked');
    }

    checkCSP() {
        const metaCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
        return {
            exists: !!metaCSP,
            content: metaCSP?.content,
            allowsEval: metaCSP?.content?.includes('unsafe-eval'),
            allowsInline: metaCSP?.content?.includes('unsafe-inline')
        };
    }

    async checkCORS() {
        // Test CORS by attempting to fetch from different origins
        const tests = [];
        
        try {
            // Test Supabase
            if (this.environmentData.windowEnv.supabaseUrl) {
                tests.push({
                    service: 'supabase',
                    url: this.environmentData.windowEnv.supabaseUrl,
                    accessible: 'testing...'
                });
            }
            
            // Test Vapi
            tests.push({
                service: 'vapi',
                url: 'https://api.vapi.ai',
                accessible: 'testing...'
            });
            
        } catch (error) {
            tests.push({ error: error.message });
        }
        
        return { tests };
    }

    checkEnvExposure() {
        // Check if sensitive environment variables are exposed
        const exposed = [];
        const sensitive = ['PRIVATE_KEY', 'SECRET', 'PASSWORD', 'TOKEN'];
        
        Object.keys(window).forEach(key => {
            if (sensitive.some(s => key.includes(s))) {
                exposed.push(key);
            }
        });
        
        return {
            exposedVars: exposed,
            hasSensitiveExposure: exposed.length > 0
        };
    }

    checkEnvironmentDifferences() {
        const isProduction = window.location.hostname.includes('legalscout.net');
        
        return {
            currentEnv: isProduction ? 'production' : 'development',
            expectedPath: isProduction ? '/' : '/dashboard',
            currentPath: window.location.pathname,
            pathMatches: window.location.pathname === (isProduction ? '/' : '/dashboard'),
            
            // Check for development-only features
            devFeatures: {
                reactDevTools: !!window.__REACT_DEVTOOLS_GLOBAL_HOOK__,
                console: window.console !== undefined,
                sourceMap: document.querySelector('script[src*=".map"]') !== null
            }
        };
    }

    async generateReport() {
        console.log('📊 [EnvTiming] Generating comprehensive report...');
        
        const issues = this.identifyIssues();
        const recommendations = this.generateRecommendations();
        
        const report = {
            summary: {
                totalTime: performance.now() - this.startTime,
                criticalIssues: issues.filter(i => i.severity === 'critical').length,
                warnings: issues.filter(i => i.severity === 'warning').length,
                environment: this.productionData?.environment || 'unknown'
            },
            
            issues,
            recommendations,
            
            rawData: {
                environment: this.environmentData,
                globals: this.globalVars,
                timing: this.timingData,
                production: this.productionData,
                checkpoints: this.checkpoints
            },
            
            timestamp: new Date().toISOString()
        };

        // Store globally
        window.environmentDiagnostics = report;
        
        console.log('🎯 [EnvTiming] DIAGNOSTIC COMPLETE');
        console.log('⏱️ Total time:', `${report.summary.totalTime.toFixed(2)}ms`);
        console.log('🚨 Critical issues:', report.summary.criticalIssues);
        console.log('⚠️ Warnings:', report.summary.warnings);
        
        // Log critical issues
        issues.filter(i => i.severity === 'critical').forEach(issue => {
            console.error('🚨 CRITICAL:', issue.message);
        });
        
        return report;
    }

    identifyIssues() {
        const issues = [];
        
        // Environment variable issues
        if (!this.environmentData.windowEnv.supabaseUrl) {
            issues.push({
                severity: 'critical',
                category: 'environment',
                message: 'Supabase URL not found in window object'
            });
        }
        
        if (!this.environmentData.windowEnv.supabaseKey) {
            issues.push({
                severity: 'critical',
                category: 'environment',
                message: 'Supabase anonymous key not found in window object'
            });
        }
        
        // Global object issues
        if (this.globalVars.missing?.length > 0) {
            issues.push({
                severity: 'critical',
                category: 'globals',
                message: `Missing critical globals: ${this.globalVars.missing.join(', ')}`
            });
        }
        
        // Timing issues
        if (this.timingData.performance?.slowLoadComplete) {
            issues.push({
                severity: 'warning',
                category: 'timing',
                message: 'Slow page load detected (>5s)'
            });
        }
        
        // Production-specific issues
        if (this.productionData?.differences && !this.productionData.differences.pathMatches) {
            issues.push({
                severity: 'warning',
                category: 'routing',
                message: `Path mismatch: expected ${this.productionData.differences.expectedPath}, got ${this.productionData.differences.currentPath}`
            });
        }
        
        return issues;
    }

    generateRecommendations() {
        const recommendations = [];
        
        // Based on identified issues
        if (!this.environmentData.windowEnv.supabaseUrl) {
            recommendations.push('Ensure Supabase environment variables are properly injected into window object');
        }
        
        if (this.globalVars.missing?.includes('React')) {
            recommendations.push('Verify React is loaded before application initialization');
        }
        
        if (this.timingData.performance?.slowLoadComplete) {
            recommendations.push('Optimize resource loading and consider code splitting');
        }
        
        return recommendations;
    }
}

// Auto-run diagnostics
console.log('🚀 [EnvTiming] Starting environment and timing diagnostics...');
const envDiagnostics = new EnvironmentTimingDiagnostics();
envDiagnostics.runDiagnostics();

// Make available globally
window.envTimingDiagnostics = envDiagnostics;
