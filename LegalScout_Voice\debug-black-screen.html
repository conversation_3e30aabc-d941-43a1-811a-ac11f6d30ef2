<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Black Screen Debug</title>
  <style>
    body {
      font-family: monospace;
      padding: 20px;
      background: #000;
      color: #00ff00;
      margin: 0;
    }
    .debug-output {
      white-space: pre-wrap;
      background: #111;
      padding: 10px;
      border: 1px solid #333;
      margin: 10px 0;
      max-height: 400px;
      overflow-y: auto;
    }
    .error { color: #ff4444; }
    .success { color: #44ff44; }
    .warning { color: #ffaa44; }
    .info { color: #4444ff; }
  </style>
</head>
<body>
  <h1>🔍 BLACK SCREEN DEBUGGER</h1>
  <div id="debug-output" class="debug-output">Starting debug...\n</div>
  
  <script>
    const output = document.getElementById('debug-output');
    
    function log(message, type = 'info') {
      const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
      const line = `[${timestamp}] ${message}\n`;
      output.textContent += line;
      output.scrollTop = output.scrollHeight;
      
      const color = {
        error: '#ff4444',
        success: '#44ff44', 
        warning: '#ffaa44',
        info: '#4444ff'
      }[type] || '#00ff00';
      
      console.log(`%c${line}`, `color: ${color}`);
    }
    
    // Capture all errors immediately
    window.addEventListener('error', (e) => {
      log(`❌ ERROR: ${e.message} at ${e.filename}:${e.lineno}:${e.colno}`, 'error');
      log(`Stack: ${e.error?.stack || 'No stack'}`, 'error');
    });
    
    window.addEventListener('unhandledrejection', (e) => {
      log(`❌ UNHANDLED REJECTION: ${e.reason}`, 'error');
    });
    
    // Override console methods to capture everything
    const originalLog = console.log;
    const originalError = console.error;
    const originalWarn = console.warn;
    
    console.log = (...args) => {
      log(`LOG: ${args.join(' ')}`, 'info');
      originalLog.apply(console, args);
    };
    
    console.error = (...args) => {
      log(`ERROR: ${args.join(' ')}`, 'error');
      originalError.apply(console, args);
    };
    
    console.warn = (...args) => {
      log(`WARN: ${args.join(' ')}`, 'warning');
      originalWarn.apply(console, args);
    };
    
    // Test basic environment
    log('🚀 Starting comprehensive black screen debug...', 'success');
    log(`URL: ${window.location.href}`, 'info');
    log(`User Agent: ${navigator.userAgent}`, 'info');
    log(`Document Ready State: ${document.readyState}`, 'info');
    
    // Test if main app is loading
    setTimeout(() => {
      log('⏰ 2 second check...', 'info');
      
      // Check if React root exists
      const root = document.getElementById('root');
      if (root) {
        log(`✅ Root element found: ${root.outerHTML.substring(0, 100)}...`, 'success');
        log(`Root children count: ${root.children.length}`, 'info');
        if (root.children.length === 0) {
          log('❌ ROOT IS EMPTY - This is likely the black screen cause!', 'error');
        }
      } else {
        log('❌ Root element not found!', 'error');
      }
      
      // Check if any scripts loaded
      const scripts = document.querySelectorAll('script');
      log(`Scripts found: ${scripts.length}`, 'info');
      
      // Check for React
      if (window.React) {
        log(`✅ React loaded: v${window.React.version}`, 'success');
      } else {
        log('❌ React not found in window', 'error');
      }
      
      // Check for main app script
      const mainScript = Array.from(scripts).find(s => s.src.includes('main') || s.src.includes('index'));
      if (mainScript) {
        log(`✅ Main script found: ${mainScript.src}`, 'success');
      } else {
        log('❌ Main script not found', 'error');
      }
      
      // Try to manually load the main app
      log('🔧 Attempting to manually load main app...', 'warning');
      
      const script = document.createElement('script');
      script.type = 'module';
      script.src = '/src/main.jsx';
      script.onload = () => log('✅ Main script loaded successfully', 'success');
      script.onerror = (e) => log(`❌ Main script failed to load: ${e}`, 'error');
      document.head.appendChild(script);
      
    }, 2000);
    
    // Check every 5 seconds
    setInterval(() => {
      const root = document.getElementById('root');
      if (root && root.children.length > 0) {
        log('🎉 ROOT NOW HAS CONTENT! Black screen resolved.', 'success');
      } else {
        log('⚠️ Root still empty...', 'warning');
      }
    }, 5000);
    
    log('Debug setup complete. Monitoring...', 'success');
  </script>
</body>
</html>
