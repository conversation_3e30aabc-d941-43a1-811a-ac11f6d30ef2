/**
 * Secure Vapi API Proxy
 * 
 * This endpoint provides secure access to Vapi operations without exposing
 * the secret API key to the client-side code.
 */

// Initialize Vapi client with server-side secret key
let vapiClient = null;

async function getVapiClient() {
  if (vapiClient) return vapiClient;

  try {
    // Use server-side environment variables (secure)
    const secretKey = process.env.VITE_VAPI_SECRET_KEY || process.env.VAPI_SECRET_KEY;
    
    if (!secretKey) {
      throw new Error('Vapi secret key not configured on server');
    }

    // Import Vapi MCP client
    const { VapiMcpClient } = await import('../src/services/vapiMcpService.js');
    vapiClient = new VapiMcpClient();
    await vapiClient.connect(secretKey);
    
    return vapiClient;
  } catch (error) {
    console.error('Failed to initialize Vapi client:', error);
    throw error;
  }
}

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    const { action, ...params } = req.body || {};
    
    if (!action) {
      return res.status(400).json({ error: 'Action parameter required' });
    }

    const vapi = await getVapiClient();

    switch (action) {
      case 'getAssistant':
        const { assistantId } = params;
        if (!assistantId) {
          return res.status(400).json({ error: 'assistantId required' });
        }
        
        const assistant = await vapi.getAssistant(assistantId);
        return res.status(200).json({ success: true, data: assistant });

      case 'listAssistants':
        const assistants = await vapi.listAssistants();
        return res.status(200).json({ success: true, data: assistants });

      case 'createAssistant':
        const { config } = params;
        if (!config) {
          return res.status(400).json({ error: 'config required' });
        }
        
        const newAssistant = await vapi.createAssistant(config);
        return res.status(200).json({ success: true, data: newAssistant });

      case 'updateAssistant':
        const { assistantId: updateId, updates } = params;
        if (!updateId || !updates) {
          return res.status(400).json({ error: 'assistantId and updates required' });
        }
        
        const updatedAssistant = await vapi.updateAssistant(updateId, updates);
        return res.status(200).json({ success: true, data: updatedAssistant });

      case 'createCall':
        const { callConfig } = params;
        if (!callConfig) {
          return res.status(400).json({ error: 'callConfig required' });
        }
        
        const call = await vapi.createCall(callConfig);
        return res.status(200).json({ success: true, data: call });

      default:
        return res.status(400).json({ error: `Unknown action: ${action}` });
    }

  } catch (error) {
    console.error('Vapi proxy error:', error);
    return res.status(500).json({ 
      error: 'Internal server error', 
      message: error.message 
    });
  }
}
