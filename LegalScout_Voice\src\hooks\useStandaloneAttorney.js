import { useState, useEffect, useCallback } from 'react';

/**
 * Hook to access the standalone attorney manager
 *
 * This hook provides access to the standalone attorney manager
 * from any React component.
 *
 * @returns {Object} The attorney state and methods
 */
export function useStandaloneAttorney() {
  const [attorney, setAttorney] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    let retryCount = 0;
    const maxRetries = 10;
    let retryTimeout;

    // Function to initialize with manager
    const initializeWithManager = () => {
      const manager = window.standaloneAttorneyManager;

      if (!manager) {
        console.log('[useStandaloneAttorney] Manager not ready, will retry...', `(attempt ${retryCount + 1}/${maxRetries})`);
        return false;
      }

      console.log('[useStandaloneAttorney] Manager found, initializing hook...');
      return manager;
    };

    // Function to retry initialization
    const retryInitialization = () => {
      if (retryCount >= maxRetries) {
        console.warn('[useStandaloneAttorney] Max retries reached, using fallback approach');

        // In development, try to load from localStorage as fallback
        if (process.env.NODE_ENV === 'development') {
          try {
            const storedAttorney = localStorage.getItem('attorney');
            if (storedAttorney) {
              const parsedAttorney = JSON.parse(storedAttorney);
              console.log('[useStandaloneAttorney] Using stored attorney as fallback:', parsedAttorney);
              setAttorney(parsedAttorney);
              return;
            }
          } catch (error) {
            console.error('[useStandaloneAttorney] Error loading fallback attorney:', error);
          }
        }

        setError(new Error('Attorney manager not available after maximum retries'));
        return;
      }

      const manager = initializeWithManager();
      if (manager) {
        setupManagerSubscription(manager);
      } else {
        retryCount++;
        retryTimeout = setTimeout(retryInitialization, 200 * retryCount); // Exponential backoff
      }
    };

    // Try to get manager immediately
    let manager = initializeWithManager();

    // If manager not ready, set up retry mechanism
    if (!manager) {
      console.log('[useStandaloneAttorney] Setting up retry mechanism for manager...');
      retryInitialization();
    } else {
      setupManagerSubscription(manager);
    }

    function setupManagerSubscription(manager) {
      console.log('[useStandaloneAttorney] Setting up manager subscription');

      // Set initial attorney state
      if (manager.attorney) {
        setAttorney(manager.attorney);
        setError(null);
      }

      // Subscribe to manager updates
      const unsubscribe = manager.subscribe((updatedAttorney) => {
        console.log('[useStandaloneAttorney] Manager updated attorney:', updatedAttorney?.id);
        setAttorney(updatedAttorney);
        setError(null);
      });

      // Return cleanup function
      return () => {
        if (retryTimeout) {
          clearTimeout(retryTimeout);
        }
        if (unsubscribe) {
          unsubscribe();
        }
      };
    }

    // Return cleanup function for useEffect
    return () => {
      if (retryTimeout) {
        clearTimeout(retryTimeout);
      }
    };
  }, []);

  const updateAttorney = useCallback(async (updates) => {
    try {
      // Get the standalone attorney manager
      const manager = window.standaloneAttorneyManager;

      if (!manager) {
        if (process.env.NODE_ENV === 'development') {
          console.error('[useStandaloneAttorney] Standalone attorney manager not found');
          // In development, just update the local state
          setAttorney(prev => ({ ...prev, ...updates }));
          return { ...attorney, ...updates };
        }
        throw new Error('Standalone attorney manager not found');
      }

      const updatedAttorney = await manager.updateAttorney(updates);
      return updatedAttorney;
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('[useStandaloneAttorney] Error updating attorney:', error);
      }
      setError(error);
      throw error;
    }
  }, [attorney]);

  const refreshAttorney = useCallback(() => {
    try {
      // Get the standalone attorney manager
      const manager = window.standaloneAttorneyManager;

      if (!manager) {
        if (process.env.NODE_ENV === 'development') {
          console.error('[useStandaloneAttorney] Standalone attorney manager not found');
        }
        return null;
      }

      return manager.loadFromLocalStorage();
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('[useStandaloneAttorney] Error refreshing attorney:', error);
      }
      setError(error);
      return null;
    }
  }, []);

  const loadAttorneyForUser = useCallback(async (userId) => {
    try {
      // Get the standalone attorney manager
      const manager = window.standaloneAttorneyManager;

      if (!manager) {
        if (process.env.NODE_ENV === 'development') {
          console.error('[useStandaloneAttorney] Standalone attorney manager not found');
        }
        throw new Error('Standalone attorney manager not found');
      }

      const loadedAttorney = await manager.loadAttorneyForUser(userId);
      // The hook's state will be updated via the manager's subscriber notification
      return loadedAttorney; // Return the result for potential chaining

    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('[useStandaloneAttorney] Error loading attorney for user:', error);
      }
      setError(error);
      // Re-throw the error so the caller can handle it
      throw error;
    }
  }, []);

  return {
    attorney,
    error,
    updateAttorney,
    refreshAttorney,
    loadAttorneyForUser
  };
}
