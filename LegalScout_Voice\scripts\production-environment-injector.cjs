/**
 * Production Environment Injector
 * 
 * This script ensures that all environment variables are properly injected
 * into the production build, creating multiple fallback mechanisms.
 */

const fs = require('fs');
const path = require('path');

// Environment variables to inject
const ENV_VARS = {
  // Supabase
  VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co',
  VITE_SUPABASE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU',
  VITE_SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU',
  
  // Vapi
  VITE_VAPI_PUBLIC_KEY: '310f0d43-27c2-47a5-a76d-e55171d024f7',
  VITE_VAPI_SECRET_KEY: '6734febc-fc65-4669-93b0-929b31ff6564',
  VITE_VAPI_PRIVATE_KEY: '6734febc-fc65-4669-93b0-929b31ff6564',
  
  // React App compatibility
  REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co',
  REACT_APP_SUPABASE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU',
  
  // Server-side compatibility
  SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co',
  SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU',
  VAPI_PUBLIC_KEY: '310f0d43-27c2-47a5-a76d-e55171d024f7',
  VAPI_SECRET_KEY: '6734febc-fc65-4669-93b0-929b31ff6564',
  VAPI_PRIVATE_KEY: '6734febc-fc65-4669-93b0-929b31ff6564',
  VAPI_TOKEN: '6734febc-fc65-4669-93b0-929b31ff6564'
};

// Create environment injection script
const createEnvInjectionScript = () => {
  const script = `
/**
 * Production Environment Variable Injector
 * 
 * This script injects environment variables into the global scope
 * to ensure they're available in production builds.
 */

(function() {
  console.log('[ProductionEnvInjector] Injecting environment variables...');

  // Define environment variables on window object
${Object.entries(ENV_VARS).map(([key, value]) => 
  `  window.${key} = '${value}';`
).join('\n')}

  // Ensure process.env exists for compatibility
  if (!window.process) {
    window.process = {
      env: {},
      browser: true,
      version: '',
      versions: { node: '' }
    };
  }

  // Copy environment variables to process.env
${Object.entries(ENV_VARS).map(([key, value]) => 
  `  window.process.env.${key} = '${value}';`
).join('\n')}

  // Set NODE_ENV
  window.process.env.NODE_ENV = 'production';

  // Ensure import.meta.env exists for compatibility
  if (!window.import) {
    window.import = {};
  }
  if (!window.import.meta) {
    window.import.meta = {
      env: {}
    };
  }

  // Copy environment variables to import.meta.env
${Object.entries(ENV_VARS).map(([key, value]) => 
  `  window.import.meta.env.${key} = '${value}';`
).join('\n')}

  // Set import.meta.env properties
  window.import.meta.env.MODE = 'production';
  window.import.meta.env.PROD = true;
  window.import.meta.env.DEV = false;

  console.log('[ProductionEnvInjector] ✅ Environment variables injected successfully');
  console.log('[ProductionEnvInjector] Available variables:', Object.keys(window).filter(k => k.startsWith('VITE_') || k.startsWith('REACT_APP_')));
})();
`;

  return script;
};

// Inject environment variables into index.html
const injectIntoIndexHtml = (distPath) => {
  const indexPath = path.join(distPath, 'index.html');
  
  if (!fs.existsSync(indexPath)) {
    console.error('❌ index.html not found at:', indexPath);
    return false;
  }

  let indexContent = fs.readFileSync(indexPath, 'utf8');
  
  // Create the injection script
  const injectionScript = createEnvInjectionScript();
  
  // Insert the script at the beginning of the head section
  const headInsertPoint = indexContent.indexOf('<head>') + '<head>'.length;
  
  if (headInsertPoint === -1 + '<head>'.length) {
    console.error('❌ Could not find <head> tag in index.html');
    return false;
  }

  const scriptTag = `
  <script>
    ${injectionScript}
  </script>`;

  indexContent = indexContent.slice(0, headInsertPoint) + scriptTag + indexContent.slice(headInsertPoint);
  
  // Write the updated content back
  fs.writeFileSync(indexPath, indexContent, 'utf8');
  
  console.log('✅ Environment variables injected into index.html');
  return true;
};

// Create standalone environment injection file
const createStandaloneInjectionFile = (distPath) => {
  const injectionScript = createEnvInjectionScript();
  const filePath = path.join(distPath, 'env-injection.js');

  fs.writeFileSync(filePath, injectionScript, 'utf8');

  console.log('✅ Standalone environment injection file created:', filePath);
  return true;
};

// Copy critical fix scripts to dist
const copyCriticalFixScripts = (distPath) => {
  const publicPath = path.join(process.cwd(), 'public');
  const criticalScripts = [
    'vm2-eval-polyfill.js',
    'production-csp-eval-fix.js',
    'production-cors-fix.js',
    'clean-auth-solution.js',
    'call-sync-diagnostic.js',
    'dashboard-iframe-manager.js',
    'system-test-integration.js'
  ];

  let copiedCount = 0;

  criticalScripts.forEach(scriptName => {
    const sourcePath = path.join(publicPath, scriptName);
    const destPath = path.join(distPath, scriptName);

    if (fs.existsSync(sourcePath)) {
      try {
        fs.copyFileSync(sourcePath, destPath);
        console.log(`✅ Copied critical script: ${scriptName}`);
        copiedCount++;
      } catch (error) {
        console.warn(`⚠️ Failed to copy ${scriptName}:`, error.message);
      }
    } else {
      console.warn(`⚠️ Critical script not found: ${scriptName}`);
    }
  });

  console.log(`✅ Copied ${copiedCount}/${criticalScripts.length} critical fix scripts`);
  return copiedCount > 0;
};

// Main injection function
const injectProductionEnvironment = () => {
  console.log('🚀 [ProductionEnvInjector] Starting production environment injection...');
  
  const distPath = path.join(process.cwd(), 'dist');
  
  if (!fs.existsSync(distPath)) {
    console.error('❌ dist directory not found. Make sure to run this after the build.');
    process.exit(1);
  }

  try {
    // Method 1: Inject into index.html
    const htmlInjected = injectIntoIndexHtml(distPath);

    // Method 2: Create standalone injection file
    const standaloneCreated = createStandaloneInjectionFile(distPath);

    // Method 3: Copy critical fix scripts
    const scriptsCopied = copyCriticalFixScripts(distPath);

    if (htmlInjected && standaloneCreated) {
      console.log('✅ [ProductionEnvInjector] Production environment injection completed successfully');
      if (scriptsCopied) {
        console.log('✅ [ProductionEnvInjector] Critical fix scripts copied successfully');
      }
    } else {
      console.warn('⚠️ [ProductionEnvInjector] Some injection methods failed, but continuing...');
    }

  } catch (error) {
    console.error('🚨 [ProductionEnvInjector] Error during injection:', error);
    process.exit(1);
  }
};

// Run if called directly
if (require.main === module) {
  injectProductionEnvironment();
}

module.exports = {
  injectProductionEnvironment,
  createEnvInjectionScript,
  ENV_VARS
};
