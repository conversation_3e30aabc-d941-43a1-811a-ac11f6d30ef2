/* Assistant-Aware Share Component Styles */

.assistant-aware-share {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* Header Styles */
.share-header {
  margin-bottom: 20px;
  border-bottom: 1px solid #e1e5e9;
  padding-bottom: 16px;
}

.share-header h3 {
  margin: 0 0 8px 0;
  color: #1a1a1a;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.assistant-name {
  color: #4B74AA;
  font-weight: 500;
}

.subdomain-info {
  margin-top: 8px;
}

.subdomain-info code {
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 14px;
  color: #4B74AA;
}

/* Warning Styles */
.no-assistant-warning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

/* Status Indicator */
.copy-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  margin-top: 12px;
  font-size: 14px;
  animation: slideIn 0.3s ease;
}

.copy-status.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.copy-status.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-close {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  font-size: 16px;
  margin-left: auto;
  padding: 0;
}

/* Share Sections */
.share-section {
  margin-bottom: 24px;
}

.share-section h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.share-section p {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
}

/* URL Field */
.url-field {
  display: flex;
  gap: 8px;
  align-items: stretch;
}

.share-url-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  font-family: 'Monaco', 'Menlo', monospace;
  background: #f8f9fa;
  color: #333;
}

.share-url-input:focus {
  outline: none;
  border-color: #4B74AA;
}

/* Copy Button */
.copy-button {
  padding: 12px 16px;
  background: #4B74AA;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.copy-button:hover:not(:disabled) {
  background: #3d5d8a;
}

.copy-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Embed Options */
.embed-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.embed-option {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.embed-option label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.code-field {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.code-textarea {
  flex: 1;
  padding: 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  background: #f8f9fa;
  color: #333;
  resize: vertical;
  min-height: 60px;
}

.code-textarea:focus {
  outline: none;
  border-color: #4B74AA;
}

/* Platform Buttons */
.platform-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.platform-button {
  padding: 10px 16px;
  border: 2px solid #e1e5e9;
  background: white;
  color: #333;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.platform-button:hover:not(:disabled) {
  border-color: #4B74AA;
  color: #4B74AA;
}

.platform-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.platform-button.email:hover:not(:disabled) {
  border-color: #dc3545;
  color: #dc3545;
}

.platform-button.social:hover:not(:disabled) {
  border-color: #1da1f2;
  color: #1da1f2;
}

.platform-button.sms:hover:not(:disabled) {
  border-color: #28a745;
  color: #28a745;
}

/* Variant Styles */

/* URL Only Variant */
.assistant-aware-share.url-only {
  padding: 16px;
}

/* Buttons Only Variant */
.assistant-aware-share.buttons-only {
  padding: 16px;
}

.share-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.share-button {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.share-button.primary {
  background: #4B74AA;
  color: white;
}

.share-button.primary:hover:not(:disabled) {
  background: #3d5d8a;
}

.share-button.secondary {
  background: white;
  color: #4B74AA;
  border: 2px solid #4B74AA;
}

.share-button.secondary:hover:not(:disabled) {
  background: #f8f9fa;
}

.share-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Compact Variant */
.assistant-aware-share.compact {
  padding: 12px;
  background: transparent;
  box-shadow: none;
}

.assistant-aware-share.compact h4 {
  margin-bottom: 12px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #fff;
}

.subdomain {
  color: #ccc;
  font-weight: 400;
  font-size: 12px;
}

.compact-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Vertical layout for sidebar */
.sidebar-share-component .compact-content {
  gap: 6px;
}

.sidebar-share-component .url-field {
  flex-direction: column;
  gap: 6px;
}

.sidebar-share-component .share-url-input {
  padding: 8px 12px;
  font-size: 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  border-radius: 4px;
}

.sidebar-share-component .copy-button {
  padding: 8px 12px;
  font-size: 12px;
  background: #4B74AA;
  border-radius: 4px;
  width: 100%;
  justify-content: center;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* Vertical quick actions for sidebar */
.sidebar-share-component .quick-actions {
  flex-direction: column;
  gap: 4px;
}

.quick-action {
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  cursor: pointer;
  color: #ccc;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  font-size: 12px;
  width: 100%;
}

.sidebar-share-component .quick-action {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #ccc;
  text-align: left;
  justify-content: flex-start;
}

.quick-action:hover:not(:disabled) {
  border-color: #4B74AA;
  color: #4B74AA;
  background: rgba(75, 116, 170, 0.1);
}

.quick-action:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Sidebar-specific styling */
.sidebar-share-component {
  margin: 0;
  padding: 0;
}

.sidebar-share-component .no-assistant-warning {
  background: rgba(255, 243, 205, 0.1);
  border: 1px solid rgba(255, 234, 167, 0.3);
  color: #ffc107;
  font-size: 12px;
  padding: 8px 12px;
  margin-bottom: 8px;
}

.sidebar-share-component .copy-status {
  font-size: 12px;
  padding: 6px 10px;
  margin-top: 8px;
}

/* Dark Theme Support */
.dark .assistant-aware-share {
  background: #1a1a1a;
  color: white;
}

.dark .share-header {
  border-bottom-color: #333;
}

.dark .share-header h3 {
  color: white;
}

.dark .share-section h4 {
  color: white;
}

.dark .share-section p {
  color: #ccc;
}

.dark .share-url-input,
.dark .code-textarea {
  background: #2a2a2a;
  border-color: #333;
  color: white;
}

.dark .platform-button {
  background: #2a2a2a;
  border-color: #333;
  color: white;
}

.dark .share-button.secondary {
  background: #2a2a2a;
}

.dark .quick-action {
  background: #2a2a2a;
  border-color: #333;
  color: #ccc;
}

/* Sidebar dark theme overrides */
.sidebar-share-component.dark .share-url-input {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: #fff;
}

.sidebar-share-component.dark .quick-action {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: #ccc;
}

/* Responsive Design */
@media (max-width: 768px) {
  .assistant-aware-share {
    padding: 16px;
  }
  
  .url-field {
    flex-direction: column;
  }
  
  .code-field {
    flex-direction: column;
  }
  
  .platform-buttons {
    flex-direction: column;
  }
  
  .share-buttons {
    flex-direction: column;
  }
  
  .compact-content {
    gap: 8px;
  }
  
  .quick-actions {
    justify-content: space-between;
  }
}
