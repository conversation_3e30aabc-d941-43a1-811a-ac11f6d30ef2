#!/usr/bin/env node

/**
 * Test Authentication Flow
 * 
 * This script tests the key components of the authentication system:
 * 1. API endpoint availability
 * 2. Supabase connection
 * 3. Vapi service connection
 * 4. manage-auth-state endpoint functionality
 */

import fetch from 'node-fetch';
import { createClient } from '@supabase/supabase-js';

// Configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:5174';
const API_BASE_URL = `${BASE_URL}/api`;

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logTest(testName) {
  console.log(`\n${colors.bold}${colors.blue}🧪 Testing: ${testName}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

async function testApiHealth() {
  logTest('API Health Check');
  
  try {
    const response = await fetch(`${API_BASE_URL}/health`);
    const data = await response.json();
    
    if (response.ok) {
      logSuccess(`API is healthy - Status: ${data.status}, Environment: ${data.environment}`);
      return true;
    } else {
      logError(`API health check failed - Status: ${response.status}`);
      return false;
    }
  } catch (error) {
    logError(`API health check error: ${error.message}`);
    return false;
  }
}

async function testEnvironmentConfig() {
  logTest('Environment Configuration');
  
  try {
    const response = await fetch(`${API_BASE_URL}/env`);
    const data = await response.json();
    
    if (response.ok) {
      logSuccess('Environment configuration retrieved');
      log(`  - Supabase URL: ${data.hasSupabaseUrl ? '✓' : '✗'}`);
      log(`  - Supabase Key: ${data.hasSupabaseKey ? '✓' : '✗'}`);
      log(`  - Vapi Key: ${data.hasVapiKey ? '✓' : '✗'}`);
      log(`  - Node Environment: ${data.nodeEnv}`);
      
      const allConfigured = data.hasSupabaseUrl && data.hasSupabaseKey && data.hasVapiKey;
      if (allConfigured) {
        logSuccess('All required environment variables are configured');
      } else {
        logWarning('Some environment variables are missing');
      }
      
      return allConfigured;
    } else {
      logError(`Environment config check failed - Status: ${response.status}`);
      return false;
    }
  } catch (error) {
    logError(`Environment config error: ${error.message}`);
    return false;
  }
}

async function testSupabaseConnection() {
  logTest('Supabase Connection');
  
  try {
    // Test environment variables
    const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      logError('Supabase environment variables not found');
      return false;
    }
    
    // Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Test connection by querying attorneys table
    const { data, error } = await supabase
      .from('attorneys')
      .select('id, email, firm_name')
      .limit(1);
    
    if (error) {
      logError(`Supabase connection error: ${error.message}`);
      return false;
    }
    
    logSuccess(`Supabase connection successful - Found ${data?.length || 0} attorneys`);
    return true;
  } catch (error) {
    logError(`Supabase connection error: ${error.message}`);
    return false;
  }
}

async function testManageAuthStateEndpoint() {
  logTest('manage-auth-state Endpoint');
  
  try {
    // Test with valid request
    const testAuthData = {
      authData: {
        user: {
          id: 'test-user-id',
          email: '<EMAIL>'
        },
        session: {
          access_token: 'test-token'
        }
      },
      action: 'login'
    };
    
    const response = await fetch(`${API_BASE_URL}/sync-tools/manage-auth-state`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testAuthData)
    });
    
    const data = await response.json();
    
    if (response.ok) {
      logSuccess('manage-auth-state endpoint is working');
      log(`  - Success: ${data.success}`);
      log(`  - Result: ${JSON.stringify(data.result || data, null, 2)}`);
      return true;
    } else {
      logError(`manage-auth-state endpoint failed - Status: ${response.status}`);
      log(`  - Error: ${JSON.stringify(data, null, 2)}`);
      return false;
    }
  } catch (error) {
    logError(`manage-auth-state endpoint error: ${error.message}`);
    return false;
  }
}

async function testVapiServiceConnection() {
  logTest('Vapi Service Connection');
  
  try {
    // Test Vapi config endpoint
    const response = await fetch(`${API_BASE_URL}/vapi/config`);
    const data = await response.json();
    
    if (response.ok) {
      logSuccess('Vapi configuration endpoint is working');
      log(`  - Available voices: ${data.voices?.length || 0}`);
      log(`  - Available models: ${data.models?.length || 0}`);
      return true;
    } else {
      logError(`Vapi config endpoint failed - Status: ${response.status}`);
      return false;
    }
  } catch (error) {
    logError(`Vapi service connection error: ${error.message}`);
    return false;
  }
}

async function runAllTests() {
  log(`${colors.bold}${colors.blue}🚀 Starting Authentication Flow Tests${colors.reset}\n`);
  
  const results = {
    apiHealth: await testApiHealth(),
    environmentConfig: await testEnvironmentConfig(),
    supabaseConnection: await testSupabaseConnection(),
    manageAuthState: await testManageAuthStateEndpoint(),
    vapiService: await testVapiServiceConnection()
  };
  
  // Summary
  console.log(`\n${colors.bold}📊 Test Results Summary${colors.reset}`);
  console.log('='.repeat(50));
  
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    const color = passed ? 'green' : 'red';
    log(`${test.padEnd(20)} ${status}`, color);
  });
  
  console.log('='.repeat(50));
  log(`Overall: ${passed}/${total} tests passed`, passed === total ? 'green' : 'red');
  
  if (passed === total) {
    logSuccess('🎉 All authentication components are working correctly!');
  } else {
    logError('⚠️  Some authentication components need attention.');
  }
  
  return passed === total;
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      logError(`Test runner error: ${error.message}`);
      process.exit(1);
    });
}

export { runAllTests };
