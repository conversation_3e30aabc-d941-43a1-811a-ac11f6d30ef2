/**
 * Authentication Service
 *
 * This service handles authentication with Supabase and synchronizes
 * the authentication state across systems.
 */

// Import the async Supabase client getter
import { getSupabaseClient, getRealSupabaseClient } from '../lib/supabase';

/**
 * Sign in with OAuth provider
 *
 * @param {string} provider - The OAuth provider (e.g., 'google', 'github')
 * @returns {Promise<Object>} The sign in result
 */
export const signInWithOAuth = async (provider) => {
  try {
    console.log('Starting OAuth sign-in with direct URL approach...');

    // Import the direct OAuth function
    const { signInWithGoogle } = await import('../lib/supabase');

    if (provider === 'google') {
      const result = await signInWithGoogle();
      return { success: true, data: result.data };
    } else {
      throw new Error(`Provider ${provider} not supported with direct OAuth`);
    }
  } catch (error) {
    console.error('OAuth sign in error:', error);
    return { success: false, error };
  }
};

/**
 * Handle OAuth callback with synchronization
 *
 * @param {Function} handleAuthState - Function to handle authentication state
 * @returns {Promise<Object>} The callback result
 */
export const handleOAuthCallback = async (handleAuthState) => {
  try {
    console.log('Handling OAuth callback with real Supabase client...');
    const supabase = await getRealSupabaseClient();
    const { data, error } = await supabase.auth.getSession();

    if (error) throw error;

    if (data.session) {
      // Use sync tools to handle authentication state
      if (handleAuthState) {
        const syncResult = await handleAuthState({
          user: data.session.user,
          session: data.session
        }, 'login');

        return {
          success: true,
          session: data.session,
          attorney: syncResult.attorney,
          syncResult
        };
      }

      return {
        success: true,
        session: data.session
      };
    }

    return { success: false, message: 'No session found' };
  } catch (error) {
    console.error('OAuth callback error:', error);
    return { success: false, error };
  }
};

/**
 * Sign out with synchronization
 *
 * @param {Function} handleAuthState - Function to handle authentication state
 * @returns {Promise<Object>} The sign out result
 */
export const signOut = async (handleAuthState) => {
  try {
    const supabase = await getSupabaseClient();

    // Get current session before signing out
    const { data: sessionData } = await supabase.auth.getSession();

    // Sign out from Supabase
    const { error } = await supabase.auth.signOut();

    if (error) throw error;

    // Use sync tools to handle logout
    if (handleAuthState && sessionData.session) {
      await handleAuthState({
        user: sessionData.session.user,
        session: sessionData.session
      }, 'logout');
    }

    return { success: true };
  } catch (error) {
    console.error('Sign out error:', error);
    return { success: false, error };
  }
};

/**
 * Get current session and sync attorney data
 *
 * @param {Function} handleAuthState - Function to handle authentication state
 * @returns {Promise<Object>} The session result
 */
export const getCurrentSession = async (handleAuthState) => {
  try {
    console.log('Getting current session with real Supabase client...');
    const supabase = await getRealSupabaseClient();
    const { data, error } = await supabase.auth.getSession();

    if (error) throw error;

    if (data.session) {
      // Use sync tools to refresh authentication state
      if (handleAuthState) {
        const syncResult = await handleAuthState({
          user: data.session.user,
          session: data.session
        }, 'refresh');

        if (syncResult.success) {
          return {
            success: true,
            session: data.session,
            attorney: syncResult.attorney,
            syncResult
          };
        }
      }

      return {
        success: true,
        session: data.session
      };
    }

    return { success: false, message: 'No session found' };
  } catch (error) {
    console.error('Get current session error:', error);
    return { success: false, error };
  }
};
