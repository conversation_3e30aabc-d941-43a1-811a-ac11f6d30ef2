import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import excludeFramerMotion from './vite-plugin-exclude-framer-motion'
import path from 'path'
import { visualizer } from 'rollup-plugin-visualizer'
import { VitePWA } from 'vite-plugin-pwa'
import viteCompression from 'vite-plugin-compression'
import fs from 'fs'

// Custom plugin to selectively copy public files
function selectivePublicCopy() {
  return {
    name: 'selective-public-copy',
    generateBundle() {
      const publicDir = path.resolve(process.cwd(), 'public');
      const outDir = path.resolve(process.cwd(), 'dist');

      // Files to keep
      const keepPatterns = [
        'favicon.ico',
        'vite.svg',
        '_preload.html',
        'subdomain_config.json',
        'sample-consultations.csv',
        /\.(png|jpg|jpeg|gif|webp|svg)$/i,
        'api',
        'assets',
        '_framer',
        'direct-fix',
        'direct-patch'
      ];

      // Copy only essential files
      if (fs.existsSync(publicDir)) {
        const files = fs.readdirSync(publicDir);

        for (const file of files) {
          const shouldKeep = keepPatterns.some(pattern => {
            if (typeof pattern === 'string') {
              return file === pattern;
            } else if (pattern instanceof RegExp) {
              return pattern.test(file);
            }
            return false;
          });

          if (shouldKeep) {
            const srcPath = path.join(publicDir, file);
            const destPath = path.join(outDir, file);

            try {
              const stat = fs.statSync(srcPath);
              if (stat.isDirectory()) {
                // Copy directory recursively
                fs.cpSync(srcPath, destPath, { recursive: true });
              } else {
                // Copy file
                fs.copyFileSync(srcPath, destPath);
              }
              console.log(`✅ Copied: ${file}`);
            } catch (error) {
              console.warn(`⚠️ Failed to copy ${file}:`, error.message);
            }
          }
        }
      }
    }
  };
}

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  define: {
    // Build-time constants
    __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
    __VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
    __DEV__: mode === 'development',
    __PROD__: mode === 'production',

    // Critical: Ensure global process object exists in production
    'global': 'globalThis',
    'process.env.NODE_ENV': JSON.stringify(mode),

    // ROOT CAUSE FIX: Use Vite's proper environment variable handling
    // These will be statically replaced at build time per Vite documentation
    'import.meta.env.VITE_SUPABASE_URL': JSON.stringify(
      process.env.VITE_SUPABASE_URL ||
      process.env.SUPABASE_URL ||
      'https://utopqxsvudgrtiwenlzl.supabase.co'
    ),
    'import.meta.env.VITE_SUPABASE_KEY': JSON.stringify(
      process.env.VITE_SUPABASE_KEY ||
      process.env.VITE_SUPABASE_ANON_KEY ||
      process.env.SUPABASE_ANON_KEY ||
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU'
    ),
    'import.meta.env.VITE_VAPI_PUBLIC_KEY': JSON.stringify(
      process.env.VITE_VAPI_PUBLIC_KEY ||
      '310f0d43-27c2-47a5-a76d-e55171d024f7'
    ),
    'import.meta.env.VITE_VAPI_SECRET_KEY': JSON.stringify(
      process.env.VITE_VAPI_SECRET_KEY ||
      '6734febc-fc65-4669-93b0-929b31ff6564'
    ),
    'import.meta.env.VITE_SUPABASE_ANON_KEY': JSON.stringify(
      process.env.VITE_SUPABASE_ANON_KEY ||
      process.env.VITE_SUPABASE_KEY ||
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU'
    ),
    'import.meta.env.VITE_VAPI_PRIVATE_KEY': JSON.stringify(process.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564'),
    'import.meta.env.MODE': JSON.stringify(mode),
    'import.meta.env.DEV': mode === 'development',
    'import.meta.env.PROD': mode === 'production'
  },
  plugins: [
    react(),
    excludeFramerMotion(),

    // Custom selective public copy
    selectivePublicCopy(),

    // Bundle analyzer (only in build mode)
    mode === 'production' && visualizer({
      filename: 'dist/stats.html',
      open: false,
      gzipSize: true,
      brotliSize: true,
    }),

    // Compression for production
    mode === 'production' && viteCompression({
      algorithm: 'gzip',
      ext: '.gz',
    }),

    mode === 'production' && viteCompression({
      algorithm: 'brotliCompress',
      ext: '.br',
    }),

    // PWA temporarily disabled due to PostCSS conflicts
    // TODO: Re-enable after resolving ES module issues
  ].filter(Boolean),
  build: {
    // MINIMAL BUILD CONFIG TO FIX EMPTY CHUNKS
    rollupOptions: {
      output: {
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js',
        format: 'es'
      }
    },
    target: 'es2020',
    outDir: 'dist',
    assetsDir: 'assets',
    emptyOutDir: true,
    sourcemap: mode === 'development',
    copyPublicDir: false,
    minify: mode === 'production' ? 'esbuild' : false,
    cssMinify: mode === 'production' ? 'esbuild' : false,
    cssCodeSplit: true,
    reportCompressedSize: false,
    write: true
  },
  optimizeDeps: {
    include: ['react', 'react-dom', 'react-router-dom'],
    exclude: ['framer-motion', 'framer-motion/*', 'three', 'three/*']
  },
  resolve: {
    extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
    alias: {
      // Add explicit alias for problematic Framer Motion modules
      'framer-motion': path.resolve(__dirname, 'src/mocks/framer-motion.js'),
      'framer-motion/dist/es/context/LayoutGroupContext.mjs': path.resolve(__dirname, 'src/mocks/LayoutGroupContext.js'),
      'framer-motion/dist/es/context/MotionConfigContext.mjs': path.resolve(__dirname, 'src/mocks/MotionConfigContext.js')
    }
  },
  publicDir: 'public',
  server: {
    port: 5173,
    strictPort: false,
    host: true,
    hmr: {
      timeout: 5000
    },
    proxy: {
      // Proxy Vapi MCP requests to bypass CORS during development
      '/vapi-mcp/sse': {
        target: 'https://mcp.vapi.ai/sse',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/vapi-mcp\/sse/, ''),
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('Vapi proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Vapi Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Vapi Response from the Target:', proxyRes.statusCode, req.url);
          });
        },
      },
      // Proxy for the Vapi MCP server
      '/vapi-mcp-server/sse': {
        target: 'https://mcp.vapi.ai',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/vapi-mcp-server\/sse/, '/sse'),
        headers: {
          'Authorization': `Bearer ${process.env.VITE_VAPI_PUBLIC_KEY || '310f0d43-27c2-47a5-a76d-e55171d024f7'}`,
        },
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('Vapi MCP server proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Vapi MCP server request:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Vapi MCP server response:', proxyRes.statusCode, req.url);
          });
        },
      },
      // Proxy for Vapi API requests
      '/api/vapi-proxy': {
        target: 'https://api.vapi.ai',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/api\/vapi-proxy/, ''),
        headers: {
          'Authorization': `Bearer ${process.env.VITE_VAPI_SECRET_KEY || '6734febc-fc65-4669-93b0-929b31ff6564'}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('Vapi proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Vapi proxy request:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Vapi proxy response:', proxyRes.statusCode, req.url);
          });
        },
      },
      // Proxy for Slack webhook to bypass CORS in development
      '/api/bug-report': {
        target: '*******************************************************************************',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/api\/bug-report/, ''),
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('Slack webhook proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Slack webhook request:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Slack webhook response:', proxyRes.statusCode, req.url);
          });
        },
      },
      // Add other proxies if needed
      // Proxy for Supabase API requests
      '/supabase-proxy': {
        target: 'https://utopqxsvudgrtiwenlzl.supabase.co',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/supabase-proxy/, ''),
        headers: {
          'apikey': process.env.VITE_SUPABASE_KEY || '',
        },
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('Supabase proxy error', err);
          });
        },
      },
      // Health endpoint for Vapi Web SDK
      '/api/health': {
        target: 'http://localhost:5173',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => '/api/health.json',
        configure: (proxy, _options) => {
          proxy.on('error', (err, req, res) => {
            console.log('Health endpoint error:', err.message);
            // Fallback response for health check
            res.writeHead(200, {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'GET, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            });
            res.end(JSON.stringify({
              status: 'healthy',
              timestamp: new Date().toISOString(),
              service: 'LegalScout Voice API (Development)',
              version: '1.0.0',
              environment: 'development'
            }));
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Health check request:', req.method, req.url);
          });
        },
      },
      // Proxy API requests to development server
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, req, res) => {
            console.log('API proxy error:', err.message);
            console.log('Make sure dev server is running: npm run dev:api');
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Proxying API request:', req.method, req.url);
          });
        },
      },
      // Proxy for mock services to handle CORS
      '/mock-*': {
        target: 'http://localhost:5173',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // Handle OPTIONS requests for CORS preflight
            if (req.method === 'OPTIONS') {
              res.writeHead(200, {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept',
                'Access-Control-Max-Age': '86400'
              });
              res.end();
              return;
            }
          });
        },
      }
    }
  },
  base: '/'
}))