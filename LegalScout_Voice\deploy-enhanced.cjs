/**
 * Enhanced Deployment Script
 * 
 * Comprehensive deployment with testing, validation, and monitoring
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');

class EnhancedDeploymentManager {
  constructor() {
    this.steps = [];
    this.errors = [];
    this.warnings = [];
    this.currentStep = 0;
  }

  async deploy() {
    console.log('🚀 LegalScout Enhanced Deployment Manager');
    console.log('=========================================');

    try {
      await this.preDeploymentChecks();
      await this.runLocalTests();
      await this.buildApplication();
      await this.validateBuild();
      await this.commitAndPush();
      await this.monitorDeployment();
      
      this.deploymentSuccess();
    } catch (error) {
      console.error('❌ Deployment failed:', error);
      this.deploymentFailed(error);
    }
  }

  async preDeploymentChecks() {
    this.logStep('Pre-deployment Checks');
    
    try {
      // Check Node.js version
      console.log(`📦 Node.js version: ${process.version}`);
      
      // Check git status
      const gitStatus = await this.runCommand('git status --porcelain');
      if (gitStatus.stdout.trim()) {
        console.log('📝 Uncommitted changes detected');
      } else {
        console.log('✅ Working directory clean');
      }

      // Check current branch
      const currentBranch = await this.runCommand('git branch --show-current');
      console.log(`🌿 Current branch: ${currentBranch.stdout.trim()}`);

      // Check critical files
      const criticalFiles = [
        'package.json',
        'src/main.jsx',
        'src/App.jsx',
        'index.html',
        'vite.config.js',
        'vercel.json'
      ];

      for (const file of criticalFiles) {
        if (fs.existsSync(file)) {
          console.log(`✅ ${file} exists`);
        } else {
          this.warnings.push(`File missing: ${file}`);
        }
      }

      this.steps.push('Pre-deployment checks: COMPLETED');
    } catch (error) {
      throw new Error(`Pre-deployment checks failed: ${error.message}`);
    }
  }

  async runLocalTests() {
    this.logStep('Running Local Tests');
    
    try {
      console.log('🧪 Running test suite...');
      
      // Run the critical issues test if available
      if (fs.existsSync('tests/critical-issues-test-suite.js')) {
        console.log('🔍 Running critical issues tests...');
        const testResult = await this.runCommand('node tests/critical-issues-test-suite.js');
        
        if (!testResult.success) {
          this.warnings.push('Critical issues tests had warnings');
        }
      }

      // Run npm test if available
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      if (packageJson.scripts && packageJson.scripts.test) {
        console.log('🧪 Running npm test...');
        const testResult = await this.runCommand('npm test');
        
        if (!testResult.success) {
          this.warnings.push('npm test failed');
        }
      }

      console.log('✅ Local tests completed');
      this.steps.push('Local tests: COMPLETED');
    } catch (error) {
      this.warnings.push(`Local tests failed: ${error.message}`);
    }
  }

  async buildApplication() {
    this.logStep('Building Application');
    
    try {
      console.log('🔨 Running production build...');
      
      // Clean previous build
      if (fs.existsSync('dist')) {
        console.log('🧹 Cleaning previous build...');
        await this.runCommand('rm -rf dist');
      }
      
      // Run build
      const buildResult = await this.runCommand('npm run vercel-build-safe');
      
      if (!buildResult.success) {
        throw new Error(`Build failed: ${buildResult.error}`);
      }
      
      console.log('✅ Build completed successfully');
      this.steps.push('Application build: COMPLETED');
    } catch (error) {
      throw new Error(`Build failed: ${error.message}`);
    }
  }

  async validateBuild() {
    this.logStep('Validating Build');
    
    try {
      // Check if dist directory exists
      if (!fs.existsSync('dist')) {
        throw new Error('dist directory not created');
      }
      
      // Check critical build files
      const criticalBuildFiles = [
        'dist/index.html',
        'dist/assets'
      ];
      
      for (const file of criticalBuildFiles) {
        if (fs.existsSync(file)) {
          console.log(`✅ ${file} exists`);
        } else {
          throw new Error(`Critical build file missing: ${file}`);
        }
      }
      
      // Check build size
      const distStats = this.getDirectorySize('dist');
      const sizeMB = (distStats / 1024 / 1024).toFixed(2);
      console.log(`📊 Build size: ${sizeMB} MB`);
      
      if (distStats > 100 * 1024 * 1024) { // 100MB
        this.warnings.push(`Build size is large (${sizeMB}MB)`);
      }
      
      // Validate index.html
      const indexContent = fs.readFileSync('dist/index.html', 'utf8');
      if (!indexContent.includes('<div id="root">')) {
        this.warnings.push('index.html may be missing React root element');
      }
      
      console.log('✅ Build validation completed');
      this.steps.push('Build validation: COMPLETED');
    } catch (error) {
      throw new Error(`Build validation failed: ${error.message}`);
    }
  }

  async commitAndPush() {
    this.logStep('Committing and Pushing Changes');
    
    try {
      // Check if there are changes to commit
      const gitStatus = await this.runCommand('git status --porcelain');
      
      if (!gitStatus.stdout.trim()) {
        console.log('✅ No changes to commit');
        this.steps.push('Git operations: NO CHANGES');
        return;
      }
      
      console.log('📝 Adding changes to git...');
      await this.runCommand('git add .');
      
      const timestamp = new Date().toISOString();
      const commitMessage = `Enhanced Deployment: ${timestamp}

✅ Pre-deployment checks passed
✅ Local tests completed
✅ Build validation successful
🚀 Ready for production deployment

Automated enhanced deployment commit`;
      
      console.log('💾 Committing changes...');
      const commitResult = await this.runCommand(`git commit -m "${commitMessage}"`);
      
      if (!commitResult.success) {
        throw new Error(`Commit failed: ${commitResult.error}`);
      }
      
      console.log('🚀 Pushing to GitHub...');
      const pushResult = await this.runCommand('git push origin main');
      
      if (!pushResult.success) {
        throw new Error(`Push failed: ${pushResult.error}`);
      }
      
      console.log('✅ Successfully pushed to GitHub');
      this.steps.push('Git operations: COMPLETED');
    } catch (error) {
      throw new Error(`Git operations failed: ${error.message}`);
    }
  }

  async monitorDeployment() {
    this.logStep('Monitoring Deployment');
    
    try {
      console.log('👀 Monitoring Vercel deployment...');
      console.log('🌐 Vercel Dashboard: https://vercel.com/dashboard');
      
      // Wait for Vercel to start deployment
      console.log('⏳ Waiting for Vercel to start deployment...');
      await this.sleep(15000);
      
      // Test production URLs
      const productionUrls = [
        'https://legalscout.net',
        'https://legalscout-git-main-damonkosts-projects.vercel.app'
      ];
      
      console.log('🔍 Testing production URLs...');
      
      for (const url of productionUrls) {
        try {
          console.log(`Testing ${url}...`);
          const response = await this.testURL(url);
          
          if (response.success) {
            console.log(`✅ ${url} - Status: ${response.status}, Time: ${response.responseTime}ms`);
          } else {
            console.log(`⚠️ ${url} - Error: ${response.error}`);
          }
        } catch (error) {
          console.log(`❌ ${url} - Failed: ${error.message}`);
        }
      }
      
      this.steps.push('Deployment monitoring: COMPLETED');
    } catch (error) {
      this.warnings.push(`Deployment monitoring failed: ${error.message}`);
    }
  }

  async runCommand(command) {
    return new Promise((resolve) => {
      exec(command, (error, stdout, stderr) => {
        if (error) {
          resolve({ success: false, error: error.message, stdout, stderr });
        } else {
          resolve({ success: true, stdout, stderr });
        }
      });
    });
  }

  async testURL(url) {
    const startTime = Date.now();
    
    try {
      const response = await fetch(url);
      const responseTime = Date.now() - startTime;
      
      return {
        success: response.ok,
        status: response.status,
        responseTime,
        error: response.ok ? null : `HTTP ${response.status}`
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        responseTime: Date.now() - startTime
      };
    }
  }

  getDirectorySize(dirPath) {
    let totalSize = 0;
    
    function calculateSize(currentPath) {
      const stats = fs.statSync(currentPath);
      
      if (stats.isFile()) {
        totalSize += stats.size;
      } else if (stats.isDirectory()) {
        const files = fs.readdirSync(currentPath);
        files.forEach(file => {
          calculateSize(require('path').join(currentPath, file));
        });
      }
    }
    
    if (fs.existsSync(dirPath)) {
      calculateSize(dirPath);
    }
    
    return totalSize;
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  logStep(stepName) {
    this.currentStep++;
    console.log(`\n${this.currentStep}. ${stepName}`);
    console.log('='.repeat(stepName.length + 4));
  }

  deploymentSuccess() {
    console.log('\n🎉 Enhanced Deployment Completed Successfully!');
    console.log('=============================================');
    
    console.log('\n✅ Completed Steps:');
    this.steps.forEach(step => console.log(`  - ${step}`));
    
    if (this.warnings.length > 0) {
      console.log('\n⚠️ Warnings:');
      this.warnings.forEach(warning => console.log(`  - ${warning}`));
    }
    
    console.log('\n🌐 Your application is available at:');
    console.log('  - https://legalscout.net');
    console.log('  - https://legalscout-git-main-damonkosts-projects.vercel.app');
    
    console.log('\n📊 Post-Deployment Checklist:');
    console.log('  ✅ Test critical user flows');
    console.log('  ✅ Check browser console for errors');
    console.log('  ✅ Verify Vapi integration works');
    console.log('  ✅ Test assistant functionality');
    console.log('  ✅ Validate Supabase connections');
    
    console.log('\n🔍 Monitoring Resources:');
    console.log('  - Vercel Dashboard: https://vercel.com/dashboard');
    console.log('  - GitHub Repository: https://github.com/damonkost/LegalScout_Voice');
    console.log('  - Production Site: https://legalscout.net');
  }

  deploymentFailed(error) {
    console.log('\n❌ Enhanced Deployment Failed');
    console.log('=============================');
    
    console.log(`\n🚨 Error: ${error.message}`);
    
    if (this.steps.length > 0) {
      console.log('\n✅ Completed Steps:');
      this.steps.forEach(step => console.log(`  - ${step}`));
    }
    
    if (this.warnings.length > 0) {
      console.log('\n⚠️ Warnings:');
      this.warnings.forEach(warning => console.log(`  - ${warning}`));
    }
    
    console.log('\n🔧 Troubleshooting Steps:');
    console.log('  1. Review the error message above');
    console.log('  2. Run local tests: npm run test:all-critical');
    console.log('  3. Test build locally: npm run vercel-build-safe');
    console.log('  4. Check git status and resolve conflicts');
    console.log('  5. Review Vercel deployment logs');
    console.log('  6. Open test-critical-issues.html for detailed testing');
    
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  const deployer = new EnhancedDeploymentManager();
  deployer.deploy().catch(console.error);
}

module.exports = EnhancedDeploymentManager;
