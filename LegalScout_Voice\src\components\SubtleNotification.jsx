/**
 * Subtle Notification Component
 * 
 * A modern, non-intrusive notification system that appears as a subtle toast
 * instead of blocking popup alerts.
 */

import React, { useState, useEffect } from 'react';
import './SubtleNotification.css';

const SubtleNotification = ({ 
  message, 
  type = 'success', 
  duration = 5000, 
  onClose,
  position = 'top-right',
  showIcon = true 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    // Slide in animation
    const timer = setTimeout(() => setIsVisible(true), 100);
    
    // Auto-hide after duration
    const hideTimer = setTimeout(() => {
      handleClose();
    }, duration);

    return () => {
      clearTimeout(timer);
      clearTimeout(hideTimer);
    };
  }, [duration]);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => {
      setIsVisible(false);
      if (onClose) onClose();
    }, 300);
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return '✅';
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'notification-top-left';
      case 'top-center':
        return 'notification-top-center';
      case 'top-right':
        return 'notification-top-right';
      case 'bottom-left':
        return 'notification-bottom-left';
      case 'bottom-center':
        return 'notification-bottom-center';
      case 'bottom-right':
        return 'notification-bottom-right';
      default:
        return 'notification-top-right';
    }
  };

  if (!isVisible && !isExiting) return null;

  return (
    <div 
      className={`
        subtle-notification 
        subtle-notification-${type} 
        ${getPositionClasses()}
        ${isVisible && !isExiting ? 'notification-enter' : ''}
        ${isExiting ? 'notification-exit' : ''}
      `}
      onClick={handleClose}
    >
      {showIcon && (
        <span className="notification-icon">
          {getIcon()}
        </span>
      )}
      <span className="notification-message">
        {message}
      </span>
      <button 
        className="notification-close"
        onClick={handleClose}
        aria-label="Close notification"
      >
        ×
      </button>
    </div>
  );
};

// Notification Manager for programmatic usage
class NotificationManager {
  constructor() {
    this.notifications = [];
    this.container = null;
    this.createContainer();
  }

  createContainer() {
    if (typeof window === 'undefined') return;
    
    this.container = document.createElement('div');
    this.container.id = 'subtle-notifications-container';
    this.container.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;
      z-index: 10000;
    `;
    document.body.appendChild(this.container);
  }

  show(message, options = {}) {
    if (typeof window === 'undefined') return;

    const {
      type = 'success',
      duration = 5000,
      position = 'top-right',
      showIcon = true
    } = options;

    const notificationId = Date.now() + Math.random();
    
    const notification = document.createElement('div');
    notification.className = `subtle-notification subtle-notification-${type} ${this.getPositionClass(position)}`;
    notification.style.pointerEvents = 'auto';
    
    notification.innerHTML = `
      ${showIcon ? `<span class="notification-icon">${this.getIcon(type)}</span>` : ''}
      <span class="notification-message">${message}</span>
      <button class="notification-close" aria-label="Close notification">×</button>
    `;

    // Add styles
    this.addStyles(notification, type, position);

    // Add to container
    this.container.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.classList.add('notification-enter');
    }, 100);

    // Auto-remove
    const removeTimer = setTimeout(() => {
      this.remove(notification);
    }, duration);

    // Close button handler
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
      clearTimeout(removeTimer);
      this.remove(notification);
    });

    // Click to dismiss
    notification.addEventListener('click', () => {
      clearTimeout(removeTimer);
      this.remove(notification);
    });

    return notificationId;
  }

  remove(notification) {
    notification.classList.add('notification-exit');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }

  addStyles(notification, type, position) {
    const baseStyles = `
      position: fixed;
      padding: 12px 16px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
      max-width: 400px;
      min-width: 300px;
      cursor: pointer;
      transition: all 0.3s ease;
      transform: translateX(100%);
      opacity: 0;
    `;

    const typeStyles = {
      success: 'background: #10B981; color: white;',
      error: 'background: #EF4444; color: white;',
      warning: 'background: #F59E0B; color: white;',
      info: 'background: #3B82F6; color: white;'
    };

    const positionStyles = {
      'top-right': 'top: 20px; right: 20px;',
      'top-left': 'top: 20px; left: 20px;',
      'top-center': 'top: 20px; left: 50%; transform: translateX(-50%);',
      'bottom-right': 'bottom: 20px; right: 20px;',
      'bottom-left': 'bottom: 20px; left: 20px;',
      'bottom-center': 'bottom: 20px; left: 50%; transform: translateX(-50%);'
    };

    notification.style.cssText = baseStyles + typeStyles[type] + positionStyles[position];

    // Close button styles
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.style.cssText = `
      background: none;
      border: none;
      color: inherit;
      font-size: 18px;
      cursor: pointer;
      padding: 0;
      margin-left: auto;
      opacity: 0.7;
      transition: opacity 0.2s;
    `;

    closeBtn.addEventListener('mouseenter', () => {
      closeBtn.style.opacity = '1';
    });

    closeBtn.addEventListener('mouseleave', () => {
      closeBtn.style.opacity = '0.7';
    });
  }

  getPositionClass(position) {
    return `notification-${position.replace('-', '-')}`;
  }

  getIcon(type) {
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };
    return icons[type] || '✅';
  }

  // Convenience methods
  success(message, options = {}) {
    return this.show(message, { ...options, type: 'success' });
  }

  error(message, options = {}) {
    return this.show(message, { ...options, type: 'error' });
  }

  warning(message, options = {}) {
    return this.show(message, { ...options, type: 'warning' });
  }

  info(message, options = {}) {
    return this.show(message, { ...options, type: 'info' });
  }
}

// Create global instance
const notificationManager = new NotificationManager();

// Add CSS animations
if (typeof window !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    .notification-enter {
      transform: translateX(0) !important;
      opacity: 1 !important;
    }
    
    .notification-exit {
      transform: translateX(100%) !important;
      opacity: 0 !important;
    }
    
    .subtle-notification:hover {
      transform: scale(1.02) !important;
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2) !important;
    }
  `;
  document.head.appendChild(style);
}

// Export both component and manager
export default SubtleNotification;
export { notificationManager };

// Global helper functions
window.showSubtleNotification = (message, options) => {
  return notificationManager.show(message, options);
};

window.showSuccessNotification = (message, options) => {
  return notificationManager.success(message, options);
};

window.showErrorNotification = (message, options) => {
  return notificationManager.error(message, options);
};

window.showWarningNotification = (message, options) => {
  return notificationManager.warning(message, options);
};

window.showInfoNotification = (message, options) => {
  return notificationManager.info(message, options);
};
