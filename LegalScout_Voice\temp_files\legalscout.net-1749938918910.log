 🚀 [LegalScout] Initializing environment...
 ✅ [LegalScout] Environment initialized
 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
emergency-auth-fix.js:1 Uncaught 
 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 [VapiMcpService] Created clean fetch from iframe
 [VapiMcpService] INFO: Vapi MCP Service initialized Object
 🚨 [App.jsx] Dashboard accessed without user - showing debug info
hwe @ index-3d96563a.js:987
hook.js:608 ❌ No email found from any source!
overrideMethod @ hook.js:608
 🚀 [LegalScout] Initializing environment...
 ✅ [LegalScout] Environment initialized
 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
emergency-auth-fix.js:1 Uncaught SyntaxError: Unexpected token '<'
 🚀 [LegalScout] Initializing environment...
 ✅ [LegalScout] Environment initialized
 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
emergency-auth-fix.js:1 Uncaught SyntaxError: Unexpected token '<'
 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 [VapiMcpService] Created clean fetch from iframe
 [VapiMcpService] INFO: Vapi MCP Service initialized [object Object]
 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 [VapiMcpService] Created clean fetch from iframe
 [VapiMcpService] INFO: Vapi MCP Service initialized [object Object]
index.ts:5 Loaded contentScript
dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 405 ()
hook.js:608 Auth state error details: Empty response from server
overrideMethod @ hook.js:608
hook.js:608 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (index-3d96563a.js:164:20810)
    at async Object.handleAuthState (index-3d96563a.js:164:30367)
    at async index-3d96563a.js:48:80287
overrideMethod @ hook.js:608
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 405 ()
api/sync-tools/check-preview-consistency:1  Failed to load resource: the server responded with a status of 405 ()
hook.js:608 Check consistency error details: Empty response from server
overrideMethod @ hook.js:608
hook.js:608 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.checkPreviewConsistency (index-3d96563a.js:164:27436)
    at async index-3d96563a.js:164:28903
    at async Object.handleAuthState (index-3d96563a.js:164:30574)
    at async index-3d96563a.js:48:80287
overrideMethod @ hook.js:608
hook.js:608 No suitable preview iframe found or contentWindow not accessible
overrideMethod @ hook.js:608
hook.js:608 No suitable preview iframe found or contentWindow not accessible
overrideMethod @ hook.js:608
hook.js:608 No suitable preview iframe found or contentWindow not accessible
overrideMethod @ hook.js:608
hook.js:608 No suitable preview iframe found or contentWindow not accessible
overrideMethod @ hook.js:608
hook.js:608 No suitable preview iframe found or contentWindow not accessible
overrideMethod @ hook.js:608
hook.js:608 No suitable preview iframe found or contentWindow not accessible
overrideMethod @ hook.js:608
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
hook.js:608 [DashboardNew] Loading timeout reached, forcing loading state to false
overrideMethod @ hook.js:608
 🚀 [LegalScout] Initializing environment...
 ✅ [LegalScout] Environment initialized
 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
emergency-auth-fix.js:1 Uncaught SyntaxError: Unexpected token '<'
 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 [VapiMcpService] Created clean fetch from iframe
 [VapiMcpService] INFO: Vapi MCP Service initialized Object
 🚀 [LegalScout] Initializing environment...
 ✅ [LegalScout] Environment initialized
 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
emergency-auth-fix.js:1 Uncaught SyntaxError: Unexpected token '<'
 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 [VapiMcpService] Created clean fetch from iframe
 [VapiMcpService] INFO: Vapi MCP Service initialized Object
api/sync-tools/manage-auth-state:1 
            
            
           Failed to load resource: the server responded with a status of 405 ()
 Auth state error details: Empty response from server
(anonymous) @ index-3d96563a.js:164
 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (index-3d96563a.js:164:20810)
    at async Object.handleAuthState (index-3d96563a.js:164:30367)
    at async index-3d96563a.js:48:80287
(anonymous) @ index-3d96563a.js:164
api/sync-tools/check-preview-consistency:1 
            
            
           Failed to load resource: the server responded with a status of 405 ()
 Check consistency error details: Empty response from server
(anonymous) @ index-3d96563a.js:164
 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.checkPreviewConsistency (index-3d96563a.js:164:27436)
    at async index-3d96563a.js:164:28903
    at async Object.handleAuthState (index-3d96563a.js:164:30574)
    at async index-3d96563a.js:48:80287
(anonymous) @ index-3d96563a.js:164
api/sync-tools/manage-auth-state:1 
            
            
           Failed to load resource: the server responded with a status of 405 ()
 Auth state error details: Empty response from server
(anonymous) @ index-3d96563a.js:164
 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (index-3d96563a.js:164:20810)
    at async Object.handleAuthState (index-3d96563a.js:164:30367)
    at async index-3d96563a.js:48:80287
(anonymous) @ index-3d96563a.js:164
api/sync-tools/check-preview-consistency:1 
            
            
           Failed to load resource: the server responded with a status of 405 ()
 Check consistency error details: Empty response from server
(anonymous) @ index-3d96563a.js:164
 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.checkPreviewConsistency (index-3d96563a.js:164:27436)
    at async index-3d96563a.js:164:28903
    at async Object.handleAuthState (index-3d96563a.js:164:30574)
    at async index-3d96563a.js:48:80287
(anonymous) @ index-3d96563a.js:164
ActiveCheckHelper.ts:21 received intentional event
hook.js:608 [VapiMcpService] Error listing assistants: Error: Not connected
    at index-3d96563a.js:50:148256
    at new Promise (<anonymous>)
    at mT.request (index-3d96563a.js:50:148197)
    at mT.callTool (index-3d96563a.js:50:274623)
    at e5.listAssistants (index-3d96563a.js:141:89124)
    at async Ye (index-3d96563a.js:308:55284)
overrideMethod @ hook.js:608
hook.js:608 [AgentTab] Error loading assistants from Vapi MCP: Error: Not connected
    at index-3d96563a.js:50:148256
    at new Promise (<anonymous>)
    at mT.request (index-3d96563a.js:50:148197)
    at mT.callTool (index-3d96563a.js:50:274623)
    at e5.listAssistants (index-3d96563a.js:141:89124)
    at async Ye (index-3d96563a.js:308:55284)
overrideMethod @ hook.js:608
 🚀 [LegalScout] Initializing environment...
 ✅ [LegalScout] Environment initialized
 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
emergency-auth-fix.js:1 Uncaught SyntaxError: Unexpected token '<'
 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
 [VapiLoader] Starting Vapi SDK loading process
 [VapiLoader] Attempting to import @vapi-ai/web package
 [VapiMcpService] Created clean fetch from iframe
 [VapiMcpService] INFO: Vapi MCP Service initialized [object Object]
api/sync-tools/manage-auth-state:1 
            
            
           Failed to load resource: the server responded with a status of 405 ()
 Auth state error details: Empty response from server
(anonymous) @ index-3d96563a.js:164
 API error, falling back to client-side implementation: Error: Empty response from server
(anonymous) @ index-3d96563a.js:164
api/sync-tools/check-preview-consistency:1 
            
            
           Failed to load resource: the server responded with a status of 405 ()
 Check consistency error details: Empty response from server
(anonymous) @ index-3d96563a.js:164
 API error, falling back to client-side implementation: Error: Empty response from server
(anonymous) @ index-3d96563a.js:164
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
