/**
 * Authentication Profile Fixer
 * 
 * Fixes authentication and profile loading issues by handling RLS policies,
 * email mismatches, and ensuring proper attorney profile creation/loading.
 */

import { getSupabaseUrl, getSupabaseKey } from '../lib/supabase';

/**
 * Fix authentication profile issues
 * @param {Object} user - Authenticated user object
 * @param {string} accessToken - Access token for authenticated requests
 * @returns {Promise<Object>} - Attorney profile or null
 */
export async function fixAuthProfile(user, accessToken) {
  console.log('[AuthProfileFixer] Starting profile fix for user:', user.email);
  console.log('[AuthProfileFixer] User ID:', user.id);
  console.log('[AuthProfileFixer] Access token length:', accessToken?.length || 'undefined');

  // Create REST API client configuration
  const apiConfig = {
    baseUrl: getSupabaseUrl(),
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'apikey': getSupabaseKey(),
      'Content-Type': 'application/json',
      'Prefer': 'return=representation'
    }
  };

  console.log('[AuthProfileFixer] API Config:', {
    baseUrl: apiConfig.baseUrl,
    hasAuth: !!apiConfig.headers.Authorization,
    hasApiKey: !!apiConfig.headers.apikey
  });

  try {
    // Step 1: Try to find existing attorney profile
    console.log('[AuthProfileFixer] Step 1: Looking for existing attorney profile...');
    const existingAttorney = await findExistingAttorney(user, apiConfig);

    if (existingAttorney) {
      console.log('[AuthProfileFixer] ✅ Found existing attorney profile:', {
        id: existingAttorney.id,
        email: existingAttorney.email,
        subdomain: existingAttorney.subdomain,
        firm_name: existingAttorney.firm_name,
        user_id: existingAttorney.user_id
      });

      // Ensure user_id is properly linked
      if (existingAttorney.user_id !== user.id) {
        console.log('[AuthProfileFixer] Updating user_id link');
        await updateAttorneyUserId(existingAttorney.id, user.id, apiConfig);
        existingAttorney.user_id = user.id;
      }

      return existingAttorney;
    }

    // Step 2: Check if this is a known attorney email that needs profile creation
    const knownAttorneyEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    if (knownAttorneyEmails.includes(user.email)) {
      console.log('[AuthProfileFixer] ✅ Creating profile for known attorney:', user.email);
      const newAttorney = await createKnownAttorneyProfile(user, apiConfig);
      console.log('[AuthProfileFixer] ✅ Successfully created attorney profile:', {
        id: newAttorney.id,
        email: newAttorney.email,
        subdomain: newAttorney.subdomain,
        firm_name: newAttorney.firm_name
      });
      return newAttorney;
    }

    console.log('[AuthProfileFixer] ❌ No existing profile found, user needs to complete profile');
    return null;

  } catch (error) {
    console.error('[AuthProfileFixer] ❌ Error fixing auth profile:', error);

    // Try fallback with direct Supabase client
    console.log('[AuthProfileFixer] 🔄 Attempting fallback with direct Supabase client...');
    try {
      const fallbackResult = await fallbackProfileFix(user);
      if (fallbackResult) {
        console.log('[AuthProfileFixer] ✅ Fallback succeeded:', fallbackResult.firm_name);
        return fallbackResult;
      }
    } catch (fallbackError) {
      console.error('[AuthProfileFixer] ❌ Fallback also failed:', fallbackError);
    }

    throw error;
  }
}

/**
 * Fallback profile fix using direct Supabase client
 * @param {Object} user - User object
 * @returns {Promise<Object>} - Attorney profile or null
 */
async function fallbackProfileFix(user) {
  console.log('[AuthProfileFixer] 🔄 Fallback: Using direct Supabase client...');

  try {
    // Import Supabase client
    const { getSupabaseClient } = await import('../lib/supabase');
    const supabase = await getSupabaseClient();

    // Try to find existing attorney (handle multiple profiles)
    const { data: existingAttorneys, error: findError } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', user.email)
      .order('created_at', { ascending: false });

    if (!findError && existingAttorneys && existingAttorneys.length > 0) {
      console.log('[AuthProfileFixer] 🔄 Fallback: Found', existingAttorneys.length, 'existing attorney profiles');

      // 🎯 SMART PROFILE SELECTION: Prefer profile with assistant ID
      let selectedProfile = existingAttorneys[0]; // Default to newest

      const profileWithAssistant = existingAttorneys.find(profile =>
        profile.vapi_assistant_id &&
        profile.vapi_assistant_id !== 'null' &&
        profile.vapi_assistant_id.trim() !== ''
      );

      if (profileWithAssistant) {
        selectedProfile = profileWithAssistant;
        console.log('[AuthProfileFixer] 🔄 Fallback: Selected profile with assistant ID:', selectedProfile.vapi_assistant_id);
      }

      console.log('[AuthProfileFixer] 🔄 Fallback: Using profile:', {
        id: selectedProfile.id,
        subdomain: selectedProfile.subdomain,
        vapi_assistant_id: selectedProfile.vapi_assistant_id
      });

      return selectedProfile;
    }

    // Check if this is a known attorney
    const knownAttorneyEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    if (knownAttorneyEmails.includes(user.email)) {
      console.log('[AuthProfileFixer] 🔄 Fallback: Creating known attorney profile...');

      const knownProfiles = {
        '<EMAIL>': {
          firm_name: 'LegalScout',
          subdomain: 'damonkost',
          vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'
        },
        '<EMAIL>': {
          firm_name: 'LegalScout',
          subdomain: 'legalscout-ai',
          vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'
        },
        '<EMAIL>': {
          firm_name: 'LegalScout',
          subdomain: 'legalscout',
          vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'
        }
      };

      const profileData = knownProfiles[user.email];
      const attorneyData = {
        user_id: user.id,
        email: user.email,
        name: user.user_metadata?.full_name || user.email.split('@')[0],
        ...profileData,
        phone: '+1234567890',
        practice_areas: ['General Practice'],
        welcome_message: "Hello! I'm Scout from LegalScout. How can I help you with your legal needs today?",
        vapi_instructions: "You are Scout, a legal assistant for LegalScout. Help potential clients understand their legal needs and collect relevant information for consultation. Be professional, helpful, and empathetic."
      };

      const { data: newAttorney, error: createError } = await supabase
        .from('attorneys')
        .insert(attorneyData)
        .select()
        .single();

      if (createError) {
        console.error('[AuthProfileFixer] 🔄 Fallback: Create failed:', createError);
        throw createError;
      }

      console.log('[AuthProfileFixer] 🔄 Fallback: Successfully created attorney');
      return newAttorney;
    }

    return null;
  } catch (error) {
    console.error('[AuthProfileFixer] 🔄 Fallback failed:', error);
    throw error;
  }
}

/**
 * Find existing attorney profile using multiple methods
 * @param {Object} user - User object
 * @param {Object} apiConfig - API configuration
 * @returns {Promise<Object|null>} - Attorney profile or null
 */
async function findExistingAttorney(user, apiConfig) {
  console.log('[AuthProfileFixer] 🔍 Searching for existing attorney profile...');

  // Method 1: Try by user_id
  try {
    console.log('[AuthProfileFixer] 🔍 Method 1: Searching by user_id:', user.id);
    const response = await fetch(`${apiConfig.baseUrl}/rest/v1/attorneys?user_id=eq.${user.id}&select=*`, {
      method: 'GET',
      headers: apiConfig.headers
    });

    console.log('[AuthProfileFixer] 🔍 Method 1 response status:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('[AuthProfileFixer] 🔍 Method 1 response data length:', data?.length || 0);
      if (data && data.length > 0) {
        console.log('[AuthProfileFixer] ✅ Found attorney by user_id:', data[0].email);
        return data[0];
      }
    } else {
      const errorText = await response.text();
      console.warn('[AuthProfileFixer] ⚠️ Method 1 failed with status:', response.status, errorText);
    }
  } catch (error) {
    console.warn('[AuthProfileFixer] ❌ User ID lookup failed:', error);
  }

  // Method 2: Try by email (handle multiple profiles - prefer one with assistant ID)
  try {
    console.log('[AuthProfileFixer] 🔍 Method 2: Searching by email:', user.email);
    const response = await fetch(`${apiConfig.baseUrl}/rest/v1/attorneys?email=eq.${encodeURIComponent(user.email)}&select=*&order=created_at.desc`, {
      method: 'GET',
      headers: apiConfig.headers
    });

    console.log('[AuthProfileFixer] 🔍 Method 2 response status:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('[AuthProfileFixer] 🔍 Method 2 response data length:', data?.length || 0);
      if (data && data.length > 0) {
        // 🎯 SMART PROFILE SELECTION: Prefer profile with assistant ID
        let selectedProfile = data[0]; // Default to first (newest)

        // Look for profile with assistant ID
        const profileWithAssistant = data.find(profile =>
          profile.vapi_assistant_id &&
          profile.vapi_assistant_id !== 'null' &&
          profile.vapi_assistant_id.trim() !== ''
        );

        if (profileWithAssistant) {
          selectedProfile = profileWithAssistant;
          console.log('[AuthProfileFixer] 🎯 Selected profile with assistant ID:', selectedProfile.vapi_assistant_id);
        } else {
          console.log('[AuthProfileFixer] 📝 Selected newest profile (no assistant ID found)');
        }

        console.log('[AuthProfileFixer] ✅ Found attorney by email:', {
          id: selectedProfile.id,
          email: selectedProfile.email,
          subdomain: selectedProfile.subdomain,
          vapi_assistant_id: selectedProfile.vapi_assistant_id,
          created_at: selectedProfile.created_at
        });

        return selectedProfile;
      }
    } else {
      const errorText = await response.text();
      console.warn('[AuthProfileFixer] ⚠️ Method 2 failed with status:', response.status, errorText);
    }
  } catch (error) {
    console.warn('[AuthProfileFixer] ❌ Email lookup failed:', error);
  }

  // Method 3: Try alternative emails for known users
  const alternativeEmails = getAlternativeEmails(user.email);

  for (const altEmail of alternativeEmails) {
    try {
      const response = await fetch(`${apiConfig.baseUrl}/rest/v1/attorneys?email=eq.${encodeURIComponent(altEmail)}&select=*`, {
        method: 'GET',
        headers: apiConfig.headers
      });

      if (response.ok) {
        const data = await response.json();
        if (data && data.length > 0) {
          console.log('[AuthProfileFixer] Found attorney by alternative email:', altEmail);
          return data[0];
        }
      }
    } catch (error) {
      console.warn('[AuthProfileFixer] Alternative email lookup failed:', altEmail, error);
    }
  }
  
  return null;
}

/**
 * Get alternative emails for known users
 * @param {string} email - Primary email
 * @returns {Array<string>} - Alternative emails
 */
function getAlternativeEmails(email) {
  const alternatives = [];
  
  if (email === '<EMAIL>') {
    alternatives.push('<EMAIL>', '<EMAIL>');
  } else if (email === '<EMAIL>') {
    alternatives.push('<EMAIL>', '<EMAIL>');
  } else if (email === '<EMAIL>') {
    alternatives.push('<EMAIL>', '<EMAIL>');
  }
  
  return alternatives;
}

/**
 * Update attorney user_id
 * @param {string} attorneyId - Attorney ID
 * @param {string} userId - User ID
 * @param {Object} apiConfig - API configuration
 * @returns {Promise<void>}
 */
async function updateAttorneyUserId(attorneyId, userId, apiConfig) {
  try {
    const response = await fetch(`${apiConfig.baseUrl}/rest/v1/attorneys?id=eq.${attorneyId}`, {
      method: 'PATCH',
      headers: apiConfig.headers,
      body: JSON.stringify({ user_id: userId })
    });

    if (!response.ok) {
      const error = await response.text();
      console.error('[AuthProfileFixer] Error updating user_id:', error);
      throw new Error(error);
    }

    console.log('[AuthProfileFixer] Successfully updated user_id');
  } catch (error) {
    console.error('[AuthProfileFixer] Failed to update user_id:', error);
    throw error;
  }
}

/**
 * Create attorney profile for known attorney (compatible with fungible subdomain system)
 * @param {Object} user - User object
 * @param {Object} apiConfig - API configuration
 * @returns {Promise<Object>} - Created attorney profile
 */
async function createKnownAttorneyProfile(user, apiConfig) {
  // 🔄 FUNGIBLE SUBDOMAIN COMPATIBLE: Use flexible subdomain assignment
  const knownProfiles = {
    '<EMAIL>': {
      firm_name: 'LegalScout',
      // Let the system auto-generate subdomain to avoid conflicts
      base_subdomain: 'damonkost',
      vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a',
      phone: '+1234567890',
      practice_areas: ['General Practice']
    },
    '<EMAIL>': {
      firm_name: 'LegalScout',
      base_subdomain: 'legalscout',
      vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2',
      phone: '+1234567890',
      practice_areas: ['General Practice'],
      welcome_message: "Hello! I'm Scout from LegalScout. How can I help you with your legal needs today?",
      vapi_instructions: "You are Scout, a legal assistant for LegalScout. Help potential clients understand their legal needs and collect relevant information for consultation. Be professional, helpful, and empathetic."
    },
    '<EMAIL>': {
      firm_name: 'LegalScout',
      base_subdomain: 'legalscout',
      vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2',
      phone: '+1234567890',
      practice_areas: ['General Practice'],
      welcome_message: "Hello! I'm Scout from LegalScout. How can I help you with your legal needs today?",
      vapi_instructions: "You are Scout, a legal assistant for LegalScout. Help potential clients understand their legal needs and collect relevant information for consultation. Be professional, helpful, and empathetic."
    }
  };
  
  const profileData = knownProfiles[user.email];
  
  if (!profileData) {
    throw new Error(`No known profile data for email: ${user.email}`);
  }
  
  const attorneyData = {
    user_id: user.id,
    email: user.email,
    name: user.user_metadata?.full_name || user.email.split('@')[0],
    ...profileData,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  try {
    console.log('[AuthProfileFixer] 📝 Creating attorney profile with data:', {
      email: attorneyData.email,
      subdomain: attorneyData.subdomain,
      firm_name: attorneyData.firm_name,
      user_id: attorneyData.user_id
    });

    const response = await fetch(`${apiConfig.baseUrl}/rest/v1/attorneys`, {
      method: 'POST',
      headers: apiConfig.headers,
      body: JSON.stringify(attorneyData)
    });

    console.log('[AuthProfileFixer] 📝 Create response status:', response.status);

    if (!response.ok) {
      const error = await response.text();
      console.error('[AuthProfileFixer] ❌ Error creating attorney profile:', error);
      throw new Error(`Failed to create attorney profile: ${error}`);
    }

    const newAttorney = await response.json();
    const createdAttorney = Array.isArray(newAttorney) ? newAttorney[0] : newAttorney;

    console.log('[AuthProfileFixer] ✅ Successfully created attorney profile:', {
      id: createdAttorney.id,
      email: createdAttorney.email,
      subdomain: createdAttorney.subdomain,
      firm_name: createdAttorney.firm_name
    });
    return createdAttorney;

  } catch (error) {
    console.error('[AuthProfileFixer] ❌ Failed to create attorney profile:', error);
    throw error;
  }
}

/**
 * Bypass RLS policies for admin operations
 * @param {Function} operation - Database operation to perform
 * @returns {Promise<any>} - Operation result
 */
export async function bypassRLS(operation) {
  try {
    // Set service role context if available
    const originalHeaders = supabase.rest.headers;
    
    // Try with service role key if available
    const serviceKey = import.meta.env.VITE_SUPABASE_SERVICE_KEY;
    if (serviceKey) {
      supabase.rest.headers = {
        ...originalHeaders,
        'Authorization': `Bearer ${serviceKey}`,
        'apikey': serviceKey
      };
    }
    
    const result = await operation();
    
    // Restore original headers
    supabase.rest.headers = originalHeaders;
    
    return result;
  } catch (error) {
    console.error('[AuthProfileFixer] RLS bypass failed:', error);
    throw error;
  }
}

/**
 * Quick health check for authentication system
 * @param {Object} user - User object
 * @returns {Promise<Object>} - Health check results
 */
export async function authHealthCheck(user) {
  const results = {
    timestamp: new Date().toISOString(),
    user: user?.email || 'unknown',
    tests: []
  };
  
  // Test 1: Supabase connection
  try {
    const { data, error } = await supabase.from('attorneys').select('count').limit(1);
    results.tests.push({
      name: 'Supabase Connection',
      status: error ? 'fail' : 'pass',
      message: error ? error.message : 'Connected successfully'
    });
  } catch (error) {
    results.tests.push({
      name: 'Supabase Connection',
      status: 'fail',
      message: error.message
    });
  }
  
  // Test 2: User authentication
  try {
    const { data: authUser, error } = await supabase.auth.getUser();
    results.tests.push({
      name: 'User Authentication',
      status: error ? 'fail' : 'pass',
      message: error ? error.message : `Authenticated as ${authUser.user?.email}`
    });
  } catch (error) {
    results.tests.push({
      name: 'User Authentication',
      status: 'fail',
      message: error.message
    });
  }
  
  // Test 3: Profile lookup
  if (user) {
    try {
      const attorney = await findExistingAttorney(user);
      results.tests.push({
        name: 'Profile Lookup',
        status: attorney ? 'pass' : 'warning',
        message: attorney ? `Found profile: ${attorney.firm_name}` : 'No profile found'
      });
    } catch (error) {
      results.tests.push({
        name: 'Profile Lookup',
        status: 'fail',
        message: error.message
      });
    }
  }
  
  return results;
}
