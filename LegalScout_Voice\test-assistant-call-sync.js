#!/usr/bin/env node

/**
 * Test Assistant-Based Call Sync
 *
 * This script tests the assistant-based call filtering functionality:
 * 1. Syncs calls from Vapi to Supabase using MCP tools
 * 2. Tests filtering by current assistant ID
 * 3. Verifies that switching assistants shows different call records
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config();

// Initialize Supabase with service role key for testing
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key:', supabaseKey ? supabaseKey.substring(0, 20) + '...' : 'undefined');

const supabase = createClient(supabaseUrl, supabaseKey);

// Mock MCP service for testing
const mockMcpService = {
  async listCalls() {
    // Return the calls we know exist from the MCP server
    return [
      {
        id: "23b43046-5354-455e-a124-72299cba04b2",
        createdAt: "2025-06-10T02:09:03.215Z",
        updatedAt: "2025-06-10T02:09:22.140Z",
        status: "ended",
        endedReason: "customer-ended-call",
        assistantId: "cd0b44b7-397e-410d-8835-ce9c3ba584b2",
        duration: 19000,
        customer: { phoneNumber: "+1234567890" },
        attorneyId: "87756a2c-a398-43f2-889a-b8815684df71"
      },
      {
        id: "9fa86c40-2a11-4b23-8ba0-30550c8de553",
        createdAt: "2025-06-10T01:45:12.751Z",
        updatedAt: "2025-06-10T01:45:25.933Z",
        status: "ended",
        endedReason: "customer-ended-call",
        assistantId: "cd0b44b7-397e-410d-8835-ce9c3ba584b2",
        duration: 13000,
        customer: { phoneNumber: "+1234567891" }
      },
      {
        id: "85da57e7-8078-469b-b945-620fcc1387ec",
        createdAt: "2025-06-10T01:26:40.248Z",
        updatedAt: "2025-06-10T01:29:24.670Z",
        status: "ended",
        endedReason: "customer-ended-call",
        assistantId: "cd0b44b7-397e-410d-8835-ce9c3ba584b2",
        duration: 164000,
        customer: { phoneNumber: "+1234567892" }
      }
    ];
  }
};

// Helper function to sync calls to Supabase
async function syncCallsToSupabase(attorneyId, calls) {
  if (!calls.length) return;

  try {
    console.log(`🔄 Syncing ${calls.length} calls to Supabase...`);

    const callRecords = calls.map(call => ({
      call_id: call.id,
      assistant_id: call.assistantId,
      attorney_id: attorneyId,
      customer_phone: call.customer?.phoneNumber,
      status: call.status,
      start_time: call.createdAt || call.startedAt || new Date().toISOString(),
      end_time: call.endedAt,
      duration: call.duration,
      metadata: {
        raw_call: call,
        cost: call.cost, // Store cost in metadata instead
        synced_at: new Date().toISOString()
      }
    }));

    // Insert call records (simple insert for testing)
    const { data, error } = await supabase
      .from('call_records')
      .insert(callRecords)
      .select();

    if (error) throw error;

    console.log(`✅ Synced ${data?.length || 0} call records to Supabase`);
    return data;

  } catch (error) {
    console.error('❌ Error syncing calls to Supabase:', error);
    throw error;
  }
}

async function testAssistantCallSync() {
  console.log('🧪 Testing Assistant-Based Call Sync');
  console.log('=====================================');

  try {
    // Step 1: Use hardcoded attorney data for testing
    console.log('\n📋 Step 1: Using attorney data...');
    const attorney = {
      id: '87756a2c-a398-43f2-889a-b8815684df71',
      email: '<EMAIL>',
      firm_name: 'LegalScout',
      current_assistant_id: '89257374-3725-4fa2-ba8b-08d2204be538',
      vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'
    };

    console.log('✅ Attorney found:', attorney.firm_name);
    console.log('   Attorney ID:', attorney.id);
    console.log('   Current Assistant ID:', attorney.current_assistant_id);
    console.log('   Vapi Assistant ID:', attorney.vapi_assistant_id);

    // Step 2: Sync calls manually using mock data
    console.log('\n📞 Step 2: Syncing calls to Supabase...');

    // Get calls from mock service
    const allCalls = await mockMcpService.listCalls();
    console.log(`📋 Found ${allCalls.length} calls from Vapi`);

    // Sync calls for current assistant
    if (attorney.current_assistant_id) {
      const currentAssistantCalls = allCalls.filter(call => call.assistantId === attorney.current_assistant_id);
      console.log(`🔄 Syncing ${currentAssistantCalls.length} calls for current assistant: ${attorney.current_assistant_id}`);
      await syncCallsToSupabase(attorney.id, currentAssistantCalls);
    }

    // Sync calls for vapi assistant (if different)
    if (attorney.vapi_assistant_id && attorney.vapi_assistant_id !== attorney.current_assistant_id) {
      const vapiAssistantCalls = allCalls.filter(call => call.assistantId === attorney.vapi_assistant_id);
      console.log(`🔄 Syncing ${vapiAssistantCalls.length} calls for vapi assistant: ${attorney.vapi_assistant_id}`);
      await syncCallsToSupabase(attorney.id, vapiAssistantCalls);
    }

    // Step 3: Check call records in database
    console.log('\n📊 Step 3: Checking call records in database...');
    
    // Get all call records for attorney
    const { data: allCallRecords, error: allCallsError } = await supabase
      .from('call_records')
      .select('*')
      .eq('attorney_id', attorney.id)
      .order('start_time', { ascending: false });

    if (allCallsError) {
      console.error('❌ Failed to get call records:', allCallsError.message);
      return;
    }

    console.log(`📋 Total call records in database: ${allCallRecords.length}`);

    // Group by assistant ID
    const callsByAssistant = {};
    allCallRecords.forEach(call => {
      if (!callsByAssistant[call.assistant_id]) {
        callsByAssistant[call.assistant_id] = [];
      }
      callsByAssistant[call.assistant_id].push(call);
    });

    console.log('\n📊 Calls by Assistant ID:');
    for (const [assistantId, calls] of Object.entries(callsByAssistant)) {
      console.log(`   ${assistantId}: ${calls.length} calls`);
      if (assistantId === attorney.current_assistant_id) {
        console.log('     ↳ This is the CURRENT assistant');
      }
      if (assistantId === attorney.vapi_assistant_id) {
        console.log('     ↳ This is the VAPI assistant');
      }
    }

    // Step 4: Test filtering by current assistant
    console.log('\n🔍 Step 4: Testing filtering by current assistant...');
    
    const { data: currentAssistantCalls, error: currentCallsError } = await supabase
      .from('call_records')
      .select('*')
      .eq('attorney_id', attorney.id)
      .eq('assistant_id', attorney.current_assistant_id)
      .order('start_time', { ascending: false });

    if (currentCallsError) {
      console.error('❌ Failed to get current assistant calls:', currentCallsError.message);
      return;
    }

    console.log(`✅ Calls for current assistant (${attorney.current_assistant_id}): ${currentAssistantCalls.length}`);

    // Step 5: Test filtering by vapi assistant
    if (attorney.vapi_assistant_id !== attorney.current_assistant_id) {
      console.log('\n🔍 Step 5: Testing filtering by vapi assistant...');
      
      const { data: vapiAssistantCalls, error: vapiCallsError } = await supabase
        .from('call_records')
        .select('*')
        .eq('attorney_id', attorney.id)
        .eq('assistant_id', attorney.vapi_assistant_id)
        .order('start_time', { ascending: false });

      if (vapiCallsError) {
        console.error('❌ Failed to get vapi assistant calls:', vapiCallsError.message);
        return;
      }

      console.log(`✅ Calls for vapi assistant (${attorney.vapi_assistant_id}): ${vapiAssistantCalls.length}`);
    }

    // Step 6: Test consultation filtering
    console.log('\n📋 Step 6: Testing consultation filtering...');
    
    const { data: allConsultations, error: consultationsError } = await supabase
      .from('consultations')
      .select('*')
      .eq('attorney_id', attorney.id)
      .order('created_at', { ascending: false });

    if (consultationsError) {
      console.error('❌ Failed to get consultations:', consultationsError.message);
      return;
    }

    console.log(`📋 Total consultations in database: ${allConsultations.length}`);

    // Filter consultations by current assistant
    const currentAssistantConsultations = allConsultations.filter(consultation => {
      const metadata = typeof consultation.metadata === 'string' 
        ? JSON.parse(consultation.metadata) 
        : consultation.metadata;
      return metadata?.assistant_id === attorney.current_assistant_id;
    });

    console.log(`✅ Consultations for current assistant: ${currentAssistantConsultations.length}`);

    console.log('\n🎉 Test completed successfully!');
    console.log('=====================================');
    console.log('✅ Call sync working');
    console.log('✅ Assistant-based filtering working');
    console.log('✅ Database queries working');
    console.log('✅ Ready for dashboard integration');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testAssistantCallSync().catch(console.error);
