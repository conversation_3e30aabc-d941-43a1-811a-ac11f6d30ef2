/**
 * Simple Subdomain Service
 * 
 * Optimal architecture:
 * 1. Subdomain → Assistant ID (from Supabase)
 * 2. UI Variables → From Supabase assistant_ui_configs
 * 3. Call Config → From Vapi directly
 */

class SimpleSubdomainService {
  constructor() {
    this.cache = new Map();
  }

  /**
   * Get complete config for subdomain
   * @param {string} subdomain 
   * @returns {Object} Complete config with UI and call data
   */
  async getSubdomainConfig(subdomain) {
    if (!subdomain || subdomain === 'default') return null;

    // Check cache
    const cacheKey = `subdomain_${subdomain}`;
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      console.log(`🔍 [SimpleSubdomain] Loading config for: ${subdomain}`);

      // Step 1: Get assistant ID from subdomain mapping
      const assistantId = await this.getAssistantIdFromSubdomain(subdomain);
      if (!assistantId) {
        console.log(`📭 [SimpleSubdomain] No assistant found for subdomain: ${subdomain}`);
        return null;
      }

      // Step 2: Get UI variables from Supabase
      const uiConfig = await this.getUIConfigFromSupabase(assistantId);

      // Step 3: Get call config from Vapi
      const callConfig = await this.getCallConfigFromVapi(assistantId);

      // Step 4: Combine everything with CORRECT field names for compatibility
      const completeConfig = {
        // Core identifiers (match existing component expectations)
        id: assistantId, // For components that expect id
        subdomain,
        assistant_id: assistantId,
        vapi_assistant_id: assistantId, // CRITICAL: This is what components expect
        current_assistant_id: assistantId,

        // UI variables from Supabase
        ...uiConfig,

        // Additional fields that components expect
        email: uiConfig?.email || '<EMAIL>',
        name: uiConfig?.firmName || 'LegalScout Assistant',

        // Call configuration from Vapi
        vapiAssistant: callConfig,

        // Status
        loadedVia: 'simple_subdomain_service',
        hasUIConfig: !!uiConfig,
        hasCallConfig: !!callConfig,
        isActive: true,
        vapiSyncStatus: 'simple_service_loaded'
      };

      // Cache result
      this.cache.set(cacheKey, completeConfig);

      console.log(`✅ [SimpleSubdomain] Config loaded for ${subdomain}:`, {
        firmName: completeConfig.firmName,
        assistant_id: assistantId,
        hasUIConfig: !!uiConfig,
        hasCallConfig: !!callConfig
      });

      return completeConfig;

    } catch (error) {
      console.error(`❌ [SimpleSubdomain] Error loading config for ${subdomain}:`, error);
      return null;
    }
  }

  /**
   * Step 1: Get assistant ID from subdomain (Supabase) - RESILIENT VERSION
   */
  async getAssistantIdFromSubdomain(subdomain) {
    try {
      console.log(`🔍 [SimpleSubdomain] Looking up assistant ID for: ${subdomain}`);

      const response = await fetch(`${this.getSupabaseUrl()}/rest/v1/v_subdomain_assistant_lookup?subdomain=eq.${subdomain}&is_active=eq.true`, {
        headers: {
          'apikey': this.getSupabaseKey(),
          'Authorization': `Bearer ${this.getSupabaseKey()}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.warn(`⚠️ [SimpleSubdomain] Supabase query failed: ${response.status} ${response.statusText}`);

        // Try fallback for known subdomains
        if (subdomain === 'assistant1test') {
          console.log(`🔄 [SimpleSubdomain] Using fallback assistant ID for ${subdomain}`);
          return 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d';
        }
        if (subdomain === 'damon') {
          console.log(`🔄 [SimpleSubdomain] Using fallback assistant ID for ${subdomain}`);
          return 'cd0b44b7-397e-410d-8835-ce9c3ba584b2';
        }

        return null;
      }

      const data = await response.json();

      if (data && data.length > 0) {
        console.log(`✅ [SimpleSubdomain] Found assistant ID: ${data[0].assistant_id}`);
        return data[0].assistant_id;
      }

      console.log(`📭 [SimpleSubdomain] No assistant found for subdomain: ${subdomain}`);
      return null;
    } catch (error) {
      console.error(`❌ [SimpleSubdomain] Failed to get assistant ID:`, error);

      // Emergency fallbacks for known subdomains
      if (subdomain === 'assistant1test') {
        console.log(`🚨 [SimpleSubdomain] Emergency fallback for ${subdomain}`);
        return 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d';
      }
      if (subdomain === 'damon') {
        console.log(`🚨 [SimpleSubdomain] Emergency fallback for ${subdomain}`);
        return 'cd0b44b7-397e-410d-8835-ce9c3ba584b2';
      }

      return null;
    }
  }

  /**
   * Step 2: Get UI config from Supabase
   */
  async getUIConfigFromSupabase(assistantId) {
    try {
      const response = await fetch(`${this.getSupabaseUrl()}/rest/v1/assistant_ui_configs?assistant_id=eq.${assistantId}`, {
        headers: {
          'apikey': this.getSupabaseKey(),
          'Authorization': `Bearer ${this.getSupabaseKey()}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.warn(`⚠️ [SimpleSubdomain] UI config query failed: ${response.status}`);
        return this.getDefaultUIConfig();
      }

      const data = await response.json();
      
      if (data && data.length > 0) {
        const uiConfig = data[0];
        return {
          // CORRECT MAPPING: Use assistant_name for the agent name display
          firmName: uiConfig.assistant_name || uiConfig.firm_name || 'LegalScout', // Agent name for display
          titleText: uiConfig.assistant_name || uiConfig.firm_name || 'LegalScout', // Agent name for display

          // Colors and styling
          primaryColor: uiConfig.primary_color || '#3B82F6',
          secondaryColor: uiConfig.secondary_color || '#1e40af',
          buttonColor: uiConfig.button_color || '#3b82f6',
          backgroundColor: uiConfig.background_color || '#ffffff',
          backgroundOpacity: uiConfig.background_opacity || '1.00',
          buttonOpacity: uiConfig.button_opacity || '1.00',
          practiceAreaBackgroundOpacity: uiConfig.practice_area_background_opacity || '0.10',
          textBackgroundColor: uiConfig.text_background_color || '#ffffff',

          // Content
          welcomeMessage: uiConfig.welcome_message || 'Hello! How can I help you today?',
          practiceDescription: uiConfig.practice_description,
          informationGathering: uiConfig.information_gathering,

          // Vapi configuration
          vapiInstructions: uiConfig.vapi_instructions || 'You are a helpful legal assistant.',
          vapiContext: uiConfig.vapi_context || '',
          voiceId: uiConfig.voice_id || 'alloy',
          voiceProvider: uiConfig.voice_provider || 'openai',
          aiModel: uiConfig.ai_model || 'gpt-4o',

          // Assets
          logoUrl: uiConfig.logo_url,
          mascot: uiConfig.mascot_url,
          buttonImage: uiConfig.assistant_image_url,

          // Additional data
          practiceAreas: uiConfig.practice_areas || [],
          officeAddress: uiConfig.office_address,
          schedulingLink: uiConfig.scheduling_link,

          // Store both for reference
          actualFirmName: uiConfig.firm_name, // The actual firm name
          agentName: uiConfig.assistant_name  // The agent/assistant name
        };
      }

      console.log(`📭 [SimpleSubdomain] No UI config found for assistant: ${assistantId}`);
      return this.getDefaultUIConfig();

    } catch (error) {
      console.error(`❌ [SimpleSubdomain] Failed to get UI config:`, error);
      return this.getDefaultUIConfig();
    }
  }

  /**
   * Step 3: Get call config from Vapi (DIRECT API ONLY)
   */
  async getCallConfigFromVapi(assistantId) {
    try {
      console.log(`🔍 [SimpleSubdomain] Getting Vapi config for: ${assistantId}`);

      // Use direct Vapi API call (skip MCP service that's failing)
      const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
        headers: {
          'Authorization': `Bearer ${this.getVapiPrivateKey()}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const assistant = await response.json();
        console.log(`✅ [SimpleSubdomain] Vapi assistant loaded:`, assistant.name);

        return {
          id: assistant.id,
          name: assistant.name,
          voice: assistant.voice,
          llm: assistant.llm,
          transcriber: assistant.transcriber,
          tools: assistant.toolIds || assistant.tools || []
        };
      } else {
        console.warn(`⚠️ [SimpleSubdomain] Vapi API returned ${response.status} for: ${assistantId}`);
      }

    } catch (error) {
      console.warn(`⚠️ [SimpleSubdomain] Vapi API failed:`, error.message);
    }

    // Always return a valid config (fallback)
    console.log(`🔄 [SimpleSubdomain] Using fallback config for: ${assistantId}`);
    return {
      id: assistantId,
      name: 'LegalScout Assistant',
      voice: { provider: 'openai', voiceId: 'alloy' },
      llm: { provider: 'openai', model: 'gpt-4o' },
      tools: []
    };
  }

  /**
   * Default UI config fallback
   */
  getDefaultUIConfig() {
    return {
      firmName: 'LegalScout',
      primaryColor: '#3B82F6',
      secondaryColor: '#1e40af',
      buttonColor: '#3b82f6',
      backgroundColor: '#ffffff',
      backgroundOpacity: '1.00',
      buttonOpacity: '1.00',
      practiceAreaBackgroundOpacity: '0.10',
      textBackgroundColor: '#ffffff',
      welcomeMessage: 'Hello! How can I help you today?',
      vapiInstructions: 'You are a helpful legal assistant.',
      voiceId: 'alloy',
      voiceProvider: 'openai',
      aiModel: 'gpt-4o',
      practiceAreas: []
    };
  }

  /**
   * Get Supabase URL
   */
  getSupabaseUrl() {
    return import.meta.env.VITE_SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
  }

  /**
   * Get Supabase key
   */
  getSupabaseKey() {
    return import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Ej8XZvB7qFpTqVrNVaOjmJlvjHlqcEWaQVgUhkJhGJo';
  }

  /**
   * Get Vapi private key
   */
  getVapiPrivateKey() {
    return import.meta.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
  }

  /**
   * Clear cache
   */
  clearCache(subdomain = null) {
    if (subdomain) {
      this.cache.delete(`subdomain_${subdomain}`);
    } else {
      this.cache.clear();
    }
  }
}

// Export singleton
export const simpleSubdomainService = new SimpleSubdomainService();
