/**
 * Create Dynamic Attorney Tool for Vapi
 * 
 * This creates a single intelligent tool that dynamically routes based on attorney context.
 * The tool automatically detects which attorney's assistant is calling and provides
 * attorney-specific functionality.
 */

import fs from 'fs';

// Configuration
const VAPI_API_URL = 'https://api.vapi.ai';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';

/**
 * Make a request to the Vapi API
 */
async function makeVapiRequest(endpoint, options = {}) {
  const url = `${VAPI_API_URL}${endpoint}`;
  
  const response = await fetch(url, {
    ...options,
    headers: {
      'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
      'Content-Type': 'application/json',
      ...options.headers
    }
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Vapi API Error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  return await response.json();
}

/**
 * Create the dynamic attorney tool
 */
async function createDynamicAttorneyTool() {
  console.log('🔧 Creating Dynamic Attorney Tool...');
  
  const toolConfig = {
    type: "function",
    name: "attorney_context_tool",
    description: "Intelligent tool that provides attorney-specific functionality based on the calling assistant's context. Automatically detects attorney information and provides relevant services.",
    
    function: {
      name: "attorney_context_tool",
      description: "Provides attorney-specific functionality including case management, client information, scheduling, and legal research",
      parameters: {
        type: "object",
        properties: {
          action: {
            type: "string",
            enum: [
              "get_attorney_info",
              "schedule_consultation", 
              "get_practice_areas",
              "transfer_to_attorney",
              "get_office_hours",
              "get_contact_info",
              "create_case_file",
              "get_legal_resources"
            ],
            description: "The specific action to perform"
          },
          client_name: {
            type: "string",
            description: "Client's name (when applicable)"
          },
          client_email: {
            type: "string", 
            description: "Client's email (when applicable)"
          },
          client_phone: {
            type: "string",
            description: "Client's phone number (when applicable)"
          },
          legal_issue: {
            type: "string",
            description: "Description of the legal issue (when applicable)"
          },
          urgency: {
            type: "string",
            enum: ["low", "medium", "high", "emergency"],
            description: "Urgency level of the matter"
          },
          additional_details: {
            type: "string",
            description: "Any additional relevant details"
          }
        },
        required: ["action"]
      }
    },
    
    server: {
      url: "https://dashboard.legalscout.net/api/tools/attorney-context",
      timeoutSeconds: 30
    },
    
    messages: [
      {
        type: "request-start",
        content: "Let me get that information for you..."
      },
      {
        type: "request-complete", 
        content: "I have the information you requested."
      },
      {
        type: "request-failed",
        content: "I'm having trouble accessing that information right now. Let me try a different approach."
      }
    ]
  };

  try {
    const tool = await makeVapiRequest('/tool', {
      method: 'POST',
      body: JSON.stringify(toolConfig)
    });
    
    console.log('✅ Dynamic Attorney Tool created successfully!');
    console.log(`Tool ID: ${tool.id}`);
    console.log(`Tool Name: ${tool.name}`);
    
    return tool;
  } catch (error) {
    console.error('❌ Error creating tool:', error.message);
    throw error;
  }
}

/**
 * Create the webhook endpoint handler code
 */
function generateWebhookHandler() {
  return `
// API Route: /api/tools/attorney-context
// This endpoint handles the dynamic attorney tool calls

import { supabase } from '../../../lib/supabase';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { message } = req.body;
    const { toolCallList, assistant, call } = message;
    
    if (!toolCallList || toolCallList.length === 0) {
      return res.status(400).json({ error: 'No tool calls found' });
    }

    const toolCall = toolCallList[0];
    const { action, client_name, client_email, client_phone, legal_issue, urgency, additional_details } = toolCall.arguments;
    
    // Get attorney context from assistant ID
    const assistantId = assistant.id || call.assistantId;
    const attorneyContext = await getAttorneyByAssistantId(assistantId);
    
    if (!attorneyContext) {
      return res.status(404).json({
        results: [{
          toolCallId: toolCall.id,
          result: "I apologize, but I couldn't identify the attorney context for this request."
        }]
      });
    }

    // Route to appropriate handler based on action
    let result;
    switch (action) {
      case 'get_attorney_info':
        result = await handleGetAttorneyInfo(attorneyContext);
        break;
      case 'schedule_consultation':
        result = await handleScheduleConsultation(attorneyContext, { client_name, client_email, client_phone, legal_issue });
        break;
      case 'get_practice_areas':
        result = await handleGetPracticeAreas(attorneyContext);
        break;
      case 'transfer_to_attorney':
        result = await handleTransferToAttorney(attorneyContext, { urgency, additional_details });
        break;
      case 'get_office_hours':
        result = await handleGetOfficeHours(attorneyContext);
        break;
      case 'get_contact_info':
        result = await handleGetContactInfo(attorneyContext);
        break;
      case 'create_case_file':
        result = await handleCreateCaseFile(attorneyContext, { client_name, client_email, client_phone, legal_issue, urgency });
        break;
      case 'get_legal_resources':
        result = await handleGetLegalResources(attorneyContext, { legal_issue });
        break;
      default:
        result = "I'm not sure how to help with that request. Please try rephrasing or ask for something else.";
    }

    return res.status(200).json({
      results: [{
        toolCallId: toolCall.id,
        result: result
      }]
    });

  } catch (error) {
    console.error('Attorney Context Tool Error:', error);
    return res.status(500).json({
      results: [{
        toolCallId: req.body.message?.toolCallList?.[0]?.id || 'unknown',
        result: "I encountered an error processing your request. Please try again."
      }]
    });
  }
}

// Helper function to get attorney by assistant ID
async function getAttorneyByAssistantId(assistantId) {
  const { data, error } = await supabase
    .from('attorneys')
    .select('*')
    .or(\`vapi_assistant_id.eq.\${assistantId},current_assistant_id.eq.\${assistantId}\`)
    .single();
    
  if (error) {
    console.error('Error fetching attorney:', error);
    return null;
  }
  
  return data;
}

// Action handlers
async function handleGetAttorneyInfo(attorney) {
  return \`I'm \${attorney.name || 'an AI assistant'} from \${attorney.firm_name}. We specialize in \${attorney.practice_areas?.join(', ') || 'various legal matters'}. Our office is located at \${attorney.office_address || 'our main location'}.\`;
}

async function handleScheduleConsultation(attorney, clientInfo) {
  // Create a consultation record
  const { data, error } = await supabase
    .from('consultations')
    .insert({
      attorney_id: attorney.id,
      client_name: clientInfo.client_name,
      client_email: clientInfo.client_email,
      client_phone: clientInfo.client_phone,
      legal_issue: clientInfo.legal_issue,
      status: 'pending',
      created_at: new Date().toISOString()
    });
    
  if (error) {
    return "I encountered an issue scheduling your consultation. Please call our office directly.";
  }
  
  return \`I've scheduled a consultation for \${clientInfo.client_name}. You'll receive a confirmation email at \${clientInfo.client_email} with the details. Our team will contact you within 24 hours to confirm the appointment time.\`;
}

async function handleGetPracticeAreas(attorney) {
  const areas = attorney.practice_areas || ['General Legal Services'];
  return \`\${attorney.firm_name} specializes in: \${areas.join(', ')}. We're here to help with any questions you might have about these areas of law.\`;
}

async function handleTransferToAttorney(attorney, details) {
  // Log the transfer request
  console.log(\`Transfer requested for \${attorney.firm_name}, urgency: \${details.urgency}\`);
  
  if (details.urgency === 'emergency') {
    return \`This appears to be an emergency. Please call our office immediately at \${attorney.phone || '(*************'} or contact emergency services if this is a life-threatening situation.\`;
  }
  
  return \`I'm connecting you with \${attorney.firm_name}. Please hold while I transfer your call. If the transfer fails, you can reach us directly at \${attorney.phone || 'our main number'}.\`;
}

async function handleGetOfficeHours(attorney) {
  return \`Our office hours are Monday through Friday, 9 AM to 5 PM. For urgent matters outside these hours, please leave a message and we'll get back to you as soon as possible. You can also email us at \${attorney.email}.\`;
}

async function handleGetContactInfo(attorney) {
  return \`You can reach \${attorney.firm_name} at:
Phone: \${attorney.phone || 'Please check our website'}
Email: \${attorney.email}
Address: \${attorney.office_address || attorney.address || 'Please check our website'}
\${attorney.scheduling_link ? \`Schedule online: \${attorney.scheduling_link}\` : ''}\`;
}

async function handleCreateCaseFile(attorney, caseInfo) {
  // Create a case file record
  const { data, error } = await supabase
    .from('case_files')
    .insert({
      attorney_id: attorney.id,
      client_name: caseInfo.client_name,
      client_email: caseInfo.client_email,
      client_phone: caseInfo.client_phone,
      case_description: caseInfo.legal_issue,
      urgency_level: caseInfo.urgency,
      status: 'new',
      created_at: new Date().toISOString()
    });
    
  if (error) {
    return "I've noted your information and will make sure our team follows up with you.";
  }
  
  return \`I've created a case file for \${caseInfo.client_name}. Your case reference number is CF-\${data[0]?.id?.slice(-8) || 'PENDING'}. Our legal team will review your case and contact you within 2 business days.\`;
}

async function handleGetLegalResources(attorney, details) {
  const issue = details.legal_issue?.toLowerCase() || '';
  
  let resources = "Here are some general legal resources that might help: ";
  
  if (issue.includes('divorce') || issue.includes('family')) {
    resources += "For family law matters, you might find helpful information at the local family court's self-help center.";
  } else if (issue.includes('criminal') || issue.includes('arrest')) {
    resources += "For criminal matters, remember you have the right to remain silent and the right to an attorney.";
  } else if (issue.includes('business') || issue.includes('contract')) {
    resources += "For business matters, the Small Business Administration has helpful resources online.";
  } else {
    resources += "I recommend consulting with our legal team for specific guidance on your situation.";
  }
  
  return resources + \` You can also schedule a consultation with \${attorney.firm_name} for personalized advice.\`;
}
`;
}

/**
 * Main execution function
 */
async function main() {
  try {
    console.log('🚀 Starting Dynamic Attorney Tool Creation...\n');
    
    // Create the tool
    const tool = await createDynamicAttorneyTool();
    
    // Generate webhook handler code
    const webhookCode = generateWebhookHandler();
    
    // Save webhook handler to file
    fs.writeFileSync('api-tools-attorney-context.js', webhookCode);
    console.log('💾 Webhook handler code saved to: api-tools-attorney-context.js');
    
    // Save tool configuration
    const toolData = {
      timestamp: new Date().toISOString(),
      tool: tool,
      webhookEndpoint: 'https://dashboard.legalscout.net/api/tools/attorney-context',
      implementation: 'Dynamic routing based on assistant context'
    };
    
    fs.writeFileSync('dynamic-attorney-tool.json', JSON.stringify(toolData, null, 2));
    console.log('💾 Tool configuration saved to: dynamic-attorney-tool.json');
    
    console.log('\n✅ Dynamic Attorney Tool created successfully!');
    console.log('\n📋 NEXT STEPS:');
    console.log('1. Deploy the webhook handler to /api/tools/attorney-context');
    console.log('2. Add this tool to your assistants using the tool ID:', tool.id);
    console.log('3. Test the tool with different attorney assistants');
    console.log('4. Monitor webhook logs for proper routing');
    
  } catch (error) {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  }
}

// Run the script
main();
