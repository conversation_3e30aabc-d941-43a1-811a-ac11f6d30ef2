/**
 * Minimal Test App
 * 
 * This is a stripped-down version of the app to isolate the black screen issue.
 * We'll progressively add components to identify what's causing the problem.
 */

import React, { useState, useEffect } from 'react';
import ComprehensiveAppDiagnostics from './diagnostics/ComprehensiveAppDiagnostics';

const TestApp = () => {
  const [stage, setStage] = useState('initial');
  const [error, setError] = useState(null);
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toISOString();
    const logEntry = { timestamp, message, type };
    setLogs(prev => [...prev, logEntry]);
    console.log(`[TestApp] ${type.toUpperCase()}: ${message}`);
  };

  useEffect(() => {
    addLog('🚀 TestApp mounted successfully', 'success');
    
    // Test basic React functionality
    setTimeout(() => {
      addLog('⏰ Timer test passed - React is working', 'success');
      setStage('react-working');
    }, 1000);

    // Test error boundary
    window.addEventListener('error', (event) => {
      addLog(`❌ Global error: ${event.error?.message || event.message}`, 'error');
      setError(event.error?.message || event.message);
    });

    window.addEventListener('unhandledrejection', (event) => {
      addLog(`❌ Unhandled promise rejection: ${event.reason}`, 'error');
      setError(event.reason);
    });

    return () => {
      addLog('🧹 TestApp unmounting', 'info');
    };
  }, []);

  const testSupabase = async () => {
    addLog('🔍 Testing Supabase import...', 'info');
    try {
      const { supabase } = await import('./lib/supabase');
      addLog('✅ Supabase imported successfully', 'success');
      
      // Test Supabase connection
      const { data, error } = await supabase.auth.getSession();
      if (error) {
        addLog(`⚠️ Supabase auth error: ${error.message}`, 'warning');
      } else {
        addLog('✅ Supabase connection successful', 'success');
      }
      setStage('supabase-tested');
    } catch (error) {
      addLog(`❌ Supabase test failed: ${error.message}`, 'error');
      setError(error.message);
    }
  };

  const testAuthContext = async () => {
    addLog('🔍 Testing Auth Context...', 'info');
    try {
      const { AuthProvider, useAuth } = await import('./contexts/AuthContext');
      addLog('✅ Auth Context imported successfully', 'success');
      setStage('auth-tested');
    } catch (error) {
      addLog(`❌ Auth Context test failed: ${error.message}`, 'error');
      setError(error.message);
    }
  };

  const testMainApp = async () => {
    addLog('🔍 Testing Main App import...', 'info');
    try {
      const App = await import('./App');
      addLog('✅ Main App imported successfully', 'success');
      setStage('app-tested');
    } catch (error) {
      addLog(`❌ Main App test failed: ${error.message}`, 'error');
      setError(error.message);
    }
  };

  const testEnvironmentVariables = () => {
    addLog('🔍 Testing environment variables...', 'info');
    
    const envVars = {
      'VITE_SUPABASE_URL': import.meta.env.VITE_SUPABASE_URL,
      'VITE_SUPABASE_KEY': import.meta.env.VITE_SUPABASE_KEY,
      'VITE_VAPI_PUBLIC_KEY': import.meta.env.VITE_VAPI_PUBLIC_KEY,
      'VITE_VAPI_SECRET_KEY': import.meta.env.VITE_VAPI_SECRET_KEY,
      'MODE': import.meta.env.MODE,
      'DEV': import.meta.env.DEV,
      'PROD': import.meta.env.PROD
    };

    Object.entries(envVars).forEach(([key, value]) => {
      if (value) {
        addLog(`✅ ${key}: ${key.includes('KEY') ? '[REDACTED]' : value}`, 'success');
      } else {
        addLog(`❌ ${key}: Missing`, 'error');
      }
    });

    setStage('env-tested');
  };

  const runAllTests = async () => {
    addLog('🚀 Running all tests...', 'info');
    testEnvironmentVariables();
    await testSupabase();
    await testAuthContext();
    await testMainApp();
    addLog('🎉 All tests completed', 'success');
  };

  if (error) {
    return (
      <div style={{ 
        padding: '20px', 
        backgroundColor: '#ffebee', 
        color: '#c62828',
        fontFamily: 'monospace'
      }}>
        <h1>❌ Error Detected</h1>
        <p><strong>Error:</strong> {error}</p>
        <button 
          onClick={() => {
            setError(null);
            setStage('initial');
            setLogs([]);
          }}
          style={{
            padding: '10px 20px',
            backgroundColor: '#c62828',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Reset Test
        </button>
        
        <h2>Logs:</h2>
        <div style={{ backgroundColor: '#fff', padding: '10px', borderRadius: '4px', marginTop: '10px' }}>
          {logs.map((log, index) => (
            <div key={index} style={{ 
              marginBottom: '5px',
              color: log.type === 'error' ? '#c62828' : 
                     log.type === 'success' ? '#2e7d32' : 
                     log.type === 'warning' ? '#f57c00' : '#000'
            }}>
              <small>{log.timestamp.split('T')[1].split('.')[0]}</small> {log.message}
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#f5f5f5',
      minHeight: '100vh'
    }}>
      <h1 style={{ color: '#2e7d32' }}>🧪 LegalScout Test App</h1>
      <p>Current Stage: <strong>{stage}</strong></p>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={testEnvironmentVariables}
          style={{
            padding: '10px 20px',
            backgroundColor: '#1976d2',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          Test Environment
        </button>
        
        <button 
          onClick={testSupabase}
          style={{
            padding: '10px 20px',
            backgroundColor: '#388e3c',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          Test Supabase
        </button>
        
        <button 
          onClick={testAuthContext}
          style={{
            padding: '10px 20px',
            backgroundColor: '#f57c00',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          Test Auth Context
        </button>
        
        <button 
          onClick={testMainApp}
          style={{
            padding: '10px 20px',
            backgroundColor: '#7b1fa2',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          Test Main App
        </button>
        
        <button 
          onClick={runAllTests}
          style={{
            padding: '10px 20px',
            backgroundColor: '#d32f2f',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Run All Tests
        </button>
      </div>

      {/* Logs Section */}
      <div style={{ marginBottom: '20px' }}>
        <h2>📋 Test Logs</h2>
        <div style={{ 
          backgroundColor: '#fff', 
          padding: '15px', 
          borderRadius: '4px',
          border: '1px solid #ddd',
          maxHeight: '300px',
          overflowY: 'auto',
          fontFamily: 'monospace'
        }}>
          {logs.length === 0 ? (
            <p style={{ color: '#666' }}>No logs yet...</p>
          ) : (
            logs.map((log, index) => (
              <div key={index} style={{ 
                marginBottom: '5px',
                color: log.type === 'error' ? '#c62828' : 
                       log.type === 'success' ? '#2e7d32' : 
                       log.type === 'warning' ? '#f57c00' : '#000'
              }}>
                <small>{log.timestamp.split('T')[1].split('.')[0]}</small> {log.message}
              </div>
            ))
          )}
        </div>
      </div>

      {/* Comprehensive Diagnostics */}
      <div style={{ marginTop: '40px' }}>
        <h2>🔬 Comprehensive Diagnostics</h2>
        <ComprehensiveAppDiagnostics />
      </div>
    </div>
  );
};

export default TestApp;
