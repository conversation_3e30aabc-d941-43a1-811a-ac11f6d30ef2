demo:16 🚀 [LegalScout] Initializing environment...
demo:38 ✅ [LegalScout] Environment initialized
index-cd9881f9.js:48 Attaching Supabase client to window.supabase
index-cd9881f9.js:100 [VapiLoader] Starting Vapi SDK loading process
index-cd9881f9.js:100 [VapiLoader] Attempting to import @vapi-ai/web package
index-cd9881f9.js:187 [VapiMcpService] Created clean fetch from iframe
index-cd9881f9.js:187 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
index-cd9881f9.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-cd9881f9.js:48:69816)
    at dG (index-cd9881f9.js:48:72276)
    at Sn (index-cd9881f9.js:48:74977)
    at hfe.setupRealtimeSubscription (index-cd9881f9.js:523:35095)
    at hfe.autoInitializeFromLocalStorage (index-cd9881f9.js:523:27820)
    at new hfe (index-cd9881f9.js:523:27600)
    at index-cd9881f9.js:523:56692
overrideMethod @ hook.js:608
Sn @ index-cd9881f9.js:48
setupRealtimeSubscription @ index-cd9881f9.js:523
autoInitializeFromLocalStorage @ index-cd9881f9.js:523
hfe @ index-cd9881f9.js:523
(anonymous) @ index-cd9881f9.js:523
index-cd9881f9.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-cd9881f9.js:48:69816)
    at dG (index-cd9881f9.js:48:72276)
    at Sn (index-cd9881f9.js:48:74977)
    at hfe.setupRealtimeSubscription (index-cd9881f9.js:523:35095)
    at hfe.autoInitializeFromLocalStorage (index-cd9881f9.js:523:27820)
    at new hfe (index-cd9881f9.js:523:27600)
    at index-cd9881f9.js:523:56692
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-cd9881f9.js:523
hfe @ index-cd9881f9.js:523
(anonymous) @ index-cd9881f9.js:523
index-cd9881f9.js:48 🔐 [AuthContext] Unexpected error checking auth: Error: Supabase client not initialized. Use getSupabaseClient() first.
    at Object.get (index-cd9881f9.js:48:75462)
    at index-cd9881f9.js:48:79257
    at index-cd9881f9.js:48:80878
    at m_ (index-cd9881f9.js:40:24270)
    at Id (index-cd9881f9.js:40:42393)
    at index-cd9881f9.js:40:40710
    at D (index-cd9881f9.js:25:1585)
    at MessagePort.j (index-cd9881f9.js:25:1948)
overrideMethod @ hook.js:608
(anonymous) @ index-cd9881f9.js:48
(anonymous) @ index-cd9881f9.js:48
m_ @ index-cd9881f9.js:40
Id @ index-cd9881f9.js:40
(anonymous) @ index-cd9881f9.js:40
D @ index-cd9881f9.js:25
j @ index-cd9881f9.js:25
index-cd9881f9.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-cd9881f9.js:48:69816)
    at dG (index-cd9881f9.js:48:72276)
    at Sn (index-cd9881f9.js:48:74977)
    at index-cd9881f9.js:48:80916
    at index-cd9881f9.js:48:82302
    at m_ (index-cd9881f9.js:40:24270)
    at Id (index-cd9881f9.js:40:42393)
    at index-cd9881f9.js:40:40710
    at D (index-cd9881f9.js:25:1585)
    at MessagePort.j (index-cd9881f9.js:25:1948)
overrideMethod @ hook.js:608
Sn @ index-cd9881f9.js:48
(anonymous) @ index-cd9881f9.js:48
(anonymous) @ index-cd9881f9.js:48
m_ @ index-cd9881f9.js:40
Id @ index-cd9881f9.js:40
(anonymous) @ index-cd9881f9.js:40
D @ index-cd9881f9.js:25
j @ index-cd9881f9.js:25
index-cd9881f9.js:48 Failed to set up auth listener: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-cd9881f9.js:48:69816)
    at dG (index-cd9881f9.js:48:72276)
    at Sn (index-cd9881f9.js:48:74977)
    at index-cd9881f9.js:48:80916
    at index-cd9881f9.js:48:82302
    at m_ (index-cd9881f9.js:40:24270)
    at Id (index-cd9881f9.js:40:42393)
    at index-cd9881f9.js:40:40710
    at D (index-cd9881f9.js:25:1585)
    at MessagePort.j (index-cd9881f9.js:25:1948)
overrideMethod @ hook.js:608
(anonymous) @ index-cd9881f9.js:48
await in (anonymous)
(anonymous) @ index-cd9881f9.js:48
m_ @ index-cd9881f9.js:40
Id @ index-cd9881f9.js:40
(anonymous) @ index-cd9881f9.js:40
D @ index-cd9881f9.js:25
j @ index-cd9881f9.js:25
index.ts:5 Loaded contentScript
demo:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
feedback.js:1 [Violation] Added non-passive event listener to a scroll-blocking 'touchstart' event. Consider marking event handler as 'passive' to make the page more responsive. See https://www.chromestatus.com/feature/5745543795965952
Ve.k.toolbar @ feedback.js:1
Ve @ feedback.js:1
(anonymous) @ feedback.js:1
u @ feedback.js:1
(anonymous) @ feedback.js:1
(anonymous) @ feedback.js:1
r @ feedback.js:1
s @ feedback.js:1
index-cd9881f9.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-cd9881f9.js:48:69816)
    at dG (index-cd9881f9.js:48:72276)
    at Sn (index-cd9881f9.js:48:74977)
    at hfe.loadAttorneyById (index-cd9881f9.js:523:34723)
    at index-cd9881f9.js:523:28060
overrideMethod @ hook.js:608
Sn @ index-cd9881f9.js:48
loadAttorneyById @ index-cd9881f9.js:523
(anonymous) @ index-cd9881f9.js:523
setTimeout
autoInitializeFromLocalStorage @ index-cd9881f9.js:523
hfe @ index-cd9881f9.js:523
(anonymous) @ index-cd9881f9.js:523
index-cd9881f9.js:523 [AttorneyProfileManager] Error loading attorney by id: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-cd9881f9.js:48:69816)
    at dG (index-cd9881f9.js:48:72276)
    at Sn (index-cd9881f9.js:48:74977)
    at hfe.loadAttorneyById (index-cd9881f9.js:523:34723)
    at index-cd9881f9.js:523:28060
overrideMethod @ hook.js:608
loadAttorneyById @ index-cd9881f9.js:523
await in loadAttorneyById
(anonymous) @ index-cd9881f9.js:523
setTimeout
autoInitializeFromLocalStorage @ index-cd9881f9.js:523
hfe @ index-cd9881f9.js:523
(anonymous) @ index-cd9881f9.js:523
index-cd9881f9.js:523 [AttorneyProfileManager] Could not refresh attorney data for auto-sync: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-cd9881f9.js:48:69816)
    at dG (index-cd9881f9.js:48:72276)
    at Sn (index-cd9881f9.js:48:74977)
    at hfe.loadAttorneyById (index-cd9881f9.js:523:34723)
    at index-cd9881f9.js:523:28060
overrideMethod @ hook.js:608
(anonymous) @ index-cd9881f9.js:523
setTimeout
autoInitializeFromLocalStorage @ index-cd9881f9.js:523
hfe @ index-cd9881f9.js:523
(anonymous) @ index-cd9881f9.js:523
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
index-cd9881f9.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-cd9881f9.js:48:69816)
    at dG (index-cd9881f9.js:48:72276)
    at Sn (index-cd9881f9.js:48:74977)
    at hfe.setupRealtimeSubscription (index-cd9881f9.js:523:35095)
    at index-cd9881f9.js:523:36862
overrideMethod @ hook.js:608
Sn @ index-cd9881f9.js:48
setupRealtimeSubscription @ index-cd9881f9.js:523
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-cd9881f9.js:523
hfe @ index-cd9881f9.js:523
(anonymous) @ index-cd9881f9.js:523
index-cd9881f9.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-cd9881f9.js:48:69816)
    at dG (index-cd9881f9.js:48:72276)
    at Sn (index-cd9881f9.js:48:74977)
    at hfe.setupRealtimeSubscription (index-cd9881f9.js:523:35095)
    at index-cd9881f9.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-cd9881f9.js:523
hfe @ index-cd9881f9.js:523
(anonymous) @ index-cd9881f9.js:523
index-cd9881f9.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-cd9881f9.js:48:69816)
    at dG (index-cd9881f9.js:48:72276)
    at Sn (index-cd9881f9.js:48:74977)
    at gL (index-cd9881f9.js:48:75961)
    at w (index-cd9881f9.js:469:41536)
    at Object.XV (index-cd9881f9.js:37:9864)
    at QV (index-cd9881f9.js:37:10018)
    at e7 (index-cd9881f9.js:37:10075)
    at ZC (index-cd9881f9.js:37:31482)
    at y6 (index-cd9881f9.js:37:31899)
overrideMethod @ hook.js:608
Sn @ index-cd9881f9.js:48
gL @ index-cd9881f9.js:48
w @ index-cd9881f9.js:469
XV @ index-cd9881f9.js:37
QV @ index-cd9881f9.js:37
e7 @ index-cd9881f9.js:37
ZC @ index-cd9881f9.js:37
y6 @ index-cd9881f9.js:37
(anonymous) @ index-cd9881f9.js:37
A2 @ index-cd9881f9.js:40
zP @ index-cd9881f9.js:37
dy @ index-cd9881f9.js:37
t2 @ index-cd9881f9.js:37
g7 @ index-cd9881f9.js:37
index-cd9881f9.js:48 💥 [Auth] Google sign-in error: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-cd9881f9.js:48:69816)
    at dG (index-cd9881f9.js:48:72276)
    at Sn (index-cd9881f9.js:48:74977)
    at gL (index-cd9881f9.js:48:75961)
    at w (index-cd9881f9.js:469:41536)
    at Object.XV (index-cd9881f9.js:37:9864)
    at QV (index-cd9881f9.js:37:10018)
    at e7 (index-cd9881f9.js:37:10075)
    at ZC (index-cd9881f9.js:37:31482)
    at y6 (index-cd9881f9.js:37:31899)
overrideMethod @ hook.js:608
gL @ index-cd9881f9.js:48
await in gL
w @ index-cd9881f9.js:469
XV @ index-cd9881f9.js:37
QV @ index-cd9881f9.js:37
e7 @ index-cd9881f9.js:37
ZC @ index-cd9881f9.js:37
y6 @ index-cd9881f9.js:37
(anonymous) @ index-cd9881f9.js:37
A2 @ index-cd9881f9.js:40
zP @ index-cd9881f9.js:37
dy @ index-cd9881f9.js:37
t2 @ index-cd9881f9.js:37
g7 @ index-cd9881f9.js:37
index-cd9881f9.js:469 Google sign-in error: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-cd9881f9.js:48:69816)
    at dG (index-cd9881f9.js:48:72276)
    at Sn (index-cd9881f9.js:48:74977)
    at gL (index-cd9881f9.js:48:75961)
    at w (index-cd9881f9.js:469:41536)
    at Object.XV (index-cd9881f9.js:37:9864)
    at QV (index-cd9881f9.js:37:10018)
    at e7 (index-cd9881f9.js:37:10075)
    at ZC (index-cd9881f9.js:37:31482)
    at y6 (index-cd9881f9.js:37:31899)
overrideMethod @ hook.js:608
w @ index-cd9881f9.js:469
await in w
XV @ index-cd9881f9.js:37
QV @ index-cd9881f9.js:37
e7 @ index-cd9881f9.js:37
ZC @ index-cd9881f9.js:37
y6 @ index-cd9881f9.js:37
(anonymous) @ index-cd9881f9.js:37
A2 @ index-cd9881f9.js:40
zP @ index-cd9881f9.js:37
dy @ index-cd9881f9.js:37
t2 @ index-cd9881f9.js:37
g7 @ index-cd9881f9.js:37
ActiveCheckHelper.ts:8 updating page active status
index-cd9881f9.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-cd9881f9.js:48:69816)
    at dG (index-cd9881f9.js:48:72276)
    at Sn (index-cd9881f9.js:48:74977)
    at hfe.setupRealtimeSubscription (index-cd9881f9.js:523:35095)
    at index-cd9881f9.js:523:36862
overrideMethod @ hook.js:608
Sn @ index-cd9881f9.js:48
setupRealtimeSubscription @ index-cd9881f9.js:523
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-cd9881f9.js:523
hfe @ index-cd9881f9.js:523
(anonymous) @ index-cd9881f9.js:523
index-cd9881f9.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-cd9881f9.js:48:69816)
    at dG (index-cd9881f9.js:48:72276)
    at Sn (index-cd9881f9.js:48:74977)
    at hfe.setupRealtimeSubscription (index-cd9881f9.js:523:35095)
    at index-cd9881f9.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-cd9881f9.js:523
hfe @ index-cd9881f9.js:523
(anonymous) @ index-cd9881f9.js:523
index-cd9881f9.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-cd9881f9.js:48:69816)
    at dG (index-cd9881f9.js:48:72276)
    at Sn (index-cd9881f9.js:48:74977)
    at hfe.setupRealtimeSubscription (index-cd9881f9.js:523:35095)
    at index-cd9881f9.js:523:36862
overrideMethod @ hook.js:608
Sn @ index-cd9881f9.js:48
setupRealtimeSubscription @ index-cd9881f9.js:523
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-cd9881f9.js:523
hfe @ index-cd9881f9.js:523
(anonymous) @ index-cd9881f9.js:523
index-cd9881f9.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-cd9881f9.js:48:69816)
    at dG (index-cd9881f9.js:48:72276)
    at Sn (index-cd9881f9.js:48:74977)
    at hfe.setupRealtimeSubscription (index-cd9881f9.js:523:35095)
    at index-cd9881f9.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-cd9881f9.js:523
hfe @ index-cd9881f9.js:523
(anonymous) @ index-cd9881f9.js:523
index-cd9881f9.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-cd9881f9.js:48:69816)
    at dG (index-cd9881f9.js:48:72276)
    at Sn (index-cd9881f9.js:48:74977)
    at hfe.setupRealtimeSubscription (index-cd9881f9.js:523:35095)
    at index-cd9881f9.js:523:36862
overrideMethod @ hook.js:608
Sn @ index-cd9881f9.js:48
setupRealtimeSubscription @ index-cd9881f9.js:523
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-cd9881f9.js:523
hfe @ index-cd9881f9.js:523
(anonymous) @ index-cd9881f9.js:523
index-cd9881f9.js:523 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-cd9881f9.js:48:69816)
    at dG (index-cd9881f9.js:48:72276)
    at Sn (index-cd9881f9.js:48:74977)
    at hfe.setupRealtimeSubscription (index-cd9881f9.js:523:35095)
    at index-cd9881f9.js:523:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
(anonymous) @ index-cd9881f9.js:523
setTimeout
setupRealtimeSubscription @ index-cd9881f9.js:523
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-cd9881f9.js:523
hfe @ index-cd9881f9.js:523
(anonymous) @ index-cd9881f9.js:523
