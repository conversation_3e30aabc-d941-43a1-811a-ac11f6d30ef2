{"timestamp": "2025-06-14T12:46:27.672Z", "tools": {"liveDossierApi": {"id": "023c4ae4-9516-413d-a6cf-8469da66bbe2", "createdAt": "2025-06-14T12:46:27.546Z", "updatedAt": "2025-06-14T12:46:27.546Z", "type": "function", "function": {"name": "live_dossier_enhanced", "description": "Updates case dossier with attorney-specific context and direct API integration", "parameters": {"type": "object", "properties": {"STATUS": {"description": "Current status of consultation", "type": "string"}, "OBJECTIVES": {"description": "User objectives", "type": "string"}, "JURISDICTION": {"description": "Location of user's legal matter", "type": "object", "properties": {"lat": {"type": "number"}, "lng": {"type": "number"}, "address": {"type": "string"}}}, "LEGAL_ISSUES": {"description": "Legal issues identified", "type": "string"}, "user_message": {"description": "Latest user message", "type": "string"}, "Scout_message": {"description": "Latest Scout response", "type": "string"}, "User_Location": {"description": "User location coordinates", "type": "array"}, "CLIENT_BACKGROUND": {"description": "Client background information", "type": "string"}, "STATEMENT_OF_FACTS": {"description": "Facts of the case", "type": "string"}}, "required": ["STATUS"]}}, "messages": [{"type": "request-start", "content": "Let me update your case information..."}, {"type": "request-complete", "content": "I've updated your dossier with the latest information."}, {"type": "request-failed", "content": "I had trouble updating your information, but I'll continue helping you."}], "orgId": "badbec9d-0ee3-43fb-8b18-7f26cdce3e4a", "server": {"url": "https://dashboard.legalscout.net/api/tools/live-dossier-enhanced", "timeoutSeconds": 10}}, "attorneyContextApi": {"id": "b187390c-0d77-48b9-b9be-402264cc19d2", "createdAt": "2025-06-14T12:46:27.747Z", "updatedAt": "2025-06-14T12:46:27.747Z", "type": "function", "function": {"name": "attorney_context_enhanced", "description": "Provides attorney-specific functionality with automatic context detection", "parameters": {"type": "object", "properties": {"action": {"description": "The action to perform", "type": "string", "enum": ["get_attorney_info", "get_practice_areas", "get_contact_info", "get_office_hours", "schedule_consultation", "create_case_file", "transfer_to_attorney"]}, "urgency": {"description": "Urgency level", "type": "string", "enum": ["low", "medium", "high", "emergency"]}, "client_name": {"description": "<PERSON><PERSON>'s name", "type": "string"}, "legal_issue": {"description": "Description of legal issue", "type": "string"}, "client_email": {"description": "Client's email", "type": "string"}, "client_phone": {"description": "C<PERSON>'s phone", "type": "string"}, "additional_details": {"description": "Additional details", "type": "string"}}, "required": ["action"]}}, "messages": [{"type": "request-start", "content": "Let me get that information for you..."}, {"type": "request-complete", "content": "Here's the information you requested."}, {"type": "request-failed", "content": "I'm having trouble accessing that information right now."}], "orgId": "badbec9d-0ee3-43fb-8b18-7f26cdce3e4a", "server": {"url": "https://dashboard.legalscout.net/api/tools/attorney-context-enhanced", "timeoutSeconds": 15}}}, "endpoints": {"dossierUpdate": "https://dashboard.legalscout.net/api/dossier-update", "attorneyContext": "https://dashboard.legalscout.net/api/attorney-context"}, "migration": {"from": "Function tools with external webhooks", "to": "API Request tools with direct integration", "benefits": ["No external dependencies", "Attorney-specific context automatically available", "Direct database integration", "Real-time updates", "Better error handling"]}}