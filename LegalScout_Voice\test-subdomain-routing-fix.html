<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Subdomain Routing Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #3B82F6;
        }
        .success { border-left-color: #10b981; }
        .error { border-left-color: #ef4444; }
        .info { border-left-color: #f59e0b; }
        button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #2563eb; }
        pre {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 Test Subdomain Routing Fix</h1>
    <p>This tests the unified subdomain routing fix to ensure App.jsx and config/attorneys.js use the same logic.</p>

    <div class="test-section">
        <h3>Test Results</h3>
        <div id="results"></div>
    </div>

    <div class="test-section">
        <h3>Actions</h3>
        <button onclick="testSubdomainRouting()">Test Subdomain Routing</button>
        <button onclick="testBothSubdomains()">Test Both Subdomains</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <script type="module">
        function addResult(type, message) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `<pre>${message}</pre>`;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        window.addResult = addResult;
        window.clearResults = clearResults;

        window.testSubdomainRouting = async function() {
            addResult('info', '🔧 Testing unified subdomain routing...');
            
            try {
                // Test the loadAttorneyConfig function directly
                const { loadAttorneyConfig } = await import('./src/config/attorneys.js');
                
                addResult('success', '✅ Successfully imported loadAttorneyConfig');
                
                // Test assistant1test subdomain
                const config1 = await loadAttorneyConfig('assistant1test');
                
                if (config1) {
                    addResult('success', `✅ assistant1test config loaded:
Firm Name: ${config1.firmName}
Assistant ID: ${config1.vapi_assistant_id}
Current Assistant: ${config1.current_assistant_id}
Loaded Via: ${config1.loadedVia}
Subdomain: ${config1.subdomain || config1.assistant_subdomain}`);
                } else {
                    addResult('error', '❌ No config returned for assistant1test');
                }
                
            } catch (error) {
                addResult('error', `❌ Test failed: ${error.message}`);
                console.error('Test error:', error);
            }
        };

        window.testBothSubdomains = async function() {
            addResult('info', '🔧 Testing both damon and assistant1test subdomains...');
            
            try {
                const { loadAttorneyConfig } = await import('./src/config/attorneys.js');
                
                // Test damon subdomain
                const damonConfig = await loadAttorneyConfig('damon');
                if (damonConfig) {
                    addResult('success', `✅ damon subdomain:
Firm: ${damonConfig.firmName}
Assistant: ${damonConfig.vapi_assistant_id}
Email: ${damonConfig.email}
Loaded Via: ${damonConfig.loadedVia}`);
                } else {
                    addResult('error', '❌ damon subdomain failed');
                }
                
                // Test assistant1test subdomain
                const testConfig = await loadAttorneyConfig('assistant1test');
                if (testConfig) {
                    addResult('success', `✅ assistant1test subdomain:
Firm: ${testConfig.firmName}
Assistant: ${testConfig.vapi_assistant_id}
Email: ${testConfig.email}
Loaded Via: ${testConfig.loadedVia}`);
                } else {
                    addResult('error', '❌ assistant1test subdomain failed');
                }
                
                // Compare results
                if (damonConfig && testConfig) {
                    const sameAttorney = damonConfig.email === testConfig.email;
                    const differentAssistants = damonConfig.vapi_assistant_id !== testConfig.vapi_assistant_id;
                    
                    if (sameAttorney && differentAssistants) {
                        addResult('success', '🎯 PERFECT! Same attorney, different assistants - multi-assistant architecture working!');
                    } else {
                        addResult('info', `ℹ️ Comparison: Same attorney: ${sameAttorney}, Different assistants: ${differentAssistants}`);
                    }
                }
                
            } catch (error) {
                addResult('error', `❌ Test failed: ${error.message}`);
                console.error('Test error:', error);
            }
        };

        // Auto-run test on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testBothSubdomains();
            }, 1000);
        });
    </script>
</body>
</html>
