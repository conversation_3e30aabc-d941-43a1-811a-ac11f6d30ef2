# Exclude individual API files to stay within Vercel's 12 function limit
# These endpoints are now handled by the consolidated router in api/index.js

# Keep only essential separate functions:
# - api/index.js (main router)
# - api/vapi-webhook-direct.js (critical webhook)

# Exclude all other API files (now handled by router)
api/ai-meta-mcp.js
api/ai-meta-mcp/
api/assistant/
api/auth/
api/billing/
api/bug-report.js
api/check-env.js
api/debug-supabase.js
api/env.js
api/firecrawl-search.js
api/health.js
api/mcp-proxy.js
api/optimized-sync.js
api/sync-tools/
api/test.js
api/utils/
api/vapi-mcp-server/
api/vapi-proxy/
api/web-search.js
api/webhook/
api/website-import.js

# Exclude test files to speed up builds
src/tests/
docs/

# Exclude development scripts (but keep essential build scripts)
scripts/
!scripts/copy-fix-scripts.js


