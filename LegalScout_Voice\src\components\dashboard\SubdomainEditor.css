/* CSS Variables for fallback */
:root {
  --border-color: #e2e8f0;
  --text-primary: #1a202c;
  --text-secondary: #718096;
  --primary-color: #4B74AA;
  --primary-color-rgb: 75, 116, 170;
  --primary-color-dark: #3A5D88;
  --radius-small: 6px;
  --radius-medium: 8px;
  --success-color: #22c55e;
  --error-color: #ef4444;
  --warning-color: #f59e0b;
}

/* Dark theme overrides */
[data-theme="dark"] {
  --border-color: #4a5568;
  --text-primary: #f7fafc;
  --text-secondary: #a0aec0;
}

/* Subdomain Display */
.subdomain-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  transition: all 0.2s ease;
}

.subdomain-display:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

.subdomain-info {
  flex: 1;
}

.subdomain-url {
  font-size: 0.95rem;
  color: var(--text-primary);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.subdomain-url strong {
  color: var(--primary-color);
}

.subdomain-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

.assistant-id-info {
  font-size: 0.7rem;
  color: var(--text-muted, #9ca3af);
  margin-top: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  opacity: 0.8;
}

.edit-subdomain-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius-small);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-subdomain-btn:hover {
  color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.1);
}

.edit-subdomain-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Subdomain Placeholder */
.subdomain-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.02);
  border: 1px dashed var(--border-color);
  border-radius: var(--radius-small);
  opacity: 0.7;
}

.subdomain-placeholder .subdomain-info {
  text-align: center;
}

.subdomain-placeholder .subdomain-url strong {
  color: var(--text-secondary);
  opacity: 0.6;
}

/* Subdomain Editor */
.subdomain-editor {
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  padding: 1.5rem;
  margin-top: 0.5rem;
}

.subdomain-editor-header {
  margin-bottom: 1.5rem;
}

.subdomain-editor-header h4 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.subdomain-editor-header p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.4;
}

.assistant-context {
  margin-top: 0.75rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: var(--radius-small);
}

.assistant-context small {
  color: var(--text-secondary);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
}

/* Input Group */
.subdomain-input-group {
  margin-bottom: 1rem;
}

.subdomain-input-wrapper {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  overflow: hidden;
  transition: border-color 0.2s ease;
}

.subdomain-input-wrapper:focus-within {
  border-color: var(--primary-color);
}

.subdomain-input {
  flex: 1;
  padding: 0.75rem;
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 0.95rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.subdomain-input:focus {
  outline: none;
}

.subdomain-input.available {
  border-color: var(--success-color, #22c55e);
}

.subdomain-input.unavailable {
  border-color: var(--error-color, #ef4444);
}

.subdomain-suffix {
  padding: 0.75rem;
  color: var(--text-secondary);
  font-size: 0.95rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background-color: rgba(255, 255, 255, 0.05);
  border-left: 1px solid var(--border-color);
}

/* Availability Status */
.availability-status {
  margin-top: 0.75rem;
}

.status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-small);
}

.status.checking {
  color: var(--text-secondary);
  background-color: rgba(255, 255, 255, 0.05);
}

.status.available {
  color: var(--success-color, #22c55e);
  background-color: rgba(34, 197, 94, 0.1);
}

.status.unavailable {
  color: var(--error-color, #ef4444);
  background-color: rgba(239, 68, 68, 0.1);
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Preview */
.subdomain-preview {
  margin: 1rem 0;
  padding: 0.75rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-small);
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.subdomain-preview strong {
  color: var(--text-primary);
}

/* Actions */
.subdomain-actions {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.cancel-btn,
.save-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: var(--radius-small);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.cancel-btn {
  background-color: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.cancel-btn:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
}

.save-btn {
  background-color: var(--primary-color);
  color: white;
}

.save-btn:hover:not(:disabled) {
  background-color: var(--primary-color-dark, #3A5D88);
}

.save-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Warning */
.subdomain-warning {
  display: flex;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: rgba(251, 191, 36, 0.1);
  border: 1px solid rgba(251, 191, 36, 0.3);
  border-radius: var(--radius-small);
  color: var(--warning-color, #f59e0b);
  font-size: 0.85rem;
  line-height: 1.4;
}

.subdomain-warning svg {
  flex-shrink: 0;
  margin-top: 0.1rem;
}

/* Error */
.subdomain-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
  padding: 0.75rem;
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: var(--radius-small);
  color: var(--error-color, #ef4444);
  font-size: 0.85rem;
}

/* Responsive */
@media (max-width: 768px) {
  .subdomain-actions {
    flex-direction: column;
  }

  .cancel-btn,
  .save-btn {
    justify-content: center;
  }
}
