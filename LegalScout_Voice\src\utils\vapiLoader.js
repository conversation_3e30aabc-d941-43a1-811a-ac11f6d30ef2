/**
 * Vapi SDK Loader for React Application
 * 
 * This module properly imports and exposes the Vapi SDK for use in the React app.
 * It handles the installed package and provides fallbacks.
 */

let VapiClass = null;
let loadingPromise = null;

/**
 * Load the Vapi SDK
 * @returns {Promise<Function>} The Vapi constructor
 */
export async function loadVapiSDK() {
  // Return cached result if already loaded
  if (VapiClass) {
    console.log('[VapiLoader] Vapi SDK already loaded');
    return VapiClass;
  }

  // Return existing loading promise if already loading
  if (loadingPromise) {
    console.log('[VapiLoader] Vapi SDK loading in progress, waiting...');
    return loadingPromise;
  }

  // Start loading process
  loadingPromise = (async () => {
    console.log('[VapiLoader] Starting Vapi SDK loading process');

    try {
      // Try to import the installed package
      console.log('[VapiLoader] Attempting to import @vapi-ai/web package');
      const VapiModule = await import('@vapi-ai/web');
      
      // Extract the Vapi class from the module
      VapiClass = VapiModule.default || VapiModule.Vapi || VapiModule;
      
      if (typeof VapiClass === 'function') {
        console.log('[VapiLoader] ✅ Successfully loaded Vapi SDK from installed package');
        
        // Expose globally for compatibility
        window.Vapi = VapiClass;
        window.__VAPI_BUNDLED__ = VapiClass;
        
        // Test instantiation
        try {
          const testInstance = new VapiClass('test-key');
          if (testInstance && typeof testInstance.start === 'function') {
            console.log('[VapiLoader] ✅ Vapi SDK validation successful');
          }
        } catch (testError) {
          console.log('[VapiLoader] ⚠️ Vapi SDK loaded but validation failed:', testError.message);
        }
        
        return VapiClass;
      } else {
        throw new Error('Imported module does not contain a valid Vapi constructor');
      }
    } catch (importError) {
      console.error('[VapiLoader] ❌ Failed to import @vapi-ai/web package:', importError);
      
      // Fallback: try to use CDN-loaded version if available
      if (window.Vapi && typeof window.Vapi === 'function') {
        console.log('[VapiLoader] 🔄 Using CDN-loaded Vapi SDK as fallback');
        VapiClass = window.Vapi;
        return VapiClass;
      }
      
      // Final fallback: load from CDN
      console.log('[VapiLoader] 🔄 Attempting to load from CDN as final fallback');
      try {
        await loadFromCDN();
        if (window.Vapi && typeof window.Vapi === 'function') {
          VapiClass = window.Vapi;
          return VapiClass;
        }
      } catch (cdnError) {
        console.error('[VapiLoader] ❌ CDN fallback also failed:', cdnError);
      }
      
      throw new Error('Failed to load Vapi SDK from all sources');
    }
  })();

  return loadingPromise;
}

/**
 * Load Vapi SDK from CDN as fallback
 */
async function loadFromCDN() {
  return new Promise((resolve, reject) => {
    // Don't load if already available
    if (window.Vapi) {
      resolve();
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://cdn.vapi.ai/web-sdk@2.2.2/dist/web-sdk.js';
    script.async = true;
    script.crossOrigin = 'anonymous';

    const timeout = setTimeout(() => {
      script.remove();
      reject(new Error('CDN loading timeout'));
    }, 10000);

    script.onload = () => {
      clearTimeout(timeout);
      console.log('[VapiLoader] ✅ Successfully loaded from CDN');
      resolve();
    };

    script.onerror = () => {
      clearTimeout(timeout);
      script.remove();
      reject(new Error('Failed to load from CDN'));
    };

    document.head.appendChild(script);
  });
}

/**
 * Get the Vapi class (must be loaded first)
 * @returns {Function|null} The Vapi constructor or null if not loaded
 */
export function getVapiClass() {
  return VapiClass;
}

/**
 * Check if Vapi SDK is loaded
 * @returns {boolean} True if loaded, false otherwise
 */
export function isVapiLoaded() {
  return VapiClass !== null;
}

/**
 * Create a Vapi instance
 * @param {string} apiKey - The Vapi API key
 * @param {Object} options - Additional options
 * @returns {Object} Vapi instance
 */
export async function createVapiInstance(apiKey, options = {}) {
  if (!VapiClass) {
    console.log('[VapiLoader] Vapi not loaded, loading now...');
    await loadVapiSDK();
  }

  if (!VapiClass) {
    throw new Error('Failed to load Vapi SDK');
  }

  if (!apiKey || typeof apiKey !== 'string') {
    throw new Error('API key must be a non-empty string');
  }

  try {
    const vapi = new VapiClass(apiKey);
    console.log(`[VapiLoader] ✅ Vapi instance created with key: ${apiKey.substring(0, 8)}...`);
    return vapi;
  } catch (error) {
    console.error(`[VapiLoader] ❌ Failed to create Vapi instance:`, error);
    throw error;
  }
}

// Auto-load on module import
loadVapiSDK().catch(error => {
  console.error('[VapiLoader] ❌ Auto-load failed:', error);
});
