/**
 * Convert Function Tools to API Request Tools
 * 
 * This script converts existing Function tools (like live_dossier) to API Request tools
 * that directly call your LegalScout API endpoints with attorney-specific context.
 */

import fs from 'fs';

// Configuration
const VAPI_API_URL = 'https://api.vapi.ai';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
const LEGALSCOUT_API_BASE = 'https://dashboard.legalscout.net/api';

/**
 * Make a request to the Vapi API
 */
async function makeVapiRequest(endpoint, options = {}) {
  const url = `${VAPI_API_URL}${endpoint}`;
  
  const response = await fetch(url, {
    ...options,
    headers: {
      'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
      'Content-Type': 'application/json',
      ...options.headers
    }
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Vapi API Error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  return await response.json();
}

/**
 * Create TRUE API Request version of live_dossier tool
 */
async function createLiveDossierApiTool() {
  console.log('🔧 Creating TRUE API Request version of live_dossier tool...');

  const toolConfig = {
    type: "apiRequest",
    name: "live_dossier_api",
    description: "Real-time case information display that updates during conversations with direct API integration",

    // HTTP Request Configuration
    method: "POST",
    url: `${LEGALSCOUT_API_BASE}/dossier-update`,

    headers: {
      "Content-Type": "application/json",
      "X-LegalScout-Tool": "LIVE_DOSSIER_API",
      "X-Assistant-ID": "{{assistant.id}}",
      "X-Call-ID": "{{call.id}}"
    },

    // Request body with parameter substitution
    body: {
      assistantId: "{{assistant.id}}",
      callId: "{{call.id}}",
      timestamp: "{{timestamp}}",
      dossier: {
        status: "{{parameters.STATUS}}",
        jurisdiction: "{{parameters.JURISDICTION}}",
        clientBackground: "{{parameters.CLIENT_BACKGROUND}}",
        legalIssues: "{{parameters.LEGAL_ISSUES}}",
        statementOfFacts: "{{parameters.STATEMENT_OF_FACTS}}",
        objectives: "{{parameters.OBJECTIVES}}"
      },
      conversation: {
        userMessage: "{{parameters.user_message}}",
        scoutMessage: "{{parameters.Scout_message}}",
        userLocation: "{{parameters.User_Location}}"
      }
    },

    // Parameters that the AI can fill
    parameters: {
      type: "object",
      properties: {
        STATUS: {
          type: "string",
          description: "Current status of consultation"
        },
        JURISDICTION: {
          type: "object",
          properties: {
            address: { type: "string" },
            lat: { type: "number" },
            lng: { type: "number" }
          },
          description: "Location of user's legal matter down to zip in lat,long"
        },
        CLIENT_BACKGROUND: {
          type: "string",
          description: "Client background information"
        },
        LEGAL_ISSUES: {
          type: "string",
          description: "Legal issues identified"
        },
        STATEMENT_OF_FACTS: {
          type: "string",
          description: "Facts of the case"
        },
        OBJECTIVES: {
          type: "string",
          description: "User objectives"
        },
        user_message: {
          type: "string",
          description: "Latest user message"
        },
        Scout_message: {
          type: "string",
          description: "Latest Scout response"
        },
        User_Location: {
          type: "array",
          description: "User location coordinates"
        }
      },
      required: ["STATUS"]
    },

    // Advanced HTTP settings
    timeoutSeconds: 10,

    // Messages for different states
    messages: [
      {
        type: "request-start",
        content: "Let me update your case information..."
      },
      {
        type: "request-complete",
        content: "I've updated your dossier with the latest information."
      },
      {
        type: "request-failed",
        content: "I had trouble updating your information, but I'll continue helping you."
      }
    ]
  };

  try {
    const tool = await makeVapiRequest('/tool', {
      method: 'POST',
      body: JSON.stringify(toolConfig)
    });
    
    console.log('✅ API Request live_dossier tool created successfully!');
    console.log(`Tool ID: ${tool.id}`);
    
    return tool;
  } catch (error) {
    console.error('❌ Error creating API Request tool:', error.message);
    throw error;
  }
}

/**
 * Create TRUE API Request attorney context tool
 */
async function createAttorneyContextApiTool() {
  console.log('🔧 Creating TRUE API Request attorney context tool...');

  const toolConfig = {
    type: "apiRequest",
    name: "attorney_context_api",
    description: "Get attorney-specific information and perform attorney-related actions with direct API integration",

    // HTTP Request Configuration
    method: "POST",
    url: `${LEGALSCOUT_API_BASE}/attorney-context`,

    headers: {
      "Content-Type": "application/json",
      "X-LegalScout-Tool": "ATTORNEY_CONTEXT_API",
      "X-Assistant-ID": "{{assistant.id}}",
      "X-Call-ID": "{{call.id}}"
    },

    // Request body with parameter substitution
    body: {
      assistantId: "{{assistant.id}}",
      callId: "{{call.id}}",
      timestamp: "{{timestamp}}",
      action: "{{parameters.action}}",
      clientInfo: {
        name: "{{parameters.client_name}}",
        email: "{{parameters.client_email}}",
        phone: "{{parameters.client_phone}}"
      },
      caseInfo: {
        legalIssue: "{{parameters.legal_issue}}",
        urgency: "{{parameters.urgency}}",
        details: "{{parameters.additional_details}}"
      }
    },

    // Parameters that the AI can fill
    parameters: {
      type: "object",
      properties: {
        action: {
          type: "string",
          enum: [
            "get_attorney_info",
            "get_practice_areas",
            "get_contact_info",
            "get_office_hours",
            "schedule_consultation",
            "create_case_file",
            "transfer_to_attorney"
          ],
          description: "The action to perform"
        },
        client_name: {
          type: "string",
          description: "Client's name"
        },
        client_email: {
          type: "string",
          description: "Client's email"
        },
        client_phone: {
          type: "string",
          description: "Client's phone"
        },
        legal_issue: {
          type: "string",
          description: "Description of legal issue"
        },
        urgency: {
          type: "string",
          enum: ["low", "medium", "high", "emergency"],
          description: "Urgency level"
        },
        additional_details: {
          type: "string",
          description: "Additional details"
        }
      },
      required: ["action"]
    },

    // Advanced HTTP settings
    timeoutSeconds: 15,

    // Messages for different states
    messages: [
      {
        type: "request-start",
        content: "Let me get that information for you..."
      },
      {
        type: "request-complete",
        content: "Here's the information you requested."
      },
      {
        type: "request-failed",
        content: "I'm having trouble accessing that information right now."
      }
    ]
  };

  try {
    const tool = await makeVapiRequest('/tool', {
      method: 'POST',
      body: JSON.stringify(toolConfig)
    });
    
    console.log('✅ API Request attorney context tool created successfully!');
    console.log(`Tool ID: ${tool.id}`);
    
    return tool;
  } catch (error) {
    console.error('❌ Error creating attorney context API tool:', error.message);
    throw error;
  }
}

/**
 * Generate API endpoint handlers
 */
function generateApiEndpoints() {
  const dossierEndpoint = `
// File: pages/api/tools/live-dossier-enhanced.js
// API endpoint for live_dossier_enhanced tool

import { supabase } from '../../lib/supabase';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    const { assistantId, callId, dossier, userMessage, scoutMessage, userLocation } = req.body;
    
    // Get attorney context from assistant ID
    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .or(\`vapi_assistant_id.eq.\${assistantId},current_assistant_id.eq.\${assistantId}\`)
      .single();
    
    if (attorneyError || !attorney) {
      console.error('Attorney not found for assistant:', assistantId);
      return res.status(404).json({ 
        success: false, 
        error: 'Attorney context not found' 
      });
    }

    // Save dossier update to database
    const { data: dossierRecord, error: dossierError } = await supabase
      .from('dossier_updates')
      .insert({
        attorney_id: attorney.id,
        assistant_id: assistantId,
        call_id: callId,
        status: dossier.status,
        jurisdiction: dossier.jurisdiction,
        client_background: dossier.clientBackground,
        legal_issues: dossier.legalIssues,
        statement_of_facts: dossier.statementOfFacts,
        objectives: dossier.objectives,
        user_message: userMessage,
        scout_message: scoutMessage,
        user_location: userLocation,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (dossierError) {
      console.error('Error saving dossier:', dossierError);
      return res.status(500).json({ 
        success: false, 
        error: 'Failed to save dossier update' 
      });
    }

    // Trigger real-time updates (if using websockets/SSE)
    // await broadcastDossierUpdate(attorney.id, dossierRecord);

    return res.status(200).json({
      success: true,
      message: 'Dossier updated successfully',
      data: {
        dossierUpdateId: dossierRecord.id,
        attorneyFirm: attorney.firm_name,
        timestamp: dossierRecord.created_at
      }
    });

  } catch (error) {
    console.error('Dossier update error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}
`;

  const attorneyContextEndpoint = `
// File: pages/api/tools/attorney-context-enhanced.js
// API endpoint for attorney_context_enhanced tool

import { supabase } from '../../lib/supabase';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    const { assistantId, action, clientInfo, caseInfo } = req.body;
    
    // Get attorney context
    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .or(\`vapi_assistant_id.eq.\${assistantId},current_assistant_id.eq.\${assistantId}\`)
      .single();
    
    if (attorneyError || !attorney) {
      return res.status(404).json({ 
        success: false, 
        error: 'Attorney not found' 
      });
    }

    let result;
    switch (action) {
      case 'get_attorney_info':
        result = \`I'm an AI assistant for \${attorney.firm_name}. We're located at \${attorney.office_address || attorney.address} and specialize in \${attorney.practice_areas?.join(', ') || 'various legal matters'}.\`;
        break;
        
      case 'get_practice_areas':
        result = \`\${attorney.firm_name} specializes in: \${attorney.practice_areas?.join(', ') || 'General Legal Services'}.\`;
        break;
        
      case 'get_contact_info':
        result = \`You can reach \${attorney.firm_name} at \${attorney.phone || 'our main number'} or email \${attorney.email}. \${attorney.scheduling_link ? \`You can also schedule online at \${attorney.scheduling_link}\` : ''}\`;
        break;
        
      case 'get_office_hours':
        result = \`Our office hours are Monday through Friday, 9 AM to 5 PM. For urgent matters, please call \${attorney.phone}.\`;
        break;
        
      case 'schedule_consultation':
        // Create consultation record
        const { data: consultation } = await supabase
          .from('consultations')
          .insert({
            attorney_id: attorney.id,
            client_name: clientInfo.name,
            client_email: clientInfo.email,
            client_phone: clientInfo.phone,
            legal_issue: caseInfo.legalIssue,
            status: 'pending'
          })
          .select()
          .single();
          
        result = \`I've scheduled a consultation for \${clientInfo.name}. You'll receive confirmation at \${clientInfo.email}.\`;
        break;
        
      default:
        result = 'I can help you with attorney information, practice areas, contact details, and scheduling consultations.';
    }

    return res.status(200).json({
      success: true,
      message: result,
      data: {
        attorney: attorney.firm_name,
        action: action
      }
    });

  } catch (error) {
    console.error('Attorney context error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}
`;

  return { dossierEndpoint, attorneyContextEndpoint };
}

/**
 * Main execution function
 */
async function main() {
  try {
    console.log('🚀 Converting Function Tools to API Request Tools...\n');
    
    // Create the new API Request tools
    const liveDossierTool = await createLiveDossierApiTool();
    const attorneyContextTool = await createAttorneyContextApiTool();
    
    // Generate API endpoints
    const { dossierEndpoint, attorneyContextEndpoint } = generateApiEndpoints();
    
    // Save endpoint code
    fs.writeFileSync('api-dossier-update.js', dossierEndpoint);
    fs.writeFileSync('api-attorney-context.js', attorneyContextEndpoint);
    
    // Save tool configurations
    const toolsData = {
      timestamp: new Date().toISOString(),
      tools: {
        liveDossierApi: liveDossierTool,
        attorneyContextApi: attorneyContextTool
      },
      endpoints: {
        dossierUpdate: `${LEGALSCOUT_API_BASE}/dossier-update`,
        attorneyContext: `${LEGALSCOUT_API_BASE}/attorney-context`
      },
      migration: {
        from: 'Function tools with external webhooks',
        to: 'API Request tools with direct integration',
        benefits: [
          'No external dependencies',
          'Attorney-specific context automatically available',
          'Direct database integration',
          'Real-time updates',
          'Better error handling'
        ]
      }
    };
    
    fs.writeFileSync('api-request-tools.json', JSON.stringify(toolsData, null, 2));
    
    console.log('\n✅ API Request Tools created successfully!');
    console.log('\n📋 CREATED TOOLS:');
    console.log(`1. live_dossier_api (ID: ${liveDossierTool.id})`);
    console.log(`2. attorney_context_api (ID: ${attorneyContextTool.id})`);
    
    console.log('\n📁 FILES CREATED:');
    console.log('• api-dossier-update.js - Dossier update endpoint');
    console.log('• api-attorney-context.js - Attorney context endpoint');
    console.log('• api-request-tools.json - Tool configurations');
    
    console.log('\n📋 NEXT STEPS:');
    console.log('1. Deploy the API endpoints to your pages/api/ directory');
    console.log('2. Create dossier_updates table in Supabase');
    console.log('3. Replace old tools with new API Request tools in assistants');
    console.log('4. Test with different attorney assistants');
    console.log('5. Remove old Function tools and Make.com webhooks');
    
  } catch (error) {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  }
}

// Run the script
main();
