dashboard:247 
            
            
           GET http://localhost:5174/fix-j<PERSON><PERSON><PERSON>-assistant.js net::ERR_ABORTED 404 (Not Found)
production-debug-vapi.js:82 Uncaught SyntaxError: Cannot use import statement outside a module (at production-debug-vapi.js:82:16)
chunk-Q72EVS5P.js?v=95736bad:521 Warning: validateDOMNesting(...): <form> cannot appear as a descendant of <form>.
    at form
    at div
    at WebsiteImporter (http://localhost:5174/src/components/dashboard/WebsiteImporter.jsx:23:28)
    at form
    at div
    at ProfileTab (http://localhost:5174/src/components/dashboard/ProfileTab.jsx:30:23)
    at div
    at div
    at div
    at div
    at DashboardNew (http://localhost:5174/src/pages/DashboardNew.jsx:50:20)
    at RenderedRoute (http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=fd7da987:5722:26)
    at Routes (http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=fd7da987:6454:3)
    at main
    at div
    at AssistantAwareProvider (http://localhost:5174/src/contexts/AssistantAwareContext.jsx:30:42)
    at App (http://localhost:5174/src/App.jsx:377:20)
    at LegalScoutApp
    at Provider (http://localhost:5174/src/contexts/AuthContext.jsx:27:16)
    at AuthProvider (http://localhost:5174/src/contexts/AuthContext.jsx:54:32)
    at InnerAuthProvider (http://localhost:5174/src/components/SyncAuthProvider.jsx:21:30)
    at Provider (http://localhost:5174/src/contexts/SyncContext.jsx:21:16)
    at SyncProvider (http://localhost:5174/src/contexts/SyncContext.jsx:33:32)
    at SyncAuthProvider (http://localhost:5174/src/components/SyncAuthProvider.jsx:34:29)
    at AttorneyStateProvider (http://localhost:5174/src/contexts/AttorneyStateContext.jsx:31:41)
    at Provider (http://localhost:5174/src/contexts/ThemeContext.jsx:20:16)
    at ThemeProvider (http://localhost:5174/src/contexts/ThemeContext.jsx:41:33)
    at Router (http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=fd7da987:6397:13)
    at BrowserRouter (http://localhost:5174/node_modules/.vite/deps/react-router-dom.js?v=fd7da987:8631:3)
    at ErrorBoundary (http://localhost:5174/src/utils/ErrorBoundary.jsx:7:5)
    at ProductionErrorBoundary (http://localhost:5174/src/components/ProductionErrorBoundary.jsx:7:5)
printWarning @ chunk-Q72EVS5P.js?v=95736bad:521
error @ chunk-Q72EVS5P.js?v=95736bad:505
validateDOMNesting @ chunk-Q72EVS5P.js?v=95736bad:8267
createInstance @ chunk-Q72EVS5P.js?v=95736bad:8339
completeWork @ chunk-Q72EVS5P.js?v=95736bad:16311
completeUnitOfWork @ chunk-Q72EVS5P.js?v=95736bad:19252
performUnitOfWork @ chunk-Q72EVS5P.js?v=95736bad:19234
workLoopSync @ chunk-Q72EVS5P.js?v=95736bad:19165
renderRootSync @ chunk-Q72EVS5P.js?v=95736bad:19144
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=95736bad:18706
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
EnhancedAssistantDropdown.jsx:75 No OAuth user email available
loadAssistants @ EnhancedAssistantDropdown.jsx:75
(anonymous) @ EnhancedAssistantDropdown.jsx:34
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=95736bad:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=95736bad:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=95736bad:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=95736bad:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=95736bad:18896
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=95736bad:9135
commitRootImpl @ chunk-Q72EVS5P.js?v=95736bad:19460
commitRoot @ chunk-Q72EVS5P.js?v=95736bad:19305
finishConcurrentRender @ chunk-Q72EVS5P.js?v=95736bad:18833
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=95736bad:18746
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
EnhancedAssistantDropdown.jsx:75 No OAuth user email available
loadAssistants @ EnhancedAssistantDropdown.jsx:75
(anonymous) @ EnhancedAssistantDropdown.jsx:34
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=95736bad:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=95736bad:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=95736bad:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=95736bad:18896
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=95736bad:9135
commitRootImpl @ chunk-Q72EVS5P.js?v=95736bad:19460
commitRoot @ chunk-Q72EVS5P.js?v=95736bad:19305
finishConcurrentRender @ chunk-Q72EVS5P.js?v=95736bad:18833
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=95736bad:18746
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:247 
            
            
           GET http://localhost:5174/fix-joespizza-assistant.js net::ERR_ABORTED 404 (Not Found)
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:247 
            
            
           GET http://localhost:5174/fix-joespizza-assistant.js net::ERR_ABORTED 404 (Not Found)
simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:247 
            
            
           GET http://localhost:5174/fix-joespizza-assistant.js net::ERR_ABORTED 404 (Not Found)
simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:247 
            
            
           GET http://localhost:5174/fix-joespizza-assistant.js net::ERR_ABORTED 404 (Not Found)
VM9080 production-debug-vapi.js:82 Uncaught SyntaxError: Cannot use import statement outside a module
VM9112 production-debug-vapi.js:82 Uncaught SyntaxError: Cannot use import statement outside a module
dashboard:182 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:182
window.fetch @ production-cors-fix.js:108
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=95736bad:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=95736bad:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=95736bad:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=95736bad:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=95736bad:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=95736bad:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=95736bad:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=95736bad:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:135:34)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=95736bad:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=95736bad:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=95736bad:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=95736bad:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
dashboard:182 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:182
window.fetch @ production-cors-fix.js:108
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=95736bad:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=95736bad:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=95736bad:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=95736bad:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=95736bad:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=95736bad:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:135:34)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=95736bad:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=95736bad:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=95736bad:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
DashboardNew.jsx:376 [DashboardNew] ⚠️ Robust state handler not available (window.resolveAttorneyState is not a function)
fetchAttorneyData @ DashboardNew.jsx:376
(anonymous) @ DashboardNew.jsx:484
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=95736bad:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=95736bad:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=95736bad:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=95736bad:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
vapiAssistantUtils.js:39 MCP not available, keeping existing assistant ID if available
ensureVapiAssistant @ vapiAssistantUtils.js:39
fetchAttorneyData @ DashboardNew.jsx:420
await in fetchAttorneyData
(anonymous) @ DashboardNew.jsx:484
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=95736bad:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=95736bad:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=95736bad:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=95736bad:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
dashboard:182 
            
            
           DELETE https://api.vapi.ai/assistant/840bfa3f-0b96-44d6-bb65-68c8836acce5 404 (Not Found)
window.fetch @ dashboard:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 840bfa3f-0b96-44d6-bb65-68c8836acce5: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
DashboardNew.jsx:218 [DashboardNew] ⚠️ AUTOMATED: Robust state handler not available (window.resolveAttorneyState not found)
runRobustStateHandler @ DashboardNew.jsx:218
await in runRobustStateHandler
(anonymous) @ DashboardNew.jsx:226
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=95736bad:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=95736bad:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=95736bad:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=95736bad:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
simple-preview:247 
            
            
           GET http://localhost:5174/fix-joespizza-assistant.js net::ERR_ABORTED 404 (Not Found)
simple-preview:247 
            
            
           GET http://localhost:5174/fix-joespizza-assistant.js net::ERR_ABORTED 404 (Not Found)
production-debug-vapi.js:82 Uncaught SyntaxError: Cannot use import statement outside a module (at production-debug-vapi.js:82:16)
production-debug-vapi.js:82 Uncaught SyntaxError: Cannot use import statement outside a module (at production-debug-vapi.js:82:16)
assistant_assignments:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_assignments?select=assistant_id&oauth_email=eq.damonkost%40gmail.com&is_active=eq.true 404 (Not Found)
assistantAssignmentService.js:46 Error loading assistant assignments: {code: '42P01', details: null, hint: null, message: 'relation "public.assistant_assignments" does not exist'}
getAssignedAssistantIds @ assistantAssignmentService.js:46
await in getAssignedAssistantIds
loadAssistants @ EnhancedAssistantDropdown.jsx:83
(anonymous) @ EnhancedAssistantDropdown.jsx:34
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=95736bad:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=95736bad:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=95736bad:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=95736bad:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
dashboard:182 
            
            
           DELETE https://api.vapi.ai/assistant/b29f29c9-d3ff-409d-9385-55dd16a1f744 404 (Not Found)
window.fetch @ dashboard:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
dashboard:182 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:182
window.fetch @ production-cors-fix.js:108
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=13bc5c80:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=13bc5c80:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=13bc5c80:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4955
dashboard:182 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:182
window.fetch @ production-cors-fix.js:108
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=13bc5c80:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=13bc5c80:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=13bc5c80:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4955
purge-assistants.js:50 ❌ Failed to delete b29f29c9-d3ff-409d-9385-55dd16a1f744: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
assistant_assignments:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_assignments?select=assistant_id&oauth_email=eq.damonkost%40gmail.com&is_active=eq.true 404 (Not Found)
consultations:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.50e13a9e-22dd-4fe8-a03e-de627c5206c1 400 (Bad Request)
consultations:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.02d1570c-0d3d-4b47-96a9-e1a1ad7db8bd 400 (Bad Request)
test-assistant-id-propagation.js:23 ⚠️ VapiAssistantConfig component not found
testAssistantIDPropagation @ test-assistant-id-propagation.js:23
runAllTests @ test-assistant-id-propagation.js:125
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
test-assistant-id-propagation.js:98 ⚠️ Vapi Direct API service not found
testVapiAPIEndpoints @ test-assistant-id-propagation.js:98
runAllTests @ test-assistant-id-propagation.js:127
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
test-assistant-id-propagation.js:141 ⚠️ Some tests failed. Check the logs above for details.
runAllTests @ test-assistant-id-propagation.js:141
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
consultations:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.e7fa3cc6-54cc-4e64-ab0f-b495091114dc 400 (Bad Request)
assistantAssignmentService.js:46 Error loading assistant assignments: {code: '42P01', details: null, hint: null, message: 'relation "public.assistant_assignments" does not exist'}
getAssignedAssistantIds @ assistantAssignmentService.js:46
await in getAssignedAssistantIds
loadAssistants @ EnhancedAssistantDropdown.jsx:83
(anonymous) @ EnhancedAssistantDropdown.jsx:34
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=95736bad:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=95736bad:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=95736bad:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
VeryCoolAssistants.jsx:133 Error loading stats for assistant 50e13a9e-22dd-4fe8-a03e-de627c5206c1: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:133
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:104
loadAssistants @ VeryCoolAssistants.jsx:90
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=95736bad:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=95736bad:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=95736bad:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=95736bad:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=95736bad:18896
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=95736bad:9135
commitRootImpl @ chunk-Q72EVS5P.js?v=95736bad:19460
commitRoot @ chunk-Q72EVS5P.js?v=95736bad:19305
finishConcurrentRender @ chunk-Q72EVS5P.js?v=95736bad:18833
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=95736bad:18746
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
VeryCoolAssistants.jsx:133 Error loading stats for assistant 02d1570c-0d3d-4b47-96a9-e1a1ad7db8bd: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:133
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:104
loadAssistants @ VeryCoolAssistants.jsx:90
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=95736bad:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=95736bad:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=95736bad:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=95736bad:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=95736bad:18896
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=95736bad:9135
commitRootImpl @ chunk-Q72EVS5P.js?v=95736bad:19460
commitRoot @ chunk-Q72EVS5P.js?v=95736bad:19305
finishConcurrentRender @ chunk-Q72EVS5P.js?v=95736bad:18833
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=95736bad:18746
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
VeryCoolAssistants.jsx:133 Error loading stats for assistant e7fa3cc6-54cc-4e64-ab0f-b495091114dc: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:133
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:104
loadAssistants @ VeryCoolAssistants.jsx:90
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:23
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=95736bad:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=95736bad:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=95736bad:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=95736bad:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
performSyncWorkOnRoot @ chunk-Q72EVS5P.js?v=95736bad:18896
flushSyncCallbacks @ chunk-Q72EVS5P.js?v=95736bad:9135
commitRootImpl @ chunk-Q72EVS5P.js?v=95736bad:19460
commitRoot @ chunk-Q72EVS5P.js?v=95736bad:19305
finishConcurrentRender @ chunk-Q72EVS5P.js?v=95736bad:18833
performConcurrentWorkOnRoot @ chunk-Q72EVS5P.js?v=95736bad:18746
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
dashboard:182 
            
            
           DELETE https://api.vapi.ai/assistant/246eca2b-f523-4bbe-ac77-066f4eb3616b 404 (Not Found)
window.fetch @ dashboard:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.f3fba553-1dee-40dc-ba56-0bb8d7befdc4 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.f7058737-df13-4ce2-9680-e35b81a977a2 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.9bd19d85-6a8f-483f-8d9d-491cdafea6b8 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.7369e8cb-e29e-4928-acbb-85244d7af966 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.127394cd-8b0a-4cfd-9c98-eef00a340b5f 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.86b893c5-13bf-4e58-bbc5-22d6dcb02975 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.6f00e186-2f61-495c-a6df-0714c56f1613 406 (Not Acceptable)
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.c…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.c3083941-c659-44f0-8f63-b3a3aef9ac67 406 (Not Acceptable)
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.a…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.a90d4440-dd1d-42ba-afcd-bbbfdcd78cee 406 (Not Acceptable)
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.0…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.09ddb2e2-0048-4232-b6b8-342f4390f22e 406 (Not Acceptable)
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.4…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.443b32a8-fa93-4c0b-8cad-480ce731f757 406 (Not Acceptable)
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.2…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.23207908-b5b7-421c-8655-8ae55447e587 406 (Not Acceptable)
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.3…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.37b058e0-2387-435c-bca8-549fc66fd4b4 406 (Not Acceptable)
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.0…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.0244cf22-2b0c-4ab2-826d-0de5fd2d90b4 406 (Not Acceptable)
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.7…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.79734881-b7d7-49d4-a411-e4540870cb47 406 (Not Acceptable)
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.0…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.0c827c56-fa7f-4cf7-8867-befab43d990c 406 (Not Acceptable)
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.d…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.d3dd6ad7-0ab9-45bb-9c13-513b349ee1f5 406 (Not Acceptable)
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.a…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.ab46fa48-33ef-4d3f-8567-1c6bd9eb381e 406 (Not Acceptable)
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.e…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.e73b3cf4-8aca-4b9a-addf-2397eb5a8da6 406 (Not Acceptable)
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.d…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.d7a25f40-0bd5-40a7-9bc3-52a76d4e2fa3 406 (Not Acceptable)
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.1…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.16d2e026-9742-433e-815a-036134d24644 406 (Not Acceptable)
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.5…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.5788c5f9-1c83-4a30-a578-1009624bdadb 406 (Not Acceptable)
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.3…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.3a4eaad2-2419-416b-9699-dd25e1b05258 406 (Not Acceptable)
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.e…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.efd05d25-519e-4fc6-a09a-816d19cb3632 406 (Not Acceptable)
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.8…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.878a1883-12a2-462c-baad-609ec63e4ca4 406 (Not Acceptable)
simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182 
            
            
           DELETE https://api.vapi.ai/assistant/840bfa3f-0b96-44d6-bb65-68c8836acce5 404 (Not Found)
window.fetch @ simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182 
            
            
           DELETE https://api.vapi.ai/assistant/840bfa3f-0b96-44d6-bb65-68c8836acce5 404 (Not Found)
window.fetch @ simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 246eca2b-f523-4bbe-ac77-066f4eb3616b: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.8…:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.87e15913-cb12-47bb-8e06-ce45aae90335 406 (Not Acceptable)
purge-assistants.js:50 ❌ Failed to delete 840bfa3f-0b96-44d6-bb65-68c8836acce5: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 840bfa3f-0b96-44d6-bb65-68c8836acce5: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.58eaa3d2-f1b8-4d41-916e-c63253bccc7a 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.86b8a17a-20cc-4068-a3bc-357633d9f9c2 406 (Not Acceptable)
simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182
window.fetch @ production-cors-fix.js:108
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=13bc5c80:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js?v=13bc5c80:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4977
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:5542
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4828
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.d4316c92-d40a-4bdb-8e45-ba77e99a5f9e 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.639e176f-5716-42f8-8519-f6be8da62fdf 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.23eef218-0dfb-4ec7-861e-ec9429f4becc 406 (Not Acceptable)
dashboard:182 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:182
window.fetch @ production-cors-fix.js:108
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=13bc5c80:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js?v=13bc5c80:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4977
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:5542
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4828
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=13bc5c80:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=13bc5c80:6268:7)
    at async SupabaseAuthClient._recoverAndRefresh (@supabase_supabase-js.js?v=13bc5c80:6204:9)
    at async SupabaseAuthClient._initialize (@supabase_supabase-js.js?v=13bc5c80:5023:7)
    at async @supabase_supabase-js.js?v=13bc5c80:4977:16
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=13bc5c80:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js?v=13bc5c80:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4977
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:5542
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4828
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.a0aab32e-dccb-4975-afee-1c2227543801 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.7402eac1-0f7b-4b6c-84c0-b27d93bf8699 406 (Not Acceptable)
simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182
window.fetch @ production-cors-fix.js:108
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=13bc5c80:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=13bc5c80:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=13bc5c80:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4955
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.3382d3d7-6b97-46a8-b645-a4176426feed 406 (Not Acceptable)
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=13bc5c80:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=13bc5c80:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=13bc5c80:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4955
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.25bfbb30-7eb7-40a9-a7a9-b8daab8cbe81 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.43d6390f-f377-4a37-aa5b-080b56605511 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.400778a8-01b5-4625-9338-d823ed257369 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.fae1d23a-8821-437f-8eb1-1c5736777cb5 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.d52b4b45-884c-4b3f-824f-49646a9c3d95 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.7b1f64be-f426-418c-bcc6-82d71513c51e 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.333f67b7-93ff-4583-ba7e-32d3355b5fad 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.cbeddcb1-15a4-4506-bb86-4adf82d6a51b 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.dc6cc543-f60a-45d3-84fe-d7ff2b0e556b 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.bb61cffc-0843-4d96-a439-838da7296ac2 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.d3ce624d-3b12-4036-83f3-a4f297dc3f1c 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.4831c692-c073-4518-b0c8-27bd34883ba4 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.91addb4c-f443-48f1-8ace-352d2c7a8e83 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.165b4c91-2cd7-4c9f-80f6-f52991ce4693 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.f9b97d13-f9c4-40af-a660-62ba5925ff2a 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.ac822d4b-c8f3-4a90-a5f5-79441c80e9c3 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.185d3677-f0d5-4afe-9e4d-bcc00aa74c91 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.3ae08d39-656f-4a30-bba2-ab420aae568c 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.2b24ed10-f3e8-44fa-b44a-83b840db4e18 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.650b559c-f406-4218-ab8d-82c446650966 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.011cbf4b-e4af-4a33-96ee-d5d6823b85bf 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.aeb12f01-18a5-4f12-9de8-762775df4d23 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.1c5e011c-ef1b-4bfa-a69f-2b3752ebdd77 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.ec6c50ac-0a4b-480a-b480-6240f58bb23d 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.060feec4-2c61-432b-98fe-6266c6f49765 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.4e899c0a-b435-45c4-abcd-8abd3ff13ec3 406 (Not Acceptable)
simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182 
            
            
           DELETE https://api.vapi.ai/assistant/b29f29c9-d3ff-409d-9385-55dd16a1f744 404 (Not Found)
window.fetch @ simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182 
            
            
           DELETE https://api.vapi.ai/assistant/b29f29c9-d3ff-409d-9385-55dd16a1f744 404 (Not Found)
window.fetch @ simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete b29f29c9-d3ff-409d-9385-55dd16a1f744: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete b29f29c9-d3ff-409d-9385-55dd16a1f744: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
dashboard:182 
            
            
           DELETE https://api.vapi.ai/assistant/459c8339-2b2b-4d9f-aff9-73b147ebcafa 404 (Not Found)
window.fetch @ dashboard:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 459c8339-2b2b-4d9f-aff9-73b147ebcafa: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182
window.fetch @ production-cors-fix.js:108
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=13bc5c80:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js?v=13bc5c80:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4977
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:5542
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4828
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=13bc5c80:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js?v=13bc5c80:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4977
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:5542
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4828
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=13bc5c80:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=13bc5c80:6268:7)
    at async SupabaseAuthClient._recoverAndRefresh (@supabase_supabase-js.js?v=13bc5c80:6204:9)
    at async SupabaseAuthClient._initialize (@supabase_supabase-js.js?v=13bc5c80:5023:7)
    at async @supabase_supabase-js.js?v=13bc5c80:4977:16
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
_recoverAndRefresh @ @supabase_supabase-js.js?v=13bc5c80:6204
await in _recoverAndRefresh
_initialize @ @supabase_supabase-js.js?v=13bc5c80:5023
await in _initialize
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4977
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:5542
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4828
dashboard:182 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ dashboard:182
window.fetch @ production-cors-fix.js:108
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=13bc5c80:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=13bc5c80:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=13bc5c80:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4955
simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182
window.fetch @ production-cors-fix.js:108
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4955
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4955
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async Object.callback (AuthContext.jsx:215:34)
    at async @supabase_supabase-js.js?v=13bc5c80:6263:11
    at async Promise.all (index 1)
    at async SupabaseAuthClient._notifyAllSubscribers (@supabase_supabase-js.js?v=13bc5c80:6268:7)
    at async BroadcastChannel.<anonymous> (@supabase_supabase-js.js?v=13bc5c80:4955:9)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
(anonymous) @ AuthContext.jsx:215
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:6263
_notifyAllSubscribers @ @supabase_supabase-js.js?v=13bc5c80:6261
(anonymous) @ @supabase_supabase-js.js?v=13bc5c80:4955
test-assistant-id-propagation.js:14 ⚠️ Assistant dropdown not found
testAssistantIDPropagation @ test-assistant-id-propagation.js:14
runAllTests @ test-assistant-id-propagation.js:125
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
test-assistant-id-propagation.js:98 ⚠️ Vapi Direct API service not found
testVapiAPIEndpoints @ test-assistant-id-propagation.js:98
runAllTests @ test-assistant-id-propagation.js:127
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
test-assistant-id-propagation.js:141 ⚠️ Some tests failed. Check the logs above for details.
runAllTests @ test-assistant-id-propagation.js:141
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
test-assistant-id-propagation.js:14 ⚠️ Assistant dropdown not found
testAssistantIDPropagation @ test-assistant-id-propagation.js:14
runAllTests @ test-assistant-id-propagation.js:125
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
test-assistant-id-propagation.js:98 ⚠️ Vapi Direct API service not found
testVapiAPIEndpoints @ test-assistant-id-propagation.js:98
runAllTests @ test-assistant-id-propagation.js:127
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
test-assistant-id-propagation.js:141 ⚠️ Some tests failed. Check the logs above for details.
runAllTests @ test-assistant-id-propagation.js:141
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182 
            
            
           DELETE https://api.vapi.ai/assistant/246eca2b-f523-4bbe-ac77-066f4eb3616b 404 (Not Found)
window.fetch @ simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 246eca2b-f523-4bbe-ac77-066f4eb3616b: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182 
            
            
           DELETE https://api.vapi.ai/assistant/246eca2b-f523-4bbe-ac77-066f4eb3616b 404 (Not Found)
window.fetch @ simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 246eca2b-f523-4bbe-ac77-066f4eb3616b: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182
window.fetch @ production-cors-fix.js:108
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=95736bad:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=95736bad:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=95736bad:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=95736bad:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=95736bad:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=95736bad:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=95736bad:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=95736bad:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:135:34)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=95736bad:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=95736bad:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=95736bad:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=95736bad:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182
window.fetch @ production-cors-fix.js:108
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=95736bad:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=95736bad:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=95736bad:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=95736bad:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=95736bad:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=95736bad:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:135:34)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=95736bad:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=95736bad:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=95736bad:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.9bd19d85-6a8f-483f-8d9d-491cdafea6b8 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.d3dd6ad7-0ab9-45bb-9c13-513b349ee1f5 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.f7058737-df13-4ce2-9680-e35b81a977a2 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.127394cd-8b0a-4cfd-9c98-eef00a340b5f 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.f3fba553-1dee-40dc-ba56-0bb8d7befdc4 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.ab46fa48-33ef-4d3f-8567-1c6bd9eb381e 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.23207908-b5b7-421c-8655-8ae55447e587 406 (Not Acceptable)
simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182
window.fetch @ production-cors-fix.js:108
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=95736bad:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=95736bad:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=95736bad:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=95736bad:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182 
            
            
           POST http://localhost:5174/api/sync-tools/manage-auth-state 500 (Internal Server Error)
window.fetch @ simple-preview?subdomain=attorney-08532&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:182
window.fetch @ production-cors-fix.js:108
(anonymous) @ useSyncTools.js:209
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=95736bad:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=95736bad:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=95736bad:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.09ddb2e2-0048-4232-b6b8-342f4390f22e 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.d7a25f40-0bd5-40a7-9bc3-52a76d4e2fa3 406 (Not Acceptable)
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=95736bad:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=95736bad:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=95736bad:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=95736bad:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:135:34)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js?v=95736bad:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js?v=95736bad:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js?v=95736bad:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js?v=95736bad:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19518
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
useSyncTools.js:237 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:237
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=95736bad:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=95736bad:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=95736bad:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
useSyncTools.js:245 API error, falling back to client-side implementation: Error: Empty response from server
    at Object.manageAuthState (useSyncTools.js:238:17)
    at async Object.handleAuthState (SyncContext.jsx:201:24)
    at async initAuth (AuthContext.jsx:135:34)
(anonymous) @ useSyncTools.js:245
await in (anonymous)
(anonymous) @ SyncContext.jsx:201
initAuth @ AuthContext.jsx:126
await in initAuth
(anonymous) @ AuthContext.jsx:174
commitHookEffectListMount @ chunk-Q72EVS5P.js?v=95736bad:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js?v=95736bad:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js?v=95736bad:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js?v=95736bad:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js?v=95736bad:19531
flushPassiveEffects @ chunk-Q72EVS5P.js?v=95736bad:19475
(anonymous) @ chunk-Q72EVS5P.js?v=95736bad:19356
workLoop @ chunk-Q72EVS5P.js?v=95736bad:197
flushWork @ chunk-Q72EVS5P.js?v=95736bad:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js?v=95736bad:384
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.6f00e186-2f61-495c-a6df-0714c56f1613 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.7369e8cb-e29e-4928-acbb-85244d7af966 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.a90d4440-dd1d-42ba-afcd-bbbfdcd78cee 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.5788c5f9-1c83-4a30-a578-1009624bdadb 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.16d2e026-9742-433e-815a-036134d24644 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.443b32a8-fa93-4c0b-8cad-480ce731f757 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.58eaa3d2-f1b8-4d41-916e-c63253bccc7a 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.0244cf22-2b0c-4ab2-826d-0de5fd2d90b4 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.3a4eaad2-2419-416b-9699-dd25e1b05258 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.a8bbdf85-eaa8-4b97-9539-33094f0c97ee 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.878a1883-12a2-462c-baad-609ec63e4ca4 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.400778a8-01b5-4625-9338-d823ed257369 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.3ae08d39-656f-4a30-bba2-ab420aae568c 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.7b1f64be-f426-418c-bcc6-82d71513c51e 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.d4316c92-d40a-4bdb-8e45-ba77e99a5f9e 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.d3ce624d-3b12-4036-83f3-a4f297dc3f1c 406 (Not Acceptable)
dashboard:182 
            
            
           DELETE https://api.vapi.ai/assistant/80a0b06c-394b-40aa-987b-35ab17123efc 404 (Not Found)
window.fetch @ dashboard:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 80a0b06c-394b-40aa-987b-35ab17123efc: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.7402eac1-0f7b-4b6c-84c0-b27d93bf8699 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.86b8a17a-20cc-4068-a3bc-357633d9f9c2 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.3382d3d7-6b97-46a8-b645-a4176426feed 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.a0aab32e-dccb-4975-afee-1c2227543801 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.43d6390f-f377-4a37-aa5b-080b56605511 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.2b24ed10-f3e8-44fa-b44a-83b840db4e18 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.650b559c-f406-4218-ab8d-82c446650966 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.185d3677-f0d5-4afe-9e4d-bcc00aa74c91 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.86b893c5-13bf-4e58-bbc5-22d6dcb02975 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.79734881-b7d7-49d4-a411-e4540870cb47 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.ec6c50ac-0a4b-480a-b480-6240f58bb23d 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.060feec4-2c61-432b-98fe-6266c6f49765 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.c3083941-c659-44f0-8f63-b3a3aef9ac67 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.aeb12f01-18a5-4f12-9de8-762775df4d23 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.e73b3cf4-8aca-4b9a-addf-2397eb5a8da6 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.87e15913-cb12-47bb-8e06-ce45aae90335 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.4e899c0a-b435-45c4-abcd-8abd3ff13ec3 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.ac822d4b-c8f3-4a90-a5f5-79441c80e9c3 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.dc6cc543-f60a-45d3-84fe-d7ff2b0e556b 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.0c827c56-fa7f-4cf7-8867-befab43d990c 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.37b058e0-2387-435c-bca8-549fc66fd4b4 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.011cbf4b-e4af-4a33-96ee-d5d6823b85bf 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.efd05d25-519e-4fc6-a09a-816d19cb3632 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.25bfbb30-7eb7-40a9-a7a9-b8daab8cbe81 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.333f67b7-93ff-4583-ba7e-32d3355b5fad 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.23eef218-0dfb-4ec7-861e-ec9429f4becc 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.cbeddcb1-15a4-4506-bb86-4adf82d6a51b 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.165b4c91-2cd7-4c9f-80f6-f52991ce4693 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.bb61cffc-0843-4d96-a439-838da7296ac2 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.d52b4b45-884c-4b3f-824f-49646a9c3d95 406 (Not Acceptable)
attorneys:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&user_id=eq.571390ac-5a83-46b2-ad3a-18b9cf39d701 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.fae1d23a-8821-437f-8eb1-1c5736777cb5 406 (Not Acceptable)
consultations:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.02d1570c-0d3d-4b47-96a9-e1a1ad7db8bd 400 (Bad Request)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.639e176f-5716-42f8-8519-f6be8da62fdf 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d 406 (Not Acceptable)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.1c5e011c-ef1b-4bfa-a69f-2b3752ebdd77 406 (Not Acceptable)
consultations:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.e7fa3cc6-54cc-4e64-ab0f-b495091114dc 400 (Bad Request)
assistant_ui_configs:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.4831c692-c073-4518-b0c8-27bd34883ba4 406 (Not Acceptable)
consultations:1 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.50e13a9e-22dd-4fe8-a03e-de627c5206c1 400 (Bad Request)
 Error loading stats for assistant 02d1570c-0d3d-4b47-96a9-e1a1ad7db8bd: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:107
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:86
loadAssistants @ VeryCoolAssistants.jsx:77
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:34
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
assistant_ui_configs:1  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.f9b97d13-f9c4-40af-a660-62ba5925ff2a 406 (Not Acceptable)
 Error loading stats for assistant e7fa3cc6-54cc-4e64-ab0f-b495091114dc: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:107
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:86
loadAssistants @ VeryCoolAssistants.jsx:77
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:34
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
assistant_ui_configs:1  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.91addb4c-f443-48f1-8ace-352d2c7a8e83 406 (Not Acceptable)
 Error loading stats for assistant 50e13a9e-22dd-4fe8-a03e-de627c5206c1: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:107
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:86
loadAssistants @ VeryCoolAssistants.jsx:77
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:34
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
consultations:1  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.02d1570c-0d3d-4b47-96a9-e1a1ad7db8bd 400 (Bad Request)
consultations:1  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.e7fa3cc6-54cc-4e64-ab0f-b495091114dc 400 (Bad Request)
consultations:1  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.2fac58aa-5504-466e-8975-c9d8b0ae1c3e&assistant_id=eq.50e13a9e-22dd-4fe8-a03e-de627c5206c1 400 (Bad Request)
 Error loading stats for assistant 02d1570c-0d3d-4b47-96a9-e1a1ad7db8bd: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:107
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:86
loadAssistants @ VeryCoolAssistants.jsx:77
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:34
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Error loading stats for assistant e7fa3cc6-54cc-4e64-ab0f-b495091114dc: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:107
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:86
loadAssistants @ VeryCoolAssistants.jsx:77
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:34
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Error loading stats for assistant 50e13a9e-22dd-4fe8-a03e-de627c5206c1: {code: '42703', details: null, hint: null, message: 'column consultations.assistant_id does not exist'}
(anonymous) @ VeryCoolAssistants.jsx:107
await in (anonymous)
loadAssistantStats @ VeryCoolAssistants.jsx:86
loadAssistants @ VeryCoolAssistants.jsx:77
await in loadAssistants
(anonymous) @ VeryCoolAssistants.jsx:34
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
attorneys:1  GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&email=eq.damonkost%40gmail.com 406 (Not Acceptable)
  DELETE https://api.vapi.ai/assistant/459c8339-2b2b-4d9f-aff9-73b147ebcafa 404 (Not Found)
window.fetch @ simple-preview:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete 459c8339-2b2b-4d9f-aff9-73b147ebcafa: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
  DELETE https://api.vapi.ai/assistant/459c8339-2b2b-4d9f-aff9-73b147ebcafa 404 (Not Found)
window.fetch @ simple-preview:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete 459c8339-2b2b-4d9f-aff9-73b147ebcafa: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
  DELETE https://api.vapi.ai/assistant/a4e45c56-5f29-4171-8917-1a5fbb36dc8b 404 (Not Found)
window.fetch @ dashboard:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete a4e45c56-5f29-4171-8917-1a5fbb36dc8b: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
  DELETE https://api.vapi.ai/assistant/80a0b06c-394b-40aa-987b-35ab17123efc 404 (Not Found)
window.fetch @ simple-preview:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete 80a0b06c-394b-40aa-987b-35ab17123efc: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
  DELETE https://api.vapi.ai/assistant/80a0b06c-394b-40aa-987b-35ab17123efc 404 (Not Found)
window.fetch @ simple-preview:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete 80a0b06c-394b-40aa-987b-35ab17123efc: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
  DELETE https://api.vapi.ai/assistant/f3298201-08e9-4d96-83e3-0b78d7460ea8 404 (Not Found)
window.fetch @ dashboard:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete f3298201-08e9-4d96-83e3-0b78d7460ea8: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
  DELETE https://api.vapi.ai/assistant/a4e45c56-5f29-4171-8917-1a5fbb36dc8b 404 (Not Found)
window.fetch @ simple-preview:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete a4e45c56-5f29-4171-8917-1a5fbb36dc8b: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
  DELETE https://api.vapi.ai/assistant/a4e45c56-5f29-4171-8917-1a5fbb36dc8b 404 (Not Found)
window.fetch @ simple-preview:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete a4e45c56-5f29-4171-8917-1a5fbb36dc8b: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
  DELETE https://api.vapi.ai/assistant/f3298201-08e9-4d96-83e3-0b78d7460ea8 404 (Not Found)
window.fetch @ simple-preview:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete f3298201-08e9-4d96-83e3-0b78d7460ea8: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
  DELETE https://api.vapi.ai/assistant/f3298201-08e9-4d96-83e3-0b78d7460ea8 404 (Not Found)
window.fetch @ simple-preview:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete f3298201-08e9-4d96-83e3-0b78d7460ea8: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
  DELETE https://api.vapi.ai/assistant/39515477-15a9-4b75-81d5-095bf3bb5691 404 (Not Found)
window.fetch @ simple-preview:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete 39515477-15a9-4b75-81d5-095bf3bb5691: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
  DELETE https://api.vapi.ai/assistant/39515477-15a9-4b75-81d5-095bf3bb5691 404 (Not Found)
window.fetch @ simple-preview:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete 39515477-15a9-4b75-81d5-095bf3bb5691: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
  DELETE https://api.vapi.ai/assistant/39515477-15a9-4b75-81d5-095bf3bb5691 404 (Not Found)
window.fetch @ dashboard:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete 39515477-15a9-4b75-81d5-095bf3bb5691: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
  DELETE https://api.vapi.ai/assistant/38719ef3-28af-49ab-bcaa-2fbc436c78d8 404 (Not Found)
window.fetch @ simple-preview:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete 38719ef3-28af-49ab-bcaa-2fbc436c78d8: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
  DELETE https://api.vapi.ai/assistant/38719ef3-28af-49ab-bcaa-2fbc436c78d8 404 (Not Found)
window.fetch @ simple-preview:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete 38719ef3-28af-49ab-bcaa-2fbc436c78d8: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 🧹 [CleanAuthSolution] Timeout waiting for Supabase
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
  DELETE https://api.vapi.ai/assistant/38719ef3-28af-49ab-bcaa-2fbc436c78d8 404 (Not Found)
window.fetch @ dashboard:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete 38719ef3-28af-49ab-bcaa-2fbc436c78d8: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
  DELETE https://api.vapi.ai/assistant/6e311928-9842-438f-b933-3817a0e63b5d 404 (Not Found)
window.fetch @ simple-preview:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete 6e311928-9842-438f-b933-3817a0e63b5d: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
  DELETE https://api.vapi.ai/assistant/6e311928-9842-438f-b933-3817a0e63b5d 404 (Not Found)
window.fetch @ dashboard:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
  DELETE https://api.vapi.ai/assistant/6e311928-9842-438f-b933-3817a0e63b5d 404 (Not Found)
window.fetch @ simple-preview:182
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete 6e311928-9842-438f-b933-3817a0e63b5d: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 ❌ Failed to delete 6e311928-9842-438f-b933-3817a0e63b5d: 404 - {"message":"Not Found","statusCode":404}
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
 [DashboardNew] Loading timeout reached, forcing loading state to false
(anonymous) @ DashboardNew.jsx:349
setTimeout
(anonymous) @ DashboardNew.jsx:347
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 🧹 [CleanAuthSolution] Timeout waiting for Supabase
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
 🧹 [CleanAuthSolution] Timeout waiting for Supabase
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
