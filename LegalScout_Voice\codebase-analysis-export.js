#!/usr/bin/env node

/**
 * PORTABLE CODEBASE ANALYSIS UTILITY
 *
 * Analyzes current LegalScout workspace and exports findings for external codebase review
 * Identifies intentional changes, worth-keeping components, and provides merge guidance
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

const ANALYSIS_OUTPUT = 'codebase-analysis-report.json';
const CURRENT_WORKSPACE = process.cwd();

// Key directories and files to analyze
const CRITICAL_PATHS = {
  // Modern dashboard components (ASSISTANTS_SCOUT branch)
  modernDashboard: [
    'src/pages/DashboardNew.jsx',
    'src/pages/DashboardNew.css',
    'src/components/dashboard/HeaderAssistantSelector.jsx',
    'src/components/dashboard/EnhancedAssistantDropdown.jsx',
    'src/components/dashboard/SubdomainEditor.jsx',
    'src/hooks/useStandaloneAttorney.js',
    'src/hooks/useDomainSync.js'
  ],
  
  // Core application files
  coreApp: [
    'src/App.jsx',
    'src/main.jsx',
    'package.json',
    'vite.config.js'
  ],
  
  // Authentication & contexts
  contexts: [
    'src/contexts/AuthContext.jsx',
    'src/contexts/ThemeContext.jsx'
  ],
  
  // Services and utilities
  services: [
    'src/services/vapiService.js',
    'src/services/supabaseService.js',
    'src/services/DomainAssistantSync.js',
    'src/services/AssistantAssignmentService.js'
  ],
  
  // Configuration files
  config: [
    '.vscode/launch.json',
    'vercel.json',
    'tsconfig.json'
  ]
};

// Clone directories that may contain conflicting versions
const CLONE_DIRS = ['fresh_clone', 'temp_clone', 'temp_repo'];

class CodebaseAnalyzer {
  constructor() {
    this.analysis = {
      timestamp: new Date().toISOString(),
      workspace: CURRENT_WORKSPACE,
      branch: this.getCurrentBranch(),
      summary: {},
      files: {},
      conflicts: {},
      recommendations: {}
    };
  }

  getCurrentBranch() {
    try {
      const gitHead = fs.readFileSync('.git/HEAD', 'utf8').trim();
      return gitHead.replace('ref: refs/heads/', '');
    } catch {
      return 'unknown';
    }
  }

  getFileHash(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      return crypto.createHash('md5').update(content).digest('hex');
    } catch {
      return null;
    }
  }

  getFileInfo(filePath) {
    try {
      const stats = fs.statSync(filePath);
      const content = fs.readFileSync(filePath, 'utf8');
      
      return {
        exists: true,
        size: stats.size,
        modified: stats.mtime.toISOString(),
        lines: content.split('\n').length,
        hash: this.getFileHash(filePath),
        hasVapi: content.includes('vapi') || content.includes('Vapi'),
        hasAssistant: content.includes('assistant') || content.includes('Assistant'),
        hasAuth: content.includes('auth') || content.includes('Auth'),
        imports: this.extractImports(content),
        exports: this.extractExports(content)
      };
    } catch {
      return { exists: false };
    }
  }

  extractImports(content) {
    const importRegex = /import\s+.*?\s+from\s+['"`]([^'"`]+)['"`]/g;
    const imports = [];
    let match;
    while ((match = importRegex.exec(content)) !== null) {
      imports.push(match[1]);
    }
    return imports;
  }

  extractExports(content) {
    const exportRegex = /export\s+(?:default\s+)?(?:const\s+|function\s+|class\s+)?(\w+)/g;
    const exports = [];
    let match;
    while ((match = exportRegex.exec(content)) !== null) {
      exports.push(match[1]);
    }
    return exports;
  }

  analyzeFileCategory(categoryName, filePaths) {
    console.log(`\n📁 Analyzing ${categoryName}...`);
    
    const categoryAnalysis = {
      totalFiles: filePaths.length,
      existingFiles: 0,
      missingFiles: 0,
      conflicts: 0,
      files: {}
    };

    filePaths.forEach(filePath => {
      const mainFile = this.getFileInfo(filePath);
      const fileAnalysis = {
        main: mainFile,
        clones: {}
      };

      // Check for versions in clone directories
      CLONE_DIRS.forEach(cloneDir => {
        const clonePath = path.join(cloneDir, filePath);
        const cloneFile = this.getFileInfo(clonePath);
        if (cloneFile.exists) {
          fileAnalysis.clones[cloneDir] = cloneFile;
        }
      });

      // Detect conflicts
      const versions = Object.values(fileAnalysis.clones).filter(f => f.exists);
      if (versions.length > 0 && mainFile.exists) {
        const hasConflict = versions.some(v => v.hash !== mainFile.hash);
        if (hasConflict) {
          categoryAnalysis.conflicts++;
          fileAnalysis.hasConflict = true;
        }
      }

      if (mainFile.exists) categoryAnalysis.existingFiles++;
      else categoryAnalysis.missingFiles++;

      categoryAnalysis.files[filePath] = fileAnalysis;
    });

    this.analysis.files[categoryName] = categoryAnalysis;
  }

  generateRecommendations() {
    const recs = {
      intentionalChanges: [],
      worthKeeping: [],
      conflictsToResolve: [],
      safeToDelete: []
    };

    // Analyze each category for recommendations
    Object.entries(this.analysis.files).forEach(([category, data]) => {
      Object.entries(data.files).forEach(([filePath, fileData]) => {
        if (fileData.hasConflict) {
          recs.conflictsToResolve.push({
            file: filePath,
            category: category,
            reason: 'Multiple versions detected with different content'
          });
        }

        if (fileData.main.exists && fileData.main.hasVapi) {
          recs.worthKeeping.push({
            file: filePath,
            category: category,
            reason: 'Contains Vapi integration code'
          });
        }

        if (category === 'modernDashboard' && fileData.main.exists) {
          recs.intentionalChanges.push({
            file: filePath,
            category: category,
            reason: 'Part of ASSISTANTS_SCOUT branch modern dashboard'
          });
        }
      });
    });

    // Clone directories analysis
    CLONE_DIRS.forEach(cloneDir => {
      if (fs.existsSync(cloneDir)) {
        recs.safeToDelete.push({
          file: cloneDir,
          category: 'cleanup',
          reason: 'Clone directory no longer needed after analysis'
        });
      }
    });

    this.analysis.recommendations = recs;
  }

  run() {
    console.log('🔍 PORTABLE CODEBASE ANALYSIS STARTING...\n');
    console.log(`📂 Workspace: ${CURRENT_WORKSPACE}`);
    console.log(`🌿 Branch: ${this.analysis.branch}`);

    // Analyze each category
    Object.entries(CRITICAL_PATHS).forEach(([category, paths]) => {
      this.analyzeFileCategory(category, paths);
    });

    // Generate recommendations
    this.generateRecommendations();

    // Create summary
    this.analysis.summary = {
      totalCategories: Object.keys(CRITICAL_PATHS).length,
      totalFiles: Object.values(CRITICAL_PATHS).flat().length,
      existingFiles: Object.values(this.analysis.files).reduce((sum, cat) => sum + cat.existingFiles, 0),
      missingFiles: Object.values(this.analysis.files).reduce((sum, cat) => sum + cat.missingFiles, 0),
      totalConflicts: Object.values(this.analysis.files).reduce((sum, cat) => sum + cat.conflicts, 0),
      cloneDirectories: CLONE_DIRS.filter(dir => fs.existsSync(dir)).length
    };

    // Export analysis
    fs.writeFileSync(ANALYSIS_OUTPUT, JSON.stringify(this.analysis, null, 2));
    
    console.log('\n✅ ANALYSIS COMPLETE!');
    console.log(`📄 Report exported to: ${ANALYSIS_OUTPUT}`);
    console.log(`📊 Summary: ${this.analysis.summary.existingFiles}/${this.analysis.summary.totalFiles} files exist, ${this.analysis.summary.totalConflicts} conflicts detected`);
    
    return this.analysis;
  }
}

// Run analysis
const analyzer = new CodebaseAnalyzer();
const analysis = analyzer.run();
