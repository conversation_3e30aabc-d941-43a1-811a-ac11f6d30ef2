/**
 * Database Assistant ID Debug Script
 * Run this in browser console to see what's actually in the database
 */

console.log('🔍 DATABASE ASSISTANT ID DEBUG');
console.log('==============================');

async function debugDatabaseAssistantIds() {
  try {
    // Check if Supabase is available
    if (!window.supabase && !window.supabaseClient) {
      console.error('❌ Supabase client not available');
      return;
    }

    const supabase = window.supabase || window.supabaseClient;
    
    // Get attorney data from localStorage
    const attorneyData = localStorage.getItem('attorney');
    if (!attorneyData) {
      console.error('❌ No attorney data in localStorage');
      return;
    }

    const attorney = JSON.parse(attorneyData);
    console.log('👤 Attorney Data:', {
      id: attorney.id,
      firm_name: attorney.firm_name,
      vapi_assistant_id: attorney.vapi_assistant_id,
      current_assistant_id: attorney.current_assistant_id
    });

    // 1. Check attorneys table
    console.log('\n📋 1. ATTORNEYS TABLE');
    const { data: attorneyRecord, error: attorneyError } = await supabase
      .from('attorneys')
      .select('id, firm_name, vapi_assistant_id, current_assistant_id, subdomain')
      .eq('id', attorney.id)
      .single();

    if (attorneyError) {
      console.error('❌ Error fetching attorney:', attorneyError);
    } else {
      console.log('✅ Attorney record:', attorneyRecord);
    }

    // 2. Check assistant_ui_configs table
    console.log('\n🎛️ 2. ASSISTANT_UI_CONFIGS TABLE');
    const { data: configs, error: configsError } = await supabase
      .from('assistant_ui_configs')
      .select('*')
      .eq('attorney_id', attorney.id);

    if (configsError) {
      console.error('❌ Error fetching configs:', configsError);
    } else {
      console.log(`✅ Found ${configs.length} assistant configs:`);
      configs.forEach((config, index) => {
        console.log(`Config ${index + 1}:`, {
          assistant_id: config.assistant_id,
          assistant_name: config.assistant_name,
          firm_name: config.firm_name,
          created_at: config.created_at
        });
      });
    }

    // 3. Check assistant_subdomains table
    console.log('\n🌐 3. ASSISTANT_SUBDOMAINS TABLE');
    const { data: subdomains, error: subdomainsError } = await supabase
      .from('assistant_subdomains')
      .select('*')
      .eq('attorney_id', attorney.id);

    if (subdomainsError) {
      console.error('❌ Error fetching subdomains:', subdomainsError);
    } else {
      console.log(`✅ Found ${subdomains.length} subdomain mappings:`);
      subdomains.forEach((subdomain, index) => {
        console.log(`Subdomain ${index + 1}:`, {
          assistant_id: subdomain.assistant_id,
          subdomain: subdomain.subdomain,
          is_primary: subdomain.is_primary,
          is_active: subdomain.is_active
        });
      });
    }

    // 4. Check for the problematic UUID
    const problematicId = '87756a2c-a398-43f2-889a-b8815684df71';
    console.log(`\n🚨 4. CHECKING FOR PROBLEMATIC UUID: ${problematicId}`);
    
    // Check in assistant_ui_configs
    const { data: problematicConfigs, error: problematicConfigsError } = await supabase
      .from('assistant_ui_configs')
      .select('*')
      .eq('assistant_id', problematicId);

    if (!problematicConfigsError && problematicConfigs.length > 0) {
      console.log(`❌ Found ${problematicConfigs.length} configs with problematic UUID:`);
      problematicConfigs.forEach(config => {
        console.log('Problematic config:', {
          attorney_id: config.attorney_id,
          assistant_name: config.assistant_name,
          created_at: config.created_at
        });
      });
    } else {
      console.log('✅ No configs found with problematic UUID');
    }

    // Check in assistant_subdomains
    const { data: problematicSubdomains, error: problematicSubdomainsError } = await supabase
      .from('assistant_subdomains')
      .select('*')
      .eq('assistant_id', problematicId);

    if (!problematicSubdomainsError && problematicSubdomains.length > 0) {
      console.log(`❌ Found ${problematicSubdomains.length} subdomains with problematic UUID:`);
      problematicSubdomains.forEach(subdomain => {
        console.log('Problematic subdomain:', {
          attorney_id: subdomain.attorney_id,
          subdomain: subdomain.subdomain,
          created_at: subdomain.created_at
        });
      });
    } else {
      console.log('✅ No subdomains found with problematic UUID');
    }

    // 5. Analysis and recommendations
    console.log('\n📊 5. ANALYSIS');
    
    const validVapiIds = [];
    const invalidIds = [];
    
    // Analyze all assistant IDs found
    const allIds = [
      attorneyRecord?.vapi_assistant_id,
      attorneyRecord?.current_assistant_id,
      ...configs.map(c => c.assistant_id),
      ...subdomains.map(s => s.assistant_id)
    ].filter(Boolean);
    
    const uniqueIds = [...new Set(allIds)];
    
    uniqueIds.forEach(id => {
      if (id.length > 40 || id.includes('-')) {
        invalidIds.push(id);
      } else {
        validVapiIds.push(id);
      }
    });
    
    console.log('Valid Vapi IDs found:', validVapiIds);
    console.log('Invalid IDs (UUIDs) found:', invalidIds);
    
    // 6. Generate fix commands
    console.log('\n🔧 6. FIX COMMANDS');
    
    if (invalidIds.includes(problematicId)) {
      console.log('❌ CRITICAL: The problematic UUID is still in your database!');
      console.log('🔧 To fix this, run these commands:');
      
      console.log('\n-- Delete problematic configs:');
      console.log(`DELETE FROM assistant_ui_configs WHERE assistant_id = '${problematicId}';`);
      
      console.log('\n-- Delete problematic subdomains:');
      console.log(`DELETE FROM assistant_subdomains WHERE assistant_id = '${problematicId}';`);
      
      console.log('\n-- Update attorney record if needed:');
      if (attorneyRecord?.vapi_assistant_id === problematicId) {
        console.log(`UPDATE attorneys SET vapi_assistant_id = NULL WHERE id = '${attorney.id}';`);
      }
      if (attorneyRecord?.current_assistant_id === problematicId) {
        console.log(`UPDATE attorneys SET current_assistant_id = NULL WHERE id = '${attorney.id}';`);
      }
    }
    
    if (validVapiIds.length > 0) {
      console.log('\n✅ You have valid Vapi IDs that can be used:');
      validVapiIds.forEach(id => {
        console.log(`- ${id}`);
      });
    }

  } catch (error) {
    console.error('❌ Debug script error:', error);
  }
}

// Auto-run the debug script
debugDatabaseAssistantIds();

// Make available globally
window.debugDatabaseAssistantIds = debugDatabaseAssistantIds;
