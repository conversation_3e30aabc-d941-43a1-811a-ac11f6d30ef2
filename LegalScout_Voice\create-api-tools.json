{"live_dossier_api": {"type": "apiRequest", "name": "live_dossier_api", "description": "Real-time case information display that updates during conversations with direct API integration", "method": "POST", "url": "https://dashboard.legalscout.net/api/dossier-update", "headers": {"Content-Type": "application/json", "X-LegalScout-Tool": "LIVE_DOSSIER_API", "X-Assistant-ID": "{{assistant.id}}", "X-Call-ID": "{{call.id}}"}, "body": {"assistantId": "{{assistant.id}}", "callId": "{{call.id}}", "timestamp": "{{timestamp}}", "dossier": {"status": "{{parameters.STATUS}}", "jurisdiction": "{{parameters.JURISDICTION}}", "clientBackground": "{{parameters.CLIENT_BACKGROUND}}", "legalIssues": "{{parameters.LEGAL_ISSUES}}", "statementOfFacts": "{{parameters.STATEMENT_OF_FACTS}}", "objectives": "{{parameters.OBJECTIVES}}"}, "conversation": {"userMessage": "{{parameters.user_message}}", "scoutMessage": "{{parameters.Scout_message}}", "userLocation": "{{parameters.User_Location}}"}}, "parameters": {"type": "object", "properties": {"STATUS": {"type": "string", "description": "Current status of consultation"}, "JURISDICTION": {"type": "object", "properties": {"address": {"type": "string"}, "lat": {"type": "number"}, "lng": {"type": "number"}}, "description": "Location of user's legal matter down to zip in lat,long"}, "CLIENT_BACKGROUND": {"type": "string", "description": "Client background information"}, "LEGAL_ISSUES": {"type": "string", "description": "Legal issues identified"}, "STATEMENT_OF_FACTS": {"type": "string", "description": "Facts of the case"}, "OBJECTIVES": {"type": "string", "description": "User objectives"}, "user_message": {"type": "string", "description": "Latest user message"}, "Scout_message": {"type": "string", "description": "Latest Scout response"}, "User_Location": {"type": "array", "description": "User location coordinates"}}, "required": ["STATUS"]}, "timeoutSeconds": 10, "messages": [{"type": "request-start", "content": "Let me update your case information..."}, {"type": "request-complete", "content": "I've updated your dossier with the latest information."}, {"type": "request-failed", "content": "I had trouble updating your information, but I'll continue helping you."}]}, "attorney_context_api": {"type": "apiRequest", "name": "attorney_context_api", "description": "Get attorney-specific information and perform attorney-related actions with direct API integration", "method": "POST", "url": "https://dashboard.legalscout.net/api/attorney-context", "headers": {"Content-Type": "application/json", "X-LegalScout-Tool": "ATTORNEY_CONTEXT_API", "X-Assistant-ID": "{{assistant.id}}", "X-Call-ID": "{{call.id}}"}, "body": {"assistantId": "{{assistant.id}}", "callId": "{{call.id}}", "timestamp": "{{timestamp}}", "action": "{{parameters.action}}", "clientInfo": {"name": "{{parameters.client_name}}", "email": "{{parameters.client_email}}", "phone": "{{parameters.client_phone}}"}, "caseInfo": {"legalIssue": "{{parameters.legal_issue}}", "urgency": "{{parameters.urgency}}", "details": "{{parameters.additional_details}}"}}, "parameters": {"type": "object", "properties": {"action": {"type": "string", "enum": ["get_attorney_info", "get_practice_areas", "get_contact_info", "get_office_hours", "schedule_consultation", "create_case_file", "transfer_to_attorney"], "description": "The action to perform"}, "client_name": {"type": "string", "description": "<PERSON><PERSON>'s name"}, "client_email": {"type": "string", "description": "Client's email"}, "client_phone": {"type": "string", "description": "C<PERSON>'s phone"}, "legal_issue": {"type": "string", "description": "Description of legal issue"}, "urgency": {"type": "string", "enum": ["low", "medium", "high", "emergency"], "description": "Urgency level"}, "additional_details": {"type": "string", "description": "Additional details"}}, "required": ["action"]}, "timeoutSeconds": 15, "messages": [{"type": "request-start", "content": "Let me get that information for you..."}, {"type": "request-complete", "content": "Here's the information you requested."}, {"type": "request-failed", "content": "I'm having trouble accessing that information right now."}]}}