import React, { useState, useRef, useEffect, useCallback } from 'react';
import { debounce } from 'lodash';
import { supabase } from '../../lib/supabase';
import {
  FaCheck,
  FaExclamationTriangle,
  FaPalette,
  FaMicrophone,
  FaUpload,
  FaVolumeUp,
  FaCode,
  FaCommentAlt,
  FaFileUpload,
  FaRobot,
  FaBug,
  FaTools,
  FaSync,
  FaExpand,
  FaMobileAlt
} from 'react-icons/fa';
import './AgentTab.css';
import { useAuth } from '../../contexts/AuthContext';
import { useAssistantAware } from '../../contexts/AssistantAwareContext';
import { filterUpdateData } from '../../utils/attorneyUtils';
import VapiDebugger from '../../components/VapiDebugger';
import VoiceAssistantConfig from './VoiceAssistantConfig';
import VoiceAssistantDiagnostics from './VoiceAssistantDiagnostics';
import AssistantInfoSection from './AssistantInfoSection';
import PracticeAreaSelector from '../common/PracticeAreaSelector';
import { AssistantConfigService } from '../../services/assistantConfigService';
import { serviceResolvers } from '../../utils/moduleResolver';
import SubdomainEditor from './SubdomainEditor';
import { assistantUIConfigService } from '../../services/assistantUIConfigService';

import { syncAttorneyProfile } from '../../services/EnhancedSyncTools';
import { getPracticeAreaTemplate } from '../../utils/schemaGenerator';
import { getTemplateByPracticeArea } from '../../config/defaultTemplates';

// Note: We're now importing filterUpdateData from attorneyUtils.js

// Function to generate a valid UUID v4
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

// Note: filterUpdateData function is already defined above

/**
 * AgentTab component for the attorney dashboard
 * Allows attorneys to customize their AI assistant
 */
const AgentTab = ({ attorney, onUpdate, previewConfig }) => {
  // Debug logging removed for production

  // Get user from auth context and also try to get attorney from auth
  const { user, attorney: authAttorney } = useAuth();

  // Get current assistant context for assistant-specific configuration
  const { currentAssistant, isAssistantSelected } = useAssistantAware();

  // Use attorney prop first, then auth attorney, then try localStorage
  const effectiveAttorney = attorney || authAttorney || (() => {
    try {
      const stored = localStorage.getItem('attorney');
      return stored ? JSON.parse(stored) : null;
    } catch {
      return null;
    }
  })();

  // Debug logging removed for production
  // State for form data
  const [formData, setFormData] = useState(() => {
    // Ensure effectiveAttorney is not null before accessing properties
    const safeAttorney = effectiveAttorney || {};

    // Get the voice ID from various sources (default to OpenAI Echo male voice)
    const voiceId = previewConfig.voiceId || safeAttorney.voice_id || 'echo';

    // Find the voice preset to get the correct provider
    const voicePresets = [
      // 11labs voices (most reliable)
      { id: 'sarah', name: 'Sarah (Female)', gender: 'female', provider: '11labs' },
      { id: 'adam', name: 'Adam (Male)', gender: 'male', provider: '11labs' },
      { id: 'daniel', name: 'Daniel (Male)', gender: 'male', provider: '11labs' },
      { id: 'josh', name: 'Josh (Male)', gender: 'male', provider: '11labs' },
      { id: 'rachel', name: 'Rachel (Female)', gender: 'female', provider: '11labs' },
      { id: 'antoni', name: 'Antoni (Male)', gender: 'male', provider: '11labs' },
      { id: 'thomas', name: 'Thomas (Male)', gender: 'male', provider: '11labs' },
      { id: 'charlie', name: 'Charlie (Male)', gender: 'male', provider: '11labs' },
      { id: 'emily', name: 'Emily (Female)', gender: 'female', provider: '11labs' },
      { id: 'callum', name: 'Callum (Male)', gender: 'male', provider: '11labs' },
      { id: 'patrick', name: 'Patrick (Male)', gender: 'male', provider: '11labs' },
      { id: 'harry', name: 'Harry (Male)', gender: 'male', provider: '11labs' },
      { id: 'liam', name: 'Liam (Male)', gender: 'male', provider: '11labs' },
      { id: 'charlotte', name: 'Charlotte (Female)', gender: 'female', provider: '11labs' },
      { id: 'matthew', name: 'Matthew (Male)', gender: 'male', provider: '11labs' },
      { id: 'james', name: 'James (Male)', gender: 'male', provider: '11labs' },

      // OpenAI voices (reliable)
      { id: 'alloy', name: 'Alloy (Neutral)', gender: 'neutral', provider: 'openai' },
      { id: 'echo', name: 'Echo (Male)', gender: 'male', provider: 'openai' },
      { id: 'fable', name: 'Fable (Female)', gender: 'female', provider: 'openai' },
      { id: 'onyx', name: 'Onyx (Male)', gender: 'male', provider: 'openai' },
      { id: 'nova', name: 'Nova (Female)', gender: 'female', provider: 'openai' },
      { id: 'shimmer', name: 'Shimmer (Female)', gender: 'female', provider: 'openai' }
    ];

    // Find the correct provider for the voice ID
    const selectedVoice = voicePresets.find(voice => voice.id === voiceId);
    const voiceProvider = previewConfig.voiceProvider || safeAttorney.voice_provider || selectedVoice?.provider || 'openai';

    // If there's a mismatch between voice and provider, fix it
    let finalVoiceId = voiceId;
    let finalVoiceProvider = voiceProvider;

    if (selectedVoice && selectedVoice.provider !== voiceProvider) {
      // Voice/provider mismatch detected, use the voice's correct provider
      finalVoiceProvider = selectedVoice.provider;
      // Voice/provider mismatch fixed
    } else if (!selectedVoice && voiceProvider === 'playht') {
      // Voice not found and trying to use PlayHT, switch to a valid PlayHT voice
      finalVoiceId = 's3://voice-cloning-zero-shot/d4ff0efe-21ce-4720-8c5e-3b4719b79457/waylon/manifest.json';
      finalVoiceProvider = 'playht';
      // Voice not found for PlayHT, switching to Waylon
    } else if (!selectedVoice) {
      // Voice not found, use default Azure voice (reliable)
      finalVoiceId = 'andrew';
      finalVoiceProvider = 'azure';
    }

    return {
      welcomeMessage: previewConfig.welcomeMessage || safeAttorney.welcome_message || "",
      firstMessage: previewConfig.firstMessage || safeAttorney.first_message || safeAttorney.information_gathering || "",
      vapiInstructions: previewConfig.vapiInstructions || safeAttorney.vapi_instructions || "",
      vapiContext: previewConfig.vapiContext || safeAttorney.vapi_context || "",
      primaryColor: previewConfig.primaryColor || safeAttorney.primary_color || '#4B74AA',
      secondaryColor: previewConfig.secondaryColor || safeAttorney.secondary_color || '#2C3E50',
      backgroundColor: previewConfig.backgroundColor || safeAttorney.background_color || '#1a1a1a',
      backgroundOpacity: previewConfig.backgroundOpacity || safeAttorney.background_opacity || 0.9,
      buttonText: previewConfig.buttonText || safeAttorney.button_text || 'Start Consultation',
      buttonOpacity: previewConfig.buttonOpacity || safeAttorney.button_opacity || 1,
      practiceAreaBackgroundOpacity: previewConfig.practiceAreaBackgroundOpacity || safeAttorney.practice_area_background_opacity || 0.1,
      textBackgroundColor: previewConfig.textBackgroundColor || safeAttorney.text_background_color || '#634C38',
      theme: previewConfig.theme || safeAttorney.theme || 'dark',
      titleText: previewConfig.titleText || safeAttorney.title_text || safeAttorney.firm_name || '',
      buttonColor: previewConfig.buttonColor || safeAttorney.button_color || '#D85722',
      voiceId: finalVoiceId,
      voiceProvider: finalVoiceProvider,
      practiceDescription: previewConfig.practiceDescription || safeAttorney.practice_description || '',
      logoUrl: previewConfig.logoUrl || safeAttorney.logo_url || safeAttorney.profile_image || safeAttorney.button_image || null
    };
  });

  // State for voice cloning and knowledge files
  const [voiceFile, setVoiceFile] = useState(null);
  const [knowledgeFile, setKnowledgeFile] = useState(null);
  const [uploadingKnowledge, setUploadingKnowledge] = useState(false);
  const [voiceCloning, setVoiceCloning] = useState(false);
  // All available voices (including ones that might not work)
  const allVoicePresets = [
    // 11labs voices
    { id: 'sarah', name: 'Sarah (Female)', gender: 'female', provider: '11labs' },
    { id: 'adam', name: 'Adam (Male)', gender: 'male', provider: '11labs' },
    { id: 'daniel', name: 'Daniel (Male)', gender: 'male', provider: '11labs' },
    { id: 'josh', name: 'Josh (Male)', gender: 'male', provider: '11labs' },
    { id: 'rachel', name: 'Rachel (Female)', gender: 'female', provider: '11labs' },
    { id: 'domi', name: 'Domi (Female)', gender: 'female', provider: '11labs' },
    { id: 'freya', name: 'Freya (Female)', gender: 'female', provider: '11labs' },
    { id: 'antoni', name: 'Antoni (Male)', gender: 'male', provider: '11labs' },
    { id: 'thomas', name: 'Thomas (Male)', gender: 'male', provider: '11labs' },
    { id: 'charlie', name: 'Charlie (Male)', gender: 'male', provider: '11labs' },
    { id: 'emily', name: 'Emily (Female)', gender: 'female', provider: '11labs' },
    { id: 'elli', name: 'Elli (Female)', gender: 'female', provider: '11labs' },
    { id: 'callum', name: 'Callum (Male)', gender: 'male', provider: '11labs' },
    { id: 'patrick', name: 'Patrick (Male)', gender: 'male', provider: '11labs' },
    { id: 'harry', name: 'Harry (Male)', gender: 'male', provider: '11labs' },
    { id: 'liam', name: 'Liam (Male)', gender: 'male', provider: '11labs' },
    { id: 'dorothy', name: 'Dorothy (Female)', gender: 'female', provider: '11labs' },
    { id: 'arnold', name: 'Arnold (Male)', gender: 'male', provider: '11labs' },
    { id: 'charlotte', name: 'Charlotte (Female)', gender: 'female', provider: '11labs' },
    { id: 'matilda', name: 'Matilda (Female)', gender: 'female', provider: '11labs' },
    { id: 'matthew', name: 'Matthew (Male)', gender: 'male', provider: '11labs' },
    { id: 'james', name: 'James (Male)', gender: 'male', provider: '11labs' },
    { id: 'joseph', name: 'Joseph (Male)', gender: 'male', provider: '11labs' },
    { id: 'jeremy', name: 'Jeremy (Male)', gender: 'male', provider: '11labs' },
    { id: 'michael', name: 'Michael (Male)', gender: 'male', provider: '11labs' },
    { id: 'ethan', name: 'Ethan (Male)', gender: 'male', provider: '11labs' },

    // OpenAI voices
    { id: 'alloy', name: 'Alloy (Neutral)', gender: 'neutral', provider: 'openai' },
    { id: 'echo', name: 'Echo (Male)', gender: 'male', provider: 'openai' },
    { id: 'fable', name: 'Fable (Female)', gender: 'female', provider: 'openai' },
    { id: 'onyx', name: 'Onyx (Male)', gender: 'male', provider: 'openai' },
    { id: 'nova', name: 'Nova (Female)', gender: 'female', provider: 'openai' },
    { id: 'shimmer', name: 'Shimmer (Female)', gender: 'female', provider: 'openai' },

    // Azure voices
    { id: 'andrew', name: 'Andrew (Male)', gender: 'male', provider: 'azure' },
    { id: 'emma', name: 'Emma (Female)', gender: 'female', provider: 'azure' },

    // PlayHT voices
    { id: 'waylon', name: 'Waylon (Male)', gender: 'male', provider: 'playht' },
    { id: 'leyro', name: 'Leyro (Male)', gender: 'male', provider: 'playht' },
    { id: 'nova-playht', name: 'Nova PlayHT (Female)', gender: 'female', provider: 'playht' },
    { id: 'stella', name: 'Stella (Female)', gender: 'female', provider: 'playht' },
    { id: 'cody', name: 'Cody (Male)', gender: 'male', provider: 'playht' },
    { id: 'maya', name: 'Maya (Female)', gender: 'female', provider: 'playht' }
  ];

  // Hidden voices (marked as not working by users)
  const [hiddenVoices, setHiddenVoices] = useState(() => {
    try {
      const stored = localStorage.getItem('hiddenVoices');
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  });

  // Filtered voice presets (excluding hidden ones)
  const voicePresets = allVoicePresets.filter(voice => !hiddenVoices.includes(voice.id));

  // Function to hide a voice
  const hideVoice = (voiceId) => {
    const newHiddenVoices = [...hiddenVoices, voiceId];
    setHiddenVoices(newHiddenVoices);
    localStorage.setItem('hiddenVoices', JSON.stringify(newHiddenVoices));

    // If the currently selected voice is being hidden, switch to a default
    if (formData.voiceId === voiceId) {
      const defaultVoice = voicePresets.find(v => v.id === 'sarah') || voicePresets[0];
      if (defaultVoice) {
        setFormData(prev => ({
          ...prev,
          voiceId: defaultVoice.id,
          voiceProvider: defaultVoice.provider
        }));
      }
    }
  };

  // Function to show all hidden voices (reset)
  const showAllVoices = () => {
    setHiddenVoices([]);
    localStorage.removeItem('hiddenVoices');
  };
  const voiceFileInputRef = useRef(null);
  const knowledgeFileInputRef = useRef(null);

  // State for UI
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);
  const [activeSection, setActiveSection] = useState('instructions');
  const [showDiagnostics, setShowDiagnostics] = useState(false);
  const [currentAssistantSubdomain, setCurrentAssistantSubdomain] = useState(null);

  // State for practice area selector
  const [selectedPracticeArea, setSelectedPracticeArea] = useState((effectiveAttorney || {}).practice_area || '');

  // State for sync status
  const [syncStatus, setSyncStatus] = useState('idle');

  /**
   * Load current assistant's subdomain
   */
  const loadCurrentAssistantSubdomain = async (assistantId) => {
    if (!assistantId || !effectiveAttorney?.id) return;

    try {
      const { supabase } = await import('../../lib/supabase');

      const { data, error } = await supabase
        .from('assistant_subdomains')
        .select('subdomain')
        .eq('assistant_id', assistantId)
        .eq('is_active', true)
        .single();

      if (!error && data) {
        setCurrentAssistantSubdomain(data.subdomain);
        console.log('🌐 Loaded assistant subdomain:', data.subdomain);
      } else {
        setCurrentAssistantSubdomain(null);
        console.log('📭 No subdomain found for assistant:', assistantId);
      }
    } catch (error) {
      console.error('Error loading assistant subdomain:', error);
      setCurrentAssistantSubdomain(null);
    }
  };

  // Load assistant subdomain when assistant changes
  useEffect(() => {
    const assistantId = effectiveAttorney?.current_assistant_id || effectiveAttorney?.vapi_assistant_id;
    if (assistantId) {
      loadCurrentAssistantSubdomain(assistantId);
    }
  }, [effectiveAttorney?.current_assistant_id, effectiveAttorney?.vapi_assistant_id]);
  const [isDarkTheme, setIsDarkTheme] = useState(formData.theme === 'dark');
  const [logoFile, setLogoFile] = useState(null);
  const [logoPreview, setLogoPreview] = useState(previewConfig.logoUrl || null);

  // State for current assistant configuration
  const [currentAssistantConfig, setCurrentAssistantConfig] = useState(null);

  // Load assistant-specific configuration when assistant changes
  useEffect(() => {
    const loadAssistantConfig = async () => {
      if (!currentAssistant?.id || !effectiveAttorney?.id) {
        console.log('[AgentTab] No assistant or attorney selected, using defaults');
        return;
      }

      try {
        console.log('[AgentTab] Loading assistant-specific configuration for:', currentAssistant.id);

        // Clear existing form data first to prevent stale data
        setFormData(prev => ({
          ...prev,
          titleText: '',
          primaryColor: '#2563eb',
          secondaryColor: '#1e40af',
          buttonColor: '#3b82f6'
        }));

        // Load assistant configuration
        const config = await AssistantConfigService.loadAssistantConfig(
          currentAssistant.id,
          effectiveAttorney.id
        );

        console.log('[AgentTab] Loaded assistant configuration:', config);

        if (config) {
          // Update form data with assistant-specific configuration
          setFormData(prev => ({
            ...prev,
            // Branding configuration
            titleText: config.branding?.titleText || config.branding?.firmName || prev.titleText,
            primaryColor: config.branding?.primaryColor || prev.primaryColor,
            secondaryColor: config.branding?.secondaryColor || prev.secondaryColor,
            buttonColor: config.branding?.buttonColor || prev.buttonColor,
            backgroundColor: config.branding?.backgroundColor || prev.backgroundColor,
            backgroundOpacity: config.branding?.backgroundOpacity || prev.backgroundOpacity,
            buttonOpacity: config.branding?.buttonOpacity || prev.buttonOpacity,
            practiceAreaBackgroundOpacity: config.branding?.practiceAreaBackgroundOpacity || prev.practiceAreaBackgroundOpacity,
            textBackgroundColor: config.branding?.textBackgroundColor || prev.textBackgroundColor,
            logoUrl: config.branding?.logoUrl || prev.logoUrl,
            practiceDescription: config.branding?.practiceDescription || prev.practiceDescription,

            // Messaging configuration
            welcomeMessage: config.messaging?.welcomeMessage || prev.welcomeMessage,
            firstMessage: config.messaging?.informationGathering || prev.firstMessage,
            vapiInstructions: config.messaging?.vapiInstructions || prev.vapiInstructions,
            vapiContext: config.messaging?.vapiContext || prev.vapiContext,

            // Voice configuration
            voiceId: config.voice?.voiceId || prev.voiceId,
            voiceProvider: config.voice?.voiceProvider || prev.voiceProvider
          }));

          // Update logo preview
          if (config.branding?.logoUrl) {
            setLogoPreview(config.branding.logoUrl);
          }

          // Update preview config
          onUpdate({
            ...config.branding,
            ...config.messaging,
            ...config.voice
          });
        }

      } catch (error) {
        console.error('[AgentTab] Error loading assistant configuration:', error);
      }
    };

    loadAssistantConfig();
  }, [currentAssistant?.id, effectiveAttorney?.id]);

  // Debounced save function for assistant-level changes
  const debouncedSaveAssistantConfig = useCallback(
    debounce(async (fieldName, fieldValue) => {
      if (!currentAssistant?.id || !effectiveAttorney?.id) return;

      try {
        console.log(`[AgentTab] Auto-saving ${fieldName} to assistant config:`, fieldValue);

        // Update the specific field using the new service
        await AssistantConfigService.updateAssistantConfig(currentAssistant.id, effectiveAttorney.id, {
          [fieldName]: fieldValue
        });

        console.log(`[AgentTab] ✅ Auto-saved ${fieldName} to assistant config`);
      } catch (error) {
        console.error(`[AgentTab] Error auto-saving ${fieldName}:`, error);
      }
    }, 1000), // 1 second debounce
    [currentAssistant?.id, effectiveAttorney?.id]
  );

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Update preview config for real-time preview
    onUpdate({ [name]: value });

    // Auto-save to assistant-level configuration
    debouncedSaveAssistantConfig(name, value);

    // If titleText is updated, sync with Vapi assistant name
    if (name === 'titleText') {
      console.log('[AgentTab] titleText changed, triggering Vapi sync:', value);
      syncAssistantNameToVapi(value);
    }
  };

  // Handle range input change
  const handleRangeChange = (e) => {
    const { name, value } = e.target;
    const numValue = parseFloat(value);
    setFormData(prev => ({ ...prev, [name]: numValue }));

    // Update preview config for real-time preview
    onUpdate({ [name]: numValue });

    // Auto-save to assistant-level configuration
    debouncedSaveAssistantConfig(name, numValue);
  };

  // Handle theme change
  const handleThemeChange = (theme) => {
    setFormData(prev => ({ ...prev, theme }));
    onUpdate({ theme });
    setIsDarkTheme(theme === 'dark');
  };

  // Sync assistant name to Vapi (debounced to avoid too many API calls)
  const syncAssistantNameToVapi = async (assistantName) => {
    console.log('[AgentTab] syncAssistantNameToVapi called with:', {
      assistantName,
      currentAssistantId: currentAssistant?.id,
      vapiAssistantId: effectiveAttorney?.vapi_assistant_id,
      hasEffectiveAttorney: !!effectiveAttorney,
      hasCurrentAssistant: !!currentAssistant
    });

    // Use current assistant ID from context, fallback to attorney's vapi_assistant_id
    const assistantIdToUpdate = currentAssistant?.id || effectiveAttorney?.vapi_assistant_id;

    if (!assistantName || !assistantIdToUpdate) {
      console.warn('[AgentTab] Skipping assistant name sync - missing data:', {
        assistantName: !!assistantName,
        assistantIdToUpdate: !!assistantIdToUpdate
      });
      return;
    }

    try {
      console.log('[AgentTab] Setting up debounced sync for assistant name:', assistantName);

      // Debounce the API call to avoid too many requests
      if (window.assistantNameSyncTimeout) {
        clearTimeout(window.assistantNameSyncTimeout);
        console.log('[AgentTab] Cleared previous sync timeout');
      }

      window.assistantNameSyncTimeout = setTimeout(async () => {
        try {
          console.log('[AgentTab] 🚀 Executing debounced assistant name sync to Vapi:', {
            assistantName,
            assistantId: effectiveAttorney.vapi_assistant_id
          });

          // Import the enhanced Vapi MCP service
          const { enhancedVapiMcpService } = await import('../../services/EnhancedVapiMcpService');

          // Initialize with API key
          const vapiApiKey = import.meta.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
          console.log('[AgentTab] Connecting to Vapi with API key:', vapiApiKey.substring(0, 8) + '...');

          const connected = await enhancedVapiMcpService.connect(vapiApiKey);
          if (!connected) {
            throw new Error('Failed to connect to Vapi MCP service');
          }
          console.log('[AgentTab] ✅ Connected to Vapi MCP service');

          // Update the assistant name in Vapi using the correct assistant ID
          console.log('[AgentTab] Updating assistant name in Vapi for assistant:', assistantIdToUpdate);
          const updateResult = await enhancedVapiMcpService.updateAssistant(
            assistantIdToUpdate,
            {
              name: assistantName
            }
          );

          if (updateResult) {
            console.log('[AgentTab] ✅ Assistant name synced to Vapi successfully:', {
              assistantName,
              updateResult
            });
          } else {
            console.warn('[AgentTab] ⚠️ Assistant name sync returned no result');
          }
        } catch (error) {
          console.error('[AgentTab] ❌ Error syncing assistant name to Vapi:', error);
        }
      }, 1000); // 1 second debounce

    } catch (error) {
      console.error('[AgentTab] Error setting up assistant name sync:', error);
    }
  };

  // Handle logo file selection
  const handleLogoChange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      console.error('[AgentTab] ❌ File too large. Maximum size is 2MB.');
      return;
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      console.error('[AgentTab] ❌ Invalid file type. Please select an image.');
      return;
    }

    try {
      // Show loading state
      setLogoPreview('loading');

      // Upload to Supabase Storage instead of converting to base64
      if (currentAssistantConfig && effectiveAttorney?.id) {
        console.log('[AgentTab] 📤 Uploading banner image to storage...');
        const imageUrl = await assistantUIConfigService.uploadImage(file, currentAssistantConfig.assistant_id, 'logo');

        // Update preview with the uploaded image URL
        setLogoPreview(imageUrl);

        // Update form data with the storage URL
        setFormData(prev => ({
          ...prev,
          logoUrl: imageUrl
        }));

        // Update the preview immediately
        onUpdate({ logoUrl: imageUrl, mascot: imageUrl, assistant_image_url: imageUrl });

        // Save to assistant config with storage URL
        console.log('[AgentTab] 💾 Saving banner image URL to assistant config...');
        await assistantUIConfigService.saveAssistantConfig(
          effectiveAttorney.id,
          currentAssistantConfig.assistant_id,
          {
            ...currentAssistantConfig,
            logo_url: imageUrl,  // Save as banner image
            assistant_image_url: imageUrl  // Also save as assistant image for dropdown
          }
        );
        console.log('[AgentTab] ✅ Banner image saved successfully');

        // Trigger assistant dropdown refresh to show new image
        window.dispatchEvent(new CustomEvent('assistantImageUpdated', {
          detail: {
            assistantId: currentAssistantConfig.assistant_id,
            imageUrl: imageUrl
          }
        }));
      }
    } catch (error) {
      console.error('[AgentTab] ❌ Error uploading banner image:', error);
      setLogoPreview(null);
    }

    setLogoFile(file);
  };

  // Remove logo
  const handleRemoveLogo = async () => {
    try {
      // Delete from storage if it's a storage URL
      if (logoPreview && currentAssistantConfig) {
        console.log('[AgentTab] 🗑️ Deleting banner image from storage...');
        await assistantUIConfigService.deleteImage(logoPreview);
      }

      setLogoFile(null);
      setLogoPreview(null);

      // Update form data to remove logo
      setFormData(prev => ({
        ...prev,
        logoUrl: ''
      }));

      // Update the preview immediately (UI-only change)
      onUpdate({ logoUrl: '', mascot: '', assistant_image_url: '' });

      // Remove the banner image from the assistant UI config immediately
      if (currentAssistantConfig && effectiveAttorney?.id) {
        await assistantUIConfigService.saveAssistantConfig(
          effectiveAttorney.id,
          currentAssistantConfig.assistant_id,
          {
            ...currentAssistantConfig,
            logo_url: '',  // Remove banner image
            assistant_image_url: ''  // Also remove assistant image for dropdown
          }
        );
        console.log('[AgentTab] ✅ Banner image removed from assistant UI config');

        // Trigger assistant dropdown refresh to show removed image
        window.dispatchEvent(new CustomEvent('assistantImageUpdated', {
          detail: {
            assistantId: currentAssistantConfig.assistant_id,
            imageUrl: null
          }
        }));
      }
    } catch (error) {
      console.error('[AgentTab] ❌ Error removing banner image:', error);
    }
  };

  // Save to Supabase function
  const handleSaveToSupabase = async (updateData) => {
    try {
      const currentAttorney = effectiveAttorney || JSON.parse(localStorage.getItem('attorney') || '{}');

      if (!currentAttorney?.id) {
        // No attorney ID found, skipping Supabase update
        return;
      }

      const { error } = await supabase
        .from('attorneys')
        .update(updateData)
        .eq('id', currentAttorney.id);

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('[AgentTab] Exception during Supabase update:', error);
      throw error;
    }
  };

  // Handle practice area selection
  const handlePracticeAreaChange = (practiceArea) => {
    setSelectedPracticeArea(practiceArea);

    // Update the attorney's practice area
    onUpdate({ practice_area: practiceArea });

    // Save to Supabase immediately
    if (effectiveAttorney?.id) {
      handleSaveToSupabase({ practice_area: practiceArea });
    }
  };

  // Apply practice area template
  const handleApplyTemplate = async (templateName) => {
    if (!templateName) return;

    try {
      setLoading(true);

      // Get template from both sources to ensure we have all fields
      const schemaTemplate = getPracticeAreaTemplate(templateName);
      const crmTemplate = getTemplateByPracticeArea(templateName);

      if (schemaTemplate || crmTemplate) {
        // Prepare updates for both form data and attorney profile
        const updates = {};

        // Set prompts from both templates
        if (crmTemplate?.summary_prompt || schemaTemplate?.summary_prompt) {
          updates.summary_prompt = crmTemplate?.summary_prompt || schemaTemplate?.summary_prompt;
        }

        if (crmTemplate?.structured_data_prompt || schemaTemplate?.structured_data_prompt) {
          updates.structured_data_prompt = crmTemplate?.structured_data_prompt || schemaTemplate?.structured_data_prompt;
        }

        if (crmTemplate?.success_prompt) {
          updates.success_evaluation_prompt = crmTemplate.success_prompt;
        }

        // Set structured data schema
        if (crmTemplate?.structured_data_schema) {
          updates.structured_data_schema = crmTemplate.structured_data_schema;
        }

        // Set custom fields from schema template
        if (schemaTemplate?.custom_fields) {
          updates.custom_fields = schemaTemplate.custom_fields;
        }

        // Update the attorney profile in Supabase
        if (effectiveAttorney?.id && Object.keys(updates).length > 0) {
          await handleSaveToSupabase(updates);
        }

        // Update preview config
        onUpdate(updates);

        setSuccess(true);
        setTimeout(() => setSuccess(false), 2000);
      }
    } catch (error) {
      console.error('Error applying practice area template:', error);
      setError('Failed to apply template. Please try again.');
      setTimeout(() => setError(null), 3000);
    } finally {
      setLoading(false);
    }
  };

  // Handle sync with enhanced components
  const handleSync = async () => {
    if (!effectiveAttorney || !effectiveAttorney.id) {
      setError('No attorney ID available for synchronization');
      return;
    }

    try {
      setSyncStatus('syncing');

      const syncResult = await syncAttorneyProfile({
        attorneyId: effectiveAttorney.id,
        forceUpdate: true
      });

      // Sync completed

      if (syncResult.success) {
        setSyncStatus('success');
        setSuccess(true);
        setTimeout(() => setSuccess(false), 1500);

        // If a new assistant was created, update the UI
        if (syncResult.action === 'created' && syncResult.assistantId) {
          // Update the attorney object in localStorage
          const storedAttorney = localStorage.getItem('attorney');
          if (storedAttorney) {
            const parsedAttorney = JSON.parse(storedAttorney);
            const updatedAttorney = {
              ...parsedAttorney,
              vapi_assistant_id: syncResult.assistantId
            };
            localStorage.setItem('attorney', JSON.stringify(updatedAttorney));
          }

          // Update the UI
          onUpdate({ vapiAssistantId: syncResult.assistantId });
        }
      } else {
        setSyncStatus('error');
        setError(`Sync error: ${syncResult.message}`);
      }
    } catch (error) {
      console.error('[AgentTab] Error syncing attorney profile:', error);
      setSyncStatus('error');
      setError(`Sync error: ${error.message}`);
    }
  };

  // Load ALL settings from Vapi assistant when component mounts (Vapi is source of truth)
  useEffect(() => {
    const loadVapiAssistantSettings = async () => {
      if (!effectiveAttorney?.vapi_assistant_id) {
        console.log('[AgentTab] No Vapi assistant ID found, skipping voice sync');
        return;
      }

      try {
        console.log('[AgentTab] Loading ALL settings from Vapi assistant:', effectiveAttorney.vapi_assistant_id);

        // PRODUCTION FIX: Use module resolver to avoid build conflicts
        const { vapiDirectApiService } = await serviceResolvers.vapiService('vapiDirectApiService');
        const { enhancedVapiMcpService } = await serviceResolvers.vapiService('enhancedVapiMcpService');

        // Initialize MCP service
        const vapiApiKey = import.meta.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
        await enhancedVapiMcpService.connect(vapiApiKey);

        // Get complete assistant data using direct API with MCP fallback
        const assistantData = await vapiDirectApiService.getCompleteAssistantData(
          effectiveAttorney.vapi_assistant_id,
          enhancedVapiMcpService
        );

        if (assistantData) {
          console.log('📋 [AgentTab] Found complete assistant data:', {
            firstMessage: assistantData.firstMessage?.substring(0, 50) + '...',
            hasSystemPrompt: !!assistantData.model?.messages?.[0]?.content,
            systemPromptLength: assistantData.model?.messages?.[0]?.content?.length || 0,
            voice: assistantData.voice,
            source: assistantData.firstMessage ? 'direct-api' : 'mcp'
          });

          // Update form data with ALL settings from Vapi (source of truth)
          setFormData(prev => ({
            ...prev,
            // Voice settings
            voiceId: assistantData.voice?.voiceId || prev.voiceId,
            voiceProvider: assistantData.voice?.provider || prev.voiceProvider,
            // Content settings (from Vapi, not Supabase)
            firstMessage: assistantData.firstMessage || prev.firstMessage,
            vapiInstructions: assistantData.model?.messages?.[0]?.content || prev.vapiInstructions,
            // Assistant name (sync with Vapi)
            titleText: assistantData.name || prev.titleText
          }));

          // Also update the preview config
          onUpdate({
            voiceId: assistantData.voice?.voiceId,
            voiceProvider: assistantData.voice?.provider || '11labs',
            firstMessage: assistantData.firstMessage,
            vapiInstructions: assistantData.model?.messages?.[0]?.content,
            titleText: assistantData.name
          });

          console.log('✅ [AgentTab] ALL settings loaded from Vapi (complete data)');
        } else {
          console.log('⚠️ [AgentTab] No assistant data found in Vapi');
        }
      } catch (error) {
        console.error('[AgentTab] Error loading voice settings from Vapi:', error);
      }
    };

    // Load ALL settings when component mounts or attorney changes
    loadVapiAssistantSettings();
  }, [effectiveAttorney?.vapi_assistant_id]); // Removed onUpdate to prevent infinite loop

  // Initialize form data from attorney prop and load assistant-specific configuration
  useEffect(() => {
    if (effectiveAttorney) {
      // Clear previous assistant state to prevent bleeding
      clearAssistantState();
      // Load new assistant configuration
      loadAssistantConfiguration();
    }
  }, [effectiveAttorney?.id, effectiveAttorney?.current_assistant_id, effectiveAttorney?.vapi_assistant_id]);

  // Clear assistant-specific state to prevent data bleeding between assistants
  const clearAssistantState = () => {
    console.log('[AgentTab] 🧹 Clearing assistant state to prevent data bleeding');

    // Clear logo/image state
    setLogoPreview(null);
    setLogoFile(null);

    // Clear assistant config
    setCurrentAssistantConfig(null);

    // Clear any assistant-specific UI state
    onUpdate({
      logoUrl: '',
      mascot: '',
      assistant_image_url: '',
      // Reset other assistant-specific fields to prevent bleeding
      titleText: '',
      welcomeMessage: '',
      vapiInstructions: '',
      voiceId: 'echo', // Default voice
      voiceProvider: '11labs', // Default provider
      primaryColor: '#2563eb', // Default color
      secondaryColor: '#1e40af',
      buttonColor: '#3b82f6'
    });
  };

  // Load assistant configuration
  const loadAssistantConfiguration = async () => {
    if (!effectiveAttorney?.id) return;

    try {
      // Get current assistant ID
      const currentAssistantId = effectiveAttorney.current_assistant_id || effectiveAttorney.vapi_assistant_id;

      if (currentAssistantId) {
        // Try to load assistant-specific configuration
        const config = await assistantUIConfigService.getAssistantConfig(
          effectiveAttorney.id,
          currentAssistantId
        );

        if (config) {
          console.log('[AgentTab] Loaded assistant-specific configuration:', config);
          setCurrentAssistantConfig(config);

          // Update form data with assistant-specific config
          setFormData(prev => ({
            ...prev,
            welcomeMessage: config.welcome_message || effectiveAttorney?.welcome_message || prev.welcomeMessage,
            informationGathering: config.information_gathering || effectiveAttorney?.information_gathering || prev.informationGathering,
            vapiInstructions: config.vapi_instructions || effectiveAttorney?.vapi_instructions || prev.vapiInstructions,
            vapiContext: config.vapi_context || effectiveAttorney?.vapi_context || prev.vapiContext,
            voiceId: config.voice_id || effectiveAttorney?.voice_id || prev.voiceId,
            voiceProvider: config.voice_provider || effectiveAttorney?.voice_provider || prev.voiceProvider,
            primaryColor: config.primary_color || effectiveAttorney?.primary_color || prev.primaryColor,
            secondaryColor: config.secondary_color || effectiveAttorney?.secondary_color || prev.secondaryColor,
            backgroundColor: config.background_color || effectiveAttorney?.background_color || prev.backgroundColor,
            backgroundOpacity: config.background_opacity || effectiveAttorney?.background_opacity || prev.backgroundOpacity,
            buttonText: effectiveAttorney?.button_text || prev.buttonText,
            buttonOpacity: config.button_opacity || effectiveAttorney?.button_opacity || prev.buttonOpacity,
            practiceAreaBackgroundOpacity: config.practice_area_background_opacity || effectiveAttorney?.practice_area_background_opacity || prev.practiceAreaBackgroundOpacity,
            textBackgroundColor: config.text_background_color || effectiveAttorney?.text_background_color || prev.textBackgroundColor,
            theme: effectiveAttorney?.theme || prev.theme,
            buttonColor: config.button_color || effectiveAttorney?.button_color || prev.buttonColor,
            titleText: config.assistant_name || effectiveAttorney?.title_text || effectiveAttorney?.firm_name || prev.titleText,
            practiceDescription: config.practice_description || effectiveAttorney?.practice_description || prev.practiceDescription,
            // Load logo from assistant config only (assistant-specific)
            logoUrl: config.logo_url || prev.logoUrl
          }));

          // Load banner image from assistant config (assistant-specific)
          const logoUrl = config.logo_url;  // Only use assistant-level logo
          setLogoPreview(logoUrl || null);
          if (logoUrl) {
            console.log('[AgentTab] ✅ Banner image loaded from assistant config:', logoUrl.substring(0, 50) + '...');
            // Update the preview immediately with the assistant's banner image
            onUpdate({ logoUrl: logoUrl, mascot: logoUrl, assistant_image_url: logoUrl });
          } else {
            console.log('[AgentTab] ⚠️ No banner image found for this assistant');
            // Clear the preview banner image for this assistant
            onUpdate({ logoUrl: '', mascot: '', assistant_image_url: '' });
          }

          // Update practice area
          if (effectiveAttorney?.practice_area) {
            setSelectedPracticeArea(effectiveAttorney.practice_area);
          }

          console.log('[AgentTab] Form data initialized from assistant configuration');
          return;
        }
      }

      // Fallback to attorney-level configuration
      console.log('[AgentTab] Loading fallback attorney configuration');
      setFormData(prev => ({
        ...prev,
        welcomeMessage: effectiveAttorney?.welcome_message || prev.welcomeMessage,
        informationGathering: effectiveAttorney?.information_gathering || prev.informationGathering,
        vapiInstructions: effectiveAttorney?.vapi_instructions || prev.vapiInstructions,
        vapiContext: effectiveAttorney?.vapi_context || prev.vapiContext,
        voiceId: effectiveAttorney?.voice_id || prev.voiceId,
        voiceProvider: effectiveAttorney?.voice_provider || prev.voiceProvider,
        primaryColor: effectiveAttorney?.primary_color || prev.primaryColor,
        secondaryColor: effectiveAttorney?.secondary_color || prev.secondaryColor,
        backgroundColor: effectiveAttorney?.background_color || prev.backgroundColor,
        backgroundOpacity: effectiveAttorney?.background_opacity || prev.backgroundOpacity,
        buttonText: effectiveAttorney?.button_text || prev.buttonText,
        buttonOpacity: effectiveAttorney?.button_opacity || prev.buttonOpacity,
        practiceAreaBackgroundOpacity: effectiveAttorney?.practice_area_background_opacity || prev.practiceAreaBackgroundOpacity,
        textBackgroundColor: effectiveAttorney?.text_background_color || prev.textBackgroundColor,
        theme: effectiveAttorney?.theme || prev.theme,
        buttonColor: effectiveAttorney?.button_color || prev.buttonColor,
        titleText: effectiveAttorney?.title_text || effectiveAttorney?.firm_name || prev.titleText,
        practiceDescription: effectiveAttorney?.practice_description || prev.practiceDescription,
        logoUrl: prev.logoUrl  // Keep existing value, don't load from attorney level
      }));

      // Clear logo preview when no assistant config (each assistant should have its own)
      setLogoPreview(null);
      // Also clear the preview banner image
      onUpdate({ logoUrl: '', mascot: '', assistant_image_url: '' });
      console.log('[AgentTab] ⚠️ No assistant config found - cleared logo preview and banner for assistant-specific data');

      // Update practice area
      if (effectiveAttorney?.practice_area) {
        setSelectedPracticeArea(effectiveAttorney.practice_area);
      }

      console.log('[AgentTab] Form data initialized from attorney fallback');
    } catch (error) {
      console.error('[AgentTab] Error loading assistant configuration:', error);
      // Fallback to attorney data on error - same as above
      setFormData(prev => ({
        ...prev,
        welcomeMessage: effectiveAttorney?.welcome_message || prev.welcomeMessage,
        informationGathering: effectiveAttorney?.information_gathering || prev.informationGathering,
        vapiInstructions: effectiveAttorney?.vapi_instructions || prev.vapiInstructions,
        vapiContext: effectiveAttorney?.vapi_context || prev.vapiContext,
        voiceId: effectiveAttorney?.voice_id || prev.voiceId,
        voiceProvider: effectiveAttorney?.voice_provider || prev.voiceProvider,
        primaryColor: effectiveAttorney?.primary_color || prev.primaryColor,
        secondaryColor: effectiveAttorney?.secondary_color || prev.secondaryColor,
        backgroundColor: effectiveAttorney?.background_color || prev.backgroundColor,
        backgroundOpacity: effectiveAttorney?.background_opacity || prev.backgroundOpacity,
        buttonText: effectiveAttorney?.button_text || prev.buttonText,
        buttonOpacity: effectiveAttorney?.button_opacity || prev.buttonOpacity,
        practiceAreaBackgroundOpacity: effectiveAttorney?.practice_area_background_opacity || prev.practiceAreaBackgroundOpacity,
        textBackgroundColor: effectiveAttorney?.text_background_color || prev.textBackgroundColor,
        theme: effectiveAttorney?.theme || prev.theme,
        buttonColor: effectiveAttorney?.button_color || prev.buttonColor,
        titleText: effectiveAttorney?.title_text || effectiveAttorney?.firm_name || prev.titleText,
        practiceDescription: effectiveAttorney?.practice_description || prev.practiceDescription,
        logoUrl: prev.logoUrl  // Keep existing value, don't load from attorney level
      }));

      // Also clear the preview banner image on error
      setLogoPreview(null);
      onUpdate({ logoUrl: '', mascot: '', assistant_image_url: '' });
      console.log('[AgentTab] ❌ Error loading assistant config - cleared banner image');
    }
  };

  // Check if attorney exists and has an ID
  useEffect(() => {
    if (!effectiveAttorney) {
      // Attorney prop is null, waiting for data to load
      return;
    }

    if (!effectiveAttorney.id) {
      // Attorney ID validation handled silently in production
    }
  }, [effectiveAttorney]);

  // Listen for refreshAgentTab event
  useEffect(() => {
    const handleRefreshAgentTab = async () => {
      console.log('[AgentTab] Refreshing agent tab');

      try {
        // Get the current attorney from localStorage
        const storedAttorney = localStorage.getItem('attorney');
        if (storedAttorney) {
          const parsedAttorney = JSON.parse(storedAttorney);

          // Update form data with the current attorney's values
          setFormData(prev => {
            // Don't load logo from attorney level - each assistant has its own
            return {
              ...prev,
              welcomeMessage: parsedAttorney.welcome_message || prev.welcomeMessage,
              informationGathering: parsedAttorney.information_gathering || prev.informationGathering,
              vapiInstructions: parsedAttorney.vapi_instructions || prev.vapiInstructions,
              vapiContext: parsedAttorney.vapi_context || prev.vapiContext,
              voiceId: parsedAttorney.voice_id || prev.voiceId,
              voiceProvider: parsedAttorney.voice_provider || prev.voiceProvider,
              primaryColor: parsedAttorney.primary_color || prev.primaryColor,
              secondaryColor: parsedAttorney.secondary_color || prev.secondaryColor,
              buttonColor: parsedAttorney.button_color || prev.buttonColor,
              vapiAssistantId: parsedAttorney.vapi_assistant_id || prev.vapiAssistantId,
              logoUrl: prev.logoUrl  // Keep existing assistant-specific logo
            };
          });

          // Update the UI (but don't override assistant-specific logo)
          const updateData = {
            welcomeMessage: parsedAttorney.welcome_message,
            firstMessage: parsedAttorney.first_message || parsedAttorney.information_gathering,
            vapiInstructions: parsedAttorney.vapi_instructions,
            vapiContext: parsedAttorney.vapi_context,
            voiceId: parsedAttorney.voice_id,
            voiceProvider: parsedAttorney.voice_provider,
            primaryColor: parsedAttorney.primary_color,
            secondaryColor: parsedAttorney.secondary_color,
            buttonColor: parsedAttorney.button_color,
            vapiAssistantId: parsedAttorney.vapi_assistant_id
            // Note: logoUrl is not included - each assistant has its own
          };

          onUpdate(updateData);

          // Don't update logo preview from attorney data - each assistant has its own

          console.log('[AgentTab] Updated form data with current attorney values');
        }
      } catch (error) {
        console.error('[AgentTab] Error refreshing agent tab:', error);
      }
    };

    // Listen for the refreshAgentTab event
    window.addEventListener('refreshAgentTab', handleRefreshAgentTab);

    // Expose the refreshAgentTab function globally
    window.refreshAgentTab = handleRefreshAgentTab;

    return () => {
      window.removeEventListener('refreshAgentTab', handleRefreshAgentTab);
    };
  }, [onUpdate]);

  // Handle voice change
  const handleVoiceChange = async (e) => {
    const voiceId = e.target.value;

    // Find the selected voice preset to get the provider
    const selectedVoice = voicePresets.find(voice => voice.id === voiceId);
    const voiceProvider = selectedVoice?.provider || 'playht';

    // Update form data with both voiceId and provider
    setFormData(prev => ({
      ...prev,
      voiceId,
      voiceProvider
    }));

    // Update preview config for real-time preview with both voiceId and provider
    onUpdate({
      voiceId,
      voiceProvider
    });

    console.log(`[AgentTab] Voice changed to: ${voiceId} (Provider: ${voiceProvider})`);

    // Update Supabase immediately for real-time sync
    try {
      setLoading(true);

      // Get the attorney from localStorage if not available
      let currentAttorney = attorney;
      if (!currentAttorney || !currentAttorney.id) {
        try {
          console.log('[AgentTab] Attorney object is missing or has no ID, checking localStorage');
          const storedAttorney = localStorage.getItem('attorney');
          if (storedAttorney) {
            currentAttorney = JSON.parse(storedAttorney);
            console.log('[AgentTab] Retrieved attorney from localStorage:', currentAttorney);
          }
        } catch (error) {
          console.error('[AgentTab] Error parsing attorney from localStorage:', error);
        }
      }

      // 💀 NO MORE DEVELOPMENT ATTORNEYS! Force proper error handling
      if (!currentAttorney || (!currentAttorney.id && !currentAttorney.subdomain && !currentAttorney.email)) {
        console.error('💀 [AgentTab] NO ATTORNEY FOUND - FORCING ROBUST STATE HANDLER!');

        // Force the robust state handler to run
        if (typeof window.resolveAttorneyState === 'function') {
          try {
            const { data: { user }, error: userError } = await supabase.auth.getUser();
            if (!userError && user?.email) {
              console.log('💀 [AgentTab] FORCING robust state handler for:', user.email);
              const stateResult = await window.resolveAttorneyState(user.email);
              if (stateResult.success && stateResult.attorney) {
                currentAttorney = stateResult.attorney;
                localStorage.setItem('attorney', JSON.stringify(currentAttorney));
                console.log('💀 [AgentTab] ✅ FORCED robust state handler succeeded!');
              } else {
                throw new Error('Robust state handler failed to resolve attorney');
              }
            } else {
              throw new Error('No authenticated user found');
            }
          } catch (error) {
            console.error('💀 [AgentTab] FORCED robust state handler failed:', error);
            throw new Error('No attorney profile found. Please contact support.');
          }
        } else {
          throw new Error('Robust state handler not available. Please refresh the page.');
        }
      }

      // Find the selected voice preset to get the provider
      const selectedVoice = voicePresets.find(voice => voice.id === voiceId);
      const voiceProvider = selectedVoice?.provider || '11labs';

      // Prepare update data
      const voiceUpdateData = {
        voice_id: voiceId,
        voice_provider: voiceProvider,
        updated_at: new Date()
      };

      // Filter update data to only include fields that exist in the database
      const filteredVoiceUpdateData = await filterUpdateData('attorneys', voiceUpdateData);

      // Check if the attorney ID is a valid UUID
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      let updateSuccessful = false;

      try {
        // Determine which identifier to use (id, subdomain, or email)
        if (currentAttorney.id && uuidRegex.test(currentAttorney.id)) {
          console.log('[AgentTab] Using ID for voice change:', currentAttorney.id);
          const { data, error } = await supabase
            .from('attorneys')
            .update(filteredVoiceUpdateData)
            .eq('id', currentAttorney.id);

          if (error) {
            console.error('[AgentTab] Error updating voice with ID:', error);
          } else {
            updateSuccessful = true;
          }
        } else if (currentAttorney.subdomain) {
          console.log('[AgentTab] Using subdomain for voice change:', currentAttorney.subdomain);
          const { data, error } = await supabase
            .from('attorneys')
            .update(filteredVoiceUpdateData)
            .eq('subdomain', currentAttorney.subdomain);

          if (error) {
            console.error('[AgentTab] Error updating voice with subdomain:', error);
          } else {
            updateSuccessful = true;
          }
        } else if (currentAttorney.email) {
          console.log('[AgentTab] Using email for voice change:', currentAttorney.email);
          const { data, error } = await supabase
            .from('attorneys')
            .update(filteredVoiceUpdateData)
            .eq('email', currentAttorney.email);

          if (error) {
            console.error('[AgentTab] Error updating voice with email:', error);
          } else {
            updateSuccessful = true;
          }
        } else {
          console.log('[AgentTab] No valid identifier found for attorney:', currentAttorney);
          // Don't throw an error, just log it
        }
      } catch (error) {
        console.error('[AgentTab] Exception during voice update:', error);
        // Don't throw an error, just log it
      }

      // If we couldn't update Supabase, at least update localStorage
      if (!updateSuccessful) {
        console.log('[AgentTab] Supabase voice update failed, updating localStorage only');

        // Update the attorney object in localStorage
        const updatedAttorney = {
          ...currentAttorney,
          voice_id: voiceId,
          voice_provider: voiceProvider
        };
        localStorage.setItem('attorney', JSON.stringify(updatedAttorney));
      } else {
        // Even if Supabase update was successful, also update localStorage to ensure consistency
        const updatedAttorney = {
          ...currentAttorney,
          voice_id: voiceId,
          voice_provider: voiceProvider
        };
        localStorage.setItem('attorney', JSON.stringify(updatedAttorney));
      }

      // Synchronize with Vapi (regardless of whether we have an assistant ID or not)
      try {
        console.log('[AgentTab] Synchronizing voice change with Vapi');

        // PRODUCTION FIX: Use module resolver to avoid build conflicts
        const { vapiAssistantService } = await serviceResolvers.vapiService('vapiAssistantService');

        // Get the complete attorney data from Supabase if the update was successful
        let attorneyToSync = null;

        if (updateSuccessful) {
          // Get the updated attorney data from Supabase - this is our single source of truth
          try {
            const { data, error } = await supabase
              .from('attorneys')
              .select('*')
              .eq(currentAttorney.id ? 'id' : currentAttorney.subdomain ? 'subdomain' : 'email',
                   currentAttorney.id || currentAttorney.subdomain || currentAttorney.email)
              .single();

            if (error) {
              console.error('[AgentTab] Error getting updated attorney data:', error);
            } else if (data) {
              console.log('[AgentTab] Retrieved complete attorney data from Supabase for Vapi voice sync');
              attorneyToSync = data;
            }
          } catch (fetchError) {
            console.error('[AgentTab] Error fetching updated attorney data:', fetchError);
          }
        }

        // If we couldn't get the updated data from Supabase, use the current attorney with updated voice
        if (!attorneyToSync) {
          console.log('[AgentTab] Using local data for Vapi voice sync (fallback)');
          attorneyToSync = {
            ...currentAttorney,
            voice_id: voiceId,
            voice_provider: voiceProvider,
            updated_at: new Date().toISOString()
          };

          // Ensure the attorney has a valid ID for assistant creation
          if (!attorneyToSync.id) {
            // Try to get from user context or generate one
            const { user } = useAuth();
            if (user && user.id) {
              attorneyToSync.id = user.id;
              attorneyToSync.user_id = user.id;
              attorneyToSync.firm_name = attorneyToSync.firm_name || `${user.email?.split('@')[0] || 'Development'}'s Law Firm`;
            } else {
              // Generate a valid UUID for development
              attorneyToSync.id = generateUUID();
              attorneyToSync.user_id = generateUUID();
              attorneyToSync.firm_name = attorneyToSync.firm_name || 'Development Law Firm';
            }
            console.log('[AgentTab] Added valid ID to attorney for assistant creation:', attorneyToSync.id);
          }
        }

        // If no assistant ID exists yet, we'll need to create one
        if (!attorneyToSync.vapi_assistant_id) {
          console.log('[AgentTab] No assistant ID found, creating a new assistant');

          // Create a new assistant
          const assistant = await vapiAssistantService.createAssistantForAttorney(attorneyToSync);

          if (assistant && assistant.id) {
            console.log('[AgentTab] Created new assistant with ID:', assistant.id);

            // Update the attorney with the new assistant ID
            attorneyToSync.vapi_assistant_id = assistant.id;

            // Update Supabase if we have a valid identifier
            if (updateSuccessful) {
              try {
                const { error } = await supabase
                  .from('attorneys')
                  .update({ vapi_assistant_id: assistant.id })
                  .eq(currentAttorney.id ? 'id' : currentAttorney.subdomain ? 'subdomain' : 'email',
                       currentAttorney.id || currentAttorney.subdomain || currentAttorney.email);

                if (error) {
                  console.error('[AgentTab] Error updating assistant ID in Supabase:', error);
                }
              } catch (updateError) {
                console.error('[AgentTab] Exception updating assistant ID in Supabase:', updateError);
              }
            }

            // Update localStorage
            const updatedAttorney = {
              ...currentAttorney,
              vapi_assistant_id: assistant.id,
              voice_id: voiceId,
              voice_provider: voiceProvider
            };
            localStorage.setItem('attorney', JSON.stringify(updatedAttorney));

            // Update the UI
            onUpdate({
              vapi_assistant_id: assistant.id,
              voiceId: voiceId,
              voiceProvider: voiceProvider
            });
          }
        } else {
          // Update voice in Vapi assistant using enhanced service
          console.log('[AgentTab] 🎵 Updating voice in Vapi assistant:', {
            assistantId: attorneyToSync.vapi_assistant_id,
            voiceId: voiceId,
            voiceProvider: voiceProvider
          });

          try {
            // Import the enhanced Vapi MCP service
            const { enhancedVapiMcpService } = await import('../../services/EnhancedVapiMcpService');

            // Initialize with API key
            const vapiApiKey = import.meta.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
            await enhancedVapiMcpService.connect(vapiApiKey);

            // Update just the voice settings in the assistant
            const updateResult = await enhancedVapiMcpService.updateAssistant(
              attorneyToSync.vapi_assistant_id,
              {
                voice: {
                  provider: voiceProvider,
                  voiceId: voiceId
                }
              }
            );

            if (updateResult) {
              console.log('[AgentTab] ✅ Voice updated successfully in Vapi assistant:', updateResult.voice);

              // Verify the update by reloading the assistant
              const updatedAssistant = await enhancedVapiMcpService.getAssistant(attorneyToSync.vapi_assistant_id);
              if (updatedAssistant?.voice?.voiceId === voiceId) {
                console.log('[AgentTab] ✅ Voice update confirmed in Vapi:', updatedAssistant.voice);
              } else {
                console.warn('[AgentTab] ⚠️ Voice update not reflected in Vapi assistant');
              }
            } else {
              console.warn('[AgentTab] ⚠️ Voice update returned no result');
            }
          } catch (vapiError) {
            console.error('[AgentTab] ❌ Enhanced Vapi service failed, trying fallback:', vapiError);

            // Fallback to original sync method
            const syncResult = await vapiAssistantService.syncAttorneyToVapi(attorneyToSync);
            if (syncResult.success) {
              console.log('[AgentTab] Vapi voice synchronization successful (fallback):', syncResult);
            } else {
              console.warn('[AgentTab] Vapi voice synchronization failed (fallback):', syncResult);
            }
          }
        }
      } catch (vapiError) {
        console.error('[AgentTab] Error synchronizing voice with Vapi:', vapiError);
        // Don't throw here, we still want to show success for the database update
      }

      // Show brief success message regardless of Supabase update
      // The important thing is that the preview is updated
      setSuccess(true);
      setTimeout(() => setSuccess(false), 1500);
    } catch (error) {
      console.error('Error updating voice settings:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle voice file selection
  const handleVoiceFileChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setVoiceFile(file);
  };

  // Handle knowledge file selection
  const handleKnowledgeFileChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setKnowledgeFile(file);

    // Auto-upload the knowledge file
    handleKnowledgeUpload(file);
  };

  // Handle knowledge file upload
  const handleKnowledgeUpload = async (file) => {
    if (!file) {
      setError("Please select a knowledge file first");
      return;
    }

    setUploadingKnowledge(true);
    setError(null);

    try {
      // Get the attorney from localStorage if not available
      let currentAttorney = attorney;
      if (!currentAttorney || !currentAttorney.id) {
        try {
          console.log('[AgentTab] Attorney object is missing or has no ID, checking localStorage');
          const storedAttorney = localStorage.getItem('attorney');
          if (storedAttorney) {
            currentAttorney = JSON.parse(storedAttorney);
            console.log('[AgentTab] Retrieved attorney from localStorage:', currentAttorney);
          }
        } catch (error) {
          console.error('[AgentTab] Error parsing attorney from localStorage:', error);
        }
      }

      // If no assistant ID, show error
      if (!currentAttorney?.vapi_assistant_id) {
        throw new Error('Please create an assistant first before uploading knowledge files');
      }

      // Generate a unique file name using available identifiers
      const fileIdentifier = currentAttorney?.id || currentAttorney?.subdomain || currentAttorney?.email || 'temp-user';
      const fileName = `knowledge-${fileIdentifier}-${Date.now()}-${file.name}`;

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('legalscout_bucket1')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true,
          public: true
        });

      if (uploadError) throw uploadError;

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('legalscout_bucket1')
        .getPublicUrl(fileName);

      // In a real implementation, we would call the Vapi API to add the file to the assistant's knowledge
      // For now, we'll simulate a successful upload
      console.log(`[AgentTab] Knowledge file uploaded: ${publicUrl}`);

      // PRODUCTION FIX: Use module resolver to avoid build conflicts
      const { vapiAssistantService } = await serviceResolvers.vapiService('vapiAssistantService');

      // TODO: Add the file to the assistant's knowledge base
      // This would be implemented in the vapiAssistantService

      // Show success message
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);

      // Clear the file input
      setKnowledgeFile(null);
      if (knowledgeFileInputRef.current) {
        knowledgeFileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error uploading knowledge file:', error);
      setError(error.message);
    } finally {
      setUploadingKnowledge(false);
    }
  };

  // Handle voice cloning
  const handleVoiceCloning = async () => {
    if (!voiceFile) {
      setError("Please select a voice recording file first");
      return;
    }

    // Get the attorney from localStorage if not available
    let currentAttorney = attorney;
    if (!currentAttorney || !currentAttorney.id) {
      try {
        console.log('[AgentTab] Attorney object is missing or has no ID, checking localStorage');
        const storedAttorney = localStorage.getItem('attorney');
        if (storedAttorney) {
          currentAttorney = JSON.parse(storedAttorney);
          console.log('[AgentTab] Retrieved attorney from localStorage:', currentAttorney);
        }
      } catch (error) {
        console.error('[AgentTab] Error parsing attorney from localStorage:', error);
      }
    }

    // If still no valid attorney, create a development one
    if (!currentAttorney || (!currentAttorney.id && !currentAttorney.subdomain && !currentAttorney.email)) {
      console.log('[AgentTab] Creating development attorney');
      currentAttorney = {
        id: 'dev-' + Date.now(),
        subdomain: 'dev-attorney',
        firm_name: 'Your Law Firm',
        name: 'Your Name',
        email: '<EMAIL>',
        user_id: 'dev-user-' + Date.now(),
        vapi_assistant_id: null // Will be created when needed
      };

      // Save to localStorage
      localStorage.setItem('attorney', JSON.stringify(currentAttorney));
      console.log('[AgentTab] Created development attorney:', currentAttorney);
    }

    setVoiceCloning(true);
    setError(null);

    try {
      // Generate a unique file name using available identifiers
      const fileIdentifier = currentAttorney?.id || currentAttorney?.subdomain || currentAttorney?.email || 'temp-user';
      const fileName = `voice-clone-${fileIdentifier}-${Date.now()}`;

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('legalscout_bucket1')
        .upload(fileName, voiceFile, {
          cacheControl: '3600',
          upsert: true,
          public: true
        });

      if (uploadError) throw uploadError;

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('legalscout_bucket1')
        .getPublicUrl(fileName);

      // In a real implementation, we would call the PlayHT API here
      // For now, we'll simulate a successful voice cloning
      const clonedVoiceId = `cloned-${Date.now()}`;

      // Update the form data with the new voice ID
      setFormData(prev => ({
        ...prev,
        voiceId: clonedVoiceId,
        voiceProvider: 'playht'
      }));

      // Update the preview
      onUpdate({
        voiceId: clonedVoiceId,
        voiceProvider: 'playht'
      });

      // Prepare update data
      const voiceUpdateData = {
        voice_id: clonedVoiceId,
        voice_provider: 'playht',
        updated_at: new Date()
      };

      // Filter update data to only include fields that exist in the database
      const filteredVoiceUpdateData = await filterUpdateData('attorneys', voiceUpdateData);

      // Check if the attorney ID is a valid UUID
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      let updateSuccessful = false;

      try {
        // Determine which identifier to use (id, subdomain, or email)
        if (currentAttorney.id && uuidRegex.test(currentAttorney.id)) {
          console.log('[AgentTab] Using ID for voice update:', currentAttorney.id);
          const { data, error } = await supabase
            .from('attorneys')
            .update(filteredVoiceUpdateData)
            .eq('id', currentAttorney.id);

          if (error) {
            console.error('[AgentTab] Error updating voice with ID:', error);
          } else {
            updateSuccessful = true;
          }
        } else if (currentAttorney.subdomain) {
          console.log('[AgentTab] Using subdomain for voice update:', currentAttorney.subdomain);
          const { data, error } = await supabase
            .from('attorneys')
            .update(filteredVoiceUpdateData)
            .eq('subdomain', currentAttorney.subdomain);

          if (error) {
            console.error('[AgentTab] Error updating voice with subdomain:', error);
          } else {
            updateSuccessful = true;
          }
        } else if (currentAttorney.email) {
          console.log('[AgentTab] Using email for voice update:', currentAttorney.email);
          const { data, error } = await supabase
            .from('attorneys')
            .update(filteredVoiceUpdateData)
            .eq('email', currentAttorney.email);

          if (error) {
            console.error('[AgentTab] Error updating voice with email:', error);
          } else {
            updateSuccessful = true;
          }
        } else {
          console.log('[AgentTab] No valid identifier found for attorney:', currentAttorney);
          // Don't throw an error, just log it
        }
      } catch (error) {
        console.error('[AgentTab] Exception during voice update:', error);
        // Don't throw an error, just log it
      }

      // If we couldn't update Supabase, at least update localStorage
      if (!updateSuccessful) {
        console.log('[AgentTab] Supabase voice update failed, updating localStorage only');

        // Update the attorney object in localStorage
        const updatedAttorney = {
          ...currentAttorney,
          voice_id: clonedVoiceId,
          voice_provider: 'playht'
        };
        localStorage.setItem('attorney', JSON.stringify(updatedAttorney));
      }

      // Show success message regardless of Supabase update
      // The important thing is that the preview is updated

      // Show success message
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (error) {
      console.error('Error cloning voice:', error);
      setError(error.message);
    } finally {
      setVoiceCloning(false);
    }
  };

  // Manual Vapi sync function
  const handleManualVapiSync = async () => {
    try {
      setLoading(true);
      console.log('[AgentTab] 🔄 Manual Vapi sync initiated...');

      const currentAttorney = effectiveAttorney || JSON.parse(localStorage.getItem('attorney') || '{}');

      if (!currentAttorney.vapi_assistant_id) {
        throw new Error('No Vapi assistant ID found');
      }

      // PRODUCTION FIX: Use module resolver to avoid build conflicts
      const { enhancedVapiMcpService } = await serviceResolvers.vapiService('enhancedVapiMcpService');

      // Initialize with API key
      const vapiApiKey = import.meta.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
      await enhancedVapiMcpService.connect(vapiApiKey);

      // Update the assistant with current form data
      const updateData = {
        firstMessage: formData.firstMessage || '',
        model: {
          messages: formData.vapiInstructions ? [
            {
              role: 'system',
              content: formData.vapiInstructions
            }
          ] : []
        }
      };

      console.log('[AgentTab] 🚀 Updating Vapi assistant with:', updateData);

      const updateResult = await enhancedVapiMcpService.updateAssistant(
        currentAttorney.vapi_assistant_id,
        updateData
      );

      if (updateResult) {
        console.log('[AgentTab] ✅ Manual Vapi sync successful:', updateResult);
        setSuccess(true);
        setError(null);
      } else {
        throw new Error('Update returned no result');
      }

    } catch (error) {
      console.error('[AgentTab] ❌ Manual Vapi sync failed:', error);
      setError(`Manual sync failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // CRITICAL: Check if we have a selected assistant
      if (!currentAssistant?.id || !effectiveAttorney?.id) {
        throw new Error('No assistant selected. Please select an assistant first.');
      }

      console.log('[AgentTab] Saving configuration for assistant:', currentAssistant.id);

      // Save complete assistant configuration using the new service
      await AssistantConfigService.saveAssistantConfig(currentAssistant.id, effectiveAttorney.id, {
        firmName: formData.titleText || effectiveAttorney.firm_name,
        titleText: formData.titleText,
        primaryColor: formData.primaryColor,
        secondaryColor: formData.secondaryColor,
        buttonColor: formData.buttonColor,
        backgroundColor: formData.backgroundColor,
        backgroundOpacity: formData.backgroundOpacity,
        buttonOpacity: formData.buttonOpacity,
        practiceAreaBackgroundOpacity: formData.practiceAreaBackgroundOpacity,
        textBackgroundColor: formData.textBackgroundColor,
        logoUrl: formData.logoUrl,
        practiceDescription: formData.practiceDescription,
        practiceAreas: formData.practiceAreas || [],
        welcomeMessage: formData.welcomeMessage,
        informationGathering: formData.firstMessage,
        vapiInstructions: formData.vapiInstructions,
        vapiContext: formData.vapiContext,
        voiceId: formData.voiceId,
        voiceProvider: formData.voiceProvider,
        aiModel: formData.aiModel || 'gpt-4o'
      });

      // Sync configuration to Vapi assistant
      console.log('[AgentTab] Syncing configuration to Vapi assistant...');
      try {
        const { vapiMcpService } = await import('../../services/vapiMcpService');
        await vapiMcpService.ensureConnection();

        // Prepare Vapi update data
        const vapiUpdateData = {
          name: formData.titleText || effectiveAttorney.firm_name,
          firstMessage: formData.welcomeMessage,
          systemPrompt: formData.vapiInstructions,
          voice: {
            provider: formData.voiceProvider,
            voiceId: formData.voiceId
          }
        };

        // Update the assistant in Vapi
        await vapiMcpService.updateAssistant(currentAssistant.id, vapiUpdateData);
        console.log('[AgentTab] Successfully synced configuration to Vapi');

      } catch (vapiError) {
        console.warn('[AgentTab] Failed to sync to Vapi:', vapiError.message);
        // Don't fail the whole operation - assistant config is saved locally
      }

      // Update the preview with all changes
      onUpdate({
        ...formData,
        firmName: formData.titleText || effectiveAttorney.firm_name
      });

      // Show success message
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);

      console.log('[AgentTab] ✅ Assistant configuration saved successfully');
    } catch (error) {
      console.error('Error updating agent settings:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle loading data from VAPI
  const handleLoadFromVapi = async (vapiData) => {
    console.log('[AgentTab] Loading data from VAPI:', vapiData);

    // Update the form data with the values from VAPI
    setFormData(prev => ({
      ...prev,
      ...vapiData
    }));

    // Show success message
    setSuccess(true);
    setTimeout(() => setSuccess(false), 3000);
  };

  // Handle Vapi assistant updates
  const handleVapiUpdate = async (updatedData) => {
    // Import the logger utility
    const { createLogger } = await import('../../utils/loggerUtils');
    const logger = createLogger('AgentTab');

    logger.info('Vapi assistant updated:', updatedData);

    // Update the attorney with the updated data
    try {
      setLoading(true);
      setError(null);

      // Extract only the fields that need to be updated
      const updateData = {};

      // Check for vapi_assistant_id
      if (updatedData.vapi_assistant_id && updatedData.vapi_assistant_id !== attorney?.vapi_assistant_id) {
        updateData.vapi_assistant_id = updatedData.vapi_assistant_id;
      }

      // Check for welcome_message
      if (updatedData.welcome_message && updatedData.welcome_message !== attorney?.welcome_message) {
        updateData.welcome_message = updatedData.welcome_message;
        // Also update the form data
        setFormData(prev => ({
          ...prev,
          welcomeMessage: updatedData.welcome_message
        }));
      }

      // Check for vapi_instructions
      if (updatedData.vapi_instructions && updatedData.vapi_instructions !== attorney?.vapi_instructions) {
        updateData.vapi_instructions = updatedData.vapi_instructions;
        // Also update the form data
        setFormData(prev => ({
          ...prev,
          vapiInstructions: updatedData.vapi_instructions
        }));
      }

      // Only update Supabase if there are changes
      if (Object.keys(updateData).length > 0) {
        logger.info('Updating attorney in Supabase with:', updateData);

        // Update Supabase
        const { data, error } = await supabase
          .from('attorneys')
          .update(updateData)
          .eq('id', attorney.id)
          .select()
          .single();

        if (error) {
          logger.error('Error updating attorney with Vapi data:', error);
          setError('Failed to update attorney with Vapi data');
          return;
        }

        logger.info('Updated attorney with Vapi data:', data);

        // Update the UI through the parent component
        onUpdate(updateData);

        // Show success message
        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
      }
    } catch (err) {
      logger.error('Error updating Vapi data:', err);
      setError('Failed to update Vapi data: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle selecting a different assistant
  const handleSelectAssistant = async (assistantId) => {
    console.log('[AgentTab] Selected assistant:', assistantId);

    try {
      // Get the current attorney from localStorage
      const storedAttorney = localStorage.getItem('attorney');
      if (storedAttorney) {
        const parsedAttorney = JSON.parse(storedAttorney);

        // Update the attorney with the new assistant ID
        const updatedAttorney = {
          ...parsedAttorney,
          vapi_assistant_id: assistantId
        };

        // Save to localStorage
        localStorage.setItem('attorney', JSON.stringify(updatedAttorney));

        // Update Supabase
        if (updatedAttorney.id) {
          const { error } = await supabase
            .from('attorneys')
            .update({ vapi_assistant_id: assistantId })
            .eq('id', updatedAttorney.id);

          if (error) {
            console.error('[AgentTab] Error updating attorney in Supabase:', error);
          } else {
            console.log('[AgentTab] Updated attorney in Supabase with new assistant ID');
          }
        }

        // Update the UI
        onUpdate({ vapiAssistantId: assistantId });
      }
    } catch (error) {
      console.error('[AgentTab] Error selecting assistant:', error);
      setError('Error selecting assistant: ' + error.message);
    }
  };

  // State for managing multiple assistants
  const [availableAssistants, setAvailableAssistants] = useState([]);
  const [loadingAssistants, setLoadingAssistants] = useState(false);

  // Load available assistants using Vapi MCP Server (following official docs)
  const loadAvailableAssistants = async () => {
    if (!effectiveAttorney?.id) return;

    try {
      setLoadingAssistants(true);
      console.log('[AgentTab] Loading assistants using Vapi MCP Server for attorney:', effectiveAttorney.id);

      // Use Vapi MCP Server to list all assistants (official approach)
      const { vapiMcpService } = await import('../../services/vapiMcpService');
      const allAssistants = await vapiMcpService.listAssistants();

      console.log('[AgentTab] Retrieved assistants from Vapi:', allAssistants);

      // Filter assistants for this attorney or use the attorney's assigned assistant
      let relevantAssistants = [];

      if (effectiveAttorney.vapi_assistant_id) {
        // Find the attorney's specific assistant
        const attorneyAssistant = allAssistants.find(assistant =>
          assistant.id === effectiveAttorney.vapi_assistant_id
        );

        if (attorneyAssistant) {
          console.log('[AgentTab] Found attorney\'s assigned assistant:', attorneyAssistant.name);
          relevantAssistants = [attorneyAssistant];
        } else {
          console.warn('[AgentTab] Attorney\'s assigned assistant not found in Vapi, showing all assistants');
          relevantAssistants = allAssistants;
        }
      } else {
        console.log('[AgentTab] No assigned assistant, showing all available assistants');
        relevantAssistants = allAssistants;
      }

      // Ensure we have at least the attorney's assistant if it exists
      if (relevantAssistants.length === 0 && effectiveAttorney.vapi_assistant_id) {
        console.log('[AgentTab] Creating fallback assistant entry for:', effectiveAttorney.vapi_assistant_id);
        relevantAssistants = [{
          id: effectiveAttorney.vapi_assistant_id,
          name: 'LegalScout Assistant',
          fallback: true
        }];
      }

      console.log('[AgentTab] Final assistant list:', relevantAssistants);
      setAvailableAssistants(relevantAssistants);
    } catch (error) {
      console.error('[AgentTab] Error loading assistants from Vapi MCP:', error);

      // Fallback: Create a basic assistant entry if we have an assistant ID
      if (effectiveAttorney.vapi_assistant_id) {
        console.log('[AgentTab] Using fallback assistant due to MCP error');
        setAvailableAssistants([{
          id: effectiveAttorney.vapi_assistant_id,
          name: 'LegalScout Assistant',
          fallback: true
        }]);
      } else {
        setAvailableAssistants([]);
      }
    } finally {
      setLoadingAssistants(false);
    }
  };

  // Load assistants when component mounts or attorney changes
  useEffect(() => {
    loadAvailableAssistants();
  }, [effectiveAttorney?.id]);

  // Handle assistant dropdown selection
  const handleAssistantDropdownChange = async (event) => {
    const selectedAssistantId = event.target.value;
    console.log('[AgentTab] Assistant dropdown changed to:', selectedAssistantId);

    if (selectedAssistantId === 'create_new') {
      // Validate attorney data before creating new assistant
      if (!effectiveAttorney || !effectiveAttorney.id) {
        console.error('[AgentTab] Cannot create assistant - missing attorney data');
        setError('Attorney information is required to create an assistant');
        return;
      }
      // Create new assistant
      await handleCreateNewAssistant();
      return;
    }

    if (selectedAssistantId && selectedAssistantId !== attorney?.vapi_assistant_id) {
      console.log('[AgentTab] Switching to assistant:', selectedAssistantId);
      await handleSelectAssistant(selectedAssistantId);
    }
  };

  // Create a new assistant
  const handleCreateNewAssistant = async () => {
    try {
      setLoading(true);

      // Ensure we have a valid attorney object
      if (!effectiveAttorney || !effectiveAttorney.id) {
        throw new Error('Attorney information is required to create an assistant');
      }

      const { vapiAssistantService } = await import('../../services/vapiAssistantService');

      // Create new assistant with current attorney settings
      // Generate a unique, meaningful name
      const timestamp = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
      const assistantNumber = availableAssistants.length + 1;
      const baseName = effectiveAttorney.firm_name || 'LegalScout';
      const assistantName = `${baseName} Agent ${assistantNumber} (${timestamp})`;

      const newAssistant = await vapiAssistantService.createAssistant({
        name: assistantName,
        firstMessage: effectiveAttorney.first_message || effectiveAttorney.information_gathering || effectiveAttorney.welcome_message || '',
        instructions: effectiveAttorney.vapi_instructions || '',
        voice: {
          provider: effectiveAttorney.voice_provider || 'openai',
          voiceId: effectiveAttorney.voice_id || 'echo'
        }
      });

      if (newAssistant && newAssistant.id) {
        console.log('✅ [AgentTab] Assistant created successfully:', newAssistant.id);

        // 1. Create assistant UI config (CRITICAL - this is what the dropdown uses)
        try {
          const { assistantUIConfigService } = await import('../../services/assistantUIConfigService');
          await assistantUIConfigService.createAssistantConfig(
            effectiveAttorney.id,
            newAssistant.id,
            {
              assistant_name: assistantName, // Use the unique name we generated
              firm_name: effectiveAttorney.firm_name,
              // Copy current settings as defaults
              vapi_instructions: effectiveAttorney.vapi_instructions,
              voice_provider: effectiveAttorney.voice_provider || 'openai',
              voice_id: effectiveAttorney.voice_id || 'echo',
              ai_model: effectiveAttorney.ai_model || 'gpt-4o'
            }
          );
          console.log('✅ [AgentTab] Assistant UI config created');
        } catch (configError) {
          console.error('[AgentTab] Error creating assistant UI config:', configError);
        }

        // 2. Create subdomain mapping (CRITICAL - this enables URLs)
        try {
          const { assistantSubdomainService } = await import('../../services/assistantSubdomainService');
          const baseSubdomain = effectiveAttorney.subdomain || 'assistant';
          await assistantSubdomainService.createSubdomainForAssistant(
            newAssistant.id,
            effectiveAttorney.id,
            baseSubdomain,
            false // Not primary
          );
          console.log('✅ [AgentTab] Assistant subdomain mapping created');
        } catch (subdomainError) {
          console.error('[AgentTab] Error creating subdomain mapping:', subdomainError);
        }

        // 3. Add to attorney_assistants table (for compatibility)
        try {
          const { error } = await supabase
            .from('attorney_assistants')
            .insert({
              attorney_id: effectiveAttorney.id,
              assistant_id: newAssistant.id,
              user_id: effectiveAttorney.user_id || effectiveAttorney.id
            });

          if (error) {
            console.error('[AgentTab] Error adding assistant mapping:', error);
          } else {
            console.log('✅ [AgentTab] Attorney-assistant mapping created');
          }
        } catch (mappingError) {
          console.error('[AgentTab] Error with attorney_assistants table:', mappingError);
        }

        // 4. Reload assistants and select the new one
        await loadAvailableAssistants();
        await handleSelectAssistant(newAssistant.id);
        setSuccess(true);
        setTimeout(() => setSuccess(false), 2000);
      }
    } catch (error) {
      console.error('[AgentTab] Error creating new assistant:', error);
      setError('Error creating new assistant: ' + error.message);
    } finally {
      setLoading(false);
    }
  };



  return (
    <div className="agent-tab">
      <div className="agent-header">
        <div className="agent-title-section">
          <h2>Agent Configuration</h2>
          <p className="tab-description">
            Customize your AI legal assistant to match your firm's branding and communication style.
          </p>
        </div>

        {/* Assistant Subdomain Editor - Context Aware */}
        {(effectiveAttorney?.current_assistant_id || effectiveAttorney?.vapi_assistant_id) ? (
          <div className="dashboard-card">
            <h3>Assistant Subdomain</h3>
            <p className="card-description">
              Configure the dedicated URL for this assistant. Each assistant can have its own subdomain for direct access.
            </p>
            <SubdomainEditor
              currentSubdomain={currentAssistantSubdomain}
              firmName={effectiveAttorney?.firm_name}
              currentAssistantId={effectiveAttorney?.current_assistant_id || effectiveAttorney?.vapi_assistant_id}
              attorneyId={effectiveAttorney?.id}
              assistantName={currentAssistantConfig?.assistant_name || 'Assistant'}
              onUpdate={async (data) => {
                console.log('Assistant subdomain updated:', data);
                // Reload the subdomain for the current assistant
                const assistantId = effectiveAttorney?.current_assistant_id || effectiveAttorney?.vapi_assistant_id;
                if (data.type === 'assistant_subdomain' && assistantId) {
                  await loadCurrentAssistantSubdomain(assistantId);
                }
              }}
              disabled={loading}
            />
          </div>
        ) : (
          <div className="dashboard-card">
            <h3>Assistant Subdomain</h3>
            <p className="card-description">
              Select an assistant from the header dropdown to configure its dedicated URL.
            </p>
            <div className="subdomain-placeholder">
              <div className="subdomain-info">
                <div className="subdomain-url">
                  <strong>select-assistant</strong>.legalscout.net
                </div>
                <div className="subdomain-label">
                  Choose an assistant from the header to configure its URL
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {error && (
        <div className="alert alert-error">
          <FaExclamationTriangle />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="alert alert-success">
          <FaCheck />
          <span>Agent settings updated successfully!</span>
        </div>
      )}






      <div className="config-tabs">
        <button
          className={`config-tab ${activeSection === 'instructions' ? 'active' : ''}`}
          onClick={() => setActiveSection('instructions')}
          title="Instructions"
        >
          <FaCode />
        </button>
        <button
          className={`config-tab ${activeSection === 'messages' ? 'active' : ''}`}
          onClick={() => setActiveSection('messages')}
          title="Text"
        >
          <FaCommentAlt />
        </button>
        <button
          className={`config-tab ${activeSection === 'appearance' ? 'active' : ''}`}
          onClick={() => setActiveSection('appearance')}
          title="Appearance"
        >
          <FaPalette />
        </button>
        <button
          className={`config-tab ${activeSection === 'voice' ? 'active' : ''}`}
          onClick={() => setActiveSection('voice')}
          title="Voice"
        >
          <FaMicrophone />
        </button>
      </div>



      <form onSubmit={handleSubmit}>
        {activeSection === 'instructions' && (
          <div className="tab-content">
            <div className="dashboard-card">
              <h3>System Instructions</h3>
              <p className="card-description">
                Configure how your AI assistant behaves and processes information.
              </p>

              <div className="form-group">
                <label htmlFor="vapiInstructions">System Instructions</label>
                <textarea
                  id="vapiInstructions"
                  name="vapiInstructions"
                  className="form-control system-prompt-textarea"
                  value={formData.vapiInstructions}
                  onChange={handleChange}
                  placeholder="Enter system instructions for your AI assistant"
                  rows="8"
                />
                <small className="form-text">
                  These instructions guide how your AI assistant behaves. They are not visible to clients.
                </small>
              </div>

              <div className="form-group">
                <label htmlFor="welcomeMessage">Welcome Message (Display Only)</label>
                <textarea
                  id="welcomeMessage"
                  name="welcomeMessage"
                  className="form-control"
                  value={formData.welcomeMessage}
                  onChange={handleChange}
                  placeholder="Enter a welcome message for display purposes"
                  rows="3"
                />
                <small className="form-text">
                  This message is shown in the UI but not spoken by the assistant. The first spoken message is set above.
                </small>
              </div>

              <div className="form-group">
                <label htmlFor="vapiContext">Knowledge Base</label>
                <div className="knowledge-upload-container">
                  <label htmlFor="knowledgeFile" className="sr-only">Upload Knowledge Files</label>
                  <input
                    type="file"
                    id="knowledgeFile"
                    ref={knowledgeFileInputRef}
                    onChange={handleKnowledgeFileChange}
                    accept=".pdf,.doc,.docx,.txt"
                    style={{ display: 'none' }}
                    aria-label="Upload Knowledge Files"
                  />
                  <button
                    type="button"
                    className="upload-button"
                    onClick={() => knowledgeFileInputRef.current.click()}
                    disabled={uploadingKnowledge}
                  >
                    <FaFileUpload />
                    <span>{uploadingKnowledge ? 'Uploading...' : 'Upload Knowledge Files'}</span>
                  </button>
                  {knowledgeFile && (
                    <div className="file-info">
                      <FaFileUpload />
                      <span>{knowledgeFile.name}</span>
                    </div>
                  )}
                  <small className="form-text">
                    Upload documents to provide your assistant with specific knowledge about your practice.
                    Supported formats: PDF, DOC, DOCX, TXT.
                  </small>
                </div>
                <textarea
                  id="vapiContext"
                  name="vapiContext"
                  className="form-control"
                  rows="4"
                  placeholder="Add specific knowledge about your practice, firm policies, or other information your agent should know."
                  value={formData.vapiContext}
                  onChange={handleChange}
                />
                <small className="form-text">
                  This additional context helps your assistant provide more accurate and relevant responses.
                </small>
              </div>
            </div>

            {/* Assistant Info and Diagnostics Section */}
            <AssistantInfoSection
              attorney={attorney}
              showDiagnostics={showDiagnostics}
              setShowDiagnostics={setShowDiagnostics}
              setLoading={setLoading}
              setError={setError}
              setSuccess={setSuccess}
              onUpdate={onUpdate}
            />
          </div>
        )}

        {activeSection === 'messages' && (
          <div className="tab-content">
            <div className="dashboard-card">
              <h3>Text Content</h3>
              <p className="card-description">
                Configure the text content displayed on your agent page.
              </p>

              <div className="form-group">
                <label htmlFor="titleText">Assistant Name / Firm Name</label>
                <input
                  type="text"
                  id="titleText"
                  name="titleText"
                  className="form-control"
                  value={formData.titleText}
                  onChange={handleChange}
                  placeholder="Enter your firm name or assistant name"
                />
                <small className="form-text">
                  This appears as the main heading in your agent preview and is specific to this assistant.
                  Each assistant can have its own branding and name.
                </small>
              </div>

              <div className="form-group">
                <label htmlFor="buttonText">Call-to-Action Button Text</label>
                <input
                  type="text"
                  id="buttonText"
                  name="buttonText"
                  className="form-control"
                  value={formData.buttonText}
                  onChange={handleChange}
                  placeholder="Enter button text"
                />
                <small className="form-text">
                  Text displayed on the button that starts the conversation.
                </small>
              </div>

              <div className="form-group">
                <label htmlFor="firstMessage">Greeting (First Message)</label>
                <textarea
                  id="firstMessage"
                  name="firstMessage"
                  className="form-control"
                  value={formData.firstMessage}
                  onChange={handleChange}
                  placeholder="Enter the first message your assistant will speak"
                  rows="3"
                />
                <small className="form-text">
                  This is the first thing your assistant will say when someone starts a conversation.
                </small>
              </div>
            </div>

            <div className="dashboard-card">
              <h3>Practice Area & AI CRM Templates</h3>
              <p className="card-description">
                Select your practice area to automatically configure custom fields and AI prompts optimized for your legal specialty.
              </p>

              <PracticeAreaSelector
                value={selectedPracticeArea}
                onChange={handlePracticeAreaChange}
                onApplyTemplate={handleApplyTemplate}
                showApplyButton={true}
                label="Legal Practice Area"
                disabled={loading}
                className="practice-area-selector-agent"
              />
            </div>

            <div className="dashboard-card">
              <h3>Practice Description</h3>
              <p className="card-description">
                Configure the practice description for this specific assistant. Each assistant can have its own unique description and messaging.
              </p>
              <div className="form-group">
                <label htmlFor="practiceDescription">Practice Description</label>
                <textarea
                  id="practiceDescription"
                  name="practiceDescription"
                  className="form-control"
                  value={formData.practiceDescription || ''}
                  onChange={handleChange}
                  placeholder="Describe your practice for this assistant"
                  rows="4"
                />
                <small className="form-text">
                  This description is specific to this assistant and will be displayed to potential clients.
                  You can use basic markdown formatting.
                </small>
              </div>
            </div>
          </div>
        )}

        {activeSection === 'appearance' && (
          <div className="tab-content">
            <div className="dashboard-card">
              <h3>Banner Image</h3>
              <p className="card-description">
                Upload a banner image that will be displayed at the top of your agent interface.
              </p>

              <div className="form-group">
                {logoPreview ? (
                  <div className="logo-preview">
                    <img
                      src={logoPreview}
                      alt="Banner preview"
                      className="logo-image"
                    />
                    <button
                      type="button"
                      onClick={handleRemoveLogo}
                      className="remove-logo-button"
                    >
                      Remove Banner
                    </button>
                  </div>
                ) : (
                  <div className="logo-upload">
                    <label htmlFor="logo-upload" className="file-input-label">
                      <FaUpload size={24} />
                      <span>Click to upload banner</span>
                      <small>PNG, JPG or SVG (max 2MB)</small>
                    </label>
                    <input
                      id="logo-upload"
                      type="file"
                      accept="image/*"
                      onChange={handleLogoChange}
                      className="file-input"
                    />
                  </div>
                )}
              </div>
            </div>

            <div className="dashboard-card">
              <h3>Visual Theme</h3>

              <div className="form-group">
                <label>Theme</label>
                <div className="theme-selector" role="group" aria-label="Theme selection">
                  <button
                    type="button"
                    className={`theme-option ${formData.theme === 'dark' ? 'active' : ''}`}
                    onClick={() => handleThemeChange('dark')}
                    aria-pressed={formData.theme === 'dark'}
                  >
                    Dark Theme
                  </button>
                  <button
                    type="button"
                    className={`theme-option ${formData.theme === 'light' ? 'active' : ''}`}
                    onClick={() => handleThemeChange('light')}
                    aria-pressed={formData.theme === 'light'}
                  >
                    Light Theme
                  </button>
                </div>
              </div>

              <div className="form-group">
                <label>Colors</label>
                <div className="color-pickers">
                  <div className="color-picker">
                    <label htmlFor="primaryColor">Primary</label>
                    <input
                      type="color"
                      id="primaryColor"
                      name="primaryColor"
                      className="color-input form-control"
                      value={formData.primaryColor}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="color-picker">
                    <label htmlFor="secondaryColor">Secondary</label>
                    <input
                      type="color"
                      id="secondaryColor"
                      name="secondaryColor"
                      className="color-input form-control"
                      value={formData.secondaryColor}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="color-picker">
                    <label htmlFor="backgroundColor">Background</label>
                    <input
                      type="color"
                      id="backgroundColor"
                      name="backgroundColor"
                      className="color-input form-control"
                      value={formData.backgroundColor}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="color-picker">
                    <label htmlFor="buttonColor">Button</label>
                    <input
                      type="color"
                      id="buttonColor"
                      name="buttonColor"
                      className="color-input form-control"
                      value={formData.buttonColor}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="color-picker">
                    <label htmlFor="textBackgroundColor">Text BG</label>
                    <input
                      type="color"
                      id="textBackgroundColor"
                      name="textBackgroundColor"
                      className="color-input form-control"
                      value={formData.textBackgroundColor}
                      onChange={handleChange}
                    />
                  </div>
                </div>
              </div>

              <div className="opacity-sliders">
                <div className="form-group">
                  <label htmlFor="backgroundOpacity">
                    Background Opacity: {formData.backgroundOpacity.toFixed(1)}
                  </label>
                  <input
                    type="range"
                    id="backgroundOpacity"
                    name="backgroundOpacity"
                    className="form-control-range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={formData.backgroundOpacity}
                    onChange={handleRangeChange}
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="buttonOpacity">
                    Button Opacity: {formData.buttonOpacity.toFixed(1)}
                  </label>
                  <input
                    type="range"
                    id="buttonOpacity"
                    name="buttonOpacity"
                    className="form-control-range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={formData.buttonOpacity}
                    onChange={handleRangeChange}
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="practiceAreaBackgroundOpacity">
                    Text Background Opacity: {formData.practiceAreaBackgroundOpacity.toFixed(1)}
                  </label>
                  <input
                    type="range"
                    id="practiceAreaBackgroundOpacity"
                    name="practiceAreaBackgroundOpacity"
                    className="form-control-range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={formData.practiceAreaBackgroundOpacity}
                    onChange={handleRangeChange}
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {activeSection === 'voice' && (
          <div className="tab-content">
            <div className="dashboard-card">
              <h3>Voice Selection</h3>
              <p className="card-description">
                Choose a voice for your AI assistant from our selection of professional voices.
              </p>

              <div className="form-group">
                <label htmlFor="voiceId">Select Voice</label>
                <div className="voice-selector-container" style={{ display: 'flex', alignItems: 'flex-start', gap: '10px' }}>
                  <select
                    id="voiceId"
                    name="voiceId"
                    className="form-control voice-select"
                    value={formData.voiceId}
                    onChange={handleVoiceChange}
                    style={{ flex: 1 }}
                  >
                    {voicePresets.filter(voice => voice.provider === '11labs').length > 0 && (
                      <optgroup label="11labs Voices">
                        {voicePresets.filter(voice => voice.provider === '11labs').map(voice => (
                          <option
                            key={voice.id}
                            value={voice.id}
                            className={`voice-option voice-${voice.gender}`}
                            data-gender={voice.gender}
                          >
                            {voice.name}
                          </option>
                        ))}
                      </optgroup>
                    )}

                    {voicePresets.filter(voice => voice.provider === 'openai').length > 0 && (
                      <optgroup label="OpenAI Voices">
                        {voicePresets.filter(voice => voice.provider === 'openai').map(voice => (
                          <option
                            key={voice.id}
                            value={voice.id}
                            className={`voice-option voice-${voice.gender}`}
                            data-gender={voice.gender}
                          >
                            {voice.name}
                          </option>
                        ))}
                      </optgroup>
                    )}

                    {voicePresets.filter(voice => voice.provider === 'azure').length > 0 && (
                      <optgroup label="Azure Voices">
                        {voicePresets.filter(voice => voice.provider === 'azure').map(voice => (
                          <option
                            key={voice.id}
                            value={voice.id}
                            className={`voice-option voice-${voice.gender}`}
                            data-gender={voice.gender}
                          >
                            {voice.name}
                          </option>
                        ))}
                      </optgroup>
                    )}

                    {voicePresets.filter(voice => voice.provider === 'playht').length > 0 && (
                      <optgroup label="PlayHT Voices">
                        {voicePresets.filter(voice => voice.provider === 'playht').map(voice => (
                          <option
                            key={voice.id}
                            value={voice.id}
                            className={`voice-option voice-${voice.gender}`}
                            data-gender={voice.gender}
                          >
                            {voice.name}
                          </option>
                        ))}
                      </optgroup>
                    )}
                  </select>

                  {/* Voice management buttons */}
                  <div className="voice-management" style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
                    <button
                      type="button"
                      className="btn btn-sm btn-outline-danger"
                      onClick={() => hideVoice(formData.voiceId)}
                      title="Mark this voice as not working and hide it"
                      style={{ fontSize: '11px', padding: '2px 6px', whiteSpace: 'nowrap' }}
                    >
                      ✕ Hide
                    </button>

                    {hiddenVoices.length > 0 && (
                      <button
                        type="button"
                        className="btn btn-sm btn-outline-secondary"
                        onClick={showAllVoices}
                        title={`Show ${hiddenVoices.length} hidden voice(s)`}
                        style={{ fontSize: '11px', padding: '2px 6px', whiteSpace: 'nowrap' }}
                      >
                        Show All
                      </button>
                    )}
                  </div>
                </div>
                <small className="form-text">
                  This voice will be used for all conversations with your AI assistant.
                  {hiddenVoices.length > 0 && ` (${hiddenVoices.length} voice${hiddenVoices.length === 1 ? '' : 's'} hidden)`}
                </small>
              </div>
            </div>

            <div className="dashboard-card">
              <h3>Voice Cloning</h3>
              <p className="card-description">
                Create a personalized AI voice by uploading a recording of your voice.
              </p>

              <div className="voice-clone-section">
                <div className="voice-upload-container">
                  <input
                    type="file"
                    id="voiceFile"
                    ref={voiceFileInputRef}
                    onChange={handleVoiceFileChange}
                    accept="audio/*"
                    style={{ display: 'none' }}
                  />

                  <div className="voice-upload-controls">
                    <button
                      type="button"
                      className="upload-button"
                      onClick={() => voiceFileInputRef.current.click()}
                    >
                      <FaUpload />
                      <span>Upload Voice Recording</span>
                    </button>

                    {voiceFile && (
                      <div className="file-info">
                        <FaVolumeUp />
                        <span>{voiceFile.name}</span>
                      </div>
                    )}
                  </div>

                  <button
                    type="button"
                    className="clone-button"
                    onClick={handleVoiceCloning}
                    disabled={!voiceFile || voiceCloning}
                  >
                    <FaMicrophone />
                    <span>{voiceCloning ? 'Cloning Voice...' : 'Clone Voice'}</span>
                  </button>
                </div>

                <div className="voice-requirements">
                  <h5>Voice Recording Requirements:</h5>
                  <ul>
                    <li>Clear audio with minimal background noise</li>
                    <li>Duration: 30 seconds to 2 minutes</li>
                    <li>Speak naturally in your normal tone</li>
                    <li>Supported formats: MP3, WAV, M4A</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}





        <div className="action-buttons">
          <button
            type="submit"
            className="dashboard-button"
            disabled={loading}
          >
            {loading ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AgentTab;
