/**
 * Assistant Data Refresh Service
 * 
 * Handles refreshing all data when switching between assistants
 */

import { vapiMcpService } from './vapiMcpService';
import { vapiAssistantService } from './vapiAssistantService';
import { callHistoryService } from './callHistoryService';
import { supabase } from '../lib/supabase';

class AssistantDataRefreshService {
  constructor() {
    this.listeners = new Set();
    this.currentAssistantId = null;
  }

  /**
   * Register a listener for assistant data changes
   * @param {Function} callback - Function to call when data refreshes
   */
  addListener(callback) {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  /**
   * Notify all listeners of data refresh
   * @param {string} assistantId - The assistant ID that was switched to
   * @param {Object} data - The refreshed data
   */
  notifyListeners(assistantId, data) {
    this.listeners.forEach(callback => {
      try {
        callback(assistantId, data);
      } catch (error) {
        console.error('Error in data refresh listener:', error);
      }
    });
  }

  /**
   * Refresh all assistant-related data
   * @param {string} attorneyId - Attorney ID
   * @param {string} assistantId - Assistant ID to refresh data for
   * @returns {Promise<Object>} Refreshed data
   */
  async refreshAssistantData(attorneyId, assistantId) {
    try {
      console.log('🔄 Refreshing all data for assistant:', assistantId);
      
      const refreshedData = {
        assistantId,
        timestamp: new Date().toISOString(),
        assistant: null,
        calls: [],
        consultations: [],
        callHistory: [],
        stats: null
      };

      // 1. Load assistant details from Vapi
      try {
        console.log('📋 Loading assistant details from Vapi...');

        // Validate assistant ID format (Vapi IDs are typically shorter than UUIDs)
        if (!assistantId || assistantId.length > 40) {
          throw new Error(`Invalid assistant ID format: ${assistantId}`);
        }

        refreshedData.assistant = await vapiAssistantService.getAssistant(assistantId);

        // Check if we got a mock response
        if (refreshedData.assistant?.mock) {
          console.warn('⚠️ Received mock assistant data from Vapi');
        } else {
          console.log('✅ Assistant details loaded:', refreshedData.assistant?.name);
        }
      } catch (error) {
        console.error('❌ Failed to load assistant details:', error);
        // Set a placeholder assistant to prevent undefined errors
        refreshedData.assistant = {
          id: assistantId,
          name: 'Assistant (Connection Error)',
          error: true
        };
      }

      // 2. Load calls from Vapi for this assistant
      try {
        console.log('📞 Loading calls from Vapi...');
        const allCalls = await vapiMcpService.listCalls();

        // Handle case where no calls exist (404 or empty response)
        if (!allCalls || !Array.isArray(allCalls)) {
          console.log('📞 No calls found in Vapi (empty response)');
          refreshedData.calls = [];
        } else {
          refreshedData.calls = allCalls.filter(call => call.assistantId === assistantId);
          console.log(`✅ Loaded ${refreshedData.calls.length} calls for assistant from ${allCalls.length} total calls`);
        }
      } catch (error) {
        console.error('❌ Failed to load calls from Vapi:', error);
        // Set empty array on error to prevent undefined issues
        refreshedData.calls = [];
      }

      // 3. Load consultations from Supabase for this attorney
      try {
        console.log('📋 Loading consultations from Supabase...');
        const { data: consultations, error } = await supabase
          .from('consultations')
          .select('*')
          .eq('attorney_id', attorneyId)
          .order('created_at', { ascending: false });

        if (error) throw error;
        refreshedData.consultations = consultations || [];
        console.log(`✅ Loaded ${refreshedData.consultations.length} consultations`);
      } catch (error) {
        console.error('❌ Failed to load consultations:', error);
      }

      // 4. Load call history from Supabase
      try {
        console.log('📞 Loading call history from Supabase...');
        const callHistoryResult = await callHistoryService.getCallHistory(attorneyId, {
          limit: 50,
          page: 1
        });
        refreshedData.callHistory = callHistoryResult.calls || [];
        console.log(`✅ Loaded ${refreshedData.callHistory.length} call records`);
      } catch (error) {
        console.error('❌ Failed to load call history:', error);
      }

      // 5. Calculate stats
      try {
        refreshedData.stats = this.calculateStats(refreshedData);
        console.log('✅ Calculated stats:', refreshedData.stats);
      } catch (error) {
        console.error('❌ Failed to calculate stats:', error);
      }

      // 6. Sync new Vapi calls to Supabase
      try {
        await this.syncVapiCallsToSupabase(attorneyId, refreshedData.calls);
      } catch (error) {
        console.error('❌ Failed to sync calls to Supabase:', error);
      }

      // Store current assistant ID
      this.currentAssistantId = assistantId;

      // Notify all listeners
      this.notifyListeners(assistantId, refreshedData);

      console.log('🎉 Successfully refreshed all assistant data');
      return refreshedData;

    } catch (error) {
      console.error('❌ Error refreshing assistant data:', error);
      throw error;
    }
  }

  /**
   * Calculate statistics from the refreshed data
   * @param {Object} data - The refreshed data
   * @returns {Object} Calculated statistics
   */
  calculateStats(data) {
    const stats = {
      totalCalls: data.calls.length,
      totalConsultations: data.consultations.length,
      totalCallHistory: data.callHistory.length,
      recentCalls: data.calls.filter(call => {
        const callDate = new Date(call.createdAt || call.startedAt);
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        return callDate > weekAgo;
      }).length,
      callsByStatus: {}
    };

    // Count calls by status
    data.calls.forEach(call => {
      const status = call.status || 'unknown';
      stats.callsByStatus[status] = (stats.callsByStatus[status] || 0) + 1;
    });

    return stats;
  }

  /**
   * Sync Vapi calls to Supabase call_records table
   * @param {string} attorneyId - Attorney ID
   * @param {Array} calls - Calls from Vapi
   */
  async syncVapiCallsToSupabase(attorneyId, calls) {
    if (!calls.length) return;

    try {
      console.log(`🔄 Syncing ${calls.length} Vapi calls to Supabase...`);

      const callRecords = calls.map(call => ({
        call_id: call.id,
        assistant_id: call.assistantId,
        attorney_id: attorneyId,
        customer_phone: call.customer?.phoneNumber,
        status: call.status,
        start_time: call.createdAt || call.startedAt || new Date().toISOString(),
        end_time: call.endedAt,
        duration: call.duration,
        cost: call.cost,
        metadata: {
          raw_call: call,
          synced_at: new Date().toISOString()
        }
      }));

      // Upsert call records (insert or update if exists)
      const { data, error } = await supabase
        .from('call_records')
        .upsert(callRecords, { 
          onConflict: 'call_id',
          ignoreDuplicates: false 
        })
        .select();

      if (error) throw error;

      console.log(`✅ Synced ${data?.length || 0} call records to Supabase`);
      return data;

    } catch (error) {
      console.error('❌ Error syncing calls to Supabase:', error);
      throw error;
    }
  }

  /**
   * Get current assistant ID
   * @returns {string|null} Current assistant ID
   */
  getCurrentAssistantId() {
    return this.currentAssistantId;
  }

  /**
   * Force refresh current assistant data
   * @param {string} attorneyId - Attorney ID
   */
  async forceRefresh(attorneyId) {
    if (!this.currentAssistantId) {
      console.warn('No current assistant to refresh');
      return null;
    }

    return await this.refreshAssistantData(attorneyId, this.currentAssistantId);
  }
}

// Export singleton instance
export const assistantDataRefreshService = new AssistantDataRefreshService();
export default assistantDataRefreshService;
