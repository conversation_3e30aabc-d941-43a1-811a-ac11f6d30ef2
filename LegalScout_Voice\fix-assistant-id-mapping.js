/**
 * Assistant ID Mapping Fix Script
 * Run this in browser console to diagnose and fix assistant ID issues
 */

console.log('🔧 ASSISTANT ID MAPPING FIX SCRIPT');
console.log('==================================');

async function fixAssistantIdMapping() {
  const results = {
    timestamp: new Date().toISOString(),
    issues: [],
    fixes: [],
    recommendations: []
  };

  try {
    // Step 1: Check current attorney data
    console.log('\n📊 Step 1: Analyzing Current Attorney Data');
    
    const attorneyData = localStorage.getItem('attorney');
    if (!attorneyData) {
      results.issues.push('❌ No attorney data found in localStorage');
      return results;
    }

    const attorney = JSON.parse(attorneyData);
    console.log('Attorney data:', {
      id: attorney.id,
      firm_name: attorney.firm_name,
      vapi_assistant_id: attorney.vapi_assistant_id,
      current_assistant_id: attorney.current_assistant_id
    });

    // Step 2: Validate assistant IDs
    console.log('\n🔍 Step 2: Validating Assistant IDs');
    
    const validateVapiId = (id, label) => {
      if (!id) {
        results.issues.push(`❌ ${label} is null/undefined`);
        return false;
      }
      
      if (id.length > 40) {
        results.issues.push(`❌ ${label} appears to be a UUID (${id.length} chars): ${id}`);
        results.recommendations.push(`💡 ${label} should be replaced with a valid Vapi assistant ID`);
        return false;
      }
      
      if (id.includes('mock') || id.startsWith('mock-')) {
        results.issues.push(`❌ ${label} is a mock ID: ${id}`);
        results.recommendations.push(`💡 ${label} should be replaced with a real Vapi assistant ID`);
        return false;
      }
      
      results.fixes.push(`✅ ${label} appears valid: ${id}`);
      return true;
    };

    const vapiIdValid = validateVapiId(attorney.vapi_assistant_id, 'vapi_assistant_id');
    const currentIdValid = validateVapiId(attorney.current_assistant_id, 'current_assistant_id');

    // Step 3: Check database for assistant configs
    console.log('\n🗄️ Step 3: Checking Database Assistant Configs');
    
    try {
      // Try to access Supabase if available
      if (window.supabase || window.supabaseClient) {
        const supabase = window.supabase || window.supabaseClient;
        
        const { data: configs, error } = await supabase
          .from('assistant_ui_configs')
          .select('assistant_id, assistant_name, firm_name')
          .eq('attorney_id', attorney.id);

        if (error) {
          results.issues.push(`❌ Database query error: ${error.message}`);
        } else {
          console.log('Database assistant configs:', configs);
          
          configs.forEach((config, index) => {
            if (validateVapiId(config.assistant_id, `Database config ${index + 1} assistant_id`)) {
              results.fixes.push(`✅ Found valid assistant in database: ${config.assistant_id} (${config.assistant_name})`);
            }
          });
          
          if (configs.length === 0) {
            results.issues.push('❌ No assistant configs found in database');
            results.recommendations.push('💡 Create assistant configs in database');
          }
        }
      } else {
        results.issues.push('❌ Supabase client not available for database check');
      }
    } catch (dbError) {
      results.issues.push(`❌ Database check failed: ${dbError.message}`);
    }

    // Step 4: Test Vapi connection with current IDs
    console.log('\n🌐 Step 4: Testing Vapi Connection');
    
    const testVapiConnection = async (assistantId, label) => {
      if (!assistantId) return;
      
      try {
        console.log(`Testing ${label}: ${assistantId}`);
        
        // Try to fetch assistant from Vapi
        const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
          headers: {
            'Authorization': `Bearer ${import.meta.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564'}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          const assistantData = await response.json();
          results.fixes.push(`✅ ${label} exists in Vapi: ${assistantData.name}`);
          return assistantData;
        } else {
          results.issues.push(`❌ ${label} not found in Vapi (${response.status}): ${assistantId}`);
          return null;
        }
      } catch (error) {
        results.issues.push(`❌ Error testing ${label}: ${error.message}`);
        return null;
      }
    };

    if (attorney.vapi_assistant_id) {
      await testVapiConnection(attorney.vapi_assistant_id, 'vapi_assistant_id');
    }
    
    if (attorney.current_assistant_id && attorney.current_assistant_id !== attorney.vapi_assistant_id) {
      await testVapiConnection(attorney.current_assistant_id, 'current_assistant_id');
    }

    // Step 5: Generate fix recommendations
    console.log('\n💡 Step 5: Generating Fix Recommendations');
    
    if (results.issues.length > 0) {
      results.recommendations.push('🔧 IMMEDIATE ACTIONS NEEDED:');
      
      if (!vapiIdValid && !currentIdValid) {
        results.recommendations.push('1. Create a new Vapi assistant');
        results.recommendations.push('2. Update attorney record with new assistant ID');
        results.recommendations.push('3. Create assistant_ui_config record');
      } else if (!vapiIdValid) {
        results.recommendations.push('1. Fix vapi_assistant_id in attorney record');
      } else if (!currentIdValid) {
        results.recommendations.push('1. Fix current_assistant_id in attorney record');
      }
      
      results.recommendations.push('4. Clear browser cache and reload');
      results.recommendations.push('5. Test assistant switching functionality');
    }

  } catch (error) {
    results.issues.push(`❌ Script error: ${error.message}`);
  }

  // Generate final report
  console.log('\n📋 ASSISTANT ID MAPPING ANALYSIS COMPLETE');
  console.log('==========================================');
  console.log(`✅ Fixes: ${results.fixes.length}`);
  console.log(`❌ Issues: ${results.issues.length}`);
  console.log(`💡 Recommendations: ${results.recommendations.length}`);
  
  console.log('\n✅ FIXES:');
  results.fixes.forEach(fix => console.log(fix));
  
  console.log('\n❌ ISSUES:');
  results.issues.forEach(issue => console.log(issue));
  
  console.log('\n💡 RECOMMENDATIONS:');
  results.recommendations.forEach(rec => console.log(rec));
  
  // Store results globally
  window.assistantIdMappingResults = results;
  console.log('\n💾 Results saved to window.assistantIdMappingResults');
  
  return results;
}

// Auto-run the fix script
fixAssistantIdMapping();

// Make available globally
window.fixAssistantIdMapping = fixAssistantIdMapping;
