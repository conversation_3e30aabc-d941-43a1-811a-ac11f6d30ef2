<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Production Fixes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #3B82F6;
        }
        .success { border-left-color: #10b981; }
        .error { border-left-color: #ef4444; }
        .info { border-left-color: #f59e0b; }
        button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #2563eb; }
        pre {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 Test Production Fixes</h1>
    <p>Testing the fixes for Supabase headers error and assistant routing issues.</p>

    <div class="test-section">
        <h3>Test Results</h3>
        <div id="results"></div>
    </div>

    <div class="test-section">
        <h3>Actions</h3>
        <button onclick="testSupabaseClient()">Test Supabase Client</button>
        <button onclick="testAssistantRouting()">Test Assistant Routing</button>
        <button onclick="testFullFlow()">Test Full Flow</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <script type="module">
        function addResult(type, message) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `<pre>${message}</pre>`;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        window.addResult = addResult;
        window.clearResults = clearResults;

        window.testSupabaseClient = async function() {
            addResult('info', '🔧 Testing Supabase client initialization...');
            
            try {
                const { getSupabaseClient } = await import('./src/lib/supabase.js');
                
                addResult('success', '✅ Successfully imported Supabase module');
                
                const client = await getSupabaseClient();
                
                if (client) {
                    addResult('success', `✅ Supabase client created: ${typeof client}`);
                    
                    // Test if it's a real client or stub
                    const hasFrom = typeof client.from === 'function';
                    const hasAuth = typeof client.auth === 'object';
                    
                    addResult('info', `Client capabilities:
- Has .from(): ${hasFrom}
- Has .auth: ${hasAuth}
- Type: ${client.constructor?.name || 'Unknown'}`);
                    
                } else {
                    addResult('error', '❌ No Supabase client returned');
                }
                
            } catch (error) {
                addResult('error', `❌ Supabase test failed: ${error.message}`);
                console.error('Supabase test error:', error);
            }
        };

        window.testAssistantRouting = async function() {
            addResult('info', '🎯 Testing assistant routing service...');
            
            try {
                const { assistantRoutingService } = await import('./src/services/assistantRoutingService.js');
                
                addResult('success', '✅ Successfully imported assistant routing service');
                
                // Test getting config for assistant1test
                const config = await assistantRoutingService.getAssistantConfig('assistant1test');
                
                if (config) {
                    addResult('success', `✅ Assistant config loaded:
Firm Name: ${config.firmName}
Assistant ID: ${config.vapi_assistant_id}
Primary Color: ${config.primaryColor}
Has UI Config: ${config.hasAssistantUIConfig}
Loaded Via: ${config.loadedVia}`);
                } else {
                    addResult('error', '❌ No assistant config returned');
                }
                
            } catch (error) {
                addResult('error', `❌ Assistant routing test failed: ${error.message}`);
                console.error('Assistant routing error:', error);
            }
        };

        window.testFullFlow = async function() {
            addResult('info', '🚀 Testing full subdomain resolution flow...');
            
            try {
                // Test both the new App.jsx logic and the config/attorneys.js logic
                
                // Test 1: App.jsx style routing
                addResult('info', '1. Testing App.jsx routing logic...');
                
                try {
                    const { assistantRoutingService } = await import('./src/services/assistantRoutingService.js');
                    const appConfig = await assistantRoutingService.getAssistantConfig('assistant1test');
                    
                    if (appConfig) {
                        addResult('success', `✅ App.jsx routing: ${appConfig.firmName} (${appConfig.vapi_assistant_id})`);
                    } else {
                        addResult('error', '❌ App.jsx routing failed');
                    }
                } catch (appError) {
                    addResult('error', `❌ App.jsx routing error: ${appError.message}`);
                    
                    // Test fallback
                    addResult('info', '1b. Testing fallback to loadAttorneyConfig...');
                    try {
                        const { loadAttorneyConfig } = await import('./src/config/attorneys.js');
                        const fallbackConfig = await loadAttorneyConfig('assistant1test');
                        
                        if (fallbackConfig) {
                            addResult('success', `✅ Fallback routing: ${fallbackConfig.firmName} (${fallbackConfig.vapi_assistant_id})`);
                        } else {
                            addResult('error', '❌ Fallback routing also failed');
                        }
                    } catch (fallbackError) {
                        addResult('error', `❌ Fallback routing error: ${fallbackError.message}`);
                    }
                }
                
                // Test 2: Direct config/attorneys.js
                addResult('info', '2. Testing config/attorneys.js directly...');
                
                try {
                    const { loadAttorneyConfig } = await import('./src/config/attorneys.js');
                    const directConfig = await loadAttorneyConfig('assistant1test');
                    
                    if (directConfig) {
                        addResult('success', `✅ Direct config: ${directConfig.firmName} (${directConfig.vapi_assistant_id})`);
                    } else {
                        addResult('error', '❌ Direct config failed');
                    }
                } catch (directError) {
                    addResult('error', `❌ Direct config error: ${directError.message}`);
                }
                
            } catch (error) {
                addResult('error', `❌ Full flow test failed: ${error.message}`);
                console.error('Full flow error:', error);
            }
        };

        // Auto-run tests on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testSupabaseClient();
                setTimeout(() => {
                    testAssistantRouting();
                    setTimeout(() => {
                        testFullFlow();
                    }, 1000);
                }, 1000);
            }, 1000);
        });
    </script>
</body>
</html>
