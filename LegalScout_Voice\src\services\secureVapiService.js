/**
 * Secure Vapi Service
 * 
 * This service provides secure access to Vapi operations through a server-side proxy,
 * ensuring that secret API keys are never exposed to the client.
 */

class SecureVapiService {
  constructor() {
    this.baseUrl = '/api/vapi-proxy';
  }

  /**
   * Make a secure API call to the Vapi proxy
   * @param {string} action - The action to perform
   * @param {Object} params - Parameters for the action
   * @returns {Promise<Object>} The API response
   */
  async makeSecureCall(action, params = {}) {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          ...params
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'API call failed');
      }

      return result.data;
    } catch (error) {
      console.error(`Secure Vapi call failed (${action}):`, error);
      throw error;
    }
  }

  /**
   * Get an assistant by ID
   * @param {string} assistantId - The assistant ID
   * @returns {Promise<Object>} The assistant data
   */
  async getAssistant(assistantId) {
    return this.makeSecureCall('getAssistant', { assistantId });
  }

  /**
   * List all assistants
   * @returns {Promise<Array>} Array of assistants
   */
  async listAssistants() {
    return this.makeSecureCall('listAssistants');
  }

  /**
   * Create a new assistant
   * @param {Object} config - Assistant configuration
   * @returns {Promise<Object>} The created assistant
   */
  async createAssistant(config) {
    return this.makeSecureCall('createAssistant', { config });
  }

  /**
   * Update an existing assistant
   * @param {string} assistantId - The assistant ID
   * @param {Object} updates - Updates to apply
   * @returns {Promise<Object>} The updated assistant
   */
  async updateAssistant(assistantId, updates) {
    return this.makeSecureCall('updateAssistant', { assistantId, updates });
  }

  /**
   * Create a call
   * @param {Object} callConfig - Call configuration
   * @returns {Promise<Object>} The created call
   */
  async createCall(callConfig) {
    return this.makeSecureCall('createCall', { callConfig });
  }

  /**
   * Verify assistant exists and is accessible
   * @param {string} assistantId - The assistant ID to verify
   * @returns {Promise<boolean>} Whether the assistant exists
   */
  async verifyAssistant(assistantId) {
    try {
      const assistant = await this.getAssistant(assistantId);
      return !!assistant;
    } catch (error) {
      console.warn('Assistant verification failed:', error);
      return false;
    }
  }

  /**
   * Ensure an assistant exists for an attorney, creating one if needed
   * @param {Object} attorneyConfig - Attorney configuration
   * @returns {Promise<Object>} Assistant information
   */
  async ensureAssistantExists(attorneyConfig) {
    try {
      // Check if assistant already exists
      if (attorneyConfig.vapi_assistant_id) {
        const exists = await this.verifyAssistant(attorneyConfig.vapi_assistant_id);
        if (exists) {
          return {
            id: attorneyConfig.vapi_assistant_id,
            status: 'verified',
            action: 'none'
          };
        }
      }

      // Create new assistant
      const assistantConfig = {
        name: attorneyConfig.firmName || 'LegalScout Assistant',
        firstMessage: attorneyConfig.welcomeMessage || 'Hello, how can I help you today?',
        firstMessageMode: "assistant-speaks-first",
        model: {
          provider: "openai",
          model: attorneyConfig.aiModel || "gpt-4o",
          messages: [
            {
              role: "system",
              content: attorneyConfig.vapiInstructions || "You are a helpful legal assistant."
            }
          ]
        },
        voice: {
          provider: "openai",
          voiceId: "alloy"
        },
        transcriber: {
          provider: "deepgram",
          model: "nova-3"
        }
      };

      const newAssistant = await this.createAssistant(assistantConfig);

      return {
        id: newAssistant.id,
        name: newAssistant.name,
        status: 'created',
        action: 'created'
      };

    } catch (error) {
      console.error('Failed to ensure assistant exists:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const secureVapiService = new SecureVapiService();
export default secureVapiService;
