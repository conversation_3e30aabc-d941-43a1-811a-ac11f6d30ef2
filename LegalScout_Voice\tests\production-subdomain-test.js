/**
 * Production Subdomain Test
 * 
 * This script can be run directly in the browser console on assistant1test.legalscout.net
 * to diagnose what's happening in the live environment.
 */

(function() {
    'use strict';
    
    const TEST_CONFIG = {
        subdomain: 'assistant1test',
        expectedAssistantId: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d',
        expectedAttorneyId: '87756a2c-a398-43f2-889a-b8815684df71',
        expectedFirmName: 'LegalScout'
    };

    class ProductionSubdomainTest {
        constructor() {
            this.results = [];
        }

        log(test, status, message, data = null) {
            const result = { test, status, message, data, timestamp: new Date().toISOString() };
            this.results.push(result);
            
            const emoji = { 'PASS': '✅', 'FAIL': '❌', 'WARN': '⚠️', 'INFO': 'ℹ️' }[status] || '📋';
            console.log(`${emoji} [${test}] ${message}`);
            if (data) console.log('   Data:', data);
        }

        /**
         * Test 1: Current Page Analysis
         */
        testCurrentPage() {
            const url = window.location.href;
            const hostname = window.location.hostname;
            const subdomain = hostname.split('.')[0];
            
            this.log('PAGE_ANALYSIS', 'INFO', 'Current page analysis', {
                url,
                hostname,
                detectedSubdomain: subdomain,
                expectedSubdomain: TEST_CONFIG.subdomain,
                subdomainMatch: subdomain === TEST_CONFIG.subdomain
            });

            if (subdomain === TEST_CONFIG.subdomain) {
                this.log('PAGE_ANALYSIS', 'PASS', 'Subdomain detection correct');
                return true;
            } else {
                this.log('PAGE_ANALYSIS', 'FAIL', `Subdomain mismatch: expected ${TEST_CONFIG.subdomain}, got ${subdomain}`);
                return false;
            }
        }

        /**
         * Test 2: DOM Content Analysis
         */
        testDOMContent() {
            const title = document.title;
            const bodyText = document.body.innerText.toLowerCase();
            
            // Look for firm name in content
            const hasLegalScout = bodyText.includes('legalscout') || title.toLowerCase().includes('legalscout');
            const hasStartConsultation = bodyText.includes('start consultation');
            const hasDefaultContent = bodyText.includes('your law firm') || bodyText.includes('default');
            
            this.log('DOM_CONTENT', 'INFO', 'DOM content analysis', {
                title,
                hasLegalScout,
                hasStartConsultation,
                hasDefaultContent,
                bodyLength: document.body.innerText.length
            });

            if (hasLegalScout && !hasDefaultContent) {
                this.log('DOM_CONTENT', 'PASS', 'Page shows correct LegalScout content');
                return true;
            } else if (hasDefaultContent) {
                this.log('DOM_CONTENT', 'FAIL', 'Page shows default/fallback content instead of LegalScout');
                return false;
            } else {
                this.log('DOM_CONTENT', 'WARN', 'Page content unclear - may be loading');
                return null;
            }
        }

        /**
         * Test 3: JavaScript State Analysis
         */
        testJavaScriptState() {
            // Check for global variables that might indicate app state
            const appState = {
                hasReact: typeof React !== 'undefined',
                hasVapi: typeof window.vapi !== 'undefined',
                hasSupabase: typeof window.supabase !== 'undefined',
                windowVars: Object.keys(window).filter(key => 
                    key.includes('attorney') || 
                    key.includes('assistant') || 
                    key.includes('legal') ||
                    key.includes('vapi')
                )
            };

            this.log('JS_STATE', 'INFO', 'JavaScript state analysis', appState);

            // Check for error indicators in console
            const hasConsoleErrors = this.checkConsoleErrors();
            
            if (hasConsoleErrors) {
                this.log('JS_STATE', 'FAIL', 'Console errors detected - check browser console');
                return false;
            } else {
                this.log('JS_STATE', 'PASS', 'No obvious JavaScript errors');
                return true;
            }
        }

        /**
         * Test 4: Network Requests Analysis
         */
        async testNetworkRequests() {
            try {
                // Try to make the same database request the app should make
                const supabaseUrl = 'https://utopqxsvudgrtiwenlzl.supabase.co';
                const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
                
                const url = `${supabaseUrl}/rest/v1/v_subdomain_assistant_lookup?subdomain=eq.${TEST_CONFIG.subdomain}&is_active=eq.true`;
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'apikey': supabaseKey,
                        'Authorization': `Bearer ${supabaseKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    this.log('NETWORK_REQUESTS', 'FAIL', `Database request failed: ${response.status}`);
                    return false;
                }

                const data = await response.json();
                
                if (!data || data.length === 0) {
                    this.log('NETWORK_REQUESTS', 'FAIL', 'No assistant mapping found in database');
                    return false;
                }

                const mapping = data[0];
                
                this.log('NETWORK_REQUESTS', 'PASS', 'Database request successful', {
                    assistant_id: mapping.assistant_id,
                    attorney_id: mapping.attorney_id,
                    firm_name: mapping.firm_name,
                    subdomain: mapping.subdomain
                });

                // Validate the mapping
                if (mapping.assistant_id === TEST_CONFIG.expectedAssistantId &&
                    mapping.attorney_id === TEST_CONFIG.expectedAttorneyId) {
                    this.log('NETWORK_REQUESTS', 'PASS', 'Database mapping is correct');
                    return true;
                } else {
                    this.log('NETWORK_REQUESTS', 'FAIL', 'Database mapping has incorrect data');
                    return false;
                }
            } catch (error) {
                this.log('NETWORK_REQUESTS', 'FAIL', `Network request error: ${error.message}`);
                return false;
            }
        }

        /**
         * Test 5: App Routing Analysis
         */
        testAppRouting() {
            // Check if the app is using the correct routing logic
            const currentPath = window.location.pathname;
            const hasHash = window.location.hash;
            
            // Look for signs of React Router or other routing
            const hasReactRouter = document.querySelector('[data-reactroot]') !== null;
            
            this.log('APP_ROUTING', 'INFO', 'App routing analysis', {
                currentPath,
                hasHash,
                hasReactRouter,
                isRoot: currentPath === '/',
                isSubdomainRoot: currentPath === '/' && window.location.hostname.includes(TEST_CONFIG.subdomain)
            });

            if (currentPath === '/' && window.location.hostname.includes(TEST_CONFIG.subdomain)) {
                this.log('APP_ROUTING', 'PASS', 'Correct subdomain root routing');
                return true;
            } else {
                this.log('APP_ROUTING', 'WARN', 'Routing may not be optimal');
                return null;
            }
        }

        /**
         * Check for console errors
         */
        checkConsoleErrors() {
            // This is a simplified check - in reality, we'd need to hook into console.error
            // For now, we'll just return false and let the user check manually
            return false;
        }

        /**
         * Run all production tests
         */
        async runAllTests() {
            console.log('🧪 Starting Production Subdomain Test...\n');
            
            const tests = [
                { name: 'Current Page Analysis', fn: () => this.testCurrentPage() },
                { name: 'DOM Content Analysis', fn: () => this.testDOMContent() },
                { name: 'JavaScript State Analysis', fn: () => this.testJavaScriptState() },
                { name: 'Network Requests Analysis', fn: () => this.testNetworkRequests() },
                { name: 'App Routing Analysis', fn: () => this.testAppRouting() }
            ];

            const results = {};
            
            for (const test of tests) {
                console.log(`\n📋 Running: ${test.name}`);
                try {
                    results[test.name] = await test.fn();
                } catch (error) {
                    this.log(test.name.toUpperCase().replace(' ', '_'), 'FAIL', `Test crashed: ${error.message}`);
                    results[test.name] = false;
                }
            }

            // Summary
            console.log('\n📊 Production Test Summary:');
            const passed = this.results.filter(r => r.status === 'PASS').length;
            const failed = this.results.filter(r => r.status === 'FAIL').length;
            const warnings = this.results.filter(r => r.status === 'WARN').length;
            
            console.log(`✅ Passed: ${passed}`);
            console.log(`❌ Failed: ${failed}`);
            console.log(`⚠️ Warnings: ${warnings}`);
            
            if (failed === 0) {
                console.log('\n🎉 Production tests look good!');
            } else {
                console.log('\n🚨 Production issues detected.');
            }

            // Recommendations
            console.log('\n💡 Recommendations:');
            if (failed > 0) {
                console.log('1. Check browser console for JavaScript errors');
                console.log('2. Verify database connectivity');
                console.log('3. Check if the latest deployment is live');
                console.log('4. Verify Supabase configuration');
            } else {
                console.log('1. The subdomain system appears to be working correctly');
                console.log('2. If you still see default content, try a hard refresh (Ctrl+F5)');
            }

            return {
                passed,
                failed,
                warnings,
                results: this.results,
                testResults: results
            };
        }
    }

    // Make it globally available
    window.ProductionSubdomainTest = ProductionSubdomainTest;
    
    // Auto-run function
    window.runProductionTest = async () => {
        const test = new ProductionSubdomainTest();
        return await test.runAllTests();
    };
    
    console.log('🧪 Production Subdomain Test loaded. Run runProductionTest() to start.');
})();
