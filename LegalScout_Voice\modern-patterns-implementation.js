/**
 * Modern Patterns Implementation
 * 
 * These are elegant, modern patterns you can selectively adopt
 * to enhance your existing architecture without breaking changes.
 */

// 1. ENHANCED SERVICE ORCHESTRATOR PATTERN
export class ServiceOrchestrator {
  constructor() {
    this.services = new Map();
    this.middleware = [];
    this.eventBus = new EventTarget();
    this.healthChecks = new Map();
  }

  register(name, service, healthCheck = null) {
    this.services.set(name, service);
    if (healthCheck) {
      this.healthChecks.set(name, healthCheck);
    }
    
    this.eventBus.dispatchEvent(new CustomEvent('serviceRegistered', {
      detail: { name, service }
    }));
  }

  compose(...serviceNames) {
    const services = serviceNames.map(name => this.services.get(name));
    return new ComposedService(services, this.middleware);
  }

  async healthCheck() {
    const results = new Map();
    
    for (const [name, check] of this.healthChecks) {
      try {
        const result = await check();
        results.set(name, { status: 'healthy', ...result });
      } catch (error) {
        results.set(name, { status: 'unhealthy', error: error.message });
      }
    }
    
    return results;
  }
}

// 2. ADAPTIVE COMPONENT PATTERN
export const useAdaptiveUI = (context) => {
  const [adaptations, setAdaptations] = useState({});
  
  useEffect(() => {
    const detectCapabilities = async () => {
      const capabilities = {
        hasVapi: await checkVapiAvailability(),
        hasMCP: await checkMCPAvailability(),
        hasOAuth: await checkOAuthStatus(),
        screenSize: getScreenSize(),
        connectionSpeed: await getConnectionSpeed()
      };
      
      const adaptations = {
        useCompactUI: capabilities.screenSize === 'small',
        enableAdvancedFeatures: capabilities.connectionSpeed === 'fast',
        showFallbacks: !capabilities.hasVapi || !capabilities.hasMCP,
        useProgressiveLoading: capabilities.connectionSpeed === 'slow'
      };
      
      setAdaptations(adaptations);
    };
    
    detectCapabilities();
  }, [context]);
  
  return adaptations;
};

// 3. ENHANCED MCP SERVICE MESH
export class MCPServiceMesh {
  constructor() {
    this.protocols = ['streamable-http', 'sse', 'websocket'];
    this.services = new Map();
    this.loadBalancer = new LoadBalancer();
    this.circuitBreaker = new CircuitBreaker();
    this.retryPolicy = new RetryPolicy();
  }

  async connect(serviceName, config) {
    const service = new MCPService(serviceName, config);
    
    // Try protocols in order of preference
    for (const protocol of this.protocols) {
      try {
        await service.connect(protocol);
        this.services.set(serviceName, service);
        return service;
      } catch (error) {
        console.warn(`Failed to connect via ${protocol}:`, error);
      }
    }
    
    throw new Error(`Failed to connect to ${serviceName} via any protocol`);
  }

  async callService(serviceName, method, params) {
    const service = this.services.get(serviceName);
    if (!service) {
      throw new Error(`Service ${serviceName} not found`);
    }

    return this.circuitBreaker.execute(async () => {
      return this.retryPolicy.execute(async () => {
        return service.call(method, params);
      });
    });
  }
}

// 4. PROGRESSIVE FEATURE LOADER
export const ProgressiveFeatureLoader = ({ features, priority = 'normal', children }) => {
  const [loadedFeatures, setLoadedFeatures] = useState(new Set());
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadFeatures = async () => {
      const priorityOrder = {
        critical: features.filter(f => f.priority === 'critical'),
        high: features.filter(f => f.priority === 'high'),
        normal: features.filter(f => f.priority === 'normal'),
        low: features.filter(f => f.priority === 'low')
      };

      // Load critical features first
      for (const feature of priorityOrder.critical) {
        try {
          await feature.load();
          setLoadedFeatures(prev => new Set([...prev, feature.name]));
        } catch (error) {
          console.error(`Failed to load critical feature ${feature.name}:`, error);
        }
      }

      // Load other features progressively
      const otherFeatures = [
        ...priorityOrder.high,
        ...priorityOrder.normal,
        ...priorityOrder.low
      ];

      for (const feature of otherFeatures) {
        try {
          await feature.load();
          setLoadedFeatures(prev => new Set([...prev, feature.name]));
        } catch (error) {
          console.warn(`Failed to load feature ${feature.name}:`, error);
        }
      }

      setLoading(false);
    };

    loadFeatures();
  }, [features]);

  return (
    <FeatureProvider value={{ loadedFeatures, loading }}>
      {children}
    </FeatureProvider>
  );
};

// 5. DYNAMIC CONFIGURATION MANAGER
export class DynamicConfigManager {
  constructor() {
    this.configStore = new Map();
    this.watchers = new Map();
    this.validators = new Map();
    this.history = [];
    this.maxHistorySize = 100;
  }

  register(key, defaultValue, validator = null) {
    this.configStore.set(key, defaultValue);
    if (validator) {
      this.validators.set(key, validator);
    }
  }

  watch(key, callback) {
    if (!this.watchers.has(key)) {
      this.watchers.set(key, new Set());
    }
    this.watchers.get(key).add(callback);

    // Return unwatch function
    return () => {
      this.watchers.get(key)?.delete(callback);
    };
  }

  async updateConfig(key, value, source = 'manual') {
    const validator = this.validators.get(key);
    if (validator && !await validator(value)) {
      throw new Error(`Invalid value for config key: ${key}`);
    }

    const oldValue = this.configStore.get(key);
    this.configStore.set(key, value);

    // Record history
    this.history.push({
      timestamp: Date.now(),
      key,
      oldValue,
      newValue: value,
      source
    });

    // Trim history if needed
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
    }

    // Notify watchers
    this.notifyWatchers(key, value, oldValue);
  }

  notifyWatchers(key, newValue, oldValue) {
    const watchers = this.watchers.get(key);
    if (watchers) {
      watchers.forEach(callback => {
        try {
          callback(newValue, oldValue, key);
        } catch (error) {
          console.error(`Error in config watcher for ${key}:`, error);
        }
      });
    }
  }

  get(key) {
    return this.configStore.get(key);
  }

  getHistory(key = null) {
    if (key) {
      return this.history.filter(entry => entry.key === key);
    }
    return [...this.history];
  }
}

// 6. ENHANCED ERROR BOUNDARY WITH RECOVERY
export class EnhancedErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
      recoveryStrategies: []
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({ errorInfo });
    
    // Determine recovery strategies
    const strategies = this.determineRecoveryStrategies(error);
    this.setState({ recoveryStrategies: strategies });

    // Report error
    this.reportError(error, errorInfo);
  }

  determineRecoveryStrategies(error) {
    const strategies = [];

    if (error.message.includes('network') || error.message.includes('fetch')) {
      strategies.push({
        name: 'Retry with exponential backoff',
        action: () => this.retryWithBackoff()
      });
    }

    if (error.message.includes('assistant') || error.message.includes('vapi')) {
      strategies.push({
        name: 'Fallback to default assistant',
        action: () => this.fallbackToDefault()
      });
    }

    strategies.push({
      name: 'Reset component state',
      action: () => this.resetState()
    });

    return strategies;
  }

  async retryWithBackoff() {
    const delay = Math.pow(2, this.state.retryCount) * 1000;
    await new Promise(resolve => setTimeout(resolve, delay));
    
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1
    }));
  }

  render() {
    if (this.state.hasError) {
      return (
        <ErrorRecoveryUI
          error={this.state.error}
          strategies={this.state.recoveryStrategies}
          onRecover={(strategy) => strategy.action()}
          fallback={this.props.fallback}
        />
      );
    }

    return this.props.children;
  }
}

// Usage Examples:

// 1. Service Orchestration
const orchestrator = new ServiceOrchestrator();
orchestrator.register('vapi', vapiService, () => vapiService.healthCheck());
orchestrator.register('supabase', supabaseService, () => supabaseService.ping());

// 2. Adaptive UI
const AdaptiveAssistantDropdown = ({ attorney, onAssistantChange }) => {
  const adaptations = useAdaptiveUI({ attorney, viewport: 'desktop' });
  
  return (
    <div className={`assistant-dropdown ${adaptations.useCompactUI ? 'compact' : 'full'}`}>
      {adaptations.enableAdvancedFeatures && <AdvancedFilters />}
      <AssistantList />
      {adaptations.showFallbacks && <FallbackOptions />}
    </div>
  );
};

// 3. Progressive Loading
const DashboardWithProgressiveFeatures = () => {
  const features = [
    { name: 'vapi', priority: 'critical', load: () => import('./VapiService') },
    { name: 'analytics', priority: 'normal', load: () => import('./Analytics') },
    { name: 'advanced-tools', priority: 'low', load: () => import('./AdvancedTools') }
  ];

  return (
    <ProgressiveFeatureLoader features={features}>
      <Dashboard />
    </ProgressiveFeatureLoader>
  );
};
