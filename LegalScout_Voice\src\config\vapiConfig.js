/**
 * Centralized Vapi Configuration
 *
 * This is the SINGLE source of truth for all Vapi API keys and configuration.
 * All other files should import from this file instead of accessing environment variables directly.
 */

// Environment variable access helpers
const getEnvVar = (key) => {
  // Try import.meta.env first (Vite), then process.env (Node.js), then window (runtime)
  if (typeof import.meta !== 'undefined' && import.meta.env && import.meta.env[key]) {
    return import.meta.env[key];
  }
  if (typeof process !== 'undefined' && process.env && process.env[key]) {
    return process.env[key];
  }
  if (typeof window !== 'undefined' && window[key]) {
    return window[key];
  }
  return null;
};

// Get API keys from environment variables with proper fallbacks
// PRODUCTION FIX: Always ensure we have the correct keys
const VAPI_PUBLIC_KEY = getEnvVar('VITE_VAPI_PUBLIC_KEY') || '310f0d43-27c2-47a5-a76d-e55171d024f7';
const VAPI_SECRET_KEY = getEnvVar('VITE_VAPI_SECRET_KEY') ||
                       getEnvVar('VITE_VAPI_PRIVATE_KEY') ||
                       getEnvVar('VAPI_SECRET_KEY') ||
                       getEnvVar('VAPI_TOKEN') ||
                       getEnvVar('VAPI_PRIVATE_KEY') ||
                       '6734febc-fc65-4669-93b0-929b31ff6564';

// PRODUCTION SAFETY: If environment variables failed, use hardcoded values
// This ensures the app works even if Vite doesn't inject the env vars properly
const FINAL_PUBLIC_KEY = VAPI_PUBLIC_KEY || '310f0d43-27c2-47a5-a76d-e55171d024f7';
const FINAL_SECRET_KEY = VAPI_SECRET_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';

// Debug logging will be done in the getVapiApiKey function when actually used

// Determine environment
const isDevelopment =
  getEnvVar('NODE_ENV') === 'development' ||
  getEnvVar('MODE') === 'development' ||
  (typeof window !== 'undefined' && window.location?.hostname === 'localhost');

/**
 * Get the appropriate API key for the given operation type
 * @param {string} operationType - 'client' for client-side operations, 'server' for server-side operations
 * @returns {string} The appropriate API key
 */
export const getVapiApiKey = (operationType = 'client') => {
  // Debug logging for production troubleshooting (only in browser)
  if (typeof window !== 'undefined' && window.location) {
    const isProduction = window.location.hostname.includes('vercel.app') ||
                        window.location.hostname.includes('legalscout.net');

    if (isProduction) {
      console.log('[VapiConfig] Production environment detected');
      console.log('[VapiConfig] Available keys:', {
        hasPublic: !!FINAL_PUBLIC_KEY,
        hasSecret: !!FINAL_SECRET_KEY,
        publicKeyPrefix: FINAL_PUBLIC_KEY ? FINAL_PUBLIC_KEY.substring(0, 8) + '...' : 'none',
        secretKeyPrefix: FINAL_SECRET_KEY ? FINAL_SECRET_KEY.substring(0, 8) + '...' : 'none'
      });
    }
  }

  // For server-side operations (assistant management, phone numbers, etc.), prefer secret key
  if (operationType === 'server') {
    const key = FINAL_SECRET_KEY || FINAL_PUBLIC_KEY;
    console.log(`[VapiConfig] Using ${FINAL_SECRET_KEY ? 'SECRET' : 'PUBLIC'} key for server operations (${operationType})`);
    console.log(`[VapiConfig] Server key: ${key ? key.substring(0, 8) + '...' : 'none'}`);
    return key;
  }

  // For client-side operations (voice calls), use public key
  if (operationType === 'client') {
    const key = FINAL_PUBLIC_KEY;
    console.log('[VapiConfig] Using PUBLIC key for client operations');
    console.log(`[VapiConfig] Client key: ${key ? key.substring(0, 8) + '...' : 'none'}`);
    return key;
  }

  // Default fallback
  const key = FINAL_PUBLIC_KEY || FINAL_SECRET_KEY;
  console.log(`[VapiConfig] Using ${FINAL_PUBLIC_KEY ? 'PUBLIC' : 'SECRET'} key for default operations`);
  console.log(`[VapiConfig] Default key: ${key ? key.substring(0, 8) + '...' : 'none'}`);
  return key;
};

/**
 * Validate that we have the required API keys
 * @returns {Object} Validation result
 */
export const validateVapiConfig = () => {
  const hasPublicKey = !!FINAL_PUBLIC_KEY;
  const hasSecretKey = !!FINAL_SECRET_KEY;

  return {
    isValid: hasPublicKey, // At minimum we need a public key
    hasPublicKey,
    hasSecretKey,
    warnings: [
      !hasPublicKey && 'Missing VITE_VAPI_PUBLIC_KEY - client operations will fail',
      !hasSecretKey && 'Missing VITE_VAPI_SECRET_KEY - server operations may be limited'
    ].filter(Boolean)
  };
};

/**
 * Get Vapi configuration object
 * @returns {Object} Complete Vapi configuration
 */
export const getVapiConfig = () => {
  return {
    // API Keys - use the final guaranteed keys
    publicKey: FINAL_PUBLIC_KEY,
    secretKey: FINAL_SECRET_KEY,

    // API Endpoints
    apiUrl: 'https://api.vapi.ai',
    mcpUrl: 'https://mcp.vapi.ai/mcp', // Updated to use Streamable HTTP endpoint

    // Default Configuration
    defaultVoice: 'echo', // OpenAI voice - reliable and commonly used
    defaultProvider: 'openai',
    defaultModel: 'gpt-4o',

    // Environment
    isDevelopment,

    // Helper functions
    getApiKey: getVapiApiKey,
    validate: validateVapiConfig
  };
};

// Export individual pieces for backward compatibility
export const VAPI_CONFIG = getVapiConfig();
export { FINAL_PUBLIC_KEY as VAPI_PUBLIC_KEY, FINAL_SECRET_KEY as VAPI_SECRET_KEY };

// Default export
export default VAPI_CONFIG;
