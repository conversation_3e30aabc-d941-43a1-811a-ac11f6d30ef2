// Google Auth Test - Run this in browser console
// Copy and paste this entire function into your browser console and run it

window.testGoogleAuth = async function() {
  console.log('🧪 Starting Google Auth Test...');
  
  // Test 1: Check if functions exist
  console.log('📋 Test 1: Function availability');
  try {
    const { signInWithGoogle, isSupabaseConfigured } = await import('/src/lib/supabase.js');
    console.log('✅ signInWithGoogle function imported:', typeof signInWithGoogle);
    console.log('✅ isSupabaseConfigured function imported:', typeof isSupabaseConfigured);
    
    // Test 2: Check Supabase configuration
    console.log('📋 Test 2: Supabase configuration');
    const configured = isSupabaseConfigured();
    console.log('✅ Supabase configured:', configured);
    
    if (!configured) {
      console.log('❌ Supabase not configured - this might be the issue');
      console.log('Environment variables:');
      console.log('- VITE_SUPABASE_URL:', import.meta.env.VITE_SUPABASE_URL ? 'SET' : 'NOT SET');
      console.log('- VITE_SUPABASE_KEY:', import.meta.env.VITE_SUPABASE_KEY ? 'SET' : 'NOT SET');
      return;
    }
    
    // Test 3: Try to get Supabase client
    console.log('📋 Test 3: Supabase client initialization');
    const { getSupabaseClient } = await import('/src/lib/supabase.js');
    const supabase = await getSupabaseClient();
    console.log('✅ Supabase client created:', !!supabase);
    
    // Test 4: Test OAuth call (without actually redirecting)
    console.log('📋 Test 4: OAuth configuration test');
    console.log('Current origin:', window.location.origin);
    console.log('Redirect URL would be:', `${window.location.origin}/auth/callback`);
    
    // Test 5: Try the actual sign-in (this will redirect if it works)
    console.log('📋 Test 5: Attempting actual Google sign-in...');
    console.log('⚠️ This will redirect to Google if successful');
    
    const result = await signInWithGoogle();
    console.log('✅ Sign-in result:', result);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
  }
};

// Also create a simple button click test
window.testButtonClick = function() {
  console.log('🧪 Testing button click...');
  
  // Find the Google sign-in button
  const button = document.querySelector('.google-button');
  if (!button) {
    console.error('❌ Google button not found');
    return;
  }
  
  console.log('✅ Google button found:', button);
  console.log('Button disabled:', button.disabled);
  console.log('Button onclick:', button.onclick);
  console.log('Button event listeners:', getEventListeners ? getEventListeners(button) : 'getEventListeners not available');
  
  // Try to click it programmatically
  console.log('🖱️ Simulating button click...');
  button.click();
};

console.log('🧪 Google Auth Test loaded!');
console.log('Run testGoogleAuth() to test the auth flow');
console.log('Run testButtonClick() to test button functionality');
