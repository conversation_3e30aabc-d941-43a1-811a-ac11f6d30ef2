/**
 * useAssistantAwareCopy Hook
 * 
 * Provides assistant-aware copy functionality with feedback.
 * All copy operations automatically use the current assistant's context.
 */

import { useState, useCallback, useEffect } from 'react';
import { useAssistantAware } from '../contexts/AssistantAwareContext';

export const useAssistantAwareCopy = () => {
  const [copyStatus, setCopyStatus] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  
  const {
    urls,
    embedCodes,
    copyToClipboard,
    currentAssistant,
    isAssistantSelected
  } = useAssistantAware();

  // DEBUG: Log what URLs we're getting
  useEffect(() => {
    console.log('🔍 [useAssistantAwareCopy] URLs updated:', {
      shareUrl: urls?.shareUrl,
      currentAssistant: currentAssistant?.id,
      isAssistantSelected,
      assistantSubdomain: currentAssistant?.subdomain
    });
  }, [urls, currentAssistant, isAssistantSelected]);

  // No success messages - removed per user request

  // Copy functions for common use cases
  const copyShareUrl = useCallback(async () => {
    setIsLoading(true);
    return await copyToClipboard(urls.shareUrl, 'Share URL');
  }, [copyToClipboard, urls.shareUrl]);

  const copyEmbedCode = useCallback(async (type = 'iframe') => {
    setIsLoading(true);
    const code = embedCodes[type];
    if (!code) {
      setIsLoading(false);
      return false;
    }
    return await copyToClipboard(code, `${type.charAt(0).toUpperCase() + type.slice(1)} embed code`);
  }, [copyToClipboard, embedCodes]);

  const copyIframeEmbed = useCallback(async () => {
    return await copyEmbedCode('iframe');
  }, [copyEmbedCode]);

  const copyWidgetEmbed = useCallback(async () => {
    return await copyEmbedCode('widget');
  }, [copyEmbedCode]);

  const copyButtonEmbed = useCallback(async () => {
    return await copyEmbedCode('button');
  }, [copyEmbedCode]);

  const copyCustomText = useCallback(async (text, type = 'Text') => {
    setIsLoading(true);
    return await copyToClipboard(text, type);
  }, [copyToClipboard]);

  // Generate shareable content
  const generateShareableContent = useCallback((platform = 'generic') => {
    if (!isAssistantSelected) {
      return {
        text: 'Check out this AI legal assistant!',
        url: 'https://legalscout.net'
      };
    }

    const firmName = currentAssistant.firmName || 'Our Firm';
    const shareUrl = urls.shareUrl;

    const templates = {
      generic: {
        text: `Get instant legal guidance from ${firmName}'s AI assistant. Available 24/7 to help with your legal questions.`,
        url: shareUrl
      },
      email: {
        subject: `Legal Assistance from ${firmName}`,
        body: `Hi,\n\nI wanted to share ${firmName}'s AI legal assistant with you. It's available 24/7 to help answer legal questions and provide guidance.\n\nYou can access it here: ${shareUrl}\n\nBest regards`
      },
      social: {
        text: `🏛️ Need legal help? ${firmName}'s AI assistant is here to help 24/7! Get instant guidance on your legal questions.`,
        url: shareUrl,
        hashtags: ['LegalTech', 'AIAssistant', 'LegalHelp']
      },
      sms: {
        text: `${firmName}'s AI legal assistant can help with your legal questions 24/7: ${shareUrl}`
      }
    };

    return templates[platform] || templates.generic;
  }, [isAssistantSelected, currentAssistant, urls.shareUrl]);

  // Copy shareable content for different platforms
  const copyForPlatform = useCallback(async (platform) => {
    const content = generateShareableContent(platform);
    
    let textToCopy;
    switch (platform) {
      case 'email':
        textToCopy = `Subject: ${content.subject}\n\n${content.body}`;
        break;
      case 'social':
        textToCopy = `${content.text}\n${content.url}\n\n${content.hashtags.map(tag => `#${tag}`).join(' ')}`;
        break;
      case 'sms':
        textToCopy = content.text;
        break;
      default:
        textToCopy = `${content.text}\n\n${content.url}`;
    }

    return await copyCustomText(textToCopy, `${platform.charAt(0).toUpperCase() + platform.slice(1)} share content`);
  }, [generateShareableContent, copyCustomText]);

  // Get status message with assistant context
  const getStatusMessage = useCallback(() => {
    if (!copyStatus) return null;

    const assistantName = currentAssistant?.firmName || 'assistant';
    const baseMessage = copyStatus.message;

    if (copyStatus.success && isAssistantSelected) {
      return `${baseMessage} (for ${assistantName})`;
    }

    return baseMessage;
  }, [copyStatus, currentAssistant, isAssistantSelected]);

  return {
    // Status
    copyStatus,
    isLoading,
    statusMessage: getStatusMessage(),
    
    // Quick copy functions
    copyShareUrl,
    copyIframeEmbed,
    copyWidgetEmbed,
    copyButtonEmbed,
    copyCustomText,
    
    // Platform-specific sharing
    copyForPlatform,
    generateShareableContent,
    
    // Current values (for display)
    shareUrl: urls.shareUrl,
    embedCodes,
    
    // Assistant context
    isAssistantSelected,
    assistantName: currentAssistant?.firmName || currentAssistant?.attorneyName,
    subdomain: currentAssistant?.subdomain,
    
    // Clear status manually
    clearStatus: () => setCopyStatus(null)
  };
};
