/**
 * SubdomainTester - Utility for testing subdomains in local development
 *
 * This utility allows you to simulate different subdomains in local development
 * by storing the current simulated subdomain in localStorage and providing
 * methods to switch between them.
 */

import { extractSubdomainFromWindow } from './subdomainExtraction.js';

const LOCAL_STORAGE_KEY = 'legalscout_test_subdomain';

/**
 * Get the current test subdomain from localStorage
 * @returns {string} Current test subdomain or null if not set
 */
export const getTestSubdomain = () => {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem(LOCAL_STORAGE_KEY);
};

/**
 * Set a test subdomain in localStorage
 * @param {string} subdomain - Subdomain to simulate
 */
export const setTestSubdomain = (subdomain) => {
  if (typeof window === 'undefined') return;
  if (subdomain) {
    localStorage.setItem(LOCAL_STORAGE_KEY, subdomain);
    console.log(`Test subdomain set to: ${subdomain}`);
  } else {
    localStorage.removeItem(LOCAL_STORAGE_KEY);
    console.log('Test subdomain cleared');
  }
};

/**
 * Clear the test subdomain from localStorage
 */
export const clearTestSubdomain = () => {
  if (typeof window === 'undefined') return;
  localStorage.removeItem(LOCAL_STORAGE_KEY);
  console.log('Test subdomain cleared');
};

/**
 * Determine the current subdomain with test subdomain support
 *
 * Returns the test subdomain if set in localStorage, otherwise extracts
 * the subdomain from the hostname.
 *
 * @returns {string} Current subdomain or 'default' if none is found
 */
export const getCurrentSubdomain = () => {
  if (typeof window === 'undefined') return 'default';

  // CRITICAL FIX: For localhost, always return 'default' unless test subdomain is explicitly set
  const hostname = window.location.hostname;
  if (hostname === 'localhost' || hostname === '127.0.0.1' || /^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
    // Only check for test subdomain on localhost
    const testSubdomain = getTestSubdomain();
    if (testSubdomain) {
      console.log('🧪 [SubdomainTester] Using test subdomain on localhost:', testSubdomain);
      return testSubdomain;
    }
    console.log('🏠 [SubdomainTester] Localhost detected, returning default subdomain');
    return 'default';
  }

  // For production domains, use normal extraction logic
  return extractSubdomainFromWindow();
};

/**
 * Create a subdomain test selector component
 *
 * This function creates and returns a DOM element with buttons to switch
 * between different test subdomains. The component is intended for development
 * use only and should not be displayed in production.
 *
 * @param {Array<string>} subdomains - List of available subdomains
 * @param {Function} onSelect - Callback function when a subdomain is selected
 * @returns {HTMLElement} DOM element with subdomain selector buttons
 */
export const createSubdomainSelector = (subdomains = [], onSelect = () => {}) => {
  if (typeof window === 'undefined' || !Array.isArray(subdomains)) return null;

  // Create container
  const container = document.createElement('div');
  container.id = 'subdomain-tester';
  container.style.position = 'fixed';
  container.style.bottom = '10px';
  container.style.right = '10px';
  container.style.zIndex = '9999';
  container.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
  container.style.padding = '10px';
  container.style.borderRadius = '5px';
  container.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.3)';
  container.style.color = 'white';
  container.style.fontFamily = 'Arial, sans-serif';
  container.style.fontSize = '12px';

  // Add title
  const title = document.createElement('div');
  title.textContent = 'Test Subdomains';
  title.style.fontWeight = 'bold';
  title.style.marginBottom = '5px';
  title.style.borderBottom = '1px solid #555';
  title.style.paddingBottom = '5px';
  container.appendChild(title);

  // Add subdomain buttons
  const buttonContainer = document.createElement('div');
  buttonContainer.style.display = 'flex';
  buttonContainer.style.flexWrap = 'wrap';
  buttonContainer.style.gap = '5px';

  // Add 'default' option
  const defaultBtn = createButton('default', getCurrentSubdomain() === 'default');
  defaultBtn.onclick = () => {
    clearTestSubdomain();
    highlightButton(defaultBtn);
    onSelect('default');
    window.location.reload();
  };
  buttonContainer.appendChild(defaultBtn);

  // Add custom subdomain buttons
  subdomains.forEach(subdomain => {
    if (subdomain === 'default') return; // Skip default, already added

    const btn = createButton(subdomain, getCurrentSubdomain() === subdomain);
    btn.onclick = () => {
      setTestSubdomain(subdomain);
      highlightButton(btn);
      onSelect(subdomain);
      window.location.reload();
    };
    buttonContainer.appendChild(btn);
  });

  container.appendChild(buttonContainer);
  return container;
};

// Helper function to create a button
function createButton(text, isActive) {
  const btn = document.createElement('button');
  btn.textContent = text;
  btn.style.padding = '3px 8px';
  btn.style.borderRadius = '3px';
  btn.style.border = 'none';
  btn.style.cursor = 'pointer';
  btn.style.fontSize = '11px';

  if (isActive) {
    btn.style.backgroundColor = '#007bff';
    btn.style.color = 'white';
  } else {
    btn.style.backgroundColor = '#e0e0e0';
    btn.style.color = '#333';
  }

  return btn;
}

// Helper function to highlight the active button
function highlightButton(activeBtn) {
  if (!activeBtn.parentNode) return;

  const buttons = activeBtn.parentNode.querySelectorAll('button');
  buttons.forEach(btn => {
    if (btn === activeBtn) {
      btn.style.backgroundColor = '#007bff';
      btn.style.color = 'white';
    } else {
      btn.style.backgroundColor = '#e0e0e0';
      btn.style.color = '#333';
    }
  });
}