import Vapi from '@vapi-ai/web';
import { supabaseService } from './supabaseService';

// Default Vapi tool IDs
const VAPI_TOOL_IDS = {
  LIVE_DOSSIER: "4a0d63cf-0b84-4eec-bddf-9c5869439d7e",
  CHECK_MATCHING_ATTORNEYS: "313b71f6-f4ea-44d4-a304-f5050e890693",
  LOCAL_ATTORNEY_SEARCH: "42375b0b-9c0b-481b-b445-d219b41f1988",
  GET_USER_INFO: "40e60896-518c-470a-a713-7abc2cd0c924"
};

// Make.com webhook URLs for tool integrations
const MAKE_WEBHOOK_URLS = {
  LIVE_DOSSIER: "https://hook.us1.make.com/2c6ez4oq26ka5kv7h7zjhiy4v18l20t1",
  CHECK_MATCHING_ATTORNEYS: "https://hook.us1.make.com/4empo1t13htixhbkz7bs9ettvxs8g0j5",
  GET_USER_INFO: "https://hook.us1.make.com/2c6ez4oq26ka5kv7h7zjhiy4v18l20t1",
  LOCAL_ATTORNEY_SEARCH: "https://hook.us1.make.com/g6k2w1raan6ovyodlvfoevdtuuw09hb6"
};

// Import the default assistant ID from constants
import { DEFAULT_ASSISTANT_ID } from '../constants/vapiConstants';

/**
 * Service for handling all Vapi-related functionality
 */
class VapiService {
  constructor() {
    // Store listener references for proper cleanup
    this.listeners = {
      'call-started': null,
      'call-end': null,
      'error': null,
      'speech-start': null,
      'speech-end': null,
      'message': null,
      'transcription': null,
      'tool-execution': null
    };

    // Instance variables for event listener references
    this.eventListeners = new Map();
  }

  /**
   * Initialize the Vapi instance
   * @param {string} apiKey - The Vapi API key
   * @returns {Object} Vapi instance
   */
  createVapiInstance(apiKey) {
    if (!apiKey) {
      console.error('Missing API key for Vapi');
      throw new Error('Missing API key for Vapi');
    }

    try {
      console.log(`🔑 [VapiService] Creating Vapi instance with API key: ${apiKey.substring(0, 8)}...`);
      console.log(`🔑 [VapiService] API key type: ${apiKey.startsWith('310f0d43') ? 'PUBLIC' : 'PRIVATE'}`);

      // Create Vapi instance with just the API key
      const vapiInstance = new Vapi(apiKey);

      if (!vapiInstance) {
        throw new Error('Failed to create Vapi instance');
      }

      console.log('Vapi instance created successfully');
      return vapiInstance;
    } catch (error) {
      console.error('Error in createVapiInstance:', error);
      throw error;
    }
  }

  /**
   * Set up event listeners for the Vapi instance
   * @param {Object} vapiInstance - The Vapi instance
   * @param {Object} callbacks - The callback functions
   */
  setupEventListeners(vapiInstance, callbacks = {}) {
    // Early return if no vapi instance
    if (!vapiInstance) {
      console.warn("No Vapi instance provided to setupEventListeners");
      return;
    }

    try {
      // Store listeners in an instance variable for later removal
      if (!this.eventListeners) {
        this.eventListeners = new Map();
      }
      this.eventListeners.clear();

      // Get the target object to attach listeners to
      let listenerTarget = vapiInstance;

      // Handle v2.x SDK where listeners might be on client
      if (!typeof listenerTarget.on === 'function' &&
          listenerTarget.client &&
          typeof listenerTarget.client.on === 'function') {
        console.log("Using client object for event listeners");
        listenerTarget = listenerTarget.client;
      }

      // Check if the target has the 'on' method
      if (typeof listenerTarget.on !== 'function') {
        console.warn("Listener target doesn't have 'on' method. Available methods:",
          Object.keys(listenerTarget).filter(key => typeof listenerTarget[key] === 'function'));

        // For v2.x SDK where the event system might be different
        if (typeof listenerTarget.addEventListener === 'function') {
          console.log("Using addEventListener instead of on");
          // Create a proxy to match the expected API
          listenerTarget.on = (eventName, callback) => {
            listenerTarget.addEventListener(eventName, callback);
          };
        } else {
          console.error("No viable event listener method found");
          return;
        }
      }

      // Helper to safely add an event listener
      const safelyAddListener = (eventName, callback) => {
        if (!callback) return;

        // Create a wrapped callback that handles errors
        const wrappedCallback = (...args) => {
          try {
            callback(...args);
          } catch (error) {
            console.error(`Error in ${eventName} event handler:`, error);
          }
        };

        // Store a reference to the wrapped callback
        this.eventListeners.set(eventName, wrappedCallback);

        // Add the listener
        try {
          listenerTarget.on(eventName, wrappedCallback);
          console.log(`Added listener for ${eventName}`);
        } catch (error) {
          console.error(`Error adding listener for ${eventName}:`, error);
        }
      };

      // Add listeners for each event type with provided callbacks
      safelyAddListener('call-start', callbacks.onCallStart);
      safelyAddListener('call-end', callbacks.onCallEnd);
      safelyAddListener('error', callbacks.onError);
      safelyAddListener('speech-start', callbacks.onSpeechStart);
      safelyAddListener('speech-end', callbacks.onSpeechEnd);
      safelyAddListener('message', callbacks.onMessage);
      safelyAddListener('transcription', callbacks.onTranscription);
      safelyAddListener('transcript', callbacks.onTranscript); // Some versions use transcript instead of transcription
      safelyAddListener('tool-execution', callbacks.onToolExecution);

      // Add model-output handler for direct message content
      safelyAddListener('model-output', callbacks.onModelOutput);

      // Add backward compatibility for call-started (some versions use this instead of call-start)
      safelyAddListener('call-started', callbacks.onCallStarted);

      // v2.x specific events
      safelyAddListener('volume-level', callbacks.onVolumeLevel);

      // Add speech recognition events
      safelyAddListener('speech-recognition-start', callbacks.onSpeechRecognitionStart);
      safelyAddListener('speech-recognition-end', callbacks.onSpeechRecognitionEnd);
      safelyAddListener('speech-recognition-result', callbacks.onSpeechRecognitionResult);

      console.log('Vapi event listeners set up successfully');
    } catch (error) {
      console.error('Error setting up Vapi event listeners:', error);
      throw error;
    }
  }

  /**
   * Remove event listeners from the Vapi instance
   * @param {Object} vapiInstance - The Vapi instance
   */
  removeEventListeners(vapiInstance) {
    if (!vapiInstance || !this.eventListeners) {
      return;
    }

    try {
      // Get the target object to remove listeners from
      let listenerTarget = vapiInstance;

      // Handle v2.x SDK where listeners might be on client
      if (!typeof listenerTarget.off === 'function' &&
          listenerTarget.client &&
          typeof listenerTarget.client.off === 'function') {
        console.log("Using client object for removing event listeners");
        listenerTarget = listenerTarget.client;
      }

      // Check for remove method - may be 'off' or 'removeEventListener' depending on SDK version
      const hasOffMethod = typeof listenerTarget.off === 'function';
      const hasRemoveMethod = typeof listenerTarget.removeEventListener === 'function';

      if (!hasOffMethod && !hasRemoveMethod) {
        console.warn("Listener target doesn't have methods to remove listeners. Available methods:",
          Object.keys(listenerTarget).filter(key => typeof listenerTarget[key] === 'function'));
        return;
      }

      // Remove each listener
      this.eventListeners.forEach((callback, eventName) => {
        console.log(`Removing listener for ${eventName}`);
        try {
          if (hasOffMethod) {
            listenerTarget.off(eventName, callback);
          } else if (hasRemoveMethod) {
            listenerTarget.removeEventListener(eventName, callback);
          }
        } catch (error) {
          console.warn(`Error removing listener for ${eventName}:`, error);
        }
      });

      // Also try to remove all listeners for common event types
      // This is a fallback in case some listeners weren't properly tracked
      const commonEventTypes = [
        'call-start', 'call-started', 'call-end',
        'error', 'speech-start', 'speech-end',
        'message', 'transcription', 'transcript',
        'tool-execution', 'volume-level', 'model-output',
        'speech-recognition-start', 'speech-recognition-end',
        'speech-recognition-result'
      ];

      if (hasOffMethod) {
        commonEventTypes.forEach(eventType => {
          try {
            // Try to remove all listeners for this event type
            if (typeof listenerTarget.removeAllListeners === 'function') {
              listenerTarget.removeAllListeners(eventType);
              console.log(`Removed all listeners for ${eventType}`);
            } else {
              // If removeAllListeners is not available, try to remove the specific listener
              const listener = this.eventListeners.get(eventType);
              if (listener) {
                listenerTarget.off(eventType, listener);
                console.log(`Removed specific listener for ${eventType}`);
              }
            }
          } catch (error) {
            console.warn(`Error removing listeners for ${eventType}:`, error);
          }
        });
      }

      // Clear the stored listeners
      this.eventListeners.clear();
      console.log("Vapi event listeners removed successfully");
    } catch (error) {
      console.error("Error removing Vapi event listeners:", error);
      // Don't throw the error, just log it
      console.warn("Continuing despite error removing event listeners");
    }
  }

  /**
   * Make a direct API call to Vapi as a fallback when the SDK has issues
   * @param {string} apiKey - The Vapi API key
   * @param {Object} payload - The payload to send to the API
   * @returns {Promise<Object>} - The API response
   */
  async directVapiCall(apiKey, payload) {
    if (!apiKey) {
      throw new Error('API key is required for direct Vapi API call');
    }

    if (!payload || !payload.assistantId) {
      throw new Error('Valid payload with assistantId is required');
    }

    console.log('Making direct API call to Vapi with payload:', {
      ...payload,
      assistantId: payload.assistantId.substring(0, 8) + '...'
    });

    const headers = new Headers();
    headers.append('Content-Type', 'application/json');
    headers.append('Authorization', `Bearer ${apiKey}`);

    const apiUrl = 'https://api.vapi.ai/call/web';

    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`Vapi API returned ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Direct API call successful');
      return data;
    } catch (error) {
      console.error('Direct Vapi API call failed:', error);
      throw error;
    }
  }

  // Static property to track if a call is in progress
  static isCallInProgress = false;

  /**
   * Start a call with the given Vapi instance
   * @param {Object} vapiInstance - The Vapi instance
   * @param {Object} params - Call parameters
   * @returns {Promise<boolean>} - True if call started successfully
   */
  async startCall(vapiInstance, params = {}) {
    try {
      if (!vapiInstance) {
        throw new Error('No Vapi instance provided');
      }

      // Check if a call is already in progress
      if (VapiService.isCallInProgress) {
        console.log("A call is already in progress. Cleaning up before starting a new call...");

        // Try to clean up any existing Daily.co iframes
        try {
          const existingIframes = document.querySelectorAll('iframe[title*="daily"]');
          if (existingIframes.length > 0) {
            console.log(`Found ${existingIframes.length} existing Daily.co iframes. Removing...`);
            existingIframes.forEach(iframe => {
              iframe.parentNode.removeChild(iframe);
            });
          }

          // Wait a moment for cleanup to complete
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (cleanupError) {
          console.warn("Error cleaning up existing iframes:", cleanupError);
        }
      }

      // Mark that a call is now in progress
      VapiService.isCallInProgress = true;

      const { assistantId, assistantOverrides } = params;

      if (!assistantId) {
        throw new Error('No assistant ID provided');
      }

      // Ensure assistantId is a string
      const assistantIdStr = String(assistantId);

      console.log(`Starting Vapi call with assistant ID: ${assistantIdStr.substring(0, 8)}...`);

      // If no assistantOverrides are provided, start the call with just the assistantId
      if (!assistantOverrides) {
        console.log('No assistantOverrides provided, using default assistant configuration');

        // Special handling for the default Scout assistant
        if (assistantIdStr === 'e3fff1dd-2e82-4cce-ac6c-8c3271eb0865') {
          console.log('Using default Scout assistant with ID e3fff1dd-2e82-4cce-ac6c-8c3271eb0865');
          console.log('Starting call with ABSOLUTELY NO OVERRIDES to ensure default voice and first message are used');
          console.log('This should use the voice configured in the Vapi dashboard');

          // Start the call with just the assistantId, no second parameter at all
          await vapiInstance.start(assistantIdStr);
          console.log('Vapi call started successfully with default assistant configuration');
          return true;
        } else {
          // For other assistants, use the default behavior
          await vapiInstance.start(assistantIdStr);
          console.log('Vapi call started successfully with default assistant configuration');
          return true;
        }
      }

      // According to Vapi docs, we should pass assistantOverrides directly to the start method
      // as the second parameter
      console.log('Using assistantOverrides directly as the second parameter to start()');

      // Check for system prompt in the correct Vapi API format (model.messages array)
      if (assistantOverrides.model && assistantOverrides.model.messages) {
        const systemMessage = assistantOverrides.model.messages.find(m => m.role === 'system');
        if (systemMessage) {
          console.log('System prompt found in model.messages:', {
            length: systemMessage.content.length,
            preview: systemMessage.content.substring(0, 100) + '...'
          });
        } else {
          console.warn('No system message found in model.messages array');
        }
      } else {
        console.warn('No model.messages found in assistantOverrides');
      }

      // Check for firstMessage in various places
      if (!assistantOverrides.firstMessage) {
        console.warn('No firstMessage found in assistantOverrides, checking other properties');

        // Try to get firstMessage from other properties
        if (assistantOverrides.initialMessage) {
          assistantOverrides.firstMessage = assistantOverrides.initialMessage;
          console.log('Using initialMessage as firstMessage:', assistantOverrides.firstMessage);
        } else if (assistantOverrides.welcomeMessage) {
          assistantOverrides.firstMessage = assistantOverrides.welcomeMessage;
          console.log('Using welcomeMessage as firstMessage:', assistantOverrides.firstMessage);
        } else if (params && params.initialMessage) {
          assistantOverrides.firstMessage = params.initialMessage;
          console.log('Using params.initialMessage as firstMessage:', assistantOverrides.firstMessage);
        } else if (params && params.welcomeMessage) {
          assistantOverrides.firstMessage = params.welcomeMessage;
          console.log('Using params.welcomeMessage as firstMessage:', assistantOverrides.firstMessage);
        }
      }

      // Always ensure firstMessageMode is set if firstMessage is provided
      if (assistantOverrides.firstMessage && !assistantOverrides.firstMessageMode) {
        assistantOverrides.firstMessageMode = 'assistant-speaks-first';
        console.log('Setting firstMessageMode to assistant-speaks-first');
      }

      // Log the full assistantOverrides for debugging
      console.log('Full assistantOverrides:', JSON.stringify(assistantOverrides, null, 2));

      // Log the assistantOverrides for debugging
      console.log('assistantOverrides being used for payload:', {
        firstMessage: assistantOverrides.firstMessage || 'Not present',
        firstMessageMode: assistantOverrides.firstMessageMode || 'Not present',
        model: assistantOverrides.model ? 'Present' : 'Not present',
        artifactPlan: assistantOverrides.artifactPlan ? 'Present' : 'Not present'
      });

      // Log the system prompt if present
      if (assistantOverrides.model && assistantOverrides.model.messages) {
        const systemMessage = assistantOverrides.model.messages.find(m => m.role === 'system');
        if (systemMessage) {
          console.log('System prompt found in assistantOverrides.model.messages:', {
            length: systemMessage.content.length,
            preview: systemMessage.content.substring(0, 100) + '...'
          });
        }
      }

      // Log the assistantOverrides for debugging
      console.log('Raw assistantOverrides being sent to Vapi API:', JSON.stringify(assistantOverrides, null, 2));

      // Log the final assistantOverrides for debugging
      console.log('Starting call with assistantOverrides:', {
        ...assistantOverrides,
        id: assistantIdStr.substring(0, 8) + '...'
      });

      // Debug log for assistantOverrides
      if (assistantOverrides) {
        console.log('Using assistantOverrides in startCall:', {
          firstMessage: assistantOverrides.firstMessage ? 'Present' : 'Not present',
          initialMessage: assistantOverrides.initialMessage ? 'Present' : 'Not present',
          systemPrompt: assistantOverrides.systemPrompt ? 'Present' : 'Not present',
          systemPromptLength: assistantOverrides.systemPrompt ? assistantOverrides.systemPrompt.length : 0
        });
      }

      // Start the call using the SDK's start method
      try {
        // Always set firstMessageMode to ensure the assistant speaks first
        assistantOverrides.firstMessageMode = 'assistant-speaks-first';
        console.log('Setting firstMessageMode to assistant-speaks-first');

        // Always set firstMessageInterruptionsEnabled to false
        assistantOverrides.firstMessageInterruptionsEnabled = false;
        console.log('Setting firstMessageInterruptionsEnabled to false');

        // Make sure firstMessage is set
        if (!assistantOverrides.firstMessage) {
          // Try to get firstMessage from various sources
          if (params && params.initialMessage) {
            assistantOverrides.firstMessage = params.initialMessage;
            console.log('Setting firstMessage from params.initialMessage:', assistantOverrides.firstMessage);
          } else if (params && params.assistantOverrides && params.assistantOverrides.firstMessage) {
            assistantOverrides.firstMessage = params.assistantOverrides.firstMessage;
            console.log('Setting firstMessage from params.assistantOverrides.firstMessage:', assistantOverrides.firstMessage);
          } else if (params && params.welcomeMessage) {
            assistantOverrides.firstMessage = params.welcomeMessage;
            console.log('Setting firstMessage from params.welcomeMessage:', assistantOverrides.firstMessage);
          } else if (params && params.assistantOverrides && params.assistantOverrides.welcomeMessage) {
            assistantOverrides.firstMessage = params.assistantOverrides.welcomeMessage;
            console.log('Setting firstMessage from params.assistantOverrides.welcomeMessage:', assistantOverrides.firstMessage);
          }
        }

        // Remove systemPrompt property if it exists - it's not supported by Vapi API
        // Also ensure firstMessage is properly set
        const { systemPrompt, ...cleanedAssistantOverrides } = assistantOverrides;

        // CRITICAL FIX: Make sure firstMessage is set from various possible sources
        // First check customInstructions directly
        if (params && params.customInstructions) {
          if (params.customInstructions.welcomeMessage) {
            cleanedAssistantOverrides.firstMessage = params.customInstructions.welcomeMessage;
            console.log('Setting firstMessage from params.customInstructions.welcomeMessage:', cleanedAssistantOverrides.firstMessage);
          } else if (params.customInstructions.initialMessage) {
            cleanedAssistantOverrides.firstMessage = params.customInstructions.initialMessage;
            console.log('Setting firstMessage from params.customInstructions.initialMessage:', cleanedAssistantOverrides.firstMessage);
          }
        }
        // Special handling for the default Scout assistant
        if (assistantIdStr === 'e3fff1dd-2e82-4cce-ac6c-8c3271eb0865') {
          console.log('Default Scout assistant detected - not setting any firstMessage override');
          // Remove firstMessage if it exists
          if (cleanedAssistantOverrides.firstMessage) {
            delete cleanedAssistantOverrides.firstMessage;
          }
          // Remove firstMessageMode if it exists
          if (cleanedAssistantOverrides.firstMessageMode) {
            delete cleanedAssistantOverrides.firstMessageMode;
          }
        } else {
          // For other assistants, check other sources if still not set
          if (!cleanedAssistantOverrides.firstMessage) {
            if (params && params.initialMessage) {
              cleanedAssistantOverrides.firstMessage = params.initialMessage;
              console.log('Setting firstMessage from params.initialMessage:', cleanedAssistantOverrides.firstMessage);
            } else if (params && params.welcomeMessage) {
              cleanedAssistantOverrides.firstMessage = params.welcomeMessage;
              console.log('Setting firstMessage from params.welcomeMessage:', cleanedAssistantOverrides.firstMessage);
            }
          }

          // Always ensure firstMessageMode is set for non-default assistants
          cleanedAssistantOverrides.firstMessageMode = 'assistant-speaks-first';

          // If firstMessage is still not set, provide a default for non-default assistants
          if (!cleanedAssistantOverrides.firstMessage) {
            cleanedAssistantOverrides.firstMessage = "Hello, I'm Scout from LegalScout. How can I help you today?";
            console.log('Using default firstMessage:', cleanedAssistantOverrides.firstMessage);
          }

          // Log the final firstMessage for debugging
          console.log('Final firstMessage being used:', cleanedAssistantOverrides.firstMessage);
        }

        if (systemPrompt) {
          console.log('Removing systemPrompt property from assistantOverrides as it is not supported by Vapi API');
        }

        // Remove any debug properties that shouldn't be sent to the API
        if (cleanedAssistantOverrides.__DEV__) {
          console.log('Removing __DEV__ property from assistantOverrides as it is not supported by Vapi API');
          delete cleanedAssistantOverrides.__DEV__;
        }

        // Remove any other properties that might cause issues with the API
        const validProperties = [
          'firstMessage', 'firstMessageMode', 'firstMessageInterruptionsEnabled',
          'voice', 'recordingEnabled', 'model', 'instructions', 'artifactPlan'
        ];

        // Create a clean object with only valid properties
        const apiSafeOverrides = {};
        for (const prop of validProperties) {
          if (cleanedAssistantOverrides[prop] !== undefined) {
            apiSafeOverrides[prop] = cleanedAssistantOverrides[prop];
          }
        }

        // Special handling for the default Scout assistant
        let finalOverrides;
        if (assistantIdStr === 'e3fff1dd-2e82-4cce-ac6c-8c3271eb0865') {
          // For the default Scout assistant, remove ALL overrides
          console.log('Default Scout assistant detected - removing ALL overrides to use default configuration');

          // Don't pass any overrides at all for the default assistant
          finalOverrides = {};

          console.log('Using empty finalOverrides for default Scout assistant to ensure default voice and first message are used');
        } else {
          finalOverrides = { ...apiSafeOverrides };

          // Continue with non-default assistant handling
          // For other assistants, ensure voice configuration is correct
          if (finalOverrides.voice) {
            // If using playht, make sure we have a valid voiceId
            if (finalOverrides.voice.provider === 'playht') {
              // PlayHT requires a full S3 URL for voice cloning
              if (!finalOverrides.voice.voiceId.startsWith('s3://')) {
                console.log('Switching from PlayHT to 11labs because voiceId is not an S3 URL');
                finalOverrides.voice.provider = '11labs';
                finalOverrides.voice.voiceId = 'sarah';
              }
            }

            // Ensure we have a valid provider and voiceId
            if (!finalOverrides.voice.provider || !finalOverrides.voice.voiceId) {
              console.log('Setting default voice provider and voiceId');
              finalOverrides.voice.provider = '11labs';
              finalOverrides.voice.voiceId = 'sarah';
            }
          } else {
            // If no voice configuration, add a default one
            console.log('No voice configuration found, adding default');
            finalOverrides.voice = {
              provider: '11labs',
              voiceId: 'sarah'
            };
          }
        }

        // Log the full assistantOverrides for debugging
        console.log('Full assistantOverrides being sent to Vapi:', JSON.stringify(finalOverrides, null, 2));

        console.log('Starting call with assistantOverrides:', {
          firstMessage: finalOverrides.firstMessage ? 'Present: ' + finalOverrides.firstMessage : 'Not present',
          firstMessageMode: finalOverrides.firstMessageMode,
          firstMessageInterruptionsEnabled: finalOverrides.firstMessageInterruptionsEnabled,
          voice: finalOverrides.voice ? {
            provider: finalOverrides.voice.provider,
            voiceId: finalOverrides.voice.voiceId ? finalOverrides.voice.voiceId.substring(0, 8) + '...' : 'Not specified'
          } : 'Not specified'
        });

        await vapiInstance.start(assistantIdStr, finalOverrides);
        console.log('Vapi call started successfully');

        // DO NOT manually send the first message - let Vapi handle it
        // This was causing the issue where the welcome message wasn't being spoken
      } catch (error) {
        console.error('Error in vapiInstance.start:', error);
        console.warn('Continuing despite error in vapiInstance.start');
        // Don't throw the error, just log it and continue
      }

      return true;
    } catch (error) {
      console.error('Error starting Vapi call:', error);
      console.warn('Continuing despite error in startCall');
      // Don't throw the error, just log it and return true to allow the application to continue
      return true;
    }
  }

  /**
   * Extract the API key from a Vapi instance
   * @param {Object} vapiInstance - The Vapi instance
   * @returns {string|null} - The API key or null if not found
   */
  extractApiKey(vapiInstance) {
    if (!vapiInstance) return null;

    // Check common locations
    if (vapiInstance._token) return vapiInstance._token;
    if (vapiInstance.token) return vapiInstance.token;
    if (vapiInstance.apiKey) return vapiInstance.apiKey;
    if (vapiInstance._apiKey) return vapiInstance._apiKey;

    // Check in client object
    if (vapiInstance.client) {
      if (vapiInstance.client._token) return vapiInstance.client._token;
      if (vapiInstance.client.token) return vapiInstance.client.token;
      if (vapiInstance.client.apiKey) return vapiInstance.client.apiKey;
      if (vapiInstance.client._apiKey) return vapiInstance.client._apiKey;
    }

    // Try to find it recursively (limited depth)
    const findApiKey = (obj, depth = 0) => {
      if (!obj || typeof obj !== 'object' || depth > 3) return null;

      for (const [key, value] of Object.entries(obj)) {
        if ((key === 'token' || key === '_token' || key === 'apiKey' || key === '_apiKey')
            && typeof value === 'string') {
          return value;
        } else if (typeof value === 'object' && value !== null) {
          const found = findApiKey(value, depth + 1);
          if (found) return found;
        }
      }
      return null;
    };

    return findApiKey(vapiInstance);
  }

  /**
   * Stop the Vapi call
   * @param {Object} vapiInstance - The Vapi instance
   * @returns {boolean} - Whether the call was successfully stopped
   */
  stopCall(vapiInstance) {
    if (!vapiInstance) {
      console.warn("Cannot stop call: No Vapi instance provided");
      return false;
    }

    // Reset the call in progress flag
    VapiService.isCallInProgress = false;

    // Set the global variable to indicate that the call is no longer active
    // This is critical to prevent race conditions during cleanup
    if (typeof window !== 'undefined') {
      window.vapiCallActive = false;
      console.log("Set window.vapiCallActive to false in vapiService.stopCall");
    }

    console.log("Attempting to stop Vapi call with instance:", {
      hasStopMethod: typeof vapiInstance.stop === 'function',
      instanceType: typeof vapiInstance,
      availableMethods: Object.keys(vapiInstance).filter(key => typeof vapiInstance[key] === 'function')
    });

    // Clean up any lingering Daily.co iframes
    try {
      if (typeof document !== 'undefined') {
        const existingIframes = document.querySelectorAll('iframe[title*="daily"]');
        if (existingIframes.length > 0) {
          console.log(`Found ${existingIframes.length} lingering Daily.co iframes. Removing during vapiService.stopCall...`);
          existingIframes.forEach(iframe => {
            iframe.parentNode.removeChild(iframe);
          });
        }
      }
    } catch (cleanupError) {
      console.warn("Error cleaning up iframes during vapiService.stopCall:", cleanupError);
    }

    try {
      // First remove all event listeners to prevent any callbacks during cleanup
      this.removeEventListeners(vapiInstance);

      // If the vapi instance has a stop method, call it
      if (typeof vapiInstance.stop === 'function') {
        // Check if the instance has a callId property
        const callId = vapiInstance.callId || null;

        if (callId) {
          console.log(`Stopping Vapi call with ID: ${callId}`);
          // Try to stop with callId first
          try {
            vapiInstance.stop(callId);
            console.log(`Vapi call with ID ${callId} stopped successfully`);
          } catch (callIdError) {
            console.warn(`Error stopping call with ID, falling back to regular stop:`, callIdError);
            vapiInstance.stop();
          }
        } else {
          // No callId, use regular stop
          vapiInstance.stop();
          console.log("Vapi call stopped successfully");
        }

        // Try to manually trigger the call-end event if it exists
        if (typeof vapiInstance.emit === 'function') {
          try {
            console.log("Manually triggering call-end event");
            vapiInstance.emit('call-end', { forced: true, reason: 'service_stop_call' });
          } catch (emitError) {
            console.warn("Error manually triggering call-end event:", emitError);
          }
        }

        // Force cleanup of the Vapi instance
        if (vapiInstance.client) {
          console.log("Cleaning up Vapi client");
          if (typeof vapiInstance.client.close === 'function') {
            try {
              vapiInstance.client.close();
              console.log("Vapi client closed successfully");
            } catch (closeError) {
              console.warn("Error closing Vapi client:", closeError);
            }
          }
        }

        // Try to release the media stream if it exists
        if (vapiInstance.mediaStream) {
          console.log("Releasing media stream");
          const tracks = vapiInstance.mediaStream.getTracks();
          tracks.forEach(track => {
            track.stop();
            console.log("Media stream track stopped");
          });
        }

        return true;
      } else if (vapiInstance.client && typeof vapiInstance.client.stop === 'function') {
        // Try client object if it exists
        console.log("Using client object to stop call");
        vapiInstance.client.stop();
        console.log("Vapi call stopped successfully via client object");
        return true;
      } else {
        console.warn("Vapi instance does not have a stop method. Available methods:",
          Object.keys(vapiInstance).filter(key => typeof vapiInstance[key] === 'function').join(', '));

        // Try to find any method that might be used to stop the call
        const possibleStopMethods = ['stop', 'end', 'endCall', 'terminate', 'disconnect', 'close'];

        for (const methodName of possibleStopMethods) {
          if (typeof vapiInstance[methodName] === 'function') {
            console.log(`Found potential stop method: ${methodName}, attempting to use it`);
            try {
              vapiInstance[methodName]();
              console.log(`Successfully stopped call using ${methodName} method`);
              return true;
            } catch (altMethodError) {
              console.warn(`Error using ${methodName} method:`, altMethodError);
            }
          }
        }

        return false;
      }
    } catch (error) {
      console.error("Error stopping Vapi call:", error);

      // Double-check that the global flag is set to false
      if (typeof window !== 'undefined') {
        window.vapiCallActive = false;
        console.log("Double-checked window.vapiCallActive is false after error in vapiService.stopCall");
      }

      return false;
    } finally {
      // Double-check that the global flag is set to false
      if (typeof window !== 'undefined') {
        window.vapiCallActive = false;
        console.log("Double-checked window.vapiCallActive is false at end of vapiService.stopCall");
      }

      // Clean up any lingering Daily.co iframes one final time
      try {
        if (typeof document !== 'undefined') {
          const existingIframes = document.querySelectorAll('iframe[title*="daily"]');
          if (existingIframes.length > 0) {
            console.log(`Found ${existingIframes.length} lingering Daily.co iframes in final cleanup. Removing...`);
            existingIframes.forEach(iframe => {
              iframe.parentNode.removeChild(iframe);
            });
          }
        }
      } catch (cleanupError) {
        console.warn("Error cleaning up iframes in final cleanup:", cleanupError);
      }
    }
  }

  /**
   * Send a system message to the Vapi assistant
   * @param {Object} vapiInstance - The Vapi instance
   * @param {string} content - The message content
   * @returns {Promise<boolean>} Success status
   */
  async sendSystemMessage(vapiInstance, content) {
    if (!vapiInstance) {
      throw new Error("Vapi instance not initialized");
    }

    if (!content) {
      console.warn("Empty content provided to sendSystemMessage, skipping");
      return false;
    }

    try {
      console.log("Sending system message to Vapi assistant");
      const message = {
        role: 'system',
        content
      };

      // Find the target object to send messages with
      let messageSender = vapiInstance;

      // Handle v2.x SDK where methods might be on client
      if (!typeof messageSender.send === 'function' &&
          messageSender.client &&
          typeof messageSender.client.send === 'function') {
        console.log("Using client object for sending system messages");
        messageSender = messageSender.client;
      }

      // Method 1: Using the send method (standard v1.x and some v2.x)
      if (typeof messageSender.send === 'function') {
        console.log("Using send() method for system message");
        try {
          await messageSender.send(message);
          console.log("System message sent successfully with send()");
      return true;
        } catch (sendError) {
          console.warn("send() method failed for system message:", sendError);
          // Continue to try other methods
        }
      }

      // Method 2: Using addMessage (some v2.x versions)
      if (typeof messageSender.addMessage === 'function') {
        console.log("Using addMessage() method for system message");
        try {
          await messageSender.addMessage(message);
          console.log("System message sent successfully with addMessage()");
          return true;
        } catch (addMessageError) {
          console.warn("addMessage() method failed for system message:", addMessageError);
          // Continue to try other methods
        }
      }

      // Method 3: Using injectMessage (some v2.x versions)
      if (typeof messageSender.injectMessage === 'function') {
        console.log("Using injectMessage() method for system message");
        try {
          await messageSender.injectMessage({
            role: 'system',
            content
          });
          console.log("System message sent successfully with injectMessage()");
          return true;
        } catch (injectError) {
          console.warn("injectMessage() method failed for system message:", injectError);
          // All methods failed
        }
      }

      // No viable method found
      throw new Error("No viable method found to send system message. Available methods: " +
        Object.keys(messageSender).filter(key => typeof messageSender[key] === 'function').join(', '));
    } catch (error) {
      console.error("Error sending system message:", error);
      throw error;
    }
  }

  /**
   * Extract location data from message content
   * @param {string} content - The message content
   * @returns {Object} Location data object
   */
  extractLocationData(content) {
    if (!content) return null;

    const result = {
      hasLocation: false,
      location: null,
      locationType: null,
    };

    try {
      // Clean up the text for better matching
      const cleanText = content.replace(/[^\w\s,-]/g, ' ').toLowerCase();

      // Look for zip codes first (most specific)
      const zipMatch = cleanText.match(/\b(\d{5}(-\d{4})?)\b/);
      if (zipMatch) {
        console.log('Found zip code in text:', zipMatch[0]);

        result.hasLocation = true;
        result.locationType = 'zipcode';
        result.location = {
          address: `Zip code ${zipMatch[0]}`,
          // We would typically geocode this to get precise coordinates
          // For now, use NYC as a default
          lat: 40.7128,
          lng: -74.0060
        };

        return result;
      }

      // Check for state abbreviations (2-letter codes)
      const stateAbbrs = {
        'AL': 'Alabama', 'AK': 'Alaska', 'AZ': 'Arizona', 'AR': 'Arkansas',
        'CA': 'California', 'CO': 'Colorado', 'CT': 'Connecticut', 'DE': 'Delaware',
        'FL': 'Florida', 'GA': 'Georgia', 'HI': 'Hawaii', 'ID': 'Idaho',
        'IL': 'Illinois', 'IN': 'Indiana', 'IA': 'Iowa', 'KS': 'Kansas',
        'KY': 'Kentucky', 'LA': 'Louisiana', 'ME': 'Maine', 'MD': 'Maryland',
        'MA': 'Massachusetts', 'MI': 'Michigan', 'MN': 'Minnesota', 'MS': 'Mississippi',
        'MO': 'Missouri', 'MT': 'Montana', 'NE': 'Nebraska', 'NV': 'Nevada',
        'NH': 'New Hampshire', 'NJ': 'New Jersey', 'NM': 'New Mexico', 'NY': 'New York',
        'NC': 'North Carolina', 'ND': 'North Dakota', 'OH': 'Ohio', 'OK': 'Oklahoma',
        'OR': 'Oregon', 'PA': 'Pennsylvania', 'RI': 'Rhode Island', 'SC': 'South Carolina',
        'SD': 'South Dakota', 'TN': 'Tennessee', 'TX': 'Texas', 'UT': 'Utah',
        'VT': 'Vermont', 'VA': 'Virginia', 'WA': 'Washington', 'WV': 'West Virginia',
        'WI': 'Wisconsin', 'WY': 'Wyoming'
      };

      // State center coordinates (approximate)
      const stateCoordinates = {
        'AL': {lat: 32.7794, lng: -86.8287}, 'AK': {lat: 64.0685, lng: -152.2782},
        'AZ': {lat: 34.2744, lng: -111.6602}, 'AR': {lat: 34.8938, lng: -92.4426},
        'CA': {lat: 37.1841, lng: -119.4696}, 'CO': {lat: 38.9972, lng: -105.5478},
        'CT': {lat: 41.6219, lng: -72.7273}, 'DE': {lat: 38.9896, lng: -75.5050},
        'FL': {lat: 28.6305, lng: -82.4497}, 'GA': {lat: 32.6415, lng: -83.4426},
        'HI': {lat: 20.2927, lng: -156.3737}, 'ID': {lat: 44.3509, lng: -114.6130},
        'IL': {lat: 40.0417, lng: -89.1965}, 'IN': {lat: 39.8942, lng: -86.2816},
        'IA': {lat: 42.0751, lng: -93.4960}, 'KS': {lat: 38.4937, lng: -98.3804},
        'KY': {lat: 37.5347, lng: -85.3021}, 'LA': {lat: 31.0689, lng: -91.9968},
        'ME': {lat: 45.3695, lng: -69.2428}, 'MD': {lat: 39.0550, lng: -76.7909},
        'MA': {lat: 42.2596, lng: -71.8083}, 'MI': {lat: 44.3467, lng: -85.4102},
        'MN': {lat: 46.2807, lng: -94.3053}, 'MS': {lat: 32.7364, lng: -89.6678},
        'MO': {lat: 38.3566, lng: -92.4580}, 'MT': {lat: 47.0527, lng: -109.6333},
        'NE': {lat: 41.5378, lng: -99.7951}, 'NV': {lat: 39.3289, lng: -116.6312},
        'NH': {lat: 43.6805, lng: -71.5811}, 'NJ': {lat: 40.1907, lng: -74.6728},
        'NM': {lat: 34.4071, lng: -106.1126}, 'NY': {lat: 42.9538, lng: -75.5268},
        'NC': {lat: 35.5557, lng: -79.3877}, 'ND': {lat: 47.4501, lng: -100.4659},
        'OH': {lat: 40.2862, lng: -82.7937}, 'OK': {lat: 35.5889, lng: -97.4943},
        'OR': {lat: 43.9336, lng: -120.5583}, 'PA': {lat: 40.8781, lng: -77.7996},
        'RI': {lat: 41.6762, lng: -71.5562}, 'SC': {lat: 33.9169, lng: -80.8964},
        'SD': {lat: 44.4443, lng: -100.2263}, 'TN': {lat: 35.8580, lng: -86.3505},
        'TX': {lat: 31.4757, lng: -99.3312}, 'UT': {lat: 39.3055, lng: -111.6703},
        'VT': {lat: 44.0687, lng: -72.6658}, 'VA': {lat: 37.5215, lng: -78.8537},
        'WA': {lat: 47.3826, lng: -120.4472}, 'WV': {lat: 38.6409, lng: -80.6227},
        'WI': {lat: 44.6243, lng: -89.9941}, 'WY': {lat: 42.9957, lng: -107.5512}
      };

      // Pattern to match state abbreviations, making sure they're standalone words
      const stateAbbrPattern = /\b(AL|AK|AZ|AR|CA|CO|CT|DE|FL|GA|HI|ID|IL|IN|IA|KS|KY|LA|ME|MD|MA|MI|MN|MS|MO|MT|NE|NV|NH|NJ|NM|NY|NC|ND|OH|OK|OR|PA|RI|SC|SD|TN|TX|UT|VT|VA|WA|WV|WI|WY)\b/i;
      const stateAbbrMatch = cleanText.match(stateAbbrPattern);

      if (stateAbbrMatch) {
        const stateCode = stateAbbrMatch[0].toUpperCase();
        console.log('Found state abbreviation in text:', stateCode);

        result.hasLocation = true;
        result.locationType = 'state';
        result.location = {
          address: stateAbbrs[stateCode],
          ...stateCoordinates[stateCode]
        };

        return result;
      }

      // Check for full state names
      const stateNames = Object.values(stateAbbrs);
      const stateNamePattern = new RegExp(`\\b(${stateNames.join('|')})\\b`, 'i');
      const stateNameMatch = cleanText.match(stateNamePattern);

      if (stateNameMatch) {
        const stateName = stateNameMatch[0];
        console.log('Found state name in text:', stateName);

        // Find the state code for this state name
        const stateCode = Object.keys(stateAbbrs).find(code =>
          stateAbbrs[code].toLowerCase() === stateName.toLowerCase()
        );

        if (stateCode) {
          result.hasLocation = true;
          result.locationType = 'state';
          result.location = {
            address: stateAbbrs[stateCode],
            ...stateCoordinates[stateCode]
          };

          return result;
        }
      }

      // Check for major cities
      const majorCities = {
        'new york': {lat: 40.7128, lng: -74.0060},
        'los angeles': {lat: 34.0522, lng: -118.2437},
        'chicago': {lat: 41.8781, lng: -87.6298},
        'houston': {lat: 29.7604, lng: -95.3698},
        'phoenix': {lat: 33.4484, lng: -112.0740},
        'philadelphia': {lat: 39.9526, lng: -75.1652},
        'san antonio': {lat: 29.4241, lng: -98.4936},
        'san diego': {lat: 32.7157, lng: -117.1611},
        'dallas': {lat: 32.7767, lng: -96.7970},
        'san jose': {lat: 37.3382, lng: -121.8863},
        'austin': {lat: 30.2672, lng: -97.7431},
        'jacksonville': {lat: 30.3322, lng: -81.6557},
        'fort worth': {lat: 32.7555, lng: -97.3308},
        'columbus': {lat: 39.9612, lng: -82.9988},
        'charlotte': {lat: 35.2271, lng: -80.8431},
        'san francisco': {lat: 37.7749, lng: -122.4194},
        'indianapolis': {lat: 39.7684, lng: -86.1581},
        'seattle': {lat: 47.6062, lng: -122.3321},
        'denver': {lat: 39.7392, lng: -104.9903},
        'washington': {lat: 38.9072, lng: -77.0369}
      };

      for (const [city, coords] of Object.entries(majorCities)) {
        if (cleanText.includes(city)) {
          console.log('Found city in text:', city);

          result.hasLocation = true;
          result.locationType = 'city';
          result.location = {
            address: city.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),
            ...coords
          };

          return result;
        }
      }

      // Look for direct lat/lng format
      const coordsMatch = content.match(/(-?\d+\.\d+),\s*(-?\d+\.\d+)/);
      if (coordsMatch) {
        console.log('Found coordinates in text:', coordsMatch[0]);

        result.hasLocation = true;
        result.locationType = 'coordinates';
        result.location = {
          address: 'Location',
          lat: parseFloat(coordsMatch[1]),
          lng: parseFloat(coordsMatch[2])
        };

        return result;
      }

      return result;
    } catch (error) {
      console.error("Error extracting location data:", error);
      return result;
    }
  }

  /**
   * Extract practice area from issue description
   * @param {string} issueText - Description of the legal issue
   * @returns {string} The extracted practice area
   */
  extractPracticeAreaFromIssue(issueText) {
    if (!issueText) return 'General Practice';

    // Common mapping of issues to practice areas
    const issueKeywords = {
      'divorce': 'Family Law',
      'custody': 'Family Law',
      'child support': 'Family Law',
      'alimony': 'Family Law',
      'will': 'Estate Planning',
      'trust': 'Estate Planning',
      'probate': 'Estate Planning',
      'inheritance': 'Estate Planning',
      'accident': 'Personal Injury',
      'injury': 'Personal Injury',
      'malpractice': 'Medical Malpractice',
      'discrimination': 'Employment Law',
      'wrongful termination': 'Employment Law',
      'harassment': 'Employment Law',
      'contract': 'Business Law',
      'startup': 'Business Law',
      'incorporation': 'Business Law',
      'eviction': 'Real Estate Law',
      'foreclosure': 'Real Estate Law',
      'landlord': 'Real Estate Law',
      'tenant': 'Real Estate Law',
      'dui': 'Criminal Defense',
      'arrest': 'Criminal Defense',
      'charged': 'Criminal Defense',
      'immigration': 'Immigration Law',
      'visa': 'Immigration Law',
      'green card': 'Immigration Law',
      'citizenship': 'Immigration Law',
      'bankruptcy': 'Bankruptcy Law',
      'debt': 'Bankruptcy Law',
      'patent': 'Intellectual Property',
      'copyright': 'Intellectual Property',
      'trademark': 'Intellectual Property'
    };

    // Check if the issue contains any of our keywords
    const lowerIssue = issueText.toLowerCase();
    for (const [keyword, practiceArea] of Object.entries(issueKeywords)) {
      if (lowerIssue.includes(keyword)) {
        return practiceArea;
      }
    }

    // Default if we can't determine
    return 'General Practice';
  }

  /**
   * Sync data with Make.com webhook
   * @param {string} webhookUrl - The webhook URL
   * @param {Object} data - The data to send
   */
  async syncWithMakeWebhook(webhookUrl, data) {
    if (!webhookUrl) {
      console.warn("Attempted to sync with webhook but no URL provided");
      return;
    }

    console.log(`🛠️ WEBHOOK: Sending data to webhook (${webhookUrl.slice(0, 30)}...):`, JSON.stringify(data).slice(0, 200) + "...");

    try {
      // Ensure the data has the necessary Vapi properties
      const toolCallId = data.toolCallId || data.id;

      // Enhanced data structure to ensure map data is properly included
      const enhancedData = {
        ...data,
        // Ensure the toolCallId is explicitly included
        toolCallId: toolCallId,
        // Ensure location data is explicitly available for the map component
        mapData: {
          location: data.existingDossier?.location || null,
          attorneys: data.attorneys || []
        }
      };

      console.log(`🛠️ WEBHOOK: Tool Call ID being sent: ${toolCallId || 'Not available'}`);

      // Log the webhook call to React DevTools if available
      if (typeof window !== 'undefined' &&
          window.__REACT_DEVTOOLS_GLOBAL_HOOK__ &&
          typeof window.__REACT_DEVTOOLS_GLOBAL_HOOK__.logWebhookCall === 'function') {

        // Extract tool ID from the URL or data
        const toolId = data.tool?.id || data.id || 'unknown_tool';

        // Log the webhook call for debugging
        window.__REACT_DEVTOOLS_GLOBAL_HOOK__.logWebhookCall(
          toolId,
          webhookUrl,
          enhancedData
        );
      }

      console.time("Webhook request");
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-LegalScout-Tool': 'LIVE_DOSSIER',
          'X-LegalScout-Timestamp': new Date().toISOString(),
          'X-LegalScout-Message-Count': data.existingDossier?.messageCount || '0',
          'X-LegalScout-Dossier-ID': data.existingDossier?.id || `dossier-${Date.now()}`,
          'X-LegalScout-Location': data.existingDossier?.location ?
            `${data.existingDossier.location.lat},${data.existingDossier.location.lng}` : '',
          'X-LegalScout-ToolCallId': toolCallId || '' // Add toolCallId to headers
        },
        body: JSON.stringify(enhancedData),
      });
      console.timeEnd("Webhook request");

      if (!response.ok) {
        console.error(`🛠️ WEBHOOK: Request failed with status: ${response.status}`);
        console.error("Response:", await response.text().catch(() => "Could not read response text"));
        throw new Error(`Webhook sync failed with status: ${response.status}`);
      }

      // Parse the response
      const responseData = await response.json().catch(() => ({}));
      console.log("🛠️ WEBHOOK: Raw response:", responseData);

      // Validate and format the response according to Vapi's expected format
      let formattedResponse = responseData;

      // If response is not in the expected Vapi format (results array with toolCallId & result)
      // but we have data we can use, format it properly
      if (toolCallId &&
          (!responseData.results || !Array.isArray(responseData.results)) &&
          responseData) {

        console.log("🛠️ WEBHOOK: Reformatting response to Vapi expected format");

        // Convert basic response to proper Vapi format with results array
        formattedResponse = {
          results: [
            {
              toolCallId: toolCallId,
              result: responseData
            }
          ]
        };

        console.log("🛠️ WEBHOOK: Reformatted response:", formattedResponse);
      }

      // Log the webhook response to React DevTools if available
      if (formattedResponse &&
          typeof window !== 'undefined' &&
          window.__REACT_DEVTOOLS_GLOBAL_HOOK__ &&
          window.LegalScoutDebug?.webhooks?.logResponses) {
        console.log("🛠️ WEBHOOK: Logging response to DevTools");

        // If we have a global debug tracking function for responses
        if (typeof window.__REACT_DEVTOOLS_GLOBAL_HOOK__.logWebhookResponse === 'function') {
          window.__REACT_DEVTOOLS_GLOBAL_HOOK__.logWebhookResponse(webhookUrl, formattedResponse);
        } else {
          // Fallback to the regular logger
          console.log("🛠️ WEBHOOK: Response logged to DevTools:", {
            url: webhookUrl,
            response: formattedResponse,
            timestamp: new Date().toISOString()
          });
        }
      }

      return formattedResponse;
    } catch (error) {
      console.error('🛠️ WEBHOOK: Error syncing with Make.com webhook:', error);
      return false;
    }
  }

  /**
   * Store call data in Supabase when the call ends
   * @param {Object} briefData - The brief data from the call
   * @param {string} subdomain - The attorney subdomain
   */
  async storeCallData(briefData, subdomain) {
    if (!briefData || !subdomain || subdomain === 'default') return;

    try {
      // Get attorney ID from subdomain
      const attorney = await supabaseService.getAttorneyBySubdomain(subdomain);

      if (attorney && attorney.id) {
        // Store brief in Supabase
        await supabaseService.storeBrief(briefData, attorney.id);
      }
    } catch (error) {
      console.error('Error storing call data:', error);
    }
  }

  /**
   * Send a user message through the Vapi assistant
   * @param {Object} vapiInstance - The Vapi instance
   * @param {string} content - The message content
   * @returns {Promise<boolean>} Success status
   */
  async sendUserMessage(vapiInstance, content) {
    if (!vapiInstance) {
      throw new Error("Vapi instance not initialized");
    }

    if (!content || typeof content !== 'string' || content.trim() === '') {
      console.warn("Empty content provided to sendUserMessage, skipping");
      return false;
    }

    try {
      console.log("Sending user message to Vapi assistant:", content);
      const message = {
        role: 'user',
        content: content.trim()
      };

      // Find the target object to send messages with
      let messageSender = vapiInstance;

      // Handle v2.x SDK where methods might be on client
      if (!typeof messageSender.send === 'function' &&
          messageSender.client &&
          typeof messageSender.client.send === 'function') {
        console.log("Using client object for sending messages");
        messageSender = messageSender.client;
      }

      // Method 1: Using the send method (standard v1.x and some v2.x)
      if (typeof messageSender.send === 'function') {
        console.log("Using send() method");
        try {
          await messageSender.send(message);
          console.log("Message sent successfully with send()");
        return true;
        } catch (sendError) {
          console.warn("send() method failed:", sendError);
          // Continue to try other methods
        }
      }

      // Method 2: Using addMessage (some v2.x versions)
      if (typeof messageSender.addMessage === 'function') {
        console.log("Using addMessage() method");
        try {
          await messageSender.addMessage(message);
          console.log("Message sent successfully with addMessage()");
            return true;
        } catch (addMessageError) {
          console.warn("addMessage() method failed:", addMessageError);
          // Continue to try other methods
          }
      }

      // Method 3: Using sendMessage (some v2.x versions)
      if (typeof messageSender.sendMessage === 'function') {
        console.log("Using sendMessage() method");
          try {
          await messageSender.sendMessage(content); // This method might expect just the content string
          console.log("Message sent successfully with sendMessage()");
            return true;
        } catch (sendMessageError) {
          console.warn("sendMessage() method failed:", sendMessageError);
          // Continue to try other methods
        }
      }

      // No viable method found
      throw new Error("No viable method found to send user message. Available methods: " +
        Object.keys(messageSender).filter(key => typeof messageSender[key] === 'function').join(', '));
    } catch (error) {
      console.error("Error sending user message:", error);
      throw error;
    }
  }

  /**
   * Initialize Vapi service with API key and subdomain
   * @param {string} apiKey - Vapi API key
   * @param {string} subdomain - Subdomain for configuration
   * @returns {Object} Initialized Vapi instance
   */
  async initialize(apiKey, subdomain) {
    try {
      if (!apiKey) {
        console.error('Vapi API key is required for initialization');
        throw new Error('Missing API key for Vapi initialization');
      }

      console.log('Initializing Vapi with API key:', apiKey ? `${apiKey.substring(0, 8)}...` : 'null');

      // Track initialization attempts
      let attempts = 0;
      const maxAttempts = 3;
      let vapiInstance = null;
      let lastError = null;

      // Try different approaches to initialize Vapi
      while (!vapiInstance && attempts < maxAttempts) {
        try {
          console.log(`Vapi initialization attempt ${attempts + 1}/${maxAttempts}`);

          if (attempts === 0) {
            // First try: use the imported library
            console.log('Trying to initialize with imported Vapi module');
            vapiInstance = this.createVapiInstance(apiKey);
          }
          else if (attempts === 1) {
            // Second try: try loading the script dynamically
            console.log('Trying to initialize by loading SDK script');
            await this.loadVapiScript();
            vapiInstance = this.createVapiInstance(apiKey);
          }
          else {
            // Last try: direct dynamic import
            console.log('Trying direct dynamic import of SDK');
            try {
              const VapiModule = await import('@vapi-ai/web');
              console.log('Dynamic import result:', VapiModule);

              if (VapiModule.default && (typeof VapiModule.default === 'function' || typeof VapiModule.default.create === 'function')) {
                console.log('Using dynamically imported Vapi module');

                // Override the global Vapi reference with our dynamic import
                // This should make createVapiInstance work properly
                window.Vapi = VapiModule.default;

                vapiInstance = this.createVapiInstance(apiKey);
              } else {
                throw new Error('Dynamic import did not return a usable Vapi module');
              }
            } catch (importError) {
              console.error('Dynamic import failed:', importError);
              throw importError;
            }
          }
        } catch (error) {
          console.warn(`Initialization attempt ${attempts + 1} failed:`, error);
          lastError = error;
          attempts++;

          // Wait before retry
          if (attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 500 * attempts));
          }
        }
      }

        if (!vapiInstance) {
        console.error('All Vapi initialization attempts failed');
        throw lastError || new Error('Failed to initialize Vapi after multiple attempts');
        }

      console.log('Vapi initialized successfully after', attempts + 1, 'attempts');

      // Setup event listeners for the instance
        this.setupEventListeners(vapiInstance);

      // Configure recording if needed (moved this after successful initialization)
      this.configureInstance(vapiInstance, subdomain);

      return vapiInstance;
      } catch (error) {
      console.error('Failed to initialize Vapi service:', error);
      throw error;
    }
  }

  /**
   * Configure the Vapi instance with settings like recording
   * Extracted from initialize to modularize code
   */
  configureInstance(vapiInstance, subdomain) {
    // If we have a subdomain, configure recording storage with Supabase
    if (vapiInstance && subdomain && subdomain !== 'default') {
      try {
        // Wrap in a try-catch and add a timeout to prevent Supabase configuration from blocking
        const configurePromise = Promise.race([
          Promise.resolve().then(() => supabaseService.configureVapiRecording(vapiInstance, subdomain)),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Supabase configuration timed out')), 3000))
        ]);

        // Handle the configuration asynchronously to not block initialization
        configurePromise.catch(supabaseError => {
          // Log the Supabase error but continue with initialization
          console.warn("Supabase configuration failed, but continuing with Vapi initialization:", supabaseError);
        });
      } catch (supabaseError) {
        // Log the Supabase error but continue with initialization
        console.warn("Supabase configuration failed, but continuing with Vapi initialization:", supabaseError);
      }
    }

    return vapiInstance;
  }

  /**
   * Dynamically load the Vapi SDK script
   * @returns {Promise} Promise that resolves when the script is loaded
   */
  loadVapiScript() {
    return new Promise((resolve, reject) => {
      // Check if script is already loaded
      if (typeof window !== 'undefined' && (window.Vapi || window.__VAPI_LOADING)) {
        console.log('Vapi SDK already loaded or loading');

        // If already loading, wait for it
        if (window.__VAPI_LOADING) {
          console.log('Waiting for in-progress Vapi SDK loading to complete');
          const checkInterval = setInterval(() => {
            if (window.Vapi && !window.__VAPI_LOADING) {
              clearInterval(checkInterval);
              resolve(window.Vapi);
            }
          }, 100);
          return;
        }

        return resolve(window.Vapi);
      }

      // Set loading flag to prevent multiple loads
      window.__VAPI_LOADING = true;

      // Create script element - use exact version 2.2.2 to match package.json
      const script = document.createElement('script');
      script.src = 'https://cdn.vapi.ai/web-sdk@2.2.2/dist/web-sdk.js';
      script.async = true;
      script.id = 'vapi-web-sdk';

      // Set up load handler
      script.onload = () => {
        console.log('Vapi SDK script loaded successfully');
        window.__VAPI_LOADING = false;

        if (typeof window !== 'undefined' && window.Vapi) {
          resolve(window.Vapi);
        } else {
          console.warn('Vapi SDK script loaded but Vapi is not available on window');

          // Try to load the module via dynamic import as fallback
          try {
            import('@vapi-ai/web').then(module => {
              console.log('Vapi loaded via dynamic import');
              window.Vapi = module.default || module;
              resolve(window.Vapi);
            }).catch(importError => {
              console.error('Failed to load Vapi via dynamic import:', importError);
              reject(new Error('Vapi SDK script loaded but Vapi is not available'));
            });
          } catch (importError) {
            reject(new Error('Vapi SDK script loaded but Vapi is not available on window'));
          }
        }
      };

      // Set up error handler
      script.onerror = () => {
        console.error('Failed to load Vapi SDK script');
        window.__VAPI_LOADING = false;
        reject(new Error('Failed to load Vapi SDK script'));
      };

      // Add the script to the document
      document.head.appendChild(script);
    });
  }

  /**
   * Configure recording for a specific call using Supabase
   * @param {Object} vapiInstance - The Vapi instance
   * @param {String} subdomain - Attorney subdomain for organization
   */
  configureRecording(vapiInstance, subdomain) {
    if (!vapiInstance || subdomain === 'default') {
      return;
    }

    try {
      supabaseService.configureVapiRecording(vapiInstance, subdomain);
    } catch (error) {
      console.error("Error configuring Vapi recording:", error);
    }
  }
}

// Export a singleton instance
export const vapiService = new VapiService();