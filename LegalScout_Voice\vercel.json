{"$schema": "https://openapi.vercel.sh/vercel.json", "framework": "vite", "buildCommand": "npm run vercel-build", "outputDirectory": "dist", "installCommand": "npm ci --prefer-offline --no-audit --legacy-peer-deps", "crons": [], "regions": ["iad1"], "build": {"env": {"NODE_OPTIONS": "--max-old-space-size=4096"}}, "env": {"VITE_SUPABASE_URL": "https://utopqxsvudgrtiwenlzl.supabase.co", "VITE_SUPABASE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU", "VITE_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU", "VITE_VAPI_PUBLIC_KEY": "310f0d43-27c2-47a5-a76d-e55171d024f7", "VITE_VAPI_SECRET_KEY": "6734febc-fc65-4669-93b0-929b31ff6564", "VITE_VAPI_PRIVATE_KEY": "6734febc-fc65-4669-93b0-929b31ff6564", "VAPI_TOKEN": "6734febc-fc65-4669-93b0-929b31ff6564", "VAPI_PRIVATE_KEY": "6734febc-fc65-4669-93b0-929b31ff6564", "SUPABASE_URL": "https://utopqxsvudgrtiwenlzl.supabase.co", "SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU", "SUPABASE_SERVICE_ROLE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczODk0ODAwNywiZXhwIjoyMDU0NTI0MDA3fQ.Noq994xfKMoQipfGli9fZcgQYig9fZovjqdEnpBe7CM", "VAPI_WEBHOOK_SECRET": "dce33c66664d1abd29d61a519b246a515c328ea56e347fb190ba7e96404de59b"}, "functions": {"api/index.js": {"maxDuration": 30, "memory": 1024}, "api/vapi-webhook-direct.js": {"maxDuration": 10, "memory": 512}}, "rewrites": [{"source": "/(about|contact|preview|demo|simple-demo|dashboard|auth|preview-frame|preview-frame-test|simple-preview|test-route)/(.*)", "destination": "/index.html"}, {"source": "/(about|contact|preview|demo|simple-demo|dashboard|auth|preview-frame|preview-frame-test|simple-preview|test-route)", "destination": "/index.html"}, {"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "X-Content-Type-Options", "value": "nosniff"}]}, {"source": "/(.*)\\.(js|css|woff2|woff|ttf|eot)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.(png|jpg|jpeg|gif|webp|svg|ico)", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept, X-Vapi-Signature, X-Requested-With, X-Original-URL, X-Supabase-Key"}, {"key": "Access-Control-Max-Age", "value": "86400"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}]}, {"source": "/api/supabase-proxy/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS, PATCH"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept, X-Original-URL, X-Supabase-Key, Range"}, {"key": "Access-Control-Max-Age", "value": "86400"}, {"key": "Access-Control-Allow-Credentials", "value": "false"}]}, {"source": "/api/vapi-mcp-server/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept, X-Vapi-Signature, X-Requested-With"}, {"key": "Access-Control-Max-Age", "value": "86400"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}]}, {"source": "/index.html", "headers": [{"key": "Content-Security-Policy-Report-Only", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: data: https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io https://vercel.live https://*.vercel.live https://*.vercel.app; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com https://c.daily.co https://*.daily.co; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: blob: https:; media-src 'self' blob: data: https:; connect-src 'self' wss: ws: https://api.vapi.ai https://mcp.vapi.ai https://dashboard.vapi.ai https://vapi.ai https://utopqxsvudgrtiwenlzl.supabase.co https://*.supabase.co https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io https://r.jina.ai https://api.openai.com https://api.firecrawl.dev https://vercel.live https://*.vercel.live https://*.vercel.app; frame-src 'self' https://c.daily.co https://*.daily.co https://vercel.live https://*.vercel.live https://*.vercel.app; worker-src 'self' blob: data:; child-src 'self' blob:; object-src 'none'; base-uri 'self';"}]}, {"source": "/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept, X-Vapi-Signature, X-Requested-With"}, {"key": "Access-Control-Max-Age", "value": "86400"}]}]}