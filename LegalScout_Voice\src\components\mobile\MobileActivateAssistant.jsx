import React, { useState, useEffect } from 'react';
import { getCurrentSubdomain } from '../../utils/subdomainTester';
import { getAttorneyConfigAsync } from '../../config/attorneys';
import { mapDatabaseToPreview } from '../../utils/configMapping';

/**
 * MobileActivateAssistant - Global mobile component for activating assistant
 * Shows across all pages when on mobile and assistant needs activation
 */
const MobileActivateAssistant = ({ onActivated }) => {
  const [showButton, setShowButton] = useState(false);
  const [activating, setActivating] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth <= 768;
      setIsMobile(mobile);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Check if assistant needs activation
  useEffect(() => {
    const checkActivationNeeded = async () => {
      try {
        const subdomain = getCurrentSubdomain();

        // Only check for specific subdomains that might need activation
        if (subdomain === 'damon') {
          const config = await getAttorneyConfigAsync(subdomain);

          // Check if this is a fallback configuration
          const needsActivation = !config ||
                                 config.isFallback ||
                                 (config.firmName === 'Your Law Firm' && !config.id) ||
                                 (!config.id && subdomain === 'damon');

          console.log('[MobileActivateAssistant] Activation check:', {
            subdomain,
            needsActivation,
            hasConfig: !!config,
            hasId: !!config?.id,
            firmName: config?.firmName
          });

          setShowButton(needsActivation && isMobile);
        }
      } catch (error) {
        console.error('[MobileActivateAssistant] Error checking activation:', error);
      }
    };

    if (isMobile) {
      checkActivationNeeded();
    } else {
      setShowButton(false);
    }
  }, [isMobile]);

  // Function to activate the assistant
  const activateAssistant = async () => {
    setActivating(true);
    try {
      console.log('[MobileActivateAssistant] Activating assistant...');

      const subdomain = getCurrentSubdomain();
      const config = await getAttorneyConfigAsync(subdomain);

      console.log('[MobileActivateAssistant] Activation result:', {
        hasConfig: !!config,
        firmName: config?.firmName,
        assistantId: config?.vapi_assistant_id,
        id: config?.id
      });

      if (config && config.vapi_assistant_id) {
        setShowButton(false);
        console.log('[MobileActivateAssistant] Assistant activated successfully');

        // Notify parent component if callback provided
        if (onActivated) {
          onActivated(config);
        }

        // Reload the page to refresh all components with new config
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        console.warn('[MobileActivateAssistant] Activation failed - no valid config found');
        if (window.showErrorNotification) {
          window.showErrorNotification('Activation failed. Please try again or contact support.');
        }
      }
    } catch (error) {
      console.error('[MobileActivateAssistant] Error activating assistant:', error);
      if (window.showErrorNotification) {
        window.showErrorNotification('Error activating assistant. Please try again.');
      }
    } finally {
      setActivating(false);
    }
  };

  // Don't render if not mobile or button not needed
  if (!isMobile || !showButton) {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      left: '50%',
      transform: 'translateX(-50%)',
      zIndex: 10000, // Higher than preview overlay
      backgroundColor: 'rgba(173, 216, 230, 0.1)', // Hollow light blue
      padding: '12px 20px',
      borderRadius: '25px',
      border: '1px solid rgba(173, 216, 230, 0.8)', // Thin light blue piping
      color: '#87CEEB', // Light blue text
      textAlign: 'center',
      maxWidth: '90vw',
      minWidth: '280px',
      boxShadow: '0 2px 15px rgba(173, 216, 230, 0.2)',
      backdropFilter: 'blur(15px)',
      animation: 'slideUp 0.3s ease-out'
    }}>
      <style>{`
        @keyframes slideUp {
          from {
            transform: translateX(-50%) translateY(100px);
            opacity: 0;
          }
          to {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
          }
        }
      `}</style>

      <div style={{
        marginBottom: '8px',
        fontSize: '16px',
        fontWeight: 'bold'
      }}>
        🚀 Assistant Not Active
      </div>

      <div style={{
        marginBottom: '12px',
        fontSize: '13px',
        color: '#ccc',
        lineHeight: '1.3'
      }}>
        Tap to sync your assistant configuration
      </div>

      <button
        onClick={activateAssistant}
        disabled={activating}
        style={{
          backgroundColor: activating ? 'rgba(173, 216, 230, 0.2)' : 'rgba(173, 216, 230, 0.15)',
          color: activating ? '#B0C4DE' : '#87CEEB',
          border: '1px solid rgba(173, 216, 230, 0.6)',
          padding: '12px 24px',
          borderRadius: '20px',
          cursor: activating ? 'not-allowed' : 'pointer',
          fontSize: '14px',
          fontWeight: 'bold',
          minWidth: '120px',
          transition: 'all 0.3s ease',
          boxShadow: activating ? 'none' : '0 2px 10px rgba(173, 216, 230, 0.2)',
          backdropFilter: 'blur(10px)'
        }}
      >
        {activating ? '⏳ Activating...' : '✨ Activate Now'}
      </button>
    </div>
  );
};

export default MobileActivateAssistant;
