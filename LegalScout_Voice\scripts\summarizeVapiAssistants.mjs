/**
 * Summarize Vapi Assistants Analysis
 * 
 * This script reads the analysis file and provides a clean summary
 */

import fs from 'fs';

function main() {
  try {
    console.log('📊 VAPI ASSISTANTS SUMMARY');
    console.log('='.repeat(50));
    
    // Read the analysis file
    const data = JSON.parse(fs.readFileSync('vapi-assistants-analysis.json', 'utf8'));
    const { summary } = data;
    
    console.log(`📈 Total Assistants: ${summary.total}`);
    console.log(`📅 Analysis Date: ${data.timestamp}`);
    
    // Show assistants with calls (most active first)
    console.log('\n🔥 MOST ACTIVE ASSISTANTS (by call count):');
    const assistantsWithCalls = Object.entries(summary.callAssociations)
      .sort(([,a], [,b]) => b.callCount - a.callCount)
      .slice(0, 10); // Top 10
    
    assistantsWithCalls.forEach(([assistantId, callInfo], index) => {
      // Find the assistant name
      const assistant = Object.values(summary.byName)
        .flat()
        .find(a => a.id === assistantId);
      
      const assistantName = assistant ? assistant.name : 'Unknown Assistant';
      console.log(`${index + 1}. ${assistantName}`);
      console.log(`   ID: ${assistantId}`);
      console.log(`   Calls: ${callInfo.callCount}`);
      console.log(`   Last Call: ${callInfo.lastCallDate}`);
      console.log(`   Status: ${JSON.stringify(callInfo.callStatuses)}`);
      console.log('');
    });
    
    // Show duplicate names
    if (summary.duplicateNames.length > 0) {
      console.log('\n⚠️  DUPLICATE ASSISTANT NAMES:');
      summary.duplicateNames.forEach(duplicate => {
        console.log(`• "${duplicate.name}" appears ${duplicate.count} times:`);
        duplicate.assistants.forEach(a => {
          const callInfo = summary.callAssociations[a.id];
          const callText = callInfo ? ` [${callInfo.callCount} calls]` : ' [no calls]';
          console.log(`  - ${a.id} (created: ${a.createdAt})${callText}`);
        });
        console.log('');
      });
    }
    
    // Show assistants with webhooks
    if (summary.withWebhooks.length > 0) {
      console.log('\n🔗 ASSISTANTS WITH WEBHOOKS:');
      summary.withWebhooks.forEach(assistant => {
        const callInfo = summary.callAssociations[assistant.id];
        const callText = callInfo ? ` [${callInfo.callCount} calls]` : ' [no calls]';
        console.log(`• ${assistant.name} (${assistant.id})${callText}`);
        console.log(`  Webhook: ${assistant.webhookUrl}`);
      });
      console.log('');
    }
    
    // Show assistants with tools
    if (summary.withTools.length > 0) {
      console.log('\n🛠️  ASSISTANTS WITH TOOLS:');
      summary.withTools.forEach(assistant => {
        const callInfo = summary.callAssociations[assistant.id];
        const callText = callInfo ? ` [${callInfo.callCount} calls]` : ' [no calls]';
        console.log(`• ${assistant.name} (${assistant.id})${callText}`);
        console.log(`  Tools: ${assistant.toolCount} (${assistant.toolIds.join(', ')})`);
      });
      console.log('');
    }
    
    // Show creation timeline
    console.log('\n📅 CREATION TIMELINE:');
    Object.entries(summary.byCreationDate)
      .sort(([a], [b]) => b.localeCompare(a))
      .slice(0, 10) // Last 10 days
      .forEach(([date, count]) => {
        console.log(`• ${date}: ${count} assistant(s) created`);
      });
    
    // Show unused assistants (no calls)
    const unusedAssistants = Object.values(summary.byName)
      .flat()
      .filter(assistant => !summary.callAssociations[assistant.id])
      .length;
    
    console.log(`\n💤 Unused Assistants: ${unusedAssistants} (no calls made)`);
    
    // Key insights
    console.log('\n🎯 KEY INSIGHTS:');
    const totalCalls = Object.values(summary.callAssociations)
      .reduce((sum, info) => sum + info.callCount, 0);
    
    console.log(`• Total calls across all assistants: ${totalCalls}`);
    console.log(`• Active assistants (with calls): ${Object.keys(summary.callAssociations).length}`);
    console.log(`• Inactive assistants: ${summary.total - Object.keys(summary.callAssociations).length}`);
    console.log(`• Duplicate names: ${summary.duplicateNames.length} sets`);
    console.log(`• Assistants with webhooks: ${summary.withWebhooks.length}`);
    console.log(`• Assistants with tools: ${summary.withTools.length}`);
    
    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    if (unusedAssistants > 0) {
      console.log(`• Consider cleaning up ${unusedAssistants} unused assistants`);
    }
    if (summary.duplicateNames.length > 0) {
      console.log(`• Review ${summary.duplicateNames.length} sets of duplicate names for consolidation`);
    }
    if (summary.withWebhooks.length === 0) {
      console.log('• Consider setting up webhooks for call monitoring and data collection');
    }
    
    console.log('\n✅ Summary complete!');
    
  } catch (error) {
    console.error('❌ Error reading analysis file:', error.message);
    console.log('💡 Make sure to run getAllVapiAssistants.mjs first');
  }
}

main();
