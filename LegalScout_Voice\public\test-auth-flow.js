/**
 * Test Authentication Flow
 * 
 * This script can be run in the browser console to test the authentication flow
 * and diagnose issues with attorney profile creation.
 */

window.testAuthFlow = async function() {
  console.log('🧪 [TestAuthFlow] Starting authentication flow test...');
  
  try {
    // Test 1: Check if user is authenticated
    console.log('🧪 [TestAuthFlow] Test 1: Checking authentication...');
    
    // Import Supabase client
    const { getSupabaseClient } = await import('/src/lib/supabase.js');
    const supabase = await getSupabaseClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.error('❌ [TestAuthFlow] Authentication error:', authError);
      return { success: false, error: 'Not authenticated' };
    }
    
    if (!user) {
      console.error('❌ [TestAuthFlow] No authenticated user found');
      return { success: false, error: 'No user found' };
    }
    
    console.log('✅ [TestAuthFlow] User authenticated:', user.email);
    
    // Test 2: Check if attorney profile exists
    console.log('🧪 [TestAuthFlow] Test 2: Checking attorney profile...');
    
    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', user.email)
      .single();
    
    if (attorneyError && attorneyError.code !== 'PGRST116') {
      console.error('❌ [TestAuthFlow] Attorney lookup error:', attorneyError);
      return { success: false, error: 'Attorney lookup failed' };
    }
    
    if (attorney) {
      console.log('✅ [TestAuthFlow] Attorney profile found:', {
        id: attorney.id,
        email: attorney.email,
        subdomain: attorney.subdomain,
        firm_name: attorney.firm_name,
        vapi_assistant_id: attorney.vapi_assistant_id
      });
      
      // Store in localStorage
      localStorage.setItem('attorney', JSON.stringify(attorney));
      console.log('💾 [TestAuthFlow] Attorney stored in localStorage');
      
      return { 
        success: true, 
        attorney,
        message: 'Attorney profile found and stored'
      };
    }
    
    // Test 3: Try to create attorney profile for known users
    console.log('🧪 [TestAuthFlow] Test 3: Creating attorney profile...');
    
    const knownEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];
    
    if (!knownEmails.includes(user.email)) {
      console.log('⚠️ [TestAuthFlow] User email not in known list:', user.email);
      return { 
        success: false, 
        error: 'User not in known attorney list',
        needsProfileCompletion: true
      };
    }
    
    // Create attorney profile
    const knownProfiles = {
      '<EMAIL>': {
        firm_name: 'LegalScout',
        subdomain: 'damonkost',
        vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'
      },
      '<EMAIL>': {
        firm_name: 'LegalScout',
        subdomain: 'legalscout-ai',
        vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'
      },
      '<EMAIL>': {
        firm_name: 'LegalScout',
        subdomain: 'legalscout',
        vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'
      }
    };
    
    const profileData = knownProfiles[user.email];
    const attorneyData = {
      user_id: user.id,
      email: user.email,
      name: user.user_metadata?.full_name || user.email.split('@')[0],
      ...profileData,
      phone: '+1234567890',
      practice_areas: ['General Practice'],
      welcome_message: "Hello! I'm Scout from LegalScout. How can I help you with your legal needs today?",
      vapi_instructions: "You are Scout, a legal assistant for LegalScout. Help potential clients understand their legal needs and collect relevant information for consultation. Be professional, helpful, and empathetic."
    };
    
    console.log('🧪 [TestAuthFlow] Creating attorney with data:', {
      email: attorneyData.email,
      subdomain: attorneyData.subdomain,
      firm_name: attorneyData.firm_name
    });
    
    const { data: newAttorney, error: createError } = await supabase
      .from('attorneys')
      .insert(attorneyData)
      .select()
      .single();
    
    if (createError) {
      console.error('❌ [TestAuthFlow] Attorney creation error:', createError);
      return { success: false, error: 'Failed to create attorney profile' };
    }
    
    console.log('✅ [TestAuthFlow] Attorney profile created:', {
      id: newAttorney.id,
      email: newAttorney.email,
      subdomain: newAttorney.subdomain,
      firm_name: newAttorney.firm_name
    });
    
    // Store in localStorage
    localStorage.setItem('attorney', JSON.stringify(newAttorney));
    console.log('💾 [TestAuthFlow] New attorney stored in localStorage');
    
    return { 
      success: true, 
      attorney: newAttorney,
      message: 'Attorney profile created and stored'
    };
    
  } catch (error) {
    console.error('❌ [TestAuthFlow] Test failed:', error);
    return { success: false, error: error.message };
  }
};

// Also add a simple function to check current state
window.checkAuthState = function() {
  console.log('🔍 [CheckAuthState] Current authentication state:');
  
  // Check localStorage
  const storedAttorney = localStorage.getItem('attorney');
  if (storedAttorney) {
    try {
      const attorney = JSON.parse(storedAttorney);
      console.log('✅ [CheckAuthState] Attorney in localStorage:', {
        email: attorney.email,
        subdomain: attorney.subdomain,
        firm_name: attorney.firm_name
      });
    } catch (error) {
      console.error('❌ [CheckAuthState] Invalid attorney data in localStorage');
    }
  } else {
    console.log('⚠️ [CheckAuthState] No attorney in localStorage');
  }
  
  // Check if robust state handler is available
  if (typeof window.resolveAttorneyState === 'function') {
    console.log('✅ [CheckAuthState] Robust state handler available');
  } else {
    console.log('⚠️ [CheckAuthState] Robust state handler not available');
  }
  
  // Check current URL
  console.log('🌐 [CheckAuthState] Current URL:', window.location.href);
  console.log('🌐 [CheckAuthState] Current pathname:', window.location.pathname);
};

console.log('🧪 [TestAuthFlow] Test functions loaded. Run testAuthFlow() or checkAuthState() in console.');
