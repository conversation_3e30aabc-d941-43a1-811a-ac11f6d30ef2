// PORTABLE MERGE UTILITY - Copy this to your other codebase
// Usage: node merge-analysis.js <path-to-analysis-report.json>

import fs from 'fs';
import path from 'path';

function analyzeMergeCandidate(reportPath) {
  const analysis = JSON.parse(fs.readFileSync(reportPath, 'utf8'));

  console.log('🔍 MERGE ANALYSIS FOR:', analysis.workspace);
  console.log('📅 Generated:', analysis.timestamp);
  console.log('🌿 Source Branch:', analysis.branch);
  console.log('✅ Application Status:', analysis.applicationRunning?.status);
  console.log('🚀 Running on Port:', analysis.applicationRunning?.port);
  console.log('');

  // Show modern components worth considering
  console.log('🎯 MODERN COMPONENTS TO CONSIDER:');
  Object.entries(analysis.modernComponents).forEach(([category, components]) => {
    console.log(`\n📁 ${category.toUpperCase()}:`);
    Object.entries(components).forEach(([name, info]) => {
      if (info.status === 'MODERN_CLEAN') {
        console.log(`  ✅ ${name} - ${info.recommendation}`);
        if (info.features) {
          info.features.forEach(feature => {
            console.log(`     • ${feature}`);
          });
        }
      }
    });
  });

  // Show architectural patterns
  console.log('\n🏗️  MODERN ARCHITECTURAL PATTERNS:');
  analysis.architecturalPatterns?.good?.forEach(pattern => {
    console.log(`  ✅ ${pattern}`);
  });

  console.log('\n🚫 ANTI-PATTERNS SUCCESSFULLY AVOIDED:');
  analysis.architecturalPatterns?.avoided?.forEach(antiPattern => {
    console.log(`  ❌ ${antiPattern}`);
  });

  // Show Vapi integration quality
  console.log('\n🎤 VAPI INTEGRATION QUALITY:');
  console.log(`  Status: ${analysis.vapiIntegration?.pattern}`);
  console.log(`  Anti-patterns: ${analysis.vapiIntegration?.antiPatterns}`);
  analysis.vapiIntegration?.features?.forEach(feature => {
    console.log(`  ✅ ${feature}`);
  });

  // Show recommendations
  console.log('\n💎 COMPONENTS TO KEEP:');
  analysis.recommendations?.keep?.forEach(item => {
    console.log(`  🔥 ${item.component} - ${item.reason}`);
  });

  console.log('\n🚫 PATTERNS TO AVOID:');
  analysis.recommendations?.avoid?.forEach(pattern => {
    console.log(`  ❌ ${pattern}`);
  });

  // Show code quality metrics
  console.log('\n📊 CODE QUALITY METRICS:');
  Object.entries(analysis.codeQuality || {}).forEach(([metric, value]) => {
    console.log(`  ${metric}: ${value}`);
  });

  // Show next steps
  console.log('\n🎯 RECOMMENDED NEXT STEPS:');
  analysis.nextSteps?.forEach((step, index) => {
    console.log(`  ${index + 1}. ${step}`);
  });

  console.log('\n🎉 SUMMARY:');
  console.log(`  • Application is running successfully on port ${analysis.applicationRunning?.port}`);
  console.log(`  • Modern, clean codebase with no legacy anti-patterns detected`);
  console.log(`  • Service-oriented architecture with proper separation of concerns`);
  console.log(`  • OAuth-based authentication with assistant-specific configurations`);
  console.log(`  • Recommended merge strategy: ${analysis.recommendations?.merge_strategy}`);

  return analysis;
}

if (process.argv[2]) {
  try {
    analyzeMergeCandidate(process.argv[2]);
  } catch (error) {
    console.error('Error analyzing merge candidate:', error.message);
    console.log('\nUsage: node merge-analysis.js <analysis-report.json>');
  }
} else {
  console.log('Usage: node merge-analysis.js <analysis-report.json>');
  console.log('\nThis utility analyzes the codebase analysis report and provides');
  console.log('recommendations for merging modern components while avoiding legacy patterns.');
}
