import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { getPracticeAreaTemplate } from '../../utils/schemaGenerator';
import JsonEditor from '../common/JsonEditor';
import { useAssistantAware } from '../../contexts/AssistantAwareContext';
import {
  DEFAULT_STRUCTURED_DATA_SCHEMA,
  DEFAULT_SUCCESS_EVALUATION_PROMPT,
  DEFAULT_SUMMARY_PROMPT,
  DEFAULT_STRUCTURED_DATA_PROMPT,
  getTemplateByPracticeArea
} from '../../config/defaultTemplates';
import './CustomFieldsTab.css';

/**
 * CustomFieldsTab component for the attorney dashboard
 * Allows attorneys to define custom fields for data collection
 */
const CustomFieldsTab = ({ attorney, onUpdate }) => {
  // State for form data
  const [customFields, setCustomFields] = useState([]);
  const [summaryPrompt, setSummaryPrompt] = useState('');
  const [structuredDataPrompt, setStructuredDataPrompt] = useState('');
  const [successEvaluationPrompt, setSuccessEvaluationPrompt] = useState('');
  const [structuredDataSchema, setStructuredDataSchema] = useState(DEFAULT_STRUCTURED_DATA_SCHEMA);
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [forceRefreshKey, setForceRefreshKey] = useState(0);

  // Use AssistantAware context to get current assistant ID
  const { currentAssistant, isAssistantSelected } = useAssistantAware();

  // Get the current assistant ID from context (preferred) or fallback to attorney
  const currentAssistantId = currentAssistant?.id || attorney?.current_assistant_id || attorney?.vapi_assistant_id;

  // Debug logging for assistant changes
  console.log('[CustomFieldsTab] Render - Current Assistant Context:', {
    currentAssistant: currentAssistant,
    currentAssistantId: currentAssistantId,
    isAssistantSelected: isAssistantSelected,
    attorneyId: attorney?.id
  });

  // Force refresh when assistant changes
  useEffect(() => {
    console.log('[CustomFieldsTab] Assistant changed, forcing refresh:', currentAssistantId);
    setForceRefreshKey(prev => prev + 1);
  }, [currentAssistantId]);

  // Load data from assistant-specific configuration when component mounts
  useEffect(() => {
    console.log('[CustomFieldsTab] useEffect triggered with assistant:', currentAssistantId);

    if (currentAssistantId && attorney?.id) {
      // Clear existing data first to prevent stale data display
      setCustomFields([]);
      setSummaryPrompt('');
      setStructuredDataPrompt('');
      setSuccessEvaluationPrompt('');
      setStructuredDataSchema(DEFAULT_STRUCTURED_DATA_SCHEMA);

      loadAssistantConfiguration();
    } else if (attorney) {
      // Fallback to attorney-level data if no assistant selected
      loadFromAttorneyData();
    } else {
      console.log('[CustomFieldsTab] No assistant selected or no attorney');
    }
  }, [currentAssistantId, attorney?.id, forceRefreshKey]);

  // Load from attorney-level data (fallback or default)
  const loadFromAttorneyData = () => {
    try {
      console.log('[CustomFieldsTab] Loading from attorney data:', {
        id: attorney.id,
        hasSummaryPrompt: !!attorney.summary_prompt,
        hasStructuredPrompt: !!attorney.structured_data_prompt,
        hasSuccessPrompt: !!attorney.success_evaluation_prompt,
        hasCustomFields: !!attorney.custom_fields
      });

      // Parse custom fields if they exist
      if (attorney.custom_fields) {
        const fields = typeof attorney.custom_fields === 'string'
          ? JSON.parse(attorney.custom_fields)
          : attorney.custom_fields;

        setCustomFields(Array.isArray(fields) ? fields : []);
        console.log('[CustomFieldsTab] Set custom fields from attorney:', fields?.length || 0);
      } else {
        // Use General Purpose template as default
        const generalTemplate = getTemplateByPracticeArea('General Purpose');
        setCustomFields(generalTemplate.custom_fields || []);
        console.log('[CustomFieldsTab] Using General Purpose template custom fields');
      }

      // Set prompts from attorney or use defaults
      setSummaryPrompt(attorney.summary_prompt || DEFAULT_SUMMARY_PROMPT);
      setStructuredDataPrompt(attorney.structured_data_prompt || DEFAULT_STRUCTURED_DATA_PROMPT);
      setSuccessEvaluationPrompt(attorney.success_evaluation_prompt || DEFAULT_SUCCESS_EVALUATION_PROMPT);

      // Set structured data schema
      if (attorney.structured_data_schema) {
        const schema = typeof attorney.structured_data_schema === 'string'
          ? JSON.parse(attorney.structured_data_schema)
          : attorney.structured_data_schema;
        setStructuredDataSchema(schema);
      } else {
        setStructuredDataSchema(DEFAULT_STRUCTURED_DATA_SCHEMA);
      }
    } catch (error) {
      console.error('[CustomFieldsTab] Error loading attorney data:', error);
      // Use General Purpose template as fallback
      const generalTemplate = getTemplateByPracticeArea('General Purpose');
      setCustomFields(generalTemplate.custom_fields || []);
      setSummaryPrompt(DEFAULT_SUMMARY_PROMPT);
      setStructuredDataPrompt(DEFAULT_STRUCTURED_DATA_PROMPT);
      setSuccessEvaluationPrompt(DEFAULT_SUCCESS_EVALUATION_PROMPT);
      setStructuredDataSchema(DEFAULT_STRUCTURED_DATA_SCHEMA);
    }
  };

  // Load assistant-specific configuration
  const loadAssistantConfiguration = async () => {
    try {
      console.log('[CustomFieldsTab] Loading assistant configuration for:', currentAssistantId);

      // Step 1: Load from Vapi first (source of truth)
      let vapiAssistantData = null;
      try {
        const { vapiMcpService } = await import('../../services/vapiMcpService');
        await vapiMcpService.ensureConnection();
        vapiAssistantData = await vapiMcpService.getAssistant(currentAssistantId);
        console.log('[CustomFieldsTab] Loaded assistant data from Vapi:', !!vapiAssistantData);
      } catch (vapiError) {
        console.log('[CustomFieldsTab] Could not load from Vapi:', vapiError.message);
      }

      // Step 2: Try to load from assistant_ui_configs table
      const { data: config, error } = await supabase
        .from('assistant_ui_configs')
        .select('custom_fields')
        .eq('assistant_id', currentAssistantId)
        .eq('attorney_id', attorney.id)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.log('[CustomFieldsTab] Error loading assistant config, falling back to attorney data:', error.message);
        loadFromAttorneyData();
        return;
      }

      // Step 3: Prioritize Vapi data over Supabase data
      let finalData = null;

      if (vapiAssistantData?.analysisPlan) {
        console.log('[CustomFieldsTab] Using Vapi analysisPlan data as source of truth');

        // Extract custom fields from Vapi structured data schema
        let extractedCustomFields = [];
        const schema = vapiAssistantData.analysisPlan.structuredDataSchema;
        if (schema?.properties) {
          extractedCustomFields = Object.entries(schema.properties).map(([key, value]) => ({
            name: key,
            label: value.title || key,
            type: value.type || 'string',
            description: value.description || '',
            required: schema.required?.includes(key) || false,
            options: value.enum || []
          }));
          console.log('[CustomFieldsTab] Extracted custom fields from Vapi schema:', extractedCustomFields);
        }

        finalData = {
          customFields: extractedCustomFields,
          summaryPrompt: vapiAssistantData.analysisPlan.summaryPrompt || DEFAULT_SUMMARY_PROMPT,
          structuredDataPrompt: vapiAssistantData.analysisPlan.structuredDataPrompt || DEFAULT_STRUCTURED_DATA_PROMPT,
          successEvaluationPrompt: vapiAssistantData.analysisPlan.successEvaluationPrompt || DEFAULT_SUCCESS_EVALUATION_PROMPT,
          structuredDataSchema: vapiAssistantData.analysisPlan.structuredDataSchema || DEFAULT_STRUCTURED_DATA_SCHEMA
        };

        // If no custom fields from Vapi schema, try to get from Supabase config as fallback
        if (extractedCustomFields.length === 0 && config && config.custom_fields) {
          const assistantConfig = typeof config.custom_fields === 'string'
            ? JSON.parse(config.custom_fields)
            : config.custom_fields;

          if (assistantConfig.dataCollection?.customFields) {
            finalData.customFields = assistantConfig.dataCollection.customFields;
          } else if (Array.isArray(assistantConfig)) {
            finalData.customFields = assistantConfig;
          }
        }
      } else if (config && config.custom_fields) {
        console.log('[CustomFieldsTab] Using Supabase assistant configuration');
        const assistantConfig = typeof config.custom_fields === 'string'
          ? JSON.parse(config.custom_fields)
          : config.custom_fields;

        if (assistantConfig.dataCollection) {
          finalData = assistantConfig.dataCollection;
        } else {
          // Legacy format
          finalData = {
            customFields: Array.isArray(assistantConfig) ? assistantConfig : [],
            summaryPrompt: attorney.summary_prompt || DEFAULT_SUMMARY_PROMPT,
            structuredDataPrompt: attorney.structured_data_prompt || DEFAULT_STRUCTURED_DATA_PROMPT,
            successEvaluationPrompt: attorney.success_evaluation_prompt || DEFAULT_SUCCESS_EVALUATION_PROMPT,
            structuredDataSchema: attorney.structured_data_schema || DEFAULT_STRUCTURED_DATA_SCHEMA
          };
        }
      } else {
        // No configuration found, fall back to attorney data
        console.log('[CustomFieldsTab] No assistant configuration found, using attorney data');
        loadFromAttorneyData();
        return;
      }

      // Step 4: Set the form data
      setCustomFields(Array.isArray(finalData.customFields) ? finalData.customFields : []);
      setSummaryPrompt(finalData.summaryPrompt || DEFAULT_SUMMARY_PROMPT);
      setStructuredDataPrompt(finalData.structuredDataPrompt || DEFAULT_STRUCTURED_DATA_PROMPT);
      setSuccessEvaluationPrompt(finalData.successEvaluationPrompt || DEFAULT_SUCCESS_EVALUATION_PROMPT);
      setStructuredDataSchema(finalData.structuredDataSchema || DEFAULT_STRUCTURED_DATA_SCHEMA);

      console.log('[CustomFieldsTab] Loaded configuration - Vapi data prioritized');
    } catch (error) {
      console.error('[CustomFieldsTab] Error loading assistant configuration:', error);
      loadFromAttorneyData();
    }
  };

  // Force refresh function to manually trigger data reload
  const forceRefresh = () => {
    console.log('[CustomFieldsTab] Force refresh triggered');
    setForceRefreshKey(prev => prev + 1);
  };

  // Add a new custom field
  const addCustomField = () => {
    setCustomFields([
      ...customFields,
      {
        name: '',
        description: '',
        type: 'string',
        required: false,
        options: []
      }
    ]);
  };

  // Remove a custom field
  const removeCustomField = (index) => {
    const updatedFields = [...customFields];
    updatedFields.splice(index, 1);
    setCustomFields(updatedFields);
  };

  // Update a custom field property
  const updateCustomField = (index, property, value) => {
    const updatedFields = [...customFields];
    updatedFields[index] = {
      ...updatedFields[index],
      [property]: value
    };
    setCustomFields(updatedFields);
  };

  // Apply a template based on practice area
  const applyTemplate = (templateName) => {
    if (!templateName) return;

    // Get template from both sources to ensure we have all fields
    const schemaTemplate = getPracticeAreaTemplate(templateName);
    const crmTemplate = getTemplateByPracticeArea(templateName);

    if (schemaTemplate || crmTemplate) {
      // Set custom fields from schema template
      if (schemaTemplate?.custom_fields) {
        setCustomFields(schemaTemplate.custom_fields);
      }

      // Set prompts from both templates
      setSummaryPrompt(crmTemplate.summary_prompt || schemaTemplate.summary_prompt || DEFAULT_SUMMARY_PROMPT);
      setStructuredDataPrompt(crmTemplate.structured_data_prompt || schemaTemplate.structured_data_prompt || DEFAULT_STRUCTURED_DATA_PROMPT);
      setSuccessEvaluationPrompt(crmTemplate.success_prompt || DEFAULT_SUCCESS_EVALUATION_PROMPT);

      // Set structured data schema
      if (crmTemplate.structured_data_schema) {
        setStructuredDataSchema(crmTemplate.structured_data_schema);
      }

      setSelectedTemplate('');
    }
  };

  // Save changes to database
  const saveChanges = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Validate custom fields
      customFields.forEach(field => {
        if (!field.name) {
          throw new Error('All custom fields must have a name');
        }
      });

      // Validate assistant and attorney IDs
      if (!currentAssistantId) {
        throw new Error('No assistant selected');
      }
      if (!attorney || !attorney.id) {
        throw new Error('Attorney ID is missing or invalid');
      }

      console.log('Saving custom fields for assistant ID:', currentAssistantId);

      // Step 1: Save to Supabase (for custom fields and backup)
      const dataCollectionConfig = {
        dataCollection: {
          customFields: customFields,
          summaryPrompt: summaryPrompt,
          structuredDataPrompt: structuredDataPrompt,
          successEvaluationPrompt: successEvaluationPrompt,
          structuredDataSchema: structuredDataSchema
        }
      };

      const { error: upsertError } = await supabase
        .from('assistant_ui_configs')
        .upsert({
          assistant_id: currentAssistantId,
          attorney_id: attorney.id,
          custom_fields: dataCollectionConfig,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'assistant_id,attorney_id'
        });

      if (upsertError) throw upsertError;

      // Step 2: Generate structured data schema from custom fields and sync to Vapi
      try {
        const { vapiMcpService } = await import('../../services/vapiMcpService');
        await vapiMcpService.ensureConnection();

        // Generate structured data schema from custom fields
        let generatedSchema = structuredDataSchema;
        if (customFields.length > 0) {
          const properties = {};
          const required = [];

          customFields.forEach(field => {
            if (field.name) {
              properties[field.name] = {
                type: field.type || 'string',
                title: field.label || field.name,
                description: field.description || ''
              };

              if (field.type === 'enum' && field.options && field.options.length > 0) {
                properties[field.name].enum = field.options;
              }

              if (field.required) {
                required.push(field.name);
              }
            }
          });

          generatedSchema = {
            type: 'object',
            properties: properties,
            required: required,
            additionalProperties: false
          };

          console.log('[CustomFieldsTab] Generated schema from custom fields:', generatedSchema);
        }

        const vapiUpdateData = {
          analysisPlan: {
            summaryPrompt: summaryPrompt,
            structuredDataPrompt: structuredDataPrompt,
            structuredDataSchema: generatedSchema,
            successEvaluationPrompt: successEvaluationPrompt
          }
        };

        await vapiMcpService.updateAssistant(currentAssistantId, vapiUpdateData);
        console.log('[CustomFieldsTab] Successfully synced analysis configuration to Vapi with generated schema');
      } catch (vapiError) {
        console.warn('[CustomFieldsTab] Failed to sync to Vapi:', vapiError.message);
        // Don't throw error - Supabase save was successful
      }

      // Show success message
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);

      // Call onUpdate callback if provided
      if (onUpdate) {
        onUpdate({
          ...attorney,
          custom_fields: customFields,
          summary_prompt: summaryPrompt,
          structured_data_prompt: structuredDataPrompt,
          success_evaluation_prompt: successEvaluationPrompt,
          structured_data_schema: structuredDataSchema
        });
      }
    } catch (error) {
      console.error('Error saving custom fields:', error);
      setError(error.message || 'Failed to save custom fields');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="custom-fields-tab">
      <div className="dashboard-card">
        <h2>Data Collection & Analysis</h2>
        <p className="card-description">
          Configure how your AI assistant analyzes conversations and extracts structured data during consultations.
          Define custom fields, evaluation criteria, and reporting formats tailored to your practice area.
          The AI will naturally guide conversations to gather this information and provide structured data for your CRM.
          {isAssistantSelected && currentAssistant?.assistantName && (
            <span className="assistant-context">
              <br /><strong>Currently configuring:</strong> {currentAssistant.assistantName}
            </span>
          )}
          <br />
          <small style={{ color: '#666', fontStyle: 'italic' }}>
            📡 <strong>Live Configuration:</strong> Analysis prompts are synced with your assistant's active settings.
            Changes are applied immediately to ensure consistency.
          </small>
        </p>

        <div className="template-selector">
          <label htmlFor="practiceAreaTemplate">Legal Practice Area:</label>
          <select
            id="practiceAreaTemplate"
            value={selectedTemplate}
            onChange={(e) => setSelectedTemplate(e.target.value)}
            className="form-control"
          >
            <option value="">Select a practice area</option>
            <option value="General Purpose">General Purpose Legal CRM</option>
            <option value="Personal Injury">Personal Injury</option>
            <option value="Family Law">Family Law</option>
            <option value="Estate Planning">Estate Planning</option>
            <option value="Criminal Defense">Criminal Defense</option>
            <option value="Business Law">Business Law</option>
            <option value="Immigration">Immigration</option>
            <option value="Intellectual Property">Intellectual Property</option>
            <option value="Real Estate">Real Estate</option>
            <option value="Employment Law">Employment Law</option>
          </select>
          <button
            className="btn btn-secondary"
            onClick={() => applyTemplate(selectedTemplate)}
            disabled={!selectedTemplate}
            title="Load the recommended AI CRM configuration for this practice area"
          >
            Apply AI CRM Template
          </button>
          <button
            className="btn btn-outline-secondary"
            onClick={forceRefresh}
            title="Force reload attorney data if fields are not showing"
            style={{ marginLeft: '10px' }}
          >
            🔄 Refresh Data
          </button>
        </div>

        <div className="prompts-section">
          <div className="form-group">
            <label htmlFor="summaryPrompt">Summary Prompt</label>
            <p className="field-description">
              This prompt is used to generate a summary of each consultation.
            </p>
            <textarea
              id="summaryPrompt"
              className="form-control"
              value={summaryPrompt}
              onChange={(e) => setSummaryPrompt(e.target.value)}
              placeholder="Provide a concise summary of this legal consultation, highlighting the client's main concerns, legal issues identified, and next steps recommended."
              rows={3}
            />
          </div>

          <div className="form-group">
            <label htmlFor="successEvaluationPrompt">Success Evaluation Prompt</label>
            <p className="field-description">
              This prompt is used to evaluate if a call was successful based on specific criteria.
            </p>
            <textarea
              id="successEvaluationPrompt"
              className="form-control"
              value={successEvaluationPrompt}
              onChange={(e) => setSuccessEvaluationPrompt(e.target.value)}
              placeholder="Evaluate whether this call was successful based on the following criteria: 1) Did the caller provide their name? 2) Did the caller explain their legal issue? 3) Did the caller provide contact information?"
              rows={3}
            />
          </div>

          <div className="form-group">
            <label htmlFor="structuredDataPrompt">Structured Data Prompt</label>
            <p className="field-description">
              This prompt is used to extract structured data from consultations.
            </p>
            <textarea
              id="structuredDataPrompt"
              className="form-control"
              value={structuredDataPrompt}
              onChange={(e) => setStructuredDataPrompt(e.target.value)}
              placeholder="Extract key information from this legal consultation, including all custom fields defined below."
              rows={3}
            />
          </div>

          <div className="advanced-options-toggle">
            <button
              type="button"
              className="btn btn-link"
              onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
            >
              {showAdvancedOptions ? 'Hide Advanced Options' : 'Show Advanced Options'}
            </button>
          </div>

          {showAdvancedOptions && (
            <div className="advanced-options">
              <div className="form-group">
                <label htmlFor="structuredDataSchema">Structured Data Schema (JSON)</label>
                <p className="field-description">
                  This schema defines the structure of the data extracted from conversations.
                  It follows the JSON Schema format and is used by the AI to format extracted information.
                </p>
                <JsonEditor
                  id="structuredDataSchema"
                  value={structuredDataSchema}
                  onChange={setStructuredDataSchema}
                  height="400px"
                />
              </div>
            </div>
          )}
        </div>

        <h3>Custom Fields</h3>
        <p className="field-description">
          Define the specific information you want to collect from potential clients.
        </p>

        {customFields.map((field, index) => (
          <div key={index} className="custom-field-row">
            <div className="form-group">
              <label htmlFor={`fieldName-${index}`}>Field Name</label>
              <input
                type="text"
                id={`fieldName-${index}`}
                className="form-control"
                value={field.name || ''}
                onChange={(e) => updateCustomField(index, 'name', e.target.value)}
                placeholder="e.g., accidentDate"
              />
            </div>

            <div className="form-group">
              <label htmlFor={`fieldDescription-${index}`}>Description</label>
              <input
                type="text"
                id={`fieldDescription-${index}`}
                className="form-control"
                value={field.description || ''}
                onChange={(e) => updateCustomField(index, 'description', e.target.value)}
                placeholder="e.g., Date when the accident occurred"
              />
            </div>

            <div className="form-group">
              <label htmlFor={`fieldType-${index}`}>Type</label>
              <select
                id={`fieldType-${index}`}
                className="form-control"
                value={field.type || 'string'}
                onChange={(e) => updateCustomField(index, 'type', e.target.value)}
              >
                <option value="string">Text</option>
                <option value="number">Number</option>
                <option value="boolean">Yes/No</option>
                <option value="enum">Multiple Choice</option>
              </select>
            </div>

            {field.type === 'enum' && (
              <div className="form-group">
                <label htmlFor={`fieldOptions-${index}`}>Options (comma separated)</label>
                <input
                  type="text"
                  id={`fieldOptions-${index}`}
                  className="form-control"
                  value={field.options?.join(', ') || ''}
                  onChange={(e) => updateCustomField(index, 'options', e.target.value.split(',').map(o => o.trim()))}
                  placeholder="e.g., Option 1, Option 2, Option 3"
                />
              </div>
            )}

            <div className="form-group checkbox-group">
              <label htmlFor={`fieldRequired-${index}`}>
                <input
                  type="checkbox"
                  id={`fieldRequired-${index}`}
                  checked={field.required || false}
                  onChange={(e) => updateCustomField(index, 'required', e.target.checked)}
                />
                Required
              </label>
            </div>

            <button
              className="btn btn-danger remove-field-btn"
              onClick={() => removeCustomField(index)}
              type="button"
            >
              Remove
            </button>
          </div>
        ))}

        <div className="field-actions">
          <button
            className="btn btn-primary"
            onClick={addCustomField}
            type="button"
          >
            Add Custom Field
          </button>
        </div>

        <div className="form-actions">
          <button
            className="btn btn-primary save-btn"
            onClick={saveChanges}
            disabled={isLoading}
          >
            {isLoading ? 'Saving...' : 'Save Changes'}
          </button>

          {success && (
            <div className="success-message">
              Changes saved successfully!
            </div>
          )}

          {error && (
            <div className="error-message">
              Error: {error}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CustomFieldsTab;
