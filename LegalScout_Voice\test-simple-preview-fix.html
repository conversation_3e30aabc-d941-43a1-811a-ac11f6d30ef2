<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple Preview Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #3B82F6;
        }
        .success { border-left-color: #10b981; }
        .error { border-left-color: #ef4444; }
        .info { border-left-color: #f59e0b; }
        button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #2563eb; }
        pre {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .iframe-container {
            margin: 20px 0;
            border: 2px solid #3B82F6;
            border-radius: 8px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <h1>🔧 Test Simple Preview Fix</h1>
    <p>Testing that SimplePreviewPage now uses the new simple subdomain service</p>

    <div class="test-section">
        <h3>Test Results</h3>
        <div id="results"></div>
    </div>

    <div class="test-section">
        <h3>Actions</h3>
        <button onclick="testSimpleService()">Test Simple Service</button>
        <button onclick="testPreviewPage()">Test Preview Page</button>
        <button onclick="showPreviewIframe()">Show Preview Iframe</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div id="iframe-container" class="iframe-container" style="display: none;">
        <h3>Preview Iframe Test</h3>
        <iframe id="preview-iframe" src=""></iframe>
    </div>

    <script type="module">
        function addResult(type, message) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `<pre>${message}</pre>`;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        window.addResult = addResult;
        window.clearResults = clearResults;

        window.testSimpleService = async function() {
            addResult('info', '🔧 Testing simple subdomain service directly...');
            
            try {
                const { simpleSubdomainService } = await import('./src/services/simpleSubdomainService.js');
                
                const config = await simpleSubdomainService.getSubdomainConfig('assistant1test');
                
                if (config) {
                    addResult('success', `✅ Simple service working:
🏢 Firm Name: ${config.firmName}
🎨 Primary Color: ${config.primaryColor}
🤖 Assistant ID: ${config.vapi_assistant_id}
🔧 Loaded Via: ${config.loadedVia}
📊 Has UI Config: ${config.hasUIConfig}
📞 Has Call Config: ${config.hasCallConfig}`);
                } else {
                    addResult('error', '❌ Simple service returned null');
                }
                
            } catch (error) {
                addResult('error', `❌ Simple service failed: ${error.message}`);
                console.error('Simple service error:', error);
            }
        };

        window.testPreviewPage = async function() {
            addResult('info', '📄 Testing preview page logic...');
            
            try {
                // Simulate what SimplePreviewPage does
                addResult('info', 'Simulating SimplePreviewPage loadAssistantConfig...');
                
                const { simpleSubdomainService } = await import('./src/services/simpleSubdomainService.js');
                const config = await simpleSubdomainService.getSubdomainConfig('assistant1test');
                
                if (config) {
                    // Simulate the config merging logic
                    const newConfig = {
                        firmName: 'Your Law Firm', // Default
                        primaryColor: '#4B74AA',   // Default
                        // ... other defaults
                    };
                    
                    // Merge assistant config (like SimplePreviewPage does)
                    Object.assign(newConfig, {
                        firmName: config.firmName,
                        primaryColor: config.primaryColor,
                        secondaryColor: config.secondaryColor,
                        buttonColor: config.buttonColor,
                        backgroundColor: config.backgroundColor,
                        welcomeMessage: config.welcomeMessage,
                        vapi_assistant_id: config.vapi_assistant_id,
                        voiceId: config.voiceId
                    });
                    
                    addResult('success', `✅ Preview page logic working:
🏢 Final Firm Name: ${newConfig.firmName}
🎨 Final Primary Color: ${newConfig.primaryColor}
🤖 Final Assistant ID: ${newConfig.vapi_assistant_id}
🎤 Final Voice ID: ${newConfig.voiceId}
💬 Final Welcome: ${newConfig.welcomeMessage?.substring(0, 50)}...`);
                    
                    // Check if it's different from defaults
                    const isCustomized = newConfig.firmName !== 'Your Law Firm' || 
                                       newConfig.primaryColor !== '#4B74AA';
                    
                    if (isCustomized) {
                        addResult('success', '🎉 SUCCESS! Preview page will show customized data, not defaults!');
                    } else {
                        addResult('error', '❌ Preview page still showing defaults');
                    }
                } else {
                    addResult('error', '❌ Preview page logic failed - no config returned');
                }
                
            } catch (error) {
                addResult('error', `❌ Preview page test failed: ${error.message}`);
                console.error('Preview page test error:', error);
            }
        };

        window.showPreviewIframe = function() {
            addResult('info', '🖼️ Loading preview iframe...');
            
            const container = document.getElementById('iframe-container');
            const iframe = document.getElementById('preview-iframe');
            
            // Use the same URL pattern as the actual app
            const previewUrl = `/simple-preview?subdomain=assistant1test&theme=dark&useEnhancedPreview=true`;
            
            iframe.src = previewUrl;
            container.style.display = 'block';
            
            addResult('success', `✅ Preview iframe loaded with URL: ${previewUrl}`);
            
            // Listen for iframe load
            iframe.onload = function() {
                addResult('info', '📄 Iframe loaded successfully');
                
                // Try to check the iframe content (if same origin)
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const firmNameElement = iframeDoc.querySelector('[class*="firm"], [class*="title"], h1, h2');
                    
                    if (firmNameElement) {
                        const firmName = firmNameElement.textContent;
                        if (firmName && firmName !== 'Your Law Firm') {
                            addResult('success', `🎉 IFRAME SUCCESS! Showing: "${firmName}"`);
                        } else {
                            addResult('error', `❌ Iframe still showing default: "${firmName}"`);
                        }
                    } else {
                        addResult('info', '📄 Iframe loaded but cannot detect firm name element');
                    }
                } catch (e) {
                    addResult('info', '📄 Iframe loaded (cross-origin, cannot inspect content)');
                }
            };
            
            iframe.onerror = function() {
                addResult('error', '❌ Iframe failed to load');
            };
        };

        // Auto-run tests
        window.addEventListener('load', () => {
            setTimeout(() => {
                testSimpleService();
                setTimeout(() => {
                    testPreviewPage();
                }, 1000);
            }, 1000);
        });
    </script>
</body>
</html>
