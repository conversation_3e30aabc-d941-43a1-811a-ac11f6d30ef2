import React, { useState, useEffect } from 'react';
import './LoginButton.css';

export const LoginButton = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authError, setAuthError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Check if we're returning from OAuth
    const params = new URLSearchParams(window.location.search);
    const code = params.get('code');
    const state = params.get('state');
    
    if (code && state) {
      handleOAuthCallback(code, state);
    }
  }, []);

  const handleOAuthCallback = async (code, state) => {
    setIsLoading(true);
    try {
      // Handle OAuth callback and complete authentication
      const response = await fetch('/api/auth/callback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code, state })
      });

      if (!response.ok) {
        throw new Error('Failed to complete authentication');
      }

      // Proceed with email verification
      await verifyEmail();
    } catch (error) {
      console.error('OAuth callback error:', error);
      setAuthError('Authentication failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const verifyEmail = async () => {
    try {
      const verifyResponse = await fetch('/api/verify-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'mcp__GMAIL_GET_PEOPLE',
          params: {
            resource_name: 'people/me',
            person_fields: 'emailAddresses'
          }
        })
      });

      const data = await verifyResponse.json();
      
      const hasLegalScoutEmail = data.emailAddresses?.some(
        email => email.value?.endsWith('@legalscout.ai')
      );

      if (hasLegalScoutEmail) {
        setIsAuthenticated(true);
        localStorage.setItem('isAuthenticated', 'true');
        // Remove OAuth params from URL
        window.history.replaceState({}, document.title, window.location.pathname);
        window.location.reload();
      } else {
        setAuthError('Access restricted to @legalscout.ai email addresses only.');
      }
    } catch (error) {
      console.error('Email verification error:', error);
      throw error;
    }
  };

  const handleGoogleLogin = async () => {
    setIsLoading(true);
    setAuthError(null);

    try {
      // Check for active connection
      const connectionResponse = await fetch('/api/check-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'mcp__COMPOSIO_CHECK_ACTIVE_CONNECTION',
          params: {
            tool: 'Gmail'
          }
        })
      });

      const connectionData = await connectionResponse.json();
      
      if (!connectionData.hasActiveConnection) {
        // Initiate new connection
        const initiateResponse = await fetch('/api/initiate-connection', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'mcp__COMPOSIO_INITIATE_CONNECTION',
            params: {
              tool: 'Gmail'
            }
          })
        });

        if (!initiateResponse.ok) {
          throw new Error('Failed to initiate Gmail connection');
        }

        const { authUrl } = await initiateResponse.json();
        // Redirect to Google OAuth
        window.location.href = authUrl;
        return;
      }

      // If we have an active connection, verify email
      await verifyEmail();
    } catch (err) {
      console.error('Authentication error:', err);
      setAuthError('Failed to authenticate. Please try again.');
      setIsLoading(false);
    }
  };

  return (
    <div className="login-container">
      {!isAuthenticated && (
        <button
          onClick={handleGoogleLogin}
          className="login-button"
          disabled={isLoading}
        >
          {isLoading ? 'Connecting...' : 'Sign in with Google'}
        </button>
      )}
      
      {authError && (
        <div className="error-message">
          {authError}
        </div>
      )}
    </div>
  );
};

// Add default export
export default LoginButton;