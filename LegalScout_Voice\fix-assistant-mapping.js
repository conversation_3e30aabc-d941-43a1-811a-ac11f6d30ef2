import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

console.log('🔧 Fixing Assistant Mapping After Cleanup');
console.log('==========================================');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixAssistantMapping() {
  try {
    // The deleted assistant ID
    const deletedAssistantId = 'cd0b44b7-397e-410d-8835-ce9c3ba584b2';
    
    // New assistant ID to use (most recent from Vapi)
    const newAssistantId = '50e13a9e-22dd-4fe8-a03e-de627c5206c1';
    
    console.log(`🔍 Looking for records with deleted assistant ID: ${deletedAssistantId}`);
    
    // Check assistant_subdomains table
    const { data: subdomainData, error: subdomainError } = await supabase
      .from('assistant_subdomains')
      .select('*')
      .eq('assistant_id', deletedAssistantId);
    
    if (subdomainError) {
      console.error('❌ Error checking assistant_subdomains:', subdomainError);
      return;
    }
    
    console.log(`📊 Found ${subdomainData.length} subdomain records to update`);
    
    if (subdomainData.length > 0) {
      console.log('🔄 Updating assistant_subdomains records...');
      
      for (const record of subdomainData) {
        const { error: updateError } = await supabase
          .from('assistant_subdomains')
          .update({ assistant_id: newAssistantId })
          .eq('id', record.id);
        
        if (updateError) {
          console.error(`❌ Failed to update subdomain record ${record.id}:`, updateError);
        } else {
          console.log(`✅ Updated subdomain record: ${record.subdomain} → ${newAssistantId}`);
        }
      }
    }
    
    // Check attorneys table for assistant_id references
    const { data: attorneyData, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .eq('assistant_id', deletedAssistantId);
    
    if (attorneyError) {
      console.error('❌ Error checking attorneys:', attorneyError);
      return;
    }
    
    console.log(`📊 Found ${attorneyData.length} attorney records to update`);
    
    if (attorneyData.length > 0) {
      console.log('🔄 Updating attorney records...');
      
      for (const attorney of attorneyData) {
        const { error: updateError } = await supabase
          .from('attorneys')
          .update({ assistant_id: newAssistantId })
          .eq('id', attorney.id);
        
        if (updateError) {
          console.error(`❌ Failed to update attorney record ${attorney.id}:`, updateError);
        } else {
          console.log(`✅ Updated attorney record: ${attorney.email} → ${newAssistantId}`);
        }
      }
    }
    
    console.log('\n🎉 Assistant mapping fix completed!');
    console.log(`📝 All records now point to assistant: ${newAssistantId}`);
    
  } catch (error) {
    console.error('❌ Fix failed:', error);
  }
}

fixAssistantMapping();
