#!/usr/bin/env node

/**
 * Migrate to Assistant-Subdomain Architecture
 * 
 * This script migrates the existing attorney-level subdomains to 
 * assistant-level subdomains for proper routing and isolation.
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config();

// Initialize Supabase with the same credentials your app uses
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_KEY || process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';

const supabase = createClient(supabaseUrl, supabaseKey);

async function migrateToAssistantSubdomains() {
  console.log('🚀 Migrating to Assistant-Subdomain Architecture');
  console.log('================================================');

  try {
    // Step 1: Create the assistant_subdomains table if it doesn't exist
    console.log('\n📋 Step 1: Ensuring assistant_subdomains table exists...');
    
    // Run the migration SQL
    const migrationSql = `
      -- Create assistant_subdomains table if it doesn't exist
      CREATE TABLE IF NOT EXISTS assistant_subdomains (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        assistant_id TEXT NOT NULL UNIQUE,
        subdomain TEXT UNIQUE NOT NULL,
        attorney_id UUID NOT NULL REFERENCES attorneys(id) ON DELETE CASCADE,
        is_primary BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Create indexes
      CREATE INDEX IF NOT EXISTS idx_assistant_subdomains_subdomain ON assistant_subdomains (subdomain);
      CREATE INDEX IF NOT EXISTS idx_assistant_subdomains_assistant_id ON assistant_subdomains (assistant_id);
      CREATE INDEX IF NOT EXISTS idx_assistant_subdomains_attorney_id ON assistant_subdomains (attorney_id);

      -- Create view
      CREATE OR REPLACE VIEW v_subdomain_assistant_lookup AS
      SELECT 
        asd.subdomain,
        asd.assistant_id,
        asd.is_primary,
        asd.is_active,
        a.id as attorney_id,
        a.firm_name,
        a.email,
        asd.created_at,
        asd.updated_at
      FROM assistant_subdomains asd
      JOIN attorneys a ON asd.attorney_id = a.id
      WHERE asd.is_active = TRUE;
    `;

    const { error: tableError } = await supabase.rpc('exec_sql', { sql: migrationSql });
    if (tableError) {
      console.log('Table creation result:', tableError);
    } else {
      console.log('✅ Table and view created successfully');
    }

    // Step 2: Get all attorneys with subdomains and assistants
    console.log('\n📊 Step 2: Getting attorneys with subdomains and assistants...');
    
    const { data: attorneys, error: attorneysError } = await supabase
      .from('attorneys')
      .select('id, subdomain, vapi_assistant_id, current_assistant_id, firm_name, email')
      .not('subdomain', 'is', null);

    if (attorneysError) {
      throw attorneysError;
    }

    console.log(`Found ${attorneys.length} attorneys with subdomains`);

    // Step 3: Migrate each attorney's assistants
    console.log('\n🔄 Step 3: Migrating assistant-subdomain mappings...');
    
    const results = [];

    for (const attorney of attorneys) {
      console.log(`\n👤 Processing ${attorney.firm_name} (${attorney.email})`);
      console.log(`   Subdomain: ${attorney.subdomain}`);
      console.log(`   Vapi Assistant: ${attorney.vapi_assistant_id}`);
      console.log(`   Current Assistant: ${attorney.current_assistant_id}`);

      const attorneyResults = [];

      // Migrate vapi_assistant_id as primary
      if (attorney.vapi_assistant_id) {
        try {
          const { data: primaryMapping, error: primaryError } = await supabase
            .from('assistant_subdomains')
            .upsert({
              assistant_id: attorney.vapi_assistant_id,
              subdomain: attorney.subdomain,
              attorney_id: attorney.id,
              is_primary: true,
              is_active: true
            }, {
              onConflict: 'assistant_id'
            })
            .select()
            .single();

          if (primaryError) {
            console.error(`   ❌ Failed to migrate vapi assistant: ${primaryError.message}`);
          } else {
            console.log(`   ✅ Migrated vapi assistant: ${attorney.subdomain} -> ${attorney.vapi_assistant_id} (primary)`);
            attorneyResults.push({ type: 'primary', mapping: primaryMapping });
          }
        } catch (error) {
          console.error(`   ❌ Error migrating vapi assistant: ${error.message}`);
        }
      }

      // Migrate current_assistant_id as secondary if different
      if (attorney.current_assistant_id && 
          attorney.current_assistant_id !== attorney.vapi_assistant_id) {
        
        try {
          const secondarySubdomain = `${attorney.subdomain}-alt`;
          
          const { data: secondaryMapping, error: secondaryError } = await supabase
            .from('assistant_subdomains')
            .upsert({
              assistant_id: attorney.current_assistant_id,
              subdomain: secondarySubdomain,
              attorney_id: attorney.id,
              is_primary: false,
              is_active: true
            }, {
              onConflict: 'assistant_id'
            })
            .select()
            .single();

          if (secondaryError) {
            console.error(`   ❌ Failed to migrate current assistant: ${secondaryError.message}`);
          } else {
            console.log(`   ✅ Migrated current assistant: ${secondarySubdomain} -> ${attorney.current_assistant_id} (secondary)`);
            attorneyResults.push({ type: 'secondary', mapping: secondaryMapping });
          }
        } catch (error) {
          console.error(`   ❌ Error migrating current assistant: ${error.message}`);
        }
      }

      results.push({
        attorney: attorney,
        mappings: attorneyResults
      });
    }

    // Step 4: Verify the migration
    console.log('\n🔍 Step 4: Verifying migration...');
    
    const { data: allMappings, error: verifyError } = await supabase
      .from('v_subdomain_assistant_lookup')
      .select('*')
      .order('firm_name');

    if (verifyError) {
      throw verifyError;
    }

    console.log(`\n📊 Migration Results:`);
    console.log(`   Total assistant-subdomain mappings created: ${allMappings.length}`);
    
    console.log('\n📋 Subdomain Mappings:');
    allMappings.forEach(mapping => {
      const primaryFlag = mapping.is_primary ? ' (PRIMARY)' : '';
      console.log(`   ${mapping.subdomain}.legalscout.net -> ${mapping.assistant_id}${primaryFlag}`);
      console.log(`     Attorney: ${mapping.firm_name} (${mapping.email})`);
    });

    // Step 5: Test the new lookup
    console.log('\n🧪 Step 5: Testing new lookup functionality...');
    
    for (const mapping of allMappings.slice(0, 3)) { // Test first 3
      const { data: testLookup, error: testError } = await supabase
        .from('v_subdomain_assistant_lookup')
        .select('*')
        .eq('subdomain', mapping.subdomain)
        .single();

      if (testError) {
        console.error(`   ❌ Lookup test failed for ${mapping.subdomain}: ${testError.message}`);
      } else {
        console.log(`   ✅ Lookup test passed: ${mapping.subdomain} -> ${testLookup.assistant_id}`);
      }
    }

    console.log('\n🎉 Migration completed successfully!');
    console.log('=====================================');
    console.log('✅ Assistant-subdomain architecture is now active');
    console.log('✅ Webhook routing will use assistant-first lookup');
    console.log('✅ Dashboard routing will use assistant-specific context');
    
    console.log('\n📝 Next Steps:');
    console.log('1. Update assistant webhook URLs to use new subdomain mappings');
    console.log('2. Test inbound calls to verify routing works correctly');
    console.log('3. Verify dashboard shows correct assistant context');

    return {
      success: true,
      totalMappings: allMappings.length,
      results: results
    };

  } catch (error) {
    console.error('❌ Migration failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Run the migration
migrateToAssistantSubdomains()
  .then(result => {
    if (result.success) {
      console.log('\n✅ Migration completed successfully');
      process.exit(0);
    } else {
      console.error('\n❌ Migration failed:', result.error);
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('❌ Migration script error:', error);
    process.exit(1);
  });
