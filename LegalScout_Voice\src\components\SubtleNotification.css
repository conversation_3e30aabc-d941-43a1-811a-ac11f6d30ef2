/* Subtle Notification Styles */

.subtle-notification {
  position: fixed;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: 400px;
  min-width: 300px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(100%);
  opacity: 0;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Type-specific styles */
.subtle-notification-success {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  color: white;
}

.subtle-notification-error {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  color: white;
}

.subtle-notification-warning {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  color: white;
}

.subtle-notification-info {
  background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
  color: white;
}

/* Position classes */
.notification-top-right {
  top: 20px;
  right: 20px;
}

.notification-top-left {
  top: 20px;
  left: 20px;
}

.notification-top-center {
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.notification-bottom-right {
  bottom: 20px;
  right: 20px;
}

.notification-bottom-left {
  bottom: 20px;
  left: 20px;
}

.notification-bottom-center {
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}

/* Animation states */
.notification-enter {
  transform: translateX(0) !important;
  opacity: 1 !important;
}

.notification-exit {
  transform: translateX(100%) !important;
  opacity: 0 !important;
}

/* For center positions, use different animations */
.notification-top-center.notification-enter,
.notification-bottom-center.notification-enter {
  transform: translateX(-50%) translateY(0) !important;
}

.notification-top-center.notification-exit {
  transform: translateX(-50%) translateY(-100%) !important;
}

.notification-bottom-center.notification-exit {
  transform: translateX(-50%) translateY(100%) !important;
}

/* For left positions, slide from left */
.notification-top-left,
.notification-bottom-left {
  transform: translateX(-100%);
}

.notification-top-left.notification-enter,
.notification-bottom-left.notification-enter {
  transform: translateX(0) !important;
}

.notification-top-left.notification-exit,
.notification-bottom-left.notification-exit {
  transform: translateX(-100%) !important;
}

/* Icon styles */
.notification-icon {
  font-size: 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Message styles */
.notification-message {
  flex: 1;
  line-height: 1.4;
  word-wrap: break-word;
}

/* Close button styles */
.notification-close {
  background: none;
  border: none;
  color: inherit;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin-left: auto;
  opacity: 0.7;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.notification-close:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
}

.notification-close:focus {
  outline: none;
  opacity: 1;
  background: rgba(255, 255, 255, 0.2);
}

/* Hover effects */
.subtle-notification:hover {
  transform: scale(1.02) !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2) !important;
}

.subtle-notification:active {
  transform: scale(0.98) !important;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .subtle-notification {
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .subtle-notification {
    transition: opacity 0.2s ease;
  }
  
  .subtle-notification:hover {
    transform: none !important;
  }
}

/* Mobile responsiveness */
@media (max-width: 480px) {
  .subtle-notification {
    max-width: calc(100vw - 40px);
    min-width: calc(100vw - 40px);
    left: 20px !important;
    right: 20px !important;
    transform: translateY(-100%) !important;
  }
  
  .notification-top-right,
  .notification-top-left,
  .notification-top-center {
    top: 20px;
    left: 20px !important;
    right: 20px !important;
    transform: translateY(-100%);
  }
  
  .notification-bottom-right,
  .notification-bottom-left,
  .notification-bottom-center {
    bottom: 20px;
    left: 20px !important;
    right: 20px !important;
    transform: translateY(100%);
  }
  
  .notification-enter {
    transform: translateY(0) !important;
  }
  
  .notification-top-right.notification-exit,
  .notification-top-left.notification-exit,
  .notification-top-center.notification-exit {
    transform: translateY(-100%) !important;
  }
  
  .notification-bottom-right.notification-exit,
  .notification-bottom-left.notification-exit,
  .notification-bottom-center.notification-exit {
    transform: translateY(100%) !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .subtle-notification {
    border: 2px solid currentColor;
    box-shadow: none;
  }
}

/* Container for multiple notifications */
#subtle-notifications-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10000;
}

/* Stacking for multiple notifications */
.subtle-notification:nth-child(1) { z-index: 10001; }
.subtle-notification:nth-child(2) { z-index: 10002; top: 80px; }
.subtle-notification:nth-child(3) { z-index: 10003; top: 140px; }
.subtle-notification:nth-child(4) { z-index: 10004; top: 200px; }
.subtle-notification:nth-child(5) { z-index: 10005; top: 260px; }

/* For bottom notifications */
.notification-bottom-right:nth-child(2),
.notification-bottom-left:nth-child(2),
.notification-bottom-center:nth-child(2) {
  bottom: 80px;
}

.notification-bottom-right:nth-child(3),
.notification-bottom-left:nth-child(3),
.notification-bottom-center:nth-child(3) {
  bottom: 140px;
}

.notification-bottom-right:nth-child(4),
.notification-bottom-left:nth-child(4),
.notification-bottom-center:nth-child(4) {
  bottom: 200px;
}

.notification-bottom-right:nth-child(5),
.notification-bottom-left:nth-child(5),
.notification-bottom-center:nth-child(5) {
  bottom: 260px;
}
