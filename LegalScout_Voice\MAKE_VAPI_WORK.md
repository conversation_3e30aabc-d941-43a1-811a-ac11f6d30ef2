# VAPI Integration Source of Truth

This document serves as the definitive reference for VAPI integration in LegalScout Voice. It outlines the correct implementation patterns, data flow, and configuration to ensure reliable voice assistant functionality.

**⚠️ CRITICAL FIXES APPLIED**: This document has been updated with solutions to critical issues discovered during implementation, including API key confusion, voice configuration errors, endpoint issues, and data structure problems.

## 🔥 CRITICAL ISSUES AND FIXES

### DASHBOARD PREVIEW CALL ISSUES (DECEMBER 2024)

**Problem**: Dashboard preview connects but assistant doesn't speak
**Status**: PARTIALLY RESOLVED - Call connects but audio issue remains

**Root Causes Found & Fixed**:
1. ✅ **Subdomain Parameter Flow**: Fixed subdomain not being passed from URL → SimplePreviewPage → Preview Components → VapiCall
2. ✅ **Hook Selection**: Switched from `useVapiCallWithDebug` (no auto-start) to `useVapiCall` (auto-starts calls)
3. ✅ **Assistant Overrides**: Removed all `assistantOverrides` for existing assistants - they're pre-configured in Vapi
4. ❌ **Audio Issue**: Call connects, shows U<PERSON>, but assistant still doesn't speak (UNRESOLVED)

**Key Principle Discovered**: For existing assistants, NEVER pass overrides:
```javascript
// CORRECT for existing assistants
await vapiInstance.start(assistantId); // No second parameter!

// WRONG - causes 400 Bad Request
await vapiInstance.start(assistantId, assistantOverrides);
```

**Working vs Non-Working Patterns**:
- **Working** (subdomain like damon.legalscout.net): Uses `useVapiCall`, correct subdomain, no overrides, assistant speaks
- **Broken** (dashboard preview): Was using wrong hook, wrong subdomain, invalid overrides, silent assistant

### Issue 1: API Key vs Assistant ID Confusion ⚠️ **CRITICAL**

**Problem**: The application was using assistant IDs as API keys in Authorization headers
```javascript
// ❌ WRONG - Using assistant ID as API key
Authorization: "Bearer 310f0d43-27c2-47a5-a76d-e55171d024f7"
```

**Solution**: Use actual API keys for authentication
```javascript
// ✅ CORRECT - Using API key for authentication
Authorization: "Bearer 6734febc-fc65-4669-93b0-929b31ff6564"

// Assistant IDs are used for referencing specific assistants
const assistantId = "310f0d43-27c2-47a5-a76d-e55171d024f7";
```

### Issue 2: Voice Configuration Errors ⚠️ **CRITICAL**

**Problem**: `"Voice sarah not found for provider playht"`
```javascript
// ❌ WRONG - sarah is not available for playht
{ provider: "playht", voiceId: "sarah" }
```

**Solution**: Use correct voice/provider combinations
```javascript
// ✅ CORRECT - sarah is available for 11labs
{ provider: "11labs", voiceId: "sarah" }
```

### Issue 3: Incorrect API Endpoints ⚠️ **CRITICAL**

**Problem**: 404 errors when calling Vapi API
```javascript
// ❌ WRONG - Plural endpoints
PATCH https://api.vapi.ai/assistants/{id}
```

**Solution**: Use correct singular endpoint format
```javascript
// ✅ CORRECT - Singular endpoints
PATCH https://api.vapi.ai/assistant/{id}
```

### Issue 4: Invalid Data Structure ⚠️ **CRITICAL**

**Problem**: 400 errors when updating assistants
```javascript
// ❌ WRONG - Invalid field structure
{
  llm: { provider: "openai", model: "gpt-4o" },      // Wrong field name
  ai_model: "gpt-4o"                                // Invalid top-level field
}
```

**Solution**: Use correct Vapi API field structure
```javascript
// ✅ CORRECT - Valid field structure
{
  model: { provider: "openai", model: "gpt-4o" }    // Correct field name
}
```

## Core Principles

1. **Single Source of Truth**: Supabase is the primary data store for all attorney configurations
2. **One-Way Sync**: Changes flow from UI → Supabase → VAPI (never in reverse)
3. **Separation of Concerns**: Home page assistant and dashboard preview assistants must operate independently
4. **Correct API Usage**: Always use proper API keys, endpoints, and data structures

## Environment Variables

```
# Required VAPI Configuration
VITE_VAPI_PUBLIC_KEY=6734febc-fc65-4669-93b0-929b31ff6564  # ✅ CORRECT API key for client-side
VITE_VAPI_BASE_URL=https://api.vapi.ai                     # Base URL for API calls

# Optional VAPI Configuration (for server-side operations)
VITE_VAPI_SECRET_KEY=6734febc-fc65-4669-93b0-929b31ff6564  # ✅ CORRECT Secret key for server operations
VAPI_TOKEN=6734febc-fc65-4669-93b0-929b31ff6564           # Alternative token format

# Default Assistant ID (❌ was previously being used incorrectly as an API key)
VITE_VAPI_DEFAULT_ASSISTANT_ID=310f0d43-27c2-47a5-a76d-e55171d024f7  # ✅ CORRECT Assistant ID (not API key)
```

**⚠️ IMPORTANT**: The value `310f0d43-27c2-47a5-a76d-e55171d024f7` is an **Assistant ID**, not an API key. It should never be used in Authorization headers.

## Data Flow Architecture

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  Dashboard  │────▶│  Supabase   │────▶│    VAPI     │
│     UI      │     │  Database   │     │ Assistants  │
└─────────────┘     └─────────────┘     └─────────────┘
        │                  ▲                   │
        │                  │                   │
        └──────────────────┘                   │
             Save to DB                        │
                                               │
┌─────────────┐                                │
│  Subdomain  │◀───────────────────────────────┘
│   Preview   │      Load assistant by ID
└─────────────┘
```

## Home Page Assistant (DEFAULT_ASSISTANT_ID)

The home page always uses the default assistant ID defined in constants:

```javascript
// src/constants/vapiConstants.js
export const DEFAULT_ASSISTANT_ID = 'e3fff1dd-2e82-4cce-ac6c-8c3271eb0865'; // Default assistant ID
```

This assistant should NEVER be modified by the dashboard functionality. It serves as a fallback and demo assistant.

## Dashboard Preview Assistant

The dashboard preview uses the attorney's specific VAPI assistant ID from Supabase:

```javascript
// Key fields in Supabase 'attorneys' table
{
  vapi_assistant_id: string,    // The VAPI assistant ID
  vapi_instructions: string,    // System instructions for the assistant
  vapi_context: string,         // Additional context for the assistant
  welcome_message: string,      // First message the assistant says
  voice_id: string,             // Voice ID to use
  voice_provider: string,       // Voice provider (e.g., "11labs", "playht")
  ai_model: string              // AI model to use (e.g., "gpt-4o")
}
```

## Correct Implementation Pattern

### 1. Creating a VAPI Assistant for an Attorney

When an attorney doesn't have a `vapi_assistant_id` in Supabase:

```javascript
// 1. Import the VAPI assistant service
import { vapiAssistantService } from '../services/vapiAssistantService';

// 2. Create a new assistant
const assistant = await vapiAssistantService.createAssistantForAttorney(attorneyData);

// 3. Update Supabase with the new assistant ID
await supabase
  .from('attorneys')
  .update({ vapi_assistant_id: assistant.id })
  .eq('id', attorneyData.id);
```

### 2. Updating a VAPI Assistant

When an attorney already has a `vapi_assistant_id` in Supabase:

```javascript
// 1. Import the VAPI assistant service
import { vapiAssistantService } from '../services/vapiAssistantService';

// 2. Update the assistant with current attorney data
await vapiAssistantService.updateAssistantConfiguration(
  attorneyData.vapi_assistant_id,
  attorneyData
);
```

### 3. Dashboard Preview Configuration

```javascript
// Create attorney preview configuration
const { previewConfig, vapiConfig } = createAttorneyPreviewConfig(attorneyData);

// Use the configuration in VapiCall component
<VapiCall
  onEndCall={handleEndCall}
  subdomain={attorneyData.subdomain}
  assistantId={vapiConfig.assistantId}
  assistantOverrides={vapiConfig.assistantOverrides}
  customInstructions={previewConfig}
/>
```

## VAPI Call Implementation

### Home Page Implementation

```javascript
// src/components/HomePageVapiCall.jsx
import React from 'react';
import { VapiCall } from '../components/VapiCall';
import { DEFAULT_ASSISTANT_ID } from '../constants/vapiConstants';

const HomePageVapiCall = () => {
  return (
    <VapiCall
      onEndCall={() => console.log('Call ended')}
      subdomain="default"
      assistantId={DEFAULT_ASSISTANT_ID}
      // No assistantOverrides - use the default assistant as-is
      assistantOverrides={null}
      // No customInstructions - use the default assistant as-is
      customInstructions={null}
      // Force using the default assistant
      forceDefaultAssistant={true}
    />
  );
};

export default HomePageVapiCall;
```

### Dashboard Preview Implementation

```javascript
// src/components/DashboardPreviewVapiCall.jsx
import React from 'react';
import { VapiCall } from '../components/VapiCall';
import { createAttorneyPreviewConfig } from '../utils/previewConfigHandler';

const DashboardPreviewVapiCall = ({ attorneyData }) => {
  // Create configuration based on attorney data
  const { previewConfig, vapiConfig } = createAttorneyPreviewConfig(attorneyData);

  return (
    <VapiCall
      onEndCall={() => console.log('Call ended')}
      subdomain={attorneyData.subdomain}
      // Use the attorney's assistant ID
      assistantId={vapiConfig.assistantId}
      // Only provide overrides if using the default assistant
      assistantOverrides={vapiConfig.assistantOverrides}
      // Always provide custom instructions
      customInstructions={previewConfig}
      // Never force default assistant in dashboard
      forceDefaultAssistant={false}
    />
  );
};

export default DashboardPreviewVapiCall;
```

## 🔧 TROUBLESHOOTING AND LESSONS LEARNED

### Critical Error Messages and Solutions

| Error | Root Cause | Solution |
|-------|------------|----------|
| `401 Unauthorized` | Using assistant ID as API key | Use `6734febc-fc65-4669-93b0-929b31ff6564` as API key |
| `404 Not Found` | Wrong endpoint (plural vs singular) | Use `/assistant` instead of `/assistants` |
| `400 Bad Request` | Invalid data structure | Use `model` instead of `llm`, remove invalid fields |
| `Voice sarah not found for provider playht` | Invalid voice/provider combination | Use `11labs` provider with `sarah` voice |

### Common Issues and Solutions

1. **Missing VAPI Assistant ID**: If an attorney doesn't have a VAPI assistant ID, create one using `vapiAssistantService.createAssistantForAttorney()`
2. **VAPI API Key Issues**: Ensure you're using the actual API key (`6734febc...`), not the assistant ID (`310f0d43...`)
3. **Preview Not Updating**: Make sure to update both Supabase and VAPI when attorney data changes
4. **Voice Configuration Errors**: Always validate voice/provider combinations before saving
5. **Endpoint Errors**: Use singular endpoints (`/assistant`, `/call`) not plural (`/assistants`, `/calls`)
6. **Data Structure Errors**: Use correct field names (`model` not `llm`, avoid `ai_model` as top-level field)

### Development Environment Issues

**Subdomain Detection**: In localhost, subdomain detection returns `'default'`, but attorney records have specific subdomains like `'attorney-71550'`.

**Solution**: The application now includes `public/set-test-subdomain.js` that automatically sets the correct subdomain for development.

### Debugging

Add these console logs to help diagnose issues:

```javascript
// Log VAPI configuration
console.log('VAPI Config:', {
  assistantId: vapiConfig.assistantId,
  hasOverrides: !!vapiConfig.assistantOverrides,
  apiKey: import.meta.env.VITE_VAPI_PUBLIC_KEY?.substring(0, 5) + '...'
});

// Log call parameters
console.log('Call Parameters:', {
  subdomain,
  assistantId,
  hasCustomInstructions: !!customInstructions,
  hasAssistantOverrides: !!assistantOverrides
});
```

## Testing VAPI Integration

To verify your VAPI integration is working correctly:

1. **Home Page Test**: The home page should always use the default assistant ID (`e3fff1dd-2e82-4cce-ac6c-8c3271eb0865`)
2. **Dashboard Test**: The dashboard should use the attorney's specific assistant ID from Supabase
3. **New Attorney Test**: Create a new attorney and verify a new VAPI assistant is created and the ID is saved to Supabase
4. **Update Test**: Update attorney data and verify the changes are reflected in the VAPI assistant

## Reference Implementation

### The `useVapiCall` Hook

The `useVapiCall` hook is the core implementation for VAPI integration:

```javascript
// src/hooks/useVapiCall.js
const useVapiCall = ({
  subdomain,
  onEndCall,
  customInstructions,
  assistantOverrides,
  assistantId
}) => {
  // Initialize VAPI with the correct API key
  const apiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;

  // Create VAPI instance
  const vapiInstance = await vapiService.createVapiInstance(apiKey);

  // Start call with the correct assistant ID and overrides
  await vapiService.startCall(vapiInstance, assistantId, assistantOverrides);

  // Return state and functions
  return {
    status,
    startCall,
    stopCall,
    // ...other state and functions
  };
};
```

### VAPI Assistant Service

The `vapiAssistantService` handles creating and updating VAPI assistants:

```javascript
// src/services/vapiAssistantService.js

/**
 * Create a new assistant for an attorney
 */
async createAssistantForAttorney(attorneyData) {
  // Create assistant name from firm name
  const assistantName = `${attorneyData.firm_name} Legal Assistant`;

  // Create instructions from attorney data or use default
  const instructions = attorneyData.vapi_instructions ||
    `You are a legal assistant for ${attorneyData.firm_name}. Help potential clients understand their legal needs and collect relevant information for consultation.`;

  // Create assistant configuration
  const assistantConfig = {
    name: assistantName,
    instructions: instructions,
    firstMessage: attorneyData.welcome_message || `Hello, I'm Scout from ${attorneyData.firm_name}. How can I help you today?`,
    firstMessageMode: "assistant-speaks-first",
    llm: {
      provider: "openai",
      model: attorneyData.ai_model || "gpt-4o"
    },
    voice: {
      provider: attorneyData.voice_provider || "playht",
      voiceId: attorneyData.voice_id || "sarah"
    },
    transcriber: {
      provider: "deepgram",
      model: "nova-3"
    }
  };

  // Create the assistant using VAPI MCP
  const assistant = await vapiMcpService.createAssistant(assistantConfig);

  return assistant;
}

/**
 * Update an existing assistant for an attorney
 */
async updateAssistantConfiguration(assistantId, attorneyData) {
  // First get the current assistant to preserve other settings
  const assistant = await vapiMcpService.getAssistant(assistantId);

  // Create update configuration
  const updateConfig = {
    name: `${attorneyData.firm_name} Legal Assistant`,
    instructions: attorneyData.vapi_instructions || assistant.instructions,
    firstMessage: attorneyData.welcome_message || assistant.firstMessage,
    llm: {
      provider: attorneyData.ai_model ? "openai" : assistant.llm.provider,
      model: attorneyData.ai_model || assistant.llm.model
    },
    voice: {
      provider: attorneyData.voice_provider || assistant.voice.provider,
      voiceId: attorneyData.voice_id || assistant.voice.voiceId
    }
  };

  // Update the assistant
  const updatedAssistant = await vapiMcpService.updateAssistant(assistantId, updateConfig);

  return updatedAssistant;
}
```
```

### Preview Configuration Handler

The `previewConfigHandler` creates the correct configuration for both demo and attorney-specific previews:

```javascript
// src/utils/previewConfigHandler.js

/**
 * Creates configuration for the demo page preview
 * @param {Object} templateConfig - Basic template configuration
 * @returns {Object} - Configuration for demo preview
 */
export const createDemoPreviewConfig = (templateConfig) => {
  // Extract template configuration with defaults
  const {
    firmName = 'Your Law Firm',
    welcomeMessage = "Hello! I'm Scout, your legal assistant. How can I help you today?",
    // ... other defaults
  } = templateConfig;

  // Create assistant overrides for the demo preview
  const assistantOverrides = {
    firstMessage: welcomeMessage,
    firstMessageMode: "assistant-speaks-first",
    model: {
      provider: "anthropic",
      model: "claude-3-sonnet-20240229",
      messages: [
        {
          role: "system",
          content: `You are a legal assistant for ${firmName}.
          Follow the default instructions for being a helpful legal assistant, but also incorporate these specific instructions:
          1. Introduce yourself as Scout from ${firmName}
          2. Gather information about the potential client's legal situation
          3. Be empathetic and professional
          4. Focus on understanding their needs before making recommendations`
        }
      ]
    }
  };

  return {
    // Basic preview configuration
    previewConfig: {
      firmName,
      welcomeMessage,
      // ... other preview config
    },
    // Vapi configuration
    vapiConfig: {
      assistantId: DEFAULT_ASSISTANT_ID,
      assistantOverrides
    }
  };
};

/**
 * Creates configuration for the attorney dashboard preview
 * @param {Object} attorneyData - Attorney data from Supabase
 * @returns {Object} - Configuration for attorney dashboard preview
 */
export const createAttorneyPreviewConfig = (attorneyData) => {
  if (!attorneyData) {
    return createDemoPreviewConfig({});
  }

  // Map database fields to preview configuration
  const previewConfig = {
    firmName: attorneyData.firm_name || 'Your Law Firm',
    welcomeMessage: attorneyData.welcome_message || "Hello! I'm Scout, your legal assistant. How can I help you today?",
    // ... other mapped fields
  };

  // Create Vapi configuration
  const vapiConfig = {
    // Use attorney's assistant ID if available, otherwise use default
    assistantId: attorneyData.vapi_assistant_id || DEFAULT_ASSISTANT_ID,
    // Only provide overrides if using the default assistant
    assistantOverrides: !attorneyData.vapi_assistant_id ? {
      firstMessage: previewConfig.welcomeMessage,
      firstMessageMode: "assistant-speaks-first",
      model: {
        provider: "anthropic",
        model: "claude-3-sonnet-20240229",
        messages: [
          {
            role: "system",
            content: attorneyData.vapi_instructions ||
              `You are a legal assistant for ${attorneyData.firm_name}. Help potential clients understand their legal needs and collect relevant information for consultation.`
          }
        ]
      }
    } : null
  };

  return {
    previewConfig,
    vapiConfig
  };
};
```

### VapiCall Component

The `VapiCall` component is the main component that handles the VAPI integration:

```javascript
// src/components/VapiCall.jsx

import React, { useState, useRef, useEffect, useMemo } from 'react'
import useVapiCall from '../hooks/useVapiCall'
import useVapiEmissions from '../hooks/useVapiEmissions'
import { CALL_STATUS, DEFAULT_ASSISTANT_ID } from '../constants/vapiConstants'
import { createDemoPreviewConfig, createAttorneyPreviewConfig } from '../utils/previewConfigHandler'

const VapiCall = ({
  onEndCall,
  subdomain = 'default',
  customInstructions = {},
  assistantOverrides = null,
  assistantId = null,
  isDemo = false,
  attorneyData = null,
  forceDefaultAssistant = false
}) => {
  // Process configuration based on the provided props
  const processedConfig = useMemo(() => {
    let config;

    // If forcing default assistant, use it directly without overrides
    if (forceDefaultAssistant) {
      config = {
        previewConfig: customInstructions,
        vapiConfig: {
          assistantId: DEFAULT_ASSISTANT_ID,
          assistantOverrides: null
        }
      };
      console.log('Using forced default assistant configuration:', config);
    } else if (isDemo) {
      // For demo preview, use the default assistant with template overrides
      config = createDemoPreviewConfig(customInstructions);
      console.log('Using demo preview configuration:', config);
    } else if (attorneyData) {
      // For attorney dashboard preview, use attorney-specific configuration
      config = createAttorneyPreviewConfig(attorneyData);
      console.log('Using attorney preview configuration:', config);
    } else {
      // Fallback to direct configuration
      config = {
        previewConfig: customInstructions,
        vapiConfig: {
          assistantId: assistantId || customInstructions.assistantId || DEFAULT_ASSISTANT_ID,
          assistantOverrides: assistantOverrides
        }
      };
      console.log('Using direct configuration:', config);
    }

    return config;
  }, [customInstructions, assistantOverrides, assistantId, isDemo, attorneyData, forceDefaultAssistant]);

  // Use our custom hook to manage call state and functionality
  const {
    status,
    dossierData,
    volumeLevel,
    assistantIsSpeaking,
    errorMessage,
    subdomainConfig,
    startCall,
    stopCall,
    vapi,
    messageHistory,
    callId
  } = useVapiCall({
    subdomain,
    onEndCall,
    // Only pass customInstructions if we're not forcing the default assistant
    customInstructions: forceDefaultAssistant ? null : (processedConfig?.previewConfig || customInstructions),
    // Only pass assistantOverrides if explicitly set in processedConfig and not null
    assistantOverrides: processedConfig?.vapiConfig?.assistantOverrides === null ? null :
                        (processedConfig?.vapiConfig?.assistantOverrides || assistantOverrides),
    // Always pass the assistantId
    assistantId: processedConfig?.vapiConfig?.assistantId
  });

  // Render the call interface based on status
  return (
    <div className="vapi-call-container">
      {/* Render different UI based on call status */}
      {status === CALL_STATUS.IDLE && (
        <button onClick={startCall}>Start Call</button>
      )}

      {status === CALL_STATUS.CONNECTING && (
        <div className="connecting-state">Connecting...</div>
      )}

      {status === CALL_STATUS.CONNECTED && (
        <div className="active-call">
          {/* Active call UI */}
          <div className="message-history">
            {messageHistory.map((msg, index) => (
              <div key={index} className={`message ${msg.role}`}>
                {msg.content}
              </div>
            ))}
          </div>

          <button onClick={stopCall}>End Call</button>
        </div>
      )}

      {status === CALL_STATUS.ERROR && (
        <div className="error-state">
          Error: {errorMessage}
          <button onClick={startCall}>Retry</button>
        </div>
      )}
    </div>
  );
};

export default VapiCall;
```

## Best Practices and Implementation Checklist

### Best Practices

1. **Separation of Concerns**
   - Keep home page and dashboard VAPI implementations separate
   - Never modify the default assistant from the dashboard
   - Use the `forceDefaultAssistant` flag for the home page

2. **Error Handling**
   - Always provide fallbacks for missing data
   - Log detailed error information
   - Show user-friendly error messages

3. **Configuration Management**
   - Use the `createAttorneyPreviewConfig` utility for consistent configuration
   - Only provide assistant overrides when using the default assistant
   - Preserve existing assistant settings when updating

4. **Performance Optimization**
   - Use memoization for configuration processing
   - Only update VAPI when attorney data actually changes
   - Batch Supabase and VAPI updates when possible

### Implementation Checklist

When implementing or modifying VAPI integration, ensure:

- [ ] Environment variables are correctly set
- [ ] Default assistant ID is correctly defined in constants
- [ ] Home page always uses the default assistant without overrides
- [ ] Dashboard preview uses the attorney's assistant ID when available
- [ ] New attorneys get a VAPI assistant created automatically
- [ ] Attorney updates sync to their VAPI assistant
- [ ] Error handling is in place for all API calls
- [ ] Console logging is appropriate for debugging
- [ ] UI provides clear feedback on call status

### Testing Checklist

Before deploying changes, test:

- [ ] Home page VAPI call works with default assistant
- [ ] Dashboard preview works with attorney-specific assistant
- [ ] Creating a new attorney creates a new VAPI assistant
- [ ] Updating attorney data updates their VAPI assistant
- [ ] Error handling works when VAPI API is unavailable
- [ ] Call controls (start, stop) work correctly
- [ ] Voice input and output work correctly
- [ ] Message history displays correctly

## 📁 KEY FILES FIXED

The following files were updated to resolve critical Vapi integration issues:

### Environment Configuration
- `.env.development` - Fixed API key vs assistant ID confusion
- `src/config/.config.js` - Updated fallback API keys
- `src/config/env.js` - Corrected VAPI_CONFIG values

### API Services
- `src/services/EnhancedVapiMcpService.js` - Fixed endpoints (singular), data structure (model vs llm), added error details
- `src/services/EnhancedSyncHelpers.js` - Fixed data structure for assistant updates
- `src/services/vapiMcpService.js` - Updated API resource paths
- `src/hooks/useVapiCall.js` - Fixed fallback API key

### Voice Configuration
- `src/components/dashboard/VoiceAssistantConfig.jsx` - Fixed voice provider defaults
- `src/components/dashboard/AgentTab.jsx` - Updated voice provider fallbacks
- `src/components/dashboard/VoiceTab.jsx` - Fixed voice provider defaults
- `src/config/attorneys.js` - Updated default voice provider

### Database Updates
- Updated attorney record in Supabase to use correct voice configuration
- Fixed `voice_provider` from "playht" to "11labs" for "sarah" voice

### Development Tools
- `public/set-test-subdomain.js` - Auto-sets correct subdomain for development
- `index.html` - Added test subdomain script
- Enhanced error logging in `EnhancedVapiMcpService.js`

## 🎯 IMPLEMENTATION PRIORITIES

Based on troubleshooting experience, implement fixes in this order:

### 1. 🔥 Critical API Issues (Immediate)
- [ ] Fix API key vs assistant ID confusion in all files
- [ ] Correct voice provider/voice combinations
- [ ] Update API endpoints to use singular form
- [ ] Fix data structure for assistant updates

### 2. ⚡ Core Functionality (High Priority)
- [ ] Implement one-way sync pattern (UI → Supabase → Vapi)
- [ ] Ensure consistent assistant creation
- [ ] Proper field loading in configuration UI
- [ ] Clear UI feedback for save operations

### 3. 🔧 Enhanced Features (Medium Priority)
- [ ] SMS notifications to attorneys
- [ ] Call control interface
- [ ] Real-time call monitoring
- [ ] Advanced error handling

## 📚 LESSONS LEARNED

1. **Always distinguish between API keys and resource IDs** - They serve different purposes and should never be confused
2. **Voice providers have specific voice catalogs** - Not all voices work with all providers; validate combinations
3. **Vapi API uses singular endpoints** - Unlike many REST APIs that use plural forms
4. **Field names matter in API requests** - Use `model` not `llm` for assistant updates
5. **Development environment needs special handling** - Subdomain detection differs from production
6. **Error messages can be misleading** - 400/404 errors may have different root causes than expected
7. **Payload debugging is essential** - Always log what you're sending to the API for troubleshooting
8. **Mock fallbacks are crucial** - Development mode should gracefully handle API failures
9. **Data structure validation is critical** - Invalid fields cause 400 errors that are hard to debug
10. **Environment variable management is key** - Ensure all environments use correct values