/**
 * Cleanup Public Directory
 * 
 * Removes all unnecessary fix scripts and test files from the public directory
 * to prevent them from being copied to dist during build.
 */

import fs from 'fs';
import path from 'path';

const publicDir = path.resolve(process.cwd(), 'public');

// Files and patterns to KEEP
const keepPatterns = [
  // Essential files
  'favicon.ico',
  'vite.svg',
  '_preload.html',
  'subdomain_config.json',
  'sample-consultations.csv',
  
  // Image files
  /\.(png|jpg|jpeg|gif|webp|svg)$/i,
  
  // Directories to keep
  'api',
  'assets',
  '_framer',
  'direct-fix',
  'direct-patch'
];

// Patterns to REMOVE (everything else)
const removePatterns = [
  // All fix scripts
  /^fix-.*\.js$/,
  /^.*-fix\.js$/,
  
  // All test files
  /^test-.*\.(js|html)$/,
  
  // All diagnostic files
  /^.*-diagnostic\.js$/,
  /^diagnostic-.*\.js$/,
  
  // All debug files
  /^debug-.*\.(js|html)$/,
  
  // All other script files
  /^agent-.*\.js$/,
  /^attorney-.*\.js$/,
  /^auth-.*\.js$/,
  /^auto-.*\.js$/,
  /^black-.*\.js$/,
  /^call-.*\.js$/,
  /^clean-.*\.js$/,
  /^clear-.*\.js$/,
  /^comprehensive-.*\.js$/,
  /^console-.*\.js$/,
  /^consolidated-.*\.js$/,
  /^controlled-.*\.js$/,
  /^critical-.*\.js$/,
  /^csp-.*\.js$/,
  /^dashboard-.*\.js$/,
  /^definitive-.*\.js$/,
  /^deployment-.*\.js$/,
  /^dev-.*\.js$/,
  /^diagnose-.*\.js$/,
  /^direct-.*\.js$/,
  /^disable-.*\.js$/,
  /^dom-.*\.js$/,
  /^elegant-.*\.js$/,
  /^emergency-.*\.js$/,
  /^empty\.js$/,
  /^enhance-.*\.js$/,
  /^enhanced-.*\.js$/,
  /^environment-.*\.js$/,
  /^error-.*\.js$/,
  /^final-.*\.js$/,
  /^find-.*\.js$/,
  /^force-.*\.js$/,
  /^framer-.*\.js$/,
  /^global-.*\.js$/,
  /^headers-.*\.js$/,
  /^immediate-.*\.js$/,
  /^inject-.*\.js$/,
  /^interaction-.*\.js$/,
  /^layout-.*\.js$/,
  /^local-.*\.js$/,
  /^manual-.*\.js$/,
  /^patch-.*\.js$/,
  /^preview-.*\.js$/,
  /^production-.*\.js$/,
  /^purge-.*\.js$/,
  /^quick-.*\.js$/,
  /^react-.*\.js$/,
  /^reset-.*\.js$/,
  /^robust-.*\.js$/,
  /^routing-.*\.js$/,
  /^self-.*\.js$/,
  /^set-.*\.js$/,
  /^simple-.*\.js$/,
  /^simplified-.*\.js$/,
  /^speech-.*\.js$/,
  /^standalone-.*\.js$/,
  /^streamlined-.*\.js$/,
  /^supabase-.*\.js$/,
  /^sync-.*\.js$/,
  /^system-.*\.js$/,
  /^unified-.*\.js$/,
  /^url-.*\.js$/,
  /^vapi-.*\.js$/,
  /^vercel-.*\.js$/,
  /^verify-.*\.js$/,
  /^vm2-.*\.js$/,
  
  // HTML test files
  /^comparison-.*\.html$/,
  /^debug-.*\.html$/,
  /^framer-.*\.html$/,
  /^index-.*\.html$/,
  /^launch\.html$/,
  /^production-.*\.html$/,
  /^reset\.html$/,
  /^simple-.*\.html$/,
  /^system-.*\.html$/,
  /^test\.html$/,
  /^vapi-.*\.html$/,
  
  // Other files
  /^.*\.mhtml$/,
  'LayoutGroupContext.mjs',
  'gptme - *************.mhtml'
];

function shouldKeep(filename) {
  // Check if file matches any keep pattern
  for (const pattern of keepPatterns) {
    if (typeof pattern === 'string') {
      if (filename === pattern) return true;
    } else if (pattern instanceof RegExp) {
      if (pattern.test(filename)) return true;
    }
  }
  return false;
}

function shouldRemove(filename) {
  // Check if file matches any remove pattern
  for (const pattern of removePatterns) {
    if (pattern instanceof RegExp) {
      if (pattern.test(filename)) return true;
    }
  }
  return false;
}

function cleanupPublicDirectory() {
  console.log('🧹 Starting public directory cleanup...');
  
  if (!fs.existsSync(publicDir)) {
    console.log('Public directory not found, skipping cleanup');
    return;
  }
  
  const files = fs.readdirSync(publicDir);
  let removedCount = 0;
  let keptCount = 0;
  
  for (const file of files) {
    const filePath = path.join(publicDir, file);
    const stat = fs.statSync(filePath);
    
    // Skip directories for now
    if (stat.isDirectory()) {
      console.log(`📁 Keeping directory: ${file}`);
      keptCount++;
      continue;
    }
    
    if (shouldKeep(file)) {
      console.log(`✅ Keeping: ${file}`);
      keptCount++;
    } else if (shouldRemove(file)) {
      try {
        fs.unlinkSync(filePath);
        console.log(`🗑️  Removed: ${file}`);
        removedCount++;
      } catch (error) {
        console.warn(`⚠️  Failed to remove ${file}: ${error.message}`);
      }
    } else {
      console.log(`❓ Unknown file (keeping): ${file}`);
      keptCount++;
    }
  }
  
  console.log(`\n📊 Cleanup Summary:`);
  console.log(`   🗑️  Removed: ${removedCount} files`);
  console.log(`   ✅ Kept: ${keptCount} files`);
  console.log(`   🎯 Public directory cleaned successfully!`);
}

// Run the cleanup
try {
  cleanupPublicDirectory();
} catch (error) {
  console.error('❌ Error during cleanup:', error.message);
  process.exit(1);
}
