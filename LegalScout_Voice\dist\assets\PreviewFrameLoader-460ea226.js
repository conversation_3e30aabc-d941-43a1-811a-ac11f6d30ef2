import{r,j as o,E as y,k as d,l as p,n as g}from"./index-dd4c5999.js";const C=()=>{const[f,t]=r.useState(null),[w,v]=r.useState(!0),[c,b]=r.useState(null),[h,m]=r.useState(!1),[n,u]=r.useState(!1);r.useEffect(()=>{(async()=>{try{console.log("[PreviewFrameLoader] Loading attorney configuration...");const i=d();console.log("[PreviewFrameLoader] Current subdomain:",i),console.log("[PreviewFrameLoader] Loading config for subdomain:",i);const e=await p(i);if(console.log("[PreviewFrameLoader] Raw attorney config loaded:",{hasConfig:!!e,firmName:e?.firmName,vapi_assistant_id:e?.vapi_assistant_id,id:e?.id,subdomain:e?.subdomain,isFallback:e?.isFallback,configKeys:e?Object.keys(e):[]}),e){const l=e.isFallback||e.firmName==="Your Law Firm"&&!e.id||!e.id&&i==="damon";console.log("[PreviewFrameLoader] Config analysis:",{isFallbackConfig:l,hasId:!!e.id,firmName:e.firmName,subdomain:i});const a=g(e);console.log("[PreviewFrameLoader] Mapped preview config:",{firmName:a.firmName,titleText:a.titleText,vapiAssistantId:a.vapiAssistantId,welcomeMessage:a.welcomeMessage}),t({...a,vapiAssistantId:e.vapi_assistant_id||a.vapiAssistantId,...e,isFallbackConfig:l}),l&&i==="damon"&&(console.log("[PreviewFrameLoader] Fallback config detected for damon, showing activate button"),m(!0))}else console.warn("[PreviewFrameLoader] No attorney config found, using defaults"),t({firmName:"Your Law Firm",titleText:"Your Law Firm",primaryColor:"#4B74AA",secondaryColor:"#2C3E50",backgroundColor:"#1a1a1a",backgroundOpacity:.9,buttonText:"Start Consultation",buttonOpacity:1,practiceAreaBackgroundOpacity:.1,textBackgroundColor:"#634C38",welcomeMessage:"Hello! I'm Scout, your legal assistant. How can I help you today?",informationGathering:"Tell me about your situation, and I'll help find the right solution for you.",logoUrl:"/PRIMARY CLEAR.png",mascot:"/PRIMARY CLEAR.png",theme:"dark",vapiAssistantId:null})}catch(i){console.error("[PreviewFrameLoader] Error loading attorney config:",i),b(i.message),t({firmName:"Your Law Firm",titleText:"Your Law Firm",primaryColor:"#4B74AA",secondaryColor:"#2C3E50",backgroundColor:"#1a1a1a",backgroundOpacity:.9,buttonText:"Start Consultation",buttonOpacity:1,practiceAreaBackgroundOpacity:.1,textBackgroundColor:"#634C38",welcomeMessage:"Hello! I'm Scout, your legal assistant. How can I help you today?",informationGathering:"Tell me about your situation, and I'll help find the right solution for you.",logoUrl:"/PRIMARY CLEAR.png",mascot:"/PRIMARY CLEAR.png",theme:"dark",vapiAssistantId:null})}finally{v(!1)}})()},[]);const x=async()=>{u(!0);try{console.log("[PreviewFrameLoader] Activating assistant for subdomain:",d());const s=d(),i=await p(s);if(console.log("[PreviewFrameLoader] Activation - loaded config:",{hasConfig:!!i,firmName:i?.firmName,assistantId:i?.vapi_assistant_id,id:i?.id}),i&&i.vapi_assistant_id){const e=g(i);t({...e,vapiAssistantId:i.vapi_assistant_id,...i,isFallbackConfig:!1}),m(!1),console.log("[PreviewFrameLoader] Assistant activated successfully")}else console.warn("[PreviewFrameLoader] Activation failed - no valid config or assistant ID found")}catch(s){console.error("[PreviewFrameLoader] Error activating assistant:",s)}finally{u(!1)}};return w?o.jsxDEV("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100vh",backgroundColor:"#1a1a1a",color:"white"},children:[o.jsxDEV("div",{style:{width:"40px",height:"40px",border:"4px solid #333",borderTop:"4px solid #4B74AA",borderRadius:"50%",animation:"spin 1s linear infinite"}},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewFrameLoader.jsx",lineNumber:181,columnNumber:9},globalThis),o.jsxDEV("p",{style:{marginTop:"20px"},children:"Loading preview..."},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewFrameLoader.jsx",lineNumber:189,columnNumber:9},globalThis),o.jsxDEV("style",{children:`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewFrameLoader.jsx",lineNumber:190,columnNumber:9},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewFrameLoader.jsx",lineNumber:172,columnNumber:7},globalThis):c?o.jsxDEV("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100vh",backgroundColor:"#1a1a1a",color:"#ff6b6b",padding:"20px",textAlign:"center"},children:[o.jsxDEV("h2",{children:"Preview Error"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewFrameLoader.jsx",lineNumber:214,columnNumber:9},globalThis),o.jsxDEV("p",{children:["Failed to load attorney configuration: ",c]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewFrameLoader.jsx",lineNumber:215,columnNumber:9},globalThis),o.jsxDEV("p",{style:{color:"#ccc",marginTop:"20px"},children:"Using default configuration instead."},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewFrameLoader.jsx",lineNumber:216,columnNumber:9},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewFrameLoader.jsx",lineNumber:203,columnNumber:7},globalThis):o.jsxDEV("div",{style:{position:"relative",width:"100%",height:"100vh"},children:[o.jsxDEV(y,{...f},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewFrameLoader.jsx",lineNumber:226,columnNumber:7},globalThis),h&&o.jsxDEV("div",{style:{position:"fixed",bottom:window.innerWidth<=768?"20px":"auto",top:window.innerWidth<=768?"auto":"20px",left:window.innerWidth<=768?"50%":"auto",right:window.innerWidth<=768?"auto":"20px",transform:window.innerWidth<=768?"translateX(-50%)":"none",zIndex:9999,backgroundColor:"rgba(173, 216, 230, 0.1)",padding:window.innerWidth<=768?"12px 20px":"15px",borderRadius:window.innerWidth<=768?"25px":"8px",border:"1px solid rgba(173, 216, 230, 0.8)",color:"#87CEEB",textAlign:"center",maxWidth:window.innerWidth<=768?"90vw":"250px",minWidth:window.innerWidth<=768?"280px":"auto",boxShadow:"0 2px 15px rgba(173, 216, 230, 0.2)",backdropFilter:"blur(15px)"},children:[o.jsxDEV("div",{style:{marginBottom:window.innerWidth<=768?"8px":"10px",fontSize:window.innerWidth<=768?"16px":"14px",fontWeight:"bold"},children:"Assistant Not Active"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewFrameLoader.jsx",lineNumber:251,columnNumber:11},globalThis),window.innerWidth>768&&o.jsxDEV("div",{style:{marginBottom:"15px",fontSize:"12px",color:"#ccc"},children:"Your assistant configuration is not synced with this subdomain."},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewFrameLoader.jsx",lineNumber:259,columnNumber:13},globalThis),o.jsxDEV("button",{onClick:x,disabled:n,style:{backgroundColor:n?"rgba(173, 216, 230, 0.2)":"rgba(173, 216, 230, 0.15)",color:n?"#B0C4DE":"#87CEEB",border:"1px solid rgba(173, 216, 230, 0.6)",padding:window.innerWidth<=768?"12px 24px":"8px 16px",borderRadius:window.innerWidth<=768?"20px":"4px",cursor:n?"not-allowed":"pointer",fontSize:window.innerWidth<=768?"14px":"12px",fontWeight:"bold",minWidth:window.innerWidth<=768?"120px":"auto",transition:"all 0.3s ease",backdropFilter:"blur(10px)"},children:n?"Activating...":window.innerWidth<=768?"Activate":"Activate Assistant"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewFrameLoader.jsx",lineNumber:263,columnNumber:11},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewFrameLoader.jsx",lineNumber:232,columnNumber:9},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewFrameLoader.jsx",lineNumber:225,columnNumber:5},globalThis)};export{C as default};
