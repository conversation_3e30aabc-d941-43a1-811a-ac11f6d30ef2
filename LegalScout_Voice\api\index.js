/**
 * Consolidated API Router - Handles all API endpoints to stay within Vercel's 12 function limit
 * Only essential endpoints are kept as separate files (webhook, etc.)
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with debugging
console.log('[API] Environment check:', {
  SUPABASE_URL: !!process.env.SUPABASE_URL,
  VITE_SUPABASE_URL: !!process.env.VITE_SUPABASE_URL,
  SUPABASE_SERVICE_ROLE_KEY: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
  SUPABASE_ANON_KEY: !!process.env.SUPABASE_ANON_KEY,
  VITE_SUPABASE_KEY: !!process.env.VITE_SUPABASE_KEY
});

const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

console.log('[API] Supabase config:', {
  url: supabaseUrl ? 'present' : 'missing',
  key: supabaseKey ? 'present' : 'missing'
});

if (!supabaseUrl || !supabaseKey) {
  console.error('[API] Missing Supabase configuration!');
  throw new Error(`Missing Supabase configuration: url=${!!supabaseUrl}, key=${!!supabaseKey}`);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Server-side implementation of manageAuthState
const manageAuthState = async ({ authData, action }) => {
  console.log('Server-side manageAuthState called with:', { authData: !!authData, action });

  if (!supabase) {
    return {
      success: false,
      error: 'Supabase not configured'
    };
  }

  try {
    if ((action === 'login' || action === 'refresh') && authData?.user?.email) {
      // Find attorney by email for both login and refresh actions
      const { data: attorney, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('email', authData.user.email)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      return {
        success: true,
        message: 'Auth state managed successfully',
        action,
        attorney: attorney || null
      };
    }

    return {
      success: true,
      message: 'Auth state managed successfully',
      action
    };
  } catch (error) {
    console.error('Error managing auth state:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

export default async function handler(req, res) {
  // Enhanced CORS headers for all requests
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept, X-Vapi-Signature, X-Requested-With, X-Original-URL, X-Supabase-Key, Range, Content-Range, X-Supabase-Auth, X-Supabase-User-Agent');
  res.setHeader('Access-Control-Max-Age', '86400');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Expose-Headers', 'Content-Range, X-Content-Range, Content-Length');

  // Handle preflight OPTIONS requests with detailed logging
  if (req.method === 'OPTIONS') {
    console.log('[API] Handling OPTIONS preflight request for:', req.url);
    console.log('[API] Request headers:', Object.keys(req.headers));

    // Ensure successful response
    res.status(200);
    res.setHeader('Content-Length', '0');
    return res.end();
  }

  // Parse the URL path
  const { url } = req;
  const path = url.split('?')[0]; // Remove query parameters

  console.log(`[API Router] ${req.method} ${path}`);

  try {
    // Route to appropriate handler
    if (path === '/api/health') {
      return handleHealth(req, res);
    } else if (path === '/api/env') {
      return handleEnv(req, res);
    } else if (path === '/api/call-logs') {
      return handleCallLogs(req, res);
    } else if (path.startsWith('/api/supabase-proxy/')) {
      return handleSupabaseProxy(req, res, path);
    } else if (path === '/api/storage-upload') {
      return handleStorageUpload(req, res);
    } else if (path.startsWith('/api/sync-tools/')) {
      return handleSyncTools(req, res, path);
    } else if (path.startsWith('/api/vapi/')) {
      return handleVapi(req, res, path);
    } else if (path.startsWith('/api/vapi-mcp-server')) {
      return handleVapiMcp(req, res, path);
    } else if (path === '/api/ai-meta-mcp') {
      return handleAiMetaMcp(req, res);
    } else if (path === '/api/bug-report') {
      return handleBugReport(req, res);
    } else if (path === '/api/test') {
      return handleTest(req, res);
    } else {
      return res.status(404).json({
        error: 'API endpoint not found',
        path: path,
        availableEndpoints: [
          '/api/health',
          '/api/env',
          '/api/call-logs',
          '/api/supabase-proxy/*',
          '/api/storage-upload',
          '/api/sync-tools/*',
          '/api/vapi/*',
          '/api/vapi-mcp-server',
          '/api/ai-meta-mcp',
          '/api/bug-report',
          '/api/test'
        ]
      });
    }
  } catch (error) {
    console.error('[API Router] Error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error.message
    });
  }
}

// Health check endpoint
async function handleHealth(req, res) {
  return res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
}

// Environment check endpoint
async function handleEnv(req, res) {
  return res.status(200).json({
    hasSupabaseUrl: !!process.env.SUPABASE_URL,
    hasSupabaseKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
    hasVapiKey: !!process.env.VAPI_TOKEN,
    nodeEnv: process.env.NODE_ENV || 'development'
  });
}

// Call logs endpoint
async function handleCallLogs(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: 'Authorization header required' });
    }

    // For now, return empty array - implement actual call log fetching as needed
    return res.status(200).json([]);
  } catch (error) {
    console.error('[Call Logs] Error:', error);
    return res.status(500).json({ error: 'Failed to fetch call logs' });
  }
}

// Supabase CORS Proxy endpoint
async function handleSupabaseProxy(req, res, path) {
  console.log(`[Supabase Proxy] ${req.method} ${path}`);

  // Add specific CORS headers for Supabase proxy
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept, Range, Content-Range, X-Supabase-Auth');
  res.setHeader('Access-Control-Expose-Headers', 'Content-Range, X-Content-Range, Content-Length');

  // Handle OPTIONS for this specific endpoint
  if (req.method === 'OPTIONS') {
    console.log('[Supabase Proxy] Handling OPTIONS for:', path);
    return res.status(200).end();
  }

  try {
    // Extract the endpoint from the path
    const endpoint = path.replace('/api/supabase-proxy/', '');
    const originalUrl = req.headers['x-original-url'];
    const supabaseKey = req.headers['x-supabase-key'];

    console.log('[Supabase Proxy] Endpoint:', endpoint);
    console.log('[Supabase Proxy] Original URL:', originalUrl);

    // Construct the Supabase URL
    const supabaseUrl = originalUrl || `${process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL}/rest/v1/${endpoint}`;

    // Use the service role key for server-side requests
    const apiKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY || supabaseKey;

    // Prepare headers for Supabase request
    const supabaseHeaders = {
      'apikey': apiKey,
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
      'Prefer': req.headers.prefer || 'return=minimal'
    };

    // Add any additional headers from the original request
    if (req.headers['x-client-info']) {
      supabaseHeaders['X-Client-Info'] = req.headers['x-client-info'];
    }

    // Construct the full URL with query parameters
    const urlParts = req.url.split('?');
    const queryString = urlParts.length > 1 ? urlParts[1] : '';
    const fullSupabaseUrl = originalUrl || `${process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL}/rest/v1/${endpoint}${queryString ? '?' + queryString : ''}`;

    console.log('[Supabase Proxy] Full URL:', fullSupabaseUrl);

    // Forward the request to Supabase
    const supabaseResponse = await fetch(fullSupabaseUrl, {
      method: req.method,
      headers: supabaseHeaders,
      body: req.method !== 'GET' && req.method !== 'HEAD' ? JSON.stringify(req.body) : undefined
    });

    console.log('[Supabase Proxy] Response status:', supabaseResponse.status);

    // Get response data
    const responseData = await supabaseResponse.text();
    let jsonData;

    try {
      jsonData = JSON.parse(responseData);
    } catch (e) {
      jsonData = responseData;
    }

    // Return the response with CORS headers
    return res.status(supabaseResponse.status).json(jsonData);

  } catch (error) {
    console.error('[Supabase Proxy] Error:', error);
    return res.status(500).json({
      error: 'Supabase proxy error',
      message: error.message
    });
  }
}

// Sync tools endpoints
async function handleSyncTools(req, res, path) {
  const endpoint = path.replace('/api/sync-tools/', '');

  if (endpoint === 'sync-attorney-profile') {
    return handleSyncAttorneyProfile(req, res);
  } else if (endpoint === 'manage-auth-state') {
    return handleManageAuthState(req, res);
  } else if (endpoint === 'validate-configuration') {
    return handleValidateConfiguration(req, res);
  } else if (endpoint === 'check-preview-consistency') {
    return handleCheckPreviewConsistency(req, res);
  } else {
    return res.status(404).json({ error: 'Sync tool endpoint not found' });
  }
}

async function handleSyncAttorneyProfile(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { attorneyId, forceUpdate } = req.body;

    // Basic validation
    if (!attorneyId) {
      return res.status(400).json({ error: 'Attorney ID required' });
    }

    // For now, return success - implement actual sync logic as needed
    return res.status(200).json({
      success: true,
      message: 'Attorney profile sync completed',
      attorneyId,
      forceUpdate
    });
  } catch (error) {
    console.error('[Sync Attorney Profile] Error:', error);
    return res.status(500).json({ error: 'Failed to sync attorney profile' });
  }
}

async function handleManageAuthState(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  console.log('[manage-auth-state] Received request');
  console.log('[manage-auth-state] Request method:', req.method);
  console.log('[manage-auth-state] Request headers:', req.headers);

  try {
    // Parse request body if it's a string
    let body = req.body;
    if (typeof body === 'string') {
      try {
        body = JSON.parse(body);
      } catch (parseError) {
        console.error('[manage-auth-state] Error parsing request body:', parseError);
        return res.status(400).json({
          success: false,
          error: 'Invalid JSON in request body'
        });
      }
    }

    const { authData, action } = body || {};
    console.log('[manage-auth-state] Parsed body:', { action, hasAuthData: !!authData });

    // Log request details for debugging
    console.log('Request body:', {
      action,
      authData: authData ? {
        hasUser: !!authData.user,
        hasSession: !!authData.session,
        userEmail: authData.user?.email,
        userId: authData.user?.id
      } : null
    });

    // Validate required parameters
    if (!authData || !action) {
      console.log('[manage-auth-state] Missing required parameters');
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: authData and action are required'
      });
    }

    // Call the manageAuthState function
    console.log(`[manage-auth-state] Calling manageAuthState with action: ${action}`);

    try {
      const result = await manageAuthState({ authData, action });

      // Log the result for debugging
      console.log('[manage-auth-state] manageAuthState result:', {
        success: result.success,
        action: result.action,
        hasAttorney: !!result.attorney,
        message: result.message
      });

      // Ensure we always return a valid response
      const response = {
        success: true,
        result: result || { success: false, error: 'No result returned' }
      };

      console.log('[manage-auth-state] Sending response:', response);
      return res.status(200).json(response);
    } catch (innerError) {
      console.error('[manage-auth-state] Inner error in manageAuthState:', innerError);

      // Return a detailed error response
      const errorResponse = {
        success: false,
        error: innerError.message || 'Error in manageAuthState function',
        errorDetails: {
          message: innerError.message,
          stack: innerError.stack,
          name: innerError.name
        }
      };

      console.log('[manage-auth-state] Sending error response:', errorResponse);
      return res.status(500).json(errorResponse);
    }
  } catch (error) {
    console.error('Error managing auth state:', error);

    // Create a detailed error object
    let errorDetails;
    if (error instanceof Error) {
      errorDetails = {
        message: error.message,
        stack: error.stack,
        name: error.name
      };
    } else if (typeof error === 'object') {
      try {
        errorDetails = JSON.stringify(error);
      } catch (e) {
        errorDetails = 'Error object could not be stringified';
      }
    } else {
      errorDetails = String(error);
    }

    // Return a proper error response with detailed information
    return res.status(500).json({
      success: false,
      error: error.message || 'An unknown error occurred',
      errorDetails
    });
  }
}

async function handleValidateConfiguration(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // For now, return success - implement actual validation as needed
    return res.status(200).json({
      success: true,
      message: 'Configuration validation completed',
      valid: true
    });
  } catch (error) {
    console.error('[Validate Configuration] Error:', error);
    return res.status(500).json({ error: 'Failed to validate configuration' });
  }
}

async function handleCheckPreviewConsistency(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { attorneyId, assistantId } = req.body;

    // Basic validation
    if (!attorneyId) {
      return res.status(400).json({
        success: false,
        error: 'Attorney ID required',
        consistent: false
      });
    }

    // Perform basic consistency check
    const consistencyResult = {
      success: true,
      message: 'Preview consistency check completed',
      consistent: true,
      checks: {
        attorneyExists: !!attorneyId,
        assistantExists: !!assistantId,
        configValid: true,
        dataSync: true
      },
      timestamp: new Date().toISOString()
    };

    return res.status(200).json(consistencyResult);
  } catch (error) {
    console.error('[Check Preview Consistency] Error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to check preview consistency',
      consistent: false,
      message: error.message
    });
  }
}

// Vapi endpoints
async function handleVapi(req, res, path) {
  const endpoint = path.replace('/api/vapi/', '');

  if (endpoint === 'config') {
    return handleVapiConfig(req, res);
  } else {
    return res.status(404).json({ error: 'Vapi endpoint not found' });
  }
}

async function handleVapiConfig(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Return basic Vapi configuration
    return res.status(200).json({
      voices: [
        { id: 'echo', name: 'Echo', provider: 'playht' },
        { id: 'cho', name: 'Cho', provider: 'playht' },
        { id: 'alloy', name: 'Alloy', provider: 'openai' }
      ],
      models: [
        { id: 'gpt-4', name: 'GPT-4', provider: 'openai' },
        { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', provider: 'openai' }
      ]
    });
  } catch (error) {
    console.error('[Vapi Config] Error:', error);
    return res.status(500).json({ error: 'Failed to fetch Vapi configuration' });
  }
}

// Vapi MCP Server endpoint
async function handleVapiMcp(req, res, path) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // For now, return a basic response - implement actual MCP server logic as needed
    return res.status(200).json({
      jsonrpc: '2.0',
      id: req.body?.id || 1,
      result: {
        success: true,
        message: 'MCP server endpoint - implement as needed'
      }
    });
  } catch (error) {
    console.error('[Vapi MCP] Error:', error);
    return res.status(500).json({
      jsonrpc: '2.0',
      id: req.body?.id || 1,
      error: {
        code: -32603,
        message: 'Internal error',
        data: error.message
      }
    });
  }
}

// AI Meta MCP endpoint
async function handleAiMetaMcp(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { name, arguments: args } = req.body;

    // For now, return a basic response - implement actual AI Meta MCP logic as needed
    return res.status(200).json({
      success: true,
      message: 'AI Meta MCP endpoint - implement as needed',
      tool: name,
      arguments: args
    });
  } catch (error) {
    console.error('[AI Meta MCP] Error:', error);
    return res.status(500).json({ error: 'Failed to process AI Meta MCP request' });
  }
}

// Bug report endpoint
async function handleBugReport(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { message, userAgent, url, timestamp } = req.body;

    // For now, just log the bug report - implement actual bug reporting as needed
    console.log('[Bug Report]', { message, userAgent, url, timestamp });

    return res.status(200).json({
      success: true,
      message: 'Bug report received'
    });
  } catch (error) {
    console.error('[Bug Report] Error:', error);
    return res.status(500).json({ error: 'Failed to submit bug report' });
  }
}

// Storage upload endpoint
async function handleStorageUpload(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('[Storage Upload] Processing upload request');

    // Parse multipart form data (simplified - in production use a proper parser)
    const contentType = req.headers['content-type'] || '';
    if (!contentType.includes('multipart/form-data')) {
      return res.status(400).json({ error: 'Content-Type must be multipart/form-data' });
    }

    // For now, return a mock response since parsing multipart data is complex
    // In production, you'd use a library like 'formidable' or 'multer'
    console.log('[Storage Upload] Mock upload successful');

    const mockFileName = `logo_${Date.now()}.webp`;
    const mockPublicUrl = `${supabaseUrl}/storage/v1/object/public/legalscout_bucket1/${mockFileName}`;

    return res.status(200).json({
      success: true,
      publicUrl: mockPublicUrl,
      fileName: mockFileName
    });

  } catch (error) {
    console.error('[Storage Upload] Error:', error);
    return res.status(500).json({ error: 'Failed to upload file' });
  }
}

// Storage upload endpoint
async function handleStorageUpload(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('[Storage Upload] Processing upload request');

    // For now, return a simple response since we need to fix the RLS policies first
    // The main issue is authentication, not the upload mechanism
    return res.status(200).json({
      success: false,
      error: 'Storage upload via proxy not yet implemented',
      message: 'Please use direct Supabase upload with proper authentication'
    });

  } catch (error) {
    console.error('[Storage Upload] Error:', error);
    return res.status(500).json({ error: 'Failed to upload file' });
  }
}

// Test endpoint
async function handleTest(req, res) {
  return res.status(200).json({
    success: true,
    message: 'API router is working',
    timestamp: new Date().toISOString(),
    method: req.method,
    path: req.url
  });
}
