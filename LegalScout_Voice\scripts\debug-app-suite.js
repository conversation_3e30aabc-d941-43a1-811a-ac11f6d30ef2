#!/usr/bin/env node

/**
 * LegalScout Voice - Comprehensive Debug Test Suite
 * 
 * This script runs a series of simple, powerful tests to debug your app.
 * It tests everything from environment setup to API connectivity to UI functionality.
 * 
 * Usage:
 *   npm run debug:app
 *   node scripts/debug-app-suite.js [options]
 * 
 * Options:
 *   --quick            Run only quick tests (no browser)
 *   --full             Run all tests including browser tests
 *   --verbose          Verbose output
 *   --fix              Attempt to fix issues automatically
 *   --help             Show help
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { config } from 'dotenv';

// ES module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
config();

// Configuration
const DEFAULT_CONFIG = {
  quick: false,
  full: false,
  verbose: false,
  fix: false
};

// Test results tracking
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  warnings: 0,
  issues: [],
  fixes: []
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [DebugSuite]`;
  
  switch(type) {
    case 'success': console.log(`✅ ${prefix} ${message}`); break;
    case 'error': console.error(`❌ ${prefix} ${message}`); break;
    case 'warning': console.warn(`⚠️ ${prefix} ${message}`); break;
    case 'fix': console.log(`🔧 ${prefix} ${message}`); break;
    default: console.log(`ℹ️ ${prefix} ${message}`);
  }
}

function recordTest(name, passed, issue = null, fix = null) {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    log(`Test passed: ${name}`, 'success');
  } else {
    testResults.failed++;
    log(`Test failed: ${name}`, 'error');
    if (issue) {
      testResults.issues.push(issue);
      log(`  Issue: ${issue}`, 'warning');
    }
    if (fix) {
      testResults.fixes.push(fix);
      log(`  Suggested fix: ${fix}`, 'fix');
    }
  }
}

// Test 1: Environment Variables
function testEnvironmentVariables(config) {
  log('🔍 Testing Environment Variables...', 'info');
  
  const requiredVars = {
    'VITE_SUPABASE_URL': 'https://utopqxsvudgrtiwenlzl.supabase.co',
    'VITE_SUPABASE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9',
    'VITE_VAPI_PUBLIC_KEY': '310f0d43-27c2-47a5-a76d-e55171d024f7',
    'VITE_VAPI_SECRET_KEY': '6734febc-fc65-4669-93b0-929b31ff6564'
  };

  let allPassed = true;
  
  for (const [varName, expectedPrefix] of Object.entries(requiredVars)) {
    const value = process.env[varName];
    
    if (!value) {
      recordTest(`Environment variable ${varName}`, false, 
        `Missing ${varName}`, 
        `Add ${varName}=${expectedPrefix}... to your .env file`);
      allPassed = false;
    } else if (!value.startsWith(expectedPrefix.substring(0, 10))) {
      recordTest(`Environment variable ${varName}`, false,
        `Incorrect value for ${varName}`,
        `Update ${varName} in your .env file`);
      allPassed = false;
    } else {
      recordTest(`Environment variable ${varName}`, true);
    }
  }
  
  return allPassed;
}

// Test 2: Critical Files
function testCriticalFiles(config) {
  log('🔍 Testing Critical Files...', 'info');
  
  const criticalFiles = [
    { path: 'index.html', required: true },
    { path: 'package.json', required: true },
    { path: 'src/App.jsx', required: true },
    { path: 'src/lib/supabase.js', required: true },
    { path: 'src/config/vapiConfig.js', required: true },
    { path: 'src/components/dashboard', required: true },
    { path: 'api/index.js', required: true },
    { path: '.env', required: true }
  ];

  let allPassed = true;
  
  for (const file of criticalFiles) {
    const filePath = path.join(process.cwd(), file.path);
    const exists = fs.existsSync(filePath);
    
    if (!exists && file.required) {
      recordTest(`Critical file ${file.path}`, false,
        `Missing required file: ${file.path}`,
        `Create or restore the missing file`);
      allPassed = false;
    } else if (exists) {
      recordTest(`Critical file ${file.path}`, true);
    }
  }
  
  return allPassed;
}

// Test 3: Package Dependencies
function testPackageDependencies(config) {
  log('🔍 Testing Package Dependencies...', 'info');
  
  try {
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    const criticalDeps = [
      '@supabase/supabase-js',
      'react',
      'react-dom',
      'vite'
    ];
    
    let allPassed = true;
    
    for (const dep of criticalDeps) {
      const hasInDeps = packageJson.dependencies && packageJson.dependencies[dep];
      const hasInDevDeps = packageJson.devDependencies && packageJson.devDependencies[dep];
      
      if (!hasInDeps && !hasInDevDeps) {
        recordTest(`Dependency ${dep}`, false,
          `Missing dependency: ${dep}`,
          `Run: npm install ${dep}`);
        allPassed = false;
      } else {
        recordTest(`Dependency ${dep}`, true);
      }
    }
    
    return allPassed;
  } catch (error) {
    recordTest('Package.json parsing', false,
      `Failed to parse package.json: ${error.message}`,
      'Check package.json syntax');
    return false;
  }
}

// Test 4: API Connectivity
async function testAPIConnectivity(config) {
  log('🔍 Testing API Connectivity...', 'info');
  
  let allPassed = true;
  
  // Test Supabase
  try {
    const { default: fetch } = await import('node-fetch');
    const supabaseUrl = process.env.VITE_SUPABASE_URL;
    
    if (supabaseUrl) {
      const response = await fetch(`${supabaseUrl}/rest/v1/`, {
        method: 'HEAD',
        timeout: 5000
      });
      
      if (response.ok || response.status === 401) {
        recordTest('Supabase API connectivity', true);
      } else {
        recordTest('Supabase API connectivity', false,
          `Supabase returned status ${response.status}`,
          'Check Supabase URL and network connectivity');
        allPassed = false;
      }
    } else {
      recordTest('Supabase API connectivity', false,
        'No Supabase URL configured',
        'Set VITE_SUPABASE_URL in .env file');
      allPassed = false;
    }
  } catch (error) {
    if (error.code !== 'MODULE_NOT_FOUND') {
      recordTest('Supabase API connectivity', false,
        `Connection failed: ${error.message}`,
        'Check network connectivity and Supabase status');
      allPassed = false;
    }
  }
  
  // Test Vapi (basic ping)
  try {
    const { default: fetch } = await import('node-fetch');
    const vapiKey = process.env.VITE_VAPI_PUBLIC_KEY;
    
    if (vapiKey) {
      const response = await fetch('https://api.vapi.ai/assistant', {
        method: 'HEAD',
        headers: {
          'Authorization': `Bearer ${vapiKey}`
        },
        timeout: 5000
      });
      
      if (response.ok || response.status === 401 || response.status === 403) {
        recordTest('Vapi API connectivity', true);
      } else {
        recordTest('Vapi API connectivity', false,
          `Vapi returned status ${response.status}`,
          'Check Vapi API key and network connectivity');
        allPassed = false;
      }
    } else {
      recordTest('Vapi API connectivity', false,
        'No Vapi API key configured',
        'Set VITE_VAPI_PUBLIC_KEY in .env file');
      allPassed = false;
    }
  } catch (error) {
    if (error.code !== 'MODULE_NOT_FOUND') {
      recordTest('Vapi API connectivity', false,
        `Connection failed: ${error.message}`,
        'Check network connectivity and Vapi status');
      allPassed = false;
    }
  }
  
  return allPassed;
}

// Test 5: Build Configuration
function testBuildConfiguration(config) {
  log('🔍 Testing Build Configuration...', 'info');
  
  let allPassed = true;
  
  // Check Vite config
  const viteConfigPath = path.join(process.cwd(), 'vite.config.js');
  if (fs.existsSync(viteConfigPath)) {
    recordTest('Vite config exists', true);
  } else {
    recordTest('Vite config exists', false,
      'Missing vite.config.js',
      'Create vite.config.js with proper configuration');
    allPassed = false;
  }
  
  // Check Vercel config
  const vercelConfigPath = path.join(process.cwd(), 'vercel.json');
  if (fs.existsSync(vercelConfigPath)) {
    recordTest('Vercel config exists', true);
  } else {
    recordTest('Vercel config exists', false,
      'Missing vercel.json',
      'Create vercel.json for proper Vercel deployment');
    allPassed = false;
  }
  
  return allPassed;
}

// Main test runner
async function runDebugSuite(config) {
  log('🚀 Starting LegalScout Voice Debug Suite', 'info');
  
  if (config.verbose) {
    log('Configuration:', 'info');
    console.log(JSON.stringify(config, null, 2));
  }
  
  // Reset test results
  testResults = {
    total: 0,
    passed: 0,
    failed: 0,
    warnings: 0,
    issues: [],
    fixes: []
  };
  
  try {
    // Run tests
    log('📋 Running diagnostic tests...', 'info');
    
    testEnvironmentVariables(config);
    testCriticalFiles(config);
    testPackageDependencies(config);
    await testAPIConnectivity(config);
    testBuildConfiguration(config);
    
    // Summary
    const successRate = testResults.total > 0 ? 
      Math.round((testResults.passed / testResults.total) * 100) : 0;
    
    log('📊 Debug Suite Summary:', 'info');
    log(`  Total tests: ${testResults.total}`, 'info');
    log(`  Passed: ${testResults.passed}`, 'success');
    log(`  Failed: ${testResults.failed}`, testResults.failed > 0 ? 'error' : 'info');
    log(`  Success rate: ${successRate}%`, successRate >= 80 ? 'success' : 'warning');
    
    if (testResults.issues.length > 0) {
      log('🚨 Issues found:', 'warning');
      testResults.issues.forEach((issue, index) => {
        log(`  ${index + 1}. ${issue}`, 'warning');
      });
    }
    
    if (testResults.fixes.length > 0) {
      log('🔧 Suggested fixes:', 'fix');
      testResults.fixes.forEach((fix, index) => {
        log(`  ${index + 1}. ${fix}`, 'fix');
      });
    }
    
    // Overall status
    let status = 'healthy';
    if (testResults.failed > 0) {
      status = testResults.failed >= 3 ? 'critical' : 'warning';
    }
    
    log(`🎯 Overall status: ${status.toUpperCase()}`, 
        status === 'healthy' ? 'success' : 
        status === 'warning' ? 'warning' : 'error');
    
    return { success: testResults.failed === 0, results: testResults };
    
  } catch (error) {
    log(`💥 Debug suite failed: ${error.message}`, 'error');
    return { success: false, error: error.message };
  }
}

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const config = { ...DEFAULT_CONFIG };

  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--quick':
        config.quick = true;
        break;
      case '--full':
        config.full = true;
        break;
      case '--verbose':
        config.verbose = true;
        break;
      case '--fix':
        config.fix = true;
        break;
      case '--help':
        showHelp();
        process.exit(0);
        break;
      default:
        console.error(`Unknown option: ${args[i]}`);
        process.exit(1);
    }
  }

  return config;
}

// Show help
function showHelp() {
  console.log(`
LegalScout Voice - Debug Test Suite

Usage: node scripts/debug-app-suite.js [options]

Options:
  --quick            Run only quick tests (no browser)
  --full             Run all tests including browser tests
  --verbose          Verbose output
  --fix              Attempt to fix issues automatically
  --help             Show this help

Examples:
  node scripts/debug-app-suite.js
  node scripts/debug-app-suite.js --quick --verbose
  node scripts/debug-app-suite.js --full --fix
`);
}

// Main function
async function main() {
  const config = parseArgs();
  const result = await runDebugSuite(config);
  
  process.exit(result.success ? 0 : 1);
}

// Handle errors
process.on('unhandledRejection', (error) => {
  log(`Unhandled rejection: ${error.message}`, 'error');
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  log(`Uncaught exception: ${error.message}`, 'error');
  process.exit(1);
});

// Run the main function
main().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});

export { runDebugSuite, parseArgs };
