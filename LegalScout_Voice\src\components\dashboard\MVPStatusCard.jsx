/**
 * MVP Status Card Component
 * 
 * Displays MVP readiness status and provides quick actions
 * to ensure attorney setup is complete for launch.
 */

import React from 'react';
import { useMVPLaunch } from '../../hooks/useMVPLaunch';
import './MVPStatusCard.css';

const MVPStatusCard = ({ attorney, onStatusChange }) => {
  const {
    mvpStatus,
    isLoading,
    error,
    ensureMVPReady,
    quickFixMVPIssues,
    getAssistantURL,
    isReady,
    needsSetup,
    hasIssues,
    readinessPercentage
  } = useMVPLaunch(attorney);

  const handleEnsureMVPReady = async () => {
    try {
      const result = await ensureMVPReady();
      if (onStatusChange) {
        onStatusChange(result);
      }
    } catch (err) {
      console.error('Failed to ensure MVP ready:', err);
    }
  };

  const handleQuickFix = async () => {
    try {
      const result = await quickFixMVPIssues();
      if (onStatusChange) {
        onStatusChange(result);
      }
    } catch (err) {
      console.error('Quick fix failed:', err);
    }
  };

  const getStatusColor = () => {
    if (isReady) return '#10b981'; // green
    if (needsSetup) return '#f59e0b'; // amber
    return '#ef4444'; // red
  };

  const getStatusText = () => {
    if (isLoading) return 'Checking...';
    if (isReady) return 'MVP Ready';
    if (needsSetup) return 'Setup Required';
    return 'Issues Detected';
  };

  const assistantURL = getAssistantURL();

  return (
    <div className="mvp-status-card">
      <div className="mvp-status-header">
        <h3>MVP Launch Status</h3>
        <div 
          className="mvp-status-badge"
          style={{ backgroundColor: getStatusColor() }}
        >
          {getStatusText()}
        </div>
      </div>

      <div className="mvp-readiness-bar">
        <div className="mvp-readiness-label">
          Setup Progress: {readinessPercentage}%
        </div>
        <div className="mvp-progress-bar">
          <div 
            className="mvp-progress-fill"
            style={{ 
              width: `${readinessPercentage}%`,
              backgroundColor: getStatusColor()
            }}
          />
        </div>
      </div>

      <div className="mvp-checklist">
        <div className={`mvp-check-item ${mvpStatus.hasWorkingAuth ? 'complete' : 'incomplete'}`}>
          <span className="mvp-check-icon">
            {mvpStatus.hasWorkingAuth ? '✅' : '⚠️'}
          </span>
          <span>Authentication Working</span>
        </div>
        
        <div className={`mvp-check-item ${mvpStatus.hasAssistant ? 'complete' : 'incomplete'}`}>
          <span className="mvp-check-icon">
            {mvpStatus.hasAssistant ? '✅' : '⚠️'}
          </span>
          <span>Assistant Created</span>
        </div>
        
        <div className={`mvp-check-item ${mvpStatus.hasSubdomain ? 'complete' : 'incomplete'}`}>
          <span className="mvp-check-icon">
            {mvpStatus.hasSubdomain ? '✅' : '⚠️'}
          </span>
          <span>Subdomain Assigned</span>
        </div>
      </div>

      {hasIssues && (
        <div className="mvp-issues">
          <h4>Issues to Resolve:</h4>
          <ul>
            {mvpStatus.issues.map((issue, index) => (
              <li key={index}>{issue}</li>
            ))}
          </ul>
        </div>
      )}

      {error && (
        <div className="mvp-error">
          <strong>Error:</strong> {error}
        </div>
      )}

      <div className="mvp-actions">
        {!isReady && (
          <button
            className="mvp-action-button primary"
            onClick={handleEnsureMVPReady}
            disabled={isLoading}
          >
            {isLoading ? 'Setting Up...' : 'Complete MVP Setup'}
          </button>
        )}

        {hasIssues && (
          <button
            className="mvp-action-button secondary"
            onClick={handleQuickFix}
            disabled={isLoading}
          >
            {isLoading ? 'Fixing...' : 'Quick Fix Issues'}
          </button>
        )}

        {isReady && assistantURL && (
          <div className="mvp-success-actions">
            <p className="mvp-success-message">
              🎉 Your assistant is ready! Share your link:
            </p>
            <div className="mvp-url-display">
              <input
                type="text"
                value={assistantURL}
                readOnly
                className="mvp-url-input"
              />
              <button
                className="mvp-copy-button"
                onClick={() => navigator.clipboard.writeText(assistantURL)}
              >
                Copy
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MVPStatusCard;
