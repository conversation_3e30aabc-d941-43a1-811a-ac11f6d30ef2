/**
 * useDomainSync Hook
 * 
 * React hook for handling domain/subdomain changes and keeping
 * assistant configurations in sync.
 */

import { useState, useCallback } from 'react';
import { domainAssistantSync } from '../services/DomainAssistantSync';
import { supabase } from '../lib/supabase';

export function useDomainSync() {
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);

  /**
   * Update subdomain for an attorney and sync all configurations
   */
  const updateSubdomain = useCallback(async (attorneyId, newSubdomain) => {
    try {
      setIsUpdating(true);
      setError(null);

      console.log(`[useDomainSync] Updating subdomain for attorney ${attorneyId} to: ${newSubdomain}`);

      // Get current attorney data
      const { data: currentAttorney, error: fetchError } = await supabase
        .from('attorneys')
        .select('*')
        .eq('id', attorneyId)
        .single();

      if (fetchError || !currentAttorney) {
        throw new Error(`Attorney not found: ${attorneyId}`);
      }

      const oldSubdomain = currentAttorney.subdomain;

      // Validate new subdomain
      const validation = await domainAssistantSync.validateDomainConfig(newSubdomain);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      // Update subdomain in database
      const { error: updateError } = await supabase
        .from('attorneys')
        .update({ 
          subdomain: newSubdomain,
          updated_at: new Date().toISOString()
        })
        .eq('id', attorneyId);

      if (updateError) {
        throw new Error(`Failed to update subdomain in database: ${updateError.message}`);
      }

      // Handle assistant configuration sync
      if (currentAttorney.vapi_assistant_id) {
        await domainAssistantSync.handleSubdomainChange(
          attorneyId,
          oldSubdomain,
          newSubdomain
        );
      }

      setLastUpdate({
        attorneyId,
        oldSubdomain,
        newSubdomain,
        timestamp: new Date().toISOString()
      });

      console.log(`[useDomainSync] ✅ Subdomain updated successfully: ${oldSubdomain} → ${newSubdomain}`);
      return { success: true, oldSubdomain, newSubdomain };

    } catch (error) {
      console.error('[useDomainSync] ❌ Error updating subdomain:', error);
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setIsUpdating(false);
    }
  }, []);

  /**
   * Sync assistant configuration for current domain
   */
  const syncAssistantForCurrentDomain = useCallback(async (attorneyId) => {
    try {
      setIsUpdating(true);
      setError(null);

      // Get attorney data
      const { data: attorney, error: fetchError } = await supabase
        .from('attorneys')
        .select('*')
        .eq('id', attorneyId)
        .single();

      if (fetchError || !attorney) {
        throw new Error(`Attorney not found: ${attorneyId}`);
      }

      if (!attorney.vapi_assistant_id) {
        throw new Error('No assistant ID found for this attorney');
      }

      // Update assistant for current domain
      const updatedAssistant = await domainAssistantSync.updateAssistantForDomain(
        attorney.vapi_assistant_id,
        attorney.subdomain,
        attorney
      );

      console.log(`[useDomainSync] ✅ Assistant synced for domain: ${attorney.subdomain}`);
      return { success: true, assistant: updatedAssistant };

    } catch (error) {
      console.error('[useDomainSync] ❌ Error syncing assistant:', error);
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setIsUpdating(false);
    }
  }, []);

  /**
   * Validate if a subdomain is available
   */
  const validateSubdomain = useCallback(async (subdomain, excludeAttorneyId = null) => {
    try {
      let query = supabase
        .from('attorneys')
        .select('id, firm_name')
        .eq('subdomain', subdomain);

      if (excludeAttorneyId) {
        query = query.neq('id', excludeAttorneyId);
      }

      const { data: existingAttorney } = await query.single();

      if (existingAttorney) {
        return {
          available: false,
          error: `Subdomain '${subdomain}' is already taken by ${existingAttorney.firm_name}`
        };
      }

      // Additional validation rules
      if (subdomain.length < 3) {
        return {
          available: false,
          error: 'Subdomain must be at least 3 characters long'
        };
      }

      if (!/^[a-z0-9-]+$/.test(subdomain)) {
        return {
          available: false,
          error: 'Subdomain can only contain lowercase letters, numbers, and hyphens'
        };
      }

      if (subdomain.startsWith('-') || subdomain.endsWith('-')) {
        return {
          available: false,
          error: 'Subdomain cannot start or end with a hyphen'
        };
      }

      // Reserved subdomains
      const reserved = ['www', 'api', 'admin', 'dashboard', 'app', 'mail', 'ftp', 'blog'];
      if (reserved.includes(subdomain)) {
        return {
          available: false,
          error: 'This subdomain is reserved'
        };
      }

      return { available: true };

    } catch (error) {
      // If no existing attorney found, subdomain is available
      if (error.code === 'PGRST116') {
        return { available: true };
      }
      
      return {
        available: false,
        error: `Error validating subdomain: ${error.message}`
      };
    }
  }, []);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    isUpdating,
    error,
    lastUpdate,
    updateSubdomain,
    syncAssistantForCurrentDomain,
    validateSubdomain,
    clearError
  };
}
