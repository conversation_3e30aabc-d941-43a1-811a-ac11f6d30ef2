<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Localhost Fix Test</title>
  <style>
    body {
      font-family: monospace;
      padding: 20px;
      background: #f5f5f5;
    }
    .test-result {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
    }
    .pass { background: #d4edda; color: #155724; }
    .fail { background: #f8d7da; color: #721c24; }
    .info { background: #d1ecf1; color: #0c5460; }
  </style>
</head>
<body>
  <h1>🧪 Localhost Fix Test</h1>
  <p>Testing if localhost properly returns 'default' subdomain instead of forcing attorney subdomain.</p>
  
  <div id="results"></div>
  
  <script type="module">
    const results = document.getElementById('results');
    
    function addResult(message, type = 'info') {
      const div = document.createElement('div');
      div.className = `test-result ${type}`;
      div.textContent = message;
      results.appendChild(div);
      console.log(`[Test] ${type.toUpperCase()}: ${message}`);
    }
    
    // Test 1: Check hostname
    addResult(`Current hostname: ${window.location.hostname}`, 'info');
    
    // Test 2: Check if localhost is detected correctly
    const hostname = window.location.hostname;
    const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1' || /^\d+\.\d+\.\d+\.\d+$/.test(hostname);
    
    if (isLocalhost) {
      addResult('✅ Localhost correctly detected', 'pass');
    } else {
      addResult('❌ Localhost not detected (this might be expected if running on different host)', 'fail');
    }
    
    // Test 3: Test subdomain extraction
    try {
      // Import the subdomain tester
      import('./src/utils/subdomainTester.js').then(({ getCurrentSubdomain }) => {
        const subdomain = getCurrentSubdomain();
        addResult(`Detected subdomain: "${subdomain}"`, 'info');
        
        if (isLocalhost && subdomain === 'default') {
          addResult('✅ PASS: Localhost returns "default" subdomain', 'pass');
        } else if (isLocalhost && subdomain !== 'default') {
          addResult(`❌ FAIL: Localhost should return "default" but got "${subdomain}"`, 'fail');
        } else {
          addResult(`ℹ️ INFO: Non-localhost environment, subdomain: "${subdomain}"`, 'info');
        }
        
        // Test 4: Test attorney subdomain detection
        import('./src/utils/subdomainExtraction.js').then(({ isAttorneySubdomain }) => {
          const isAttorney = isAttorneySubdomain(subdomain);
          addResult(`Is attorney subdomain: ${isAttorney}`, 'info');
          
          if (isLocalhost && !isAttorney) {
            addResult('✅ PASS: Localhost is NOT treated as attorney subdomain', 'pass');
          } else if (isLocalhost && isAttorney) {
            addResult('❌ FAIL: Localhost should NOT be treated as attorney subdomain', 'fail');
          } else {
            addResult(`ℹ️ INFO: Non-localhost environment, attorney subdomain: ${isAttorney}`, 'info');
          }
          
          // Test 5: Check localStorage for test subdomain
          const testSubdomain = localStorage.getItem('testSubdomain');
          if (testSubdomain) {
            addResult(`⚠️ WARNING: Test subdomain set in localStorage: "${testSubdomain}"`, 'info');
            addResult('This will override the default behavior. Clear localStorage to test normal flow.', 'info');
          } else {
            addResult('✅ PASS: No test subdomain in localStorage', 'pass');
          }
          
          addResult('🎉 Test completed! Check results above.', 'info');
        });
      });
    } catch (error) {
      addResult(`❌ ERROR: Failed to import modules: ${error.message}`, 'fail');
    }
  </script>
</body>
</html>
