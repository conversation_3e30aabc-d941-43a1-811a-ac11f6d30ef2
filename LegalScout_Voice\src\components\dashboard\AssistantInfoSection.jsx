import React, { useState, useEffect } from 'react';
import { FaRobot, Fa<PERSON>ools, FaPlus, FaSync } from 'react-icons/fa';
import { supabase } from '../../lib/supabase';
import { useAssistantAware } from '../../contexts/AssistantAwareContext';
import VoiceAssistantDiagnostics from './VoiceAssistantDiagnostics';

// Generate UUID function
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

/**
 * AssistantInfoSection Component
 *
 * Displays assistant information with name and ID, includes diagnostics toggle,
 * and provides create assistant functionality.
 */
const AssistantInfoSection = ({
  attorney,
  showDiagnostics,
  setShowDiagnostics,
  onCreateAssistant,
  setLoading,
  setError,
  setSuccess,
  onUpdate
}) => {
  const [assistantData, setAssistantData] = useState(null);
  const [loadingAssistant, setLoadingAssistant] = useState(false);

  // Use AssistantAware context to get current assistant ID
  const { currentAssistant, isAssistantSelected } = useAssistantAware();

  // Get the current assistant ID from context (preferred) or fallback to attorney
  const rawAssistantId = currentAssistant?.id || attorney?.current_assistant_id || attorney?.vapi_assistant_id;

  // CRITICAL FIX: Validate assistant ID format
  const isValidVapiId = (id) => {
    return id &&
           typeof id === 'string' &&
           id.length <= 40 &&
           !id.includes('mock') &&
           !id.startsWith('mock-') &&
           id !== 'null' &&
           id !== 'undefined';
  };

  const currentAssistantId = isValidVapiId(rawAssistantId) ? rawAssistantId : null;

  // Log validation results for debugging
  console.log('[AssistantInfoSection] Assistant ID validation:', {
    rawId: rawAssistantId,
    isValid: isValidVapiId(rawAssistantId),
    finalId: currentAssistantId,
    source: currentAssistant?.id ? 'context' : attorney?.current_assistant_id ? 'current' : 'vapi'
  });

  // Load assistant data when component mounts or assistant ID changes
  useEffect(() => {
    if (currentAssistantId && currentAssistantId !== 'null' && currentAssistantId !== 'undefined') {
      loadAssistantData();
    }
  }, [currentAssistantId]);

  const loadAssistantData = async () => {
    if (!currentAssistantId) {
      console.log('[AssistantInfoSection] No valid assistant ID, skipping load');
      return;
    }

    setLoadingAssistant(true);
    try {
      console.log('[AssistantInfoSection] Loading assistant data for ID:', currentAssistantId);

      // Import the Vapi service dynamically
      const { vapiMcpService } = await import('../../services/vapiMcpService');

      // Try to get assistant data using current assistant ID
      const assistant = await vapiMcpService.getAssistant(currentAssistantId);

      if (assistant && !assistant.mock) {
        console.log('[AssistantInfoSection] ✅ Loaded real assistant data:', assistant.name);
        setAssistantData(assistant);

        // Check multiple possible fields for instructions
        const instructions = assistant.instructions ||
                           assistant.systemPrompt ||
                           assistant.model?.systemPrompt ||
                           assistant.llm?.systemPrompt ||
                           assistant.prompt;

        // Update the attorney's vapi_instructions with the data from Vapi
        if (instructions && onUpdate) {
          onUpdate({
            vapi_instructions: instructions,
            welcome_message: assistant.firstMessage || attorney.welcome_message
          });
        }
      } else if (assistant?.mock) {
        console.warn('[AssistantInfoSection] ⚠️ Received mock assistant data, not using it');
        setAssistantData(null);
      } else {
        console.warn('[AssistantInfoSection] ⚠️ No assistant data received');
        setAssistantData(null);
      }
    } catch (error) {
      console.error('[AssistantInfoSection] ❌ Error loading assistant data:', error);

      // Check if it's an invalid ID format error
      if (error.message.includes('Invalid assistant ID format') || error.message.includes('Supabase UUID')) {
        console.error('[AssistantInfoSection] 🚨 CRITICAL: Invalid assistant ID format detected!');
        setAssistantData({
          error: true,
          message: 'Invalid assistant ID format - please check your assistant configuration'
        });
      } else {
        setAssistantData(null);
      }
    } finally {
      setLoadingAssistant(false);
    }
  };

  const handleCreateAssistant = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get the attorney from localStorage if not available
      let currentAttorney = attorney;
      if (!currentAttorney || !currentAttorney.id) {
        try {
          console.log('[AssistantInfoSection] Attorney object is missing or has no ID, checking localStorage');
          const storedAttorney = localStorage.getItem('attorney');
          if (storedAttorney) {
            currentAttorney = JSON.parse(storedAttorney);
            console.log('[AssistantInfoSection] Retrieved attorney from localStorage:', currentAttorney);
          }
        } catch (error) {
          console.error('[AssistantInfoSection] Error parsing attorney from localStorage:', error);
        }
      }

      // If still no valid attorney, create a development one
      if (!currentAttorney || (!currentAttorney.id && !currentAttorney.subdomain && !currentAttorney.email)) {
        currentAttorney = {
          id: generateUUID(), // Generate a valid UUID
          subdomain: 'dev-attorney',
          firm_name: 'Development Law Firm',
          name: 'Dev Attorney',
          email: '<EMAIL>',
          user_id: generateUUID(), // Generate a valid UUID
        };

        // Save to localStorage
        localStorage.setItem('attorney', JSON.stringify(currentAttorney));
      }

      // Import the Vapi assistant service dynamically
      const { vapiAssistantService } = await import('../../services/vapiAssistantService');

      // Create a new assistant
      const assistant = await vapiAssistantService.createAssistantForAttorney(currentAttorney);

      if (assistant && assistant.id) {

        // Update the attorney object with the new assistant ID
        const updatedAttorney = { ...currentAttorney, vapi_assistant_id: assistant.id };

        // Check if the attorney ID is a valid UUID
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        const isValidUuid = uuidRegex.test(currentAttorney.id);

        if (isValidUuid) {
          try {
            // Update the attorney in Supabase
            const { error } = await supabase
              .from('attorneys')
              .update({ vapi_assistant_id: assistant.id })
              .eq('id', currentAttorney.id);

            if (error) {
              console.error('[AssistantInfoSection] Error updating attorney in Supabase:', error);
              // Don't throw error, just log it and continue
            }
          } catch (updateError) {
            console.error('[AssistantInfoSection] Exception updating attorney in Supabase:', updateError);
            // Don't throw error, just log it and continue
          }
        }

        // Update localStorage
        localStorage.setItem('attorney', JSON.stringify(updatedAttorney));

        // Update the local attorney state
        onUpdate({ vapi_assistant_id: assistant.id });

        // Load the new assistant data
        setAssistantData(assistant);

        // Show success message
        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
      } else {
        throw new Error('Failed to create assistant - no assistant ID returned');
      }
    } catch (error) {
      console.error('[AssistantInfoSection] Error creating new assistant:', error);
      setError('Error creating new assistant: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to check if assistant ID is valid (not null, undefined, or mock)
  const isValidAssistantId = (assistantId) => {
    return assistantId &&
           assistantId !== 'null' &&
           assistantId !== 'undefined' &&
           !assistantId.startsWith('mock-');
  };

  // Check for invalid assistant ID format (UUID instead of Vapi ID)
  if (rawAssistantId && !currentAssistantId) {
    return (
      <div className="assistant-info-section">
        <div className="assistant-info">
          <div className="alert alert-error">
            <span>
              ⚠️ Invalid Assistant ID Format Detected
            </span>
            <div style={{ marginTop: '8px', fontSize: '14px' }}>
              <strong>Current ID:</strong> {rawAssistantId}<br/>
              <strong>Issue:</strong> This appears to be a Supabase UUID, not a Vapi assistant ID.<br/>
              <strong>Solution:</strong> Create a new assistant or update the ID mapping.
            </div>
          </div>
          <button
            type="button"
            className="create-assistant-button"
            onClick={handleCreateAssistant}
          >
            <FaPlus /> Create New Assistant
          </button>
        </div>
      </div>
    );
  }

  // If no valid assistant ID, show create assistant option
  if (!isValidAssistantId(currentAssistantId)) {
    const isMockId = currentAssistantId && currentAssistantId.startsWith('mock-');

    return (
      <div className="assistant-info-section">
        <div className="assistant-info">
          <div className="alert alert-warning">
            <span>
              {isMockId
                ? 'Voice assistant needs to be properly configured. Mock assistant detected.'
                : 'No assistant configured. Click below to create one.'}
            </span>
          </div>
          <button
            type="button"
            className="create-assistant-button"
            onClick={handleCreateAssistant}
          >
            <FaPlus /> {isMockId ? 'Fix Assistant' : 'Create Assistant'}
          </button>
        </div>
      </div>
    );
  }

  // Show assistant info with name and ID
  return (
    <div className="assistant-info-section">
      <div className="assistant-info">
        {assistantData?.error ? (
          <div className="alert alert-error">
            <div className="assistant-display">
              <div className="assistant-main-info">
                <FaRobot className="assistant-icon" />
                <div className="assistant-details">
                  <div className="assistant-name">
                    ❌ Assistant Configuration Error
                  </div>
                  <div className="assistant-id">
                    ID: {currentAssistantId}
                  </div>
                  <div className="error-message">
                    {assistantData.message}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="alert alert-info">
            <div className="assistant-display">
              <div className="assistant-main-info">
                <FaRobot className="assistant-icon" />
                <div className="assistant-details">
                  <div className="assistant-name">
                    {loadingAssistant ? 'Loading...' : (assistantData?.name || 'Assistant')}
                  </div>
                  <div className="assistant-id">
                    ID: {currentAssistantId}
                  </div>
                </div>
              </div>
              <div className="assistant-actions">
                <button
                  type="button"
                  className="diagnostics-toggle-button"
                  onClick={() => setShowDiagnostics(!showDiagnostics)}
                  title={showDiagnostics ? 'Hide Diagnostics' : 'Show Diagnostics'}
                >
                  <FaTools />
                </button>
                <button
                  type="button"
                  className="refresh-assistant-button"
                  onClick={loadAssistantData}
                  title="Refresh Assistant Data"
                >
                  <FaSync />
                </button>
              </div>
            </div>
          </div>
        )}

        <button
          type="button"
          className="create-assistant-button secondary"
          onClick={handleCreateAssistant}
        >
          <FaPlus /> Create New Assistant
        </button>
      </div>

      {/* Diagnostics Section */}
      {showDiagnostics && (
        <div className="diagnostics-section">
          <VoiceAssistantDiagnostics attorney={attorney} />
        </div>
      )}
    </div>
  );
};

export default AssistantInfoSection;
