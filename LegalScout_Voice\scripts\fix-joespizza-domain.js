/**
 * Fix joespizza domain configuration
 * 
 * This script fixes the assistant configuration for joespizza.legalscout.net
 * and can be used as a template for future domain changes.
 */

import { domainAssistantSync } from '../src/services/DomainAssistantSync.js';
import { supabase } from '../src/lib/supabase.js';

async function fixJoespizzaDomain() {
  try {
    console.log('🔧 [FixJoespizzaDomain] Starting domain fix for joespizza...');

    // Get the joespizza attorney data
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('subdomain', 'joespizza')
      .single();

    if (error || !attorney) {
      throw new Error('Joespizza attorney record not found');
    }

    console.log('✅ [FixJoespizzaDomain] Found attorney record:', {
      id: attorney.id,
      firm_name: attorney.firm_name,
      subdomain: attorney.subdomain,
      vapi_assistant_id: attorney.vapi_assistant_id
    });

    // Update the assistant configuration for the joespizza domain
    if (attorney.vapi_assistant_id) {
      console.log('🔄 [FixJoespizzaDomain] Updating assistant configuration...');
      
      const updatedAssistant = await domainAssistantSync.updateAssistantForDomain(
        attorney.vapi_assistant_id,
        'joespizza',
        attorney
      );

      console.log('✅ [FixJoespizzaDomain] Assistant updated:', {
        id: updatedAssistant.id,
        name: updatedAssistant.name,
        firstMessage: updatedAssistant.firstMessage
      });
    } else {
      console.warn('⚠️ [FixJoespizzaDomain] No assistant ID found for joespizza attorney');
    }

    // Validate the configuration
    console.log('🔍 [FixJoespizzaDomain] Validating domain configuration...');
    const validation = await domainAssistantSync.validateDomainConfig(
      'joespizza', 
      attorney.vapi_assistant_id
    );

    if (validation.valid) {
      console.log('✅ [FixJoespizzaDomain] Domain configuration is valid');
    } else {
      console.error('❌ [FixJoespizzaDomain] Domain configuration validation failed:', validation.error);
    }

    console.log('🎉 [FixJoespizzaDomain] Domain fix completed successfully!');
    console.log('🌐 [FixJoespizzaDomain] joespizza.legalscout.net should now work correctly');

  } catch (error) {
    console.error('❌ [FixJoespizzaDomain] Error fixing domain:', error);
    throw error;
  }
}

// Run the fix if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  fixJoespizzaDomain()
    .then(() => {
      console.log('✅ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}

export { fixJoespizzaDomain };
