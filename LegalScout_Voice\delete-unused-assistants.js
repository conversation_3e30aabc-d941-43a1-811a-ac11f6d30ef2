/**
 * Delete Unused Vapi Assistants
 * This script will delete the identified unused assistants
 */

// Vapi API configuration
const VAPI_API_URL = 'https://api.vapi.ai';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';

// Assistants safe to delete (orphaned, no calls, not linked to attorneys)
const ASSISTANTS_TO_DELETE = [
  { id: "ebf4cc11-afd7-4956-a39d-21f1d78d8021", name: "LegalScout Legal Assistant" },
  { id: "a6b9eac5-e93f-4730-85b0-e33b27a6ff8f", name: "General Counsel Online Assistant" },
  { id: "3ba35836-bd76-46fc-aaa1-1d90c6386bcc", name: "LegalScout System Legal Assistant" },
  { id: "1d7c6603-8e79-44da-86af-05f2f215d3b5", name: "LegalScout System Legal Assistant" },
  { id: "e457a827-58b4-4d71-98da-c59bc607913d", name: "LegalScout System Legal Assistant" },
  { id: "b79bce3c-36da-41d3-b773-4bc1dd94e3e8", name: "LegalScout System" },
  { id: "fc747eb2-a566-45b3-b8cb-9db749c65889", name: "LegalScout System Legal Assistant" },
  { id: "3969782a-40f6-41d7-bad8-a21d4b6f686b", name: "LegalScout System Legal Assistant" },
  { id: "cd0b44b7-397e-410d-8835-ce9c3ba584b2", name: "LegalScout" },
  { id: "630083f2-1b17-41ca-a4a4-a29322266885", name: "LegalScout System Legal Assistant" }
];

// Protected assistants (DO NOT DELETE)
const PROTECTED_ASSISTANTS = [
  "f9b97d13-f9c4-40af-a660-62ba5925ff2a", // damon - 9 calls
  "96fc17d7-293e-46e3-b957-185a29daa2b8", // robert - 1 call
  "4831c692-c073-4518-b0c8-27bd34883ba4"  // damonandlaurakost - linked to attorney
];

/**
 * Delete a single assistant
 */
async function deleteAssistant(assistantId, assistantName) {
  try {
    console.log(`🗑️ Deleting: ${assistantId} (${assistantName})`);
    
    const response = await fetch(`${VAPI_API_URL}/assistant/${assistantId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      console.log(`   ✅ Successfully deleted: ${assistantName}`);
      return { success: true, id: assistantId, name: assistantName };
    } else {
      const errorText = await response.text();
      console.error(`   ❌ Failed to delete ${assistantName}: ${response.status} ${errorText}`);
      return { success: false, id: assistantId, name: assistantName, error: `${response.status} ${errorText}` };
    }
  } catch (error) {
    console.error(`   ❌ Error deleting ${assistantName}:`, error.message);
    return { success: false, id: assistantId, name: assistantName, error: error.message };
  }
}

/**
 * Delete all unused assistants
 */
async function deleteUnusedAssistants() {
  console.log('🧹 VAPI ASSISTANT CLEANUP');
  console.log('========================\n');
  
  console.log(`📊 Summary:`);
  console.log(`   🗑️ Assistants to delete: ${ASSISTANTS_TO_DELETE.length}`);
  console.log(`   🛡️ Protected assistants: ${PROTECTED_ASSISTANTS.length}\n`);
  
  console.log('🗑️ DELETING UNUSED ASSISTANTS:\n');
  
  const results = {
    deleted: [],
    failed: [],
    total: ASSISTANTS_TO_DELETE.length
  };
  
  // Delete assistants one by one with a small delay
  for (let i = 0; i < ASSISTANTS_TO_DELETE.length; i++) {
    const assistant = ASSISTANTS_TO_DELETE[i];
    
    // Double-check this assistant is not protected
    if (PROTECTED_ASSISTANTS.includes(assistant.id)) {
      console.log(`   ⚠️ SKIPPING PROTECTED: ${assistant.id} (${assistant.name})`);
      continue;
    }
    
    const result = await deleteAssistant(assistant.id, assistant.name);
    
    if (result.success) {
      results.deleted.push(result);
    } else {
      results.failed.push(result);
    }
    
    // Small delay between deletions to be respectful to the API
    if (i < ASSISTANTS_TO_DELETE.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  // Summary
  console.log('\n📊 CLEANUP RESULTS:');
  console.log(`   ✅ Successfully deleted: ${results.deleted.length} assistants`);
  console.log(`   ❌ Failed to delete: ${results.failed.length} assistants`);
  console.log(`   📈 Success rate: ${Math.round((results.deleted.length / results.total) * 100)}%\n`);
  
  if (results.deleted.length > 0) {
    console.log('✅ SUCCESSFULLY DELETED:');
    results.deleted.forEach((result, index) => {
      console.log(`   ${index + 1}. ${result.name} (${result.id})`);
    });
    console.log('');
  }
  
  if (results.failed.length > 0) {
    console.log('❌ FAILED TO DELETE:');
    results.failed.forEach((result, index) => {
      console.log(`   ${index + 1}. ${result.name} (${result.id})`);
      console.log(`      Error: ${result.error}`);
    });
    console.log('');
  }
  
  console.log('🎉 Cleanup completed!');
  console.log('Your Vapi account now has only the assistants that are actively being used.\n');
  
  return results;
}

// Run the cleanup
if (typeof require !== 'undefined' && require.main === module) {
  deleteUnusedAssistants()
    .then(results => {
      if (results.failed.length === 0) {
        console.log('✅ All unused assistants were successfully deleted!');
        process.exit(0);
      } else {
        console.log(`⚠️ Cleanup completed with ${results.failed.length} failures.`);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Cleanup failed:', error);
      process.exit(1);
    });
}

// Export for browser use
if (typeof window !== 'undefined') {
  window.deleteUnusedAssistants = deleteUnusedAssistants;
  window.deleteAssistant = deleteAssistant;
}
