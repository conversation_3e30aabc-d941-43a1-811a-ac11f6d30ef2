/**
 * MVP Launch Hook
 * 
 * React hook for integrating MVP launch functionality into components
 * without breaking existing functionality.
 */

import { useState, useEffect, useCallback } from 'react';
import { mvpLaunchService } from '../services/mvpLaunchService';

export const useMVPLaunch = (attorney) => {
  const [mvpStatus, setMvpStatus] = useState({
    isReady: false,
    status: 'checking',
    issues: [],
    hasAssistant: false,
    hasSubdomain: false,
    hasWorkingAuth: false
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Check MVP status for current attorney
   */
  const checkMVPStatus = useCallback(async () => {
    if (!attorney?.id) {
      setMvpStatus({
        isReady: false,
        status: 'no-attorney',
        issues: ['No attorney data available'],
        hasAssistant: false,
        hasSubdomain: false,
        hasWorkingAuth: false
      });
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      const status = await mvpLaunchService.getMVPStatus(attorney);
      setMvpStatus(status);
      
      console.log('✅ [useMVPLaunch] MVP status checked:', status);
    } catch (err) {
      console.error('❌ [useMVPLaunch] Error checking MVP status:', err);
      setError(err.message);
      setMvpStatus({
        isReady: false,
        status: 'error',
        issues: [err.message],
        hasAssistant: false,
        hasSubdomain: false,
        hasWorkingAuth: false
      });
    } finally {
      setIsLoading(false);
    }
  }, [attorney?.id]);

  /**
   * Ensure attorney is MVP ready
   */
  const ensureMVPReady = useCallback(async () => {
    if (!attorney?.id) {
      throw new Error('No attorney data available');
    }

    try {
      setIsLoading(true);
      setError(null);
      
      console.log('🚀 [useMVPLaunch] Ensuring MVP ready for attorney:', attorney.id);
      
      const result = await mvpLaunchService.ensureMVPReadyAttorney(attorney);
      
      if (result.success) {
        // Update MVP status
        await checkMVPStatus();
        console.log('✅ [useMVPLaunch] Attorney is now MVP ready');
        return result;
      } else {
        throw new Error(result.error || 'Failed to ensure MVP ready');
      }
    } catch (err) {
      console.error('❌ [useMVPLaunch] Error ensuring MVP ready:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [attorney?.id, checkMVPStatus]);

  /**
   * Quick fix for MVP issues
   */
  const quickFixMVPIssues = useCallback(async () => {
    if (!attorney?.id) {
      throw new Error('No attorney data available');
    }

    try {
      setIsLoading(true);
      setError(null);
      
      console.log('🔧 [useMVPLaunch] Running quick fix for attorney:', attorney.id);
      
      const result = await mvpLaunchService.quickFixMVPIssues(attorney);
      
      if (result.success) {
        // Update MVP status
        await checkMVPStatus();
        console.log('✅ [useMVPLaunch] Quick fix completed');
        return result;
      } else {
        throw new Error(result.message || 'Quick fix failed');
      }
    } catch (err) {
      console.error('❌ [useMVPLaunch] Error in quick fix:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [attorney?.id, checkMVPStatus]);

  /**
   * Get assistant URL for current attorney
   */
  const getAssistantURL = useCallback(() => {
    if (!attorney?.subdomain) {
      return null;
    }
    return `https://${attorney.subdomain}.legalscout.net`;
  }, [attorney?.subdomain]);

  /**
   * Get MVP readiness percentage
   */
  const getMVPReadinessPercentage = useCallback(() => {
    const checks = [
      mvpStatus.hasAssistant,
      mvpStatus.hasSubdomain,
      mvpStatus.hasWorkingAuth
    ];
    
    const passedChecks = checks.filter(Boolean).length;
    return Math.round((passedChecks / checks.length) * 100);
  }, [mvpStatus]);

  // Auto-check MVP status when attorney changes
  useEffect(() => {
    if (attorney?.id) {
      checkMVPStatus();
    }
  }, [attorney?.id, checkMVPStatus]);

  return {
    // Status
    mvpStatus,
    isLoading,
    error,
    
    // Actions
    checkMVPStatus,
    ensureMVPReady,
    quickFixMVPIssues,
    
    // Utilities
    getAssistantURL,
    getMVPReadinessPercentage,
    
    // Computed values
    isReady: mvpStatus.isReady,
    needsSetup: mvpStatus.status === 'needs-setup',
    hasIssues: mvpStatus.issues.length > 0,
    readinessPercentage: getMVPReadinessPercentage()
  };
};
