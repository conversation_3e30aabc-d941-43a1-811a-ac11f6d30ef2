(index):16 🚀 [LegalScout] Initializing environment...
(index):38 ✅ [LegalScout] Environment initialized
(index):127 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
(index):214 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
index-c2747ca6.js:100 [VapiLoader] Starting Vapi SDK loading process
index-c2747ca6.js:100 [VapiLoader] Attempting to import @vapi-ai/web package
index-c2747ca6.js:187 [VapiMcpService] Created clean fetch from iframe
index-c2747ca6.js:187 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
index-c2747ca6.js:48 ❌ [Supabase] Error creating client, falling back to stub: TypeError: Cannot read properties of undefined (reading 'headers')
    at new pG (index-c2747ca6.js:48:69816)
    at bL (index-c2747ca6.js:48:72276)
    at wL (index-c2747ca6.js:48:74304)
    at Ja (index-c2747ca6.js:48:75754)
    at index-c2747ca6.js:48:79242
    at index-c2747ca6.js:48:80903
    at __ (index-c2747ca6.js:40:24270)
    at Id (index-c2747ca6.js:40:42393)
    at index-c2747ca6.js:40:40710
    at D (index-c2747ca6.js:25:1585)
wL @ index-c2747ca6.js:48
Ja @ index-c2747ca6.js:48
(anonymous) @ index-c2747ca6.js:48
(anonymous) @ index-c2747ca6.js:48
__ @ index-c2747ca6.js:40
Id @ index-c2747ca6.js:40
(anonymous) @ index-c2747ca6.js:40
D @ index-c2747ca6.js:25
j @ index-c2747ca6.js:25
simple-preview?subdomain=assistant1test&theme=dark&useEnhancedPreview=true:16 🚀 [LegalScout] Initializing environment...
simple-preview?subdomain=assistant1test&theme=dark&useEnhancedPreview=true:38 ✅ [LegalScout] Environment initialized
simple-preview?subdomain=assistant1test&theme=dark&useEnhancedPreview=true:127 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
simple-preview?subdomain=assistant1test&theme=dark&useEnhancedPreview=true:214 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
index-c2747ca6.js:100 [VapiLoader] Starting Vapi SDK loading process
index-c2747ca6.js:100 [VapiLoader] Attempting to import @vapi-ai/web package
index-c2747ca6.js:187 [VapiMcpService] Created clean fetch from iframe
index-c2747ca6.js:187 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
index-c2747ca6.js:48 ❌ [Supabase] Error creating client, falling back to stub: TypeError: Cannot read properties of undefined (reading 'headers')
    at new pG (index-c2747ca6.js:48:69816)
    at bL (index-c2747ca6.js:48:72276)
    at wL (index-c2747ca6.js:48:74304)
    at Ja (index-c2747ca6.js:48:75754)
    at index-c2747ca6.js:48:79242
    at index-c2747ca6.js:48:80903
    at __ (index-c2747ca6.js:40:24270)
    at Id (index-c2747ca6.js:40:42393)
    at index-c2747ca6.js:40:40710
    at D (index-c2747ca6.js:25:1585)
wL @ index-c2747ca6.js:48
Ja @ index-c2747ca6.js:48
(anonymous) @ index-c2747ca6.js:48
(anonymous) @ index-c2747ca6.js:48
__ @ index-c2747ca6.js:40
Id @ index-c2747ca6.js:40
(anonymous) @ index-c2747ca6.js:40
D @ index-c2747ca6.js:25
j @ index-c2747ca6.js:25
