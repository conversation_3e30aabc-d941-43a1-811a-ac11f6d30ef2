/**
 * Assistant Configuration Diagnostics Script
 * 
 * Run this in the browser console to diagnose assistant configuration issues
 */

window.assistantConfigDiagnostics = {
  
  async runDiagnostics() {
    console.log('🔍 [Assistant Config Diagnostics] Starting comprehensive diagnostics...');
    
    const results = {
      timestamp: new Date().toISOString(),
      issues: [],
      fixes: [],
      recommendations: []
    };
    
    try {
      // 1. Check AssistantUIConfigService methods
      console.log('📋 [Diagnostics] Checking AssistantUIConfigService methods...');
      await this.checkAssistantUIConfigService(results);
      
      // 2. Check Vapi MCP Service connection
      console.log('🔌 [Diagnostics] Checking Vapi MCP Service connection...');
      await this.checkVapiMcpConnection(results);
      
      // 3. Check Assistant ID consistency
      console.log('🆔 [Diagnostics] Checking Assistant ID consistency...');
      await this.checkAssistantIdConsistency(results);
      
      // 4. Check API endpoints
      console.log('🌐 [Diagnostics] Checking API endpoints...');
      await this.checkApiEndpoints(results);
      
      // 5. Generate report
      this.generateReport(results);
      
      return results;
      
    } catch (error) {
      console.error('❌ [Diagnostics] Error running diagnostics:', error);
      results.issues.push({
        type: 'diagnostic_error',
        message: error.message,
        severity: 'high'
      });
      return results;
    }
  },
  
  async checkAssistantUIConfigService(results) {
    try {
      // Try to import the service
      const { assistantUIConfigService } = await import('/src/services/assistantUIConfigService.js');
      
      // Check if required methods exist
      const requiredMethods = [
        'saveAssistantConfig',
        'updateAssistantConfig',
        'getAssistantConfig',
        'createDefaultConfig'
      ];
      
      const missingMethods = [];
      for (const method of requiredMethods) {
        if (typeof assistantUIConfigService[method] !== 'function') {
          missingMethods.push(method);
        }
      }
      
      if (missingMethods.length > 0) {
        results.issues.push({
          type: 'missing_methods',
          service: 'assistantUIConfigService',
          methods: missingMethods,
          severity: 'high'
        });
      } else {
        results.fixes.push({
          type: 'service_methods_ok',
          service: 'assistantUIConfigService',
          message: 'All required methods are available'
        });
      }
      
    } catch (error) {
      results.issues.push({
        type: 'service_import_error',
        service: 'assistantUIConfigService',
        message: error.message,
        severity: 'high'
      });
    }
  },
  
  async checkVapiMcpConnection(results) {
    try {
      // Try to import the Vapi MCP service
      const { vapiMcpService } = await import('/src/services/vapiMcpService.js');
      
      // Check connection status
      const isConnected = await vapiMcpService.ensureConnection();
      
      if (!isConnected) {
        results.issues.push({
          type: 'vapi_connection_failed',
          message: 'Vapi MCP Service connection failed',
          severity: 'high'
        });
        
        results.recommendations.push({
          type: 'connection_fix',
          message: 'Check Vapi API keys and MCP server configuration'
        });
      } else {
        results.fixes.push({
          type: 'vapi_connection_ok',
          message: 'Vapi MCP Service connection successful'
        });
      }
      
    } catch (error) {
      results.issues.push({
        type: 'vapi_service_error',
        message: error.message,
        severity: 'high'
      });
    }
  },
  
  async checkAssistantIdConsistency(results) {
    try {
      // Check if AssistantAwareContext is available
      const attorney = JSON.parse(localStorage.getItem('attorney') || '{}');
      
      if (!attorney.id) {
        results.issues.push({
          type: 'no_attorney_data',
          message: 'No attorney data found in localStorage',
          severity: 'medium'
        });
        return;
      }
      
      // Check for assistant ID consistency
      const assistantIds = {
        vapi_assistant_id: attorney.vapi_assistant_id,
        current_assistant_id: attorney.current_assistant_id
      };
      
      console.log('🔍 [Diagnostics] Assistant IDs found:', assistantIds);
      
      if (!assistantIds.vapi_assistant_id && !assistantIds.current_assistant_id) {
        results.issues.push({
          type: 'no_assistant_ids',
          message: 'No assistant IDs found for attorney',
          severity: 'high'
        });
      } else if (assistantIds.vapi_assistant_id !== assistantIds.current_assistant_id) {
        results.issues.push({
          type: 'inconsistent_assistant_ids',
          message: 'Vapi assistant ID and current assistant ID are different',
          data: assistantIds,
          severity: 'medium'
        });
      } else {
        results.fixes.push({
          type: 'assistant_ids_consistent',
          message: 'Assistant IDs are consistent',
          data: assistantIds
        });
      }
      
    } catch (error) {
      results.issues.push({
        type: 'assistant_id_check_error',
        message: error.message,
        severity: 'medium'
      });
    }
  },
  
  async checkApiEndpoints(results) {
    const endpoints = [
      '/api/sync-tools/manage-auth-state',
      '/api/vapi-mcp-server'
    ];
    
    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ test: true })
        });
        
        if (response.status === 500) {
          results.issues.push({
            type: 'api_endpoint_error',
            endpoint,
            status: response.status,
            severity: 'high'
          });
        } else {
          results.fixes.push({
            type: 'api_endpoint_ok',
            endpoint,
            status: response.status
          });
        }
        
      } catch (error) {
        results.issues.push({
          type: 'api_endpoint_unreachable',
          endpoint,
          message: error.message,
          severity: 'high'
        });
      }
    }
  },
  
  generateReport(results) {
    console.log('\n📊 [Assistant Config Diagnostics] REPORT');
    console.log('='.repeat(50));
    
    console.log(`\n🕒 Timestamp: ${results.timestamp}`);
    
    if (results.issues.length > 0) {
      console.log(`\n❌ Issues Found (${results.issues.length}):`);
      results.issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. [${issue.severity.toUpperCase()}] ${issue.type}: ${issue.message}`);
        if (issue.data) {
          console.log(`     Data:`, issue.data);
        }
      });
    }
    
    if (results.fixes.length > 0) {
      console.log(`\n✅ Fixes Applied (${results.fixes.length}):`);
      results.fixes.forEach((fix, index) => {
        console.log(`  ${index + 1}. ${fix.type}: ${fix.message}`);
      });
    }
    
    if (results.recommendations.length > 0) {
      console.log(`\n💡 Recommendations (${results.recommendations.length}):`);
      results.recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec.type}: ${rec.message}`);
      });
    }
    
    console.log('\n' + '='.repeat(50));
    
    // Summary
    const highIssues = results.issues.filter(i => i.severity === 'high').length;
    const mediumIssues = results.issues.filter(i => i.severity === 'medium').length;
    
    if (highIssues > 0) {
      console.log(`🚨 CRITICAL: ${highIssues} high-severity issues need immediate attention`);
    }
    if (mediumIssues > 0) {
      console.log(`⚠️  WARNING: ${mediumIssues} medium-severity issues should be addressed`);
    }
    if (results.issues.length === 0) {
      console.log('🎉 SUCCESS: No issues detected!');
    }
  }
};

// Auto-run diagnostics
console.log('🔧 [Assistant Config Diagnostics] Diagnostic tool loaded. Run window.assistantConfigDiagnostics.runDiagnostics() to start.');
