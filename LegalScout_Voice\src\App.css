/* Base styles */
:root {
  /* Light theme (default) */
  --color-bg1: #ffffff;
  --color-bg2: #f5f5f5;
  --color1: 41, 121, 255;
  --color2: 15, 55, 75;
  --color3: 15, 76, 125;
  --color4: 242, 101, 34;
  --color5: 242, 153, 74;
  --color-interactive: 75, 116, 170;
  --circle-size: 10%;
  --blending: soft-light;

  /* Theme colors */
  --bg-primary: #121212;
  --bg-secondary: #1e1e1e;
  --text-primary: #f5f5f5;
  --text-secondary: #b0b0b0;
  --accent-primary: #3b82f6;
  --accent-secondary: #8b5cf6;
  --gradient-start: #2563eb;
  --gradient-end: #7c3aed;
  --card-bg: #1f2937;
  --card-hover: #374151;
  --border-color: rgba(255, 255, 255, 0.03);
  --nav-bg: rgba(18, 18, 18, 0.95);
  --nav-text: #f5f5f5;
  --nav-hover: rgba(30, 30, 30, 0.7);
  --header-glow: rgba(59, 130, 246, 0.15);
  --primary-color: #4B74AA;
  --secondary-color: #607D8B;
  --accent-color: #8A6E57;
  --error-color: #ff5252;
  --success-color: #4caf50;
  --warning-color: #fb8c00;
  --info-color: #2196f3;
  --border-radius: 8px;
  --transition-speed: 0.3s;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.12), 0 1px 3px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --max-width: 1200px;
  --nav-height: 60px;
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Light theme */
[data-theme="light"] {
  --color-bg1: #ffffff;
  --color-bg2: #f5f5f5;
  --blending: soft-light;  /* Softer blend mode for light theme */
  --bg-primary: #f8f9fa;
  --bg-secondary: #ffffff;
  --text-primary: #212121;
  --text-secondary: #616161;
  --accent-primary: #4B74AA;
  --accent-secondary: #607D8B;
  --gradient-start: #4B74AA;
  --gradient-end: #607D8B;
  --card-bg: #ffffff;
  --card-hover: #f3f4f6;
  --border-color: rgba(0, 0, 0, 0.1);
  --nav-bg: rgba(99, 76, 56, 0.95);
  --nav-text: #ffffff;
  --nav-hover: rgba(240, 240, 240, 0.8);
  --header-glow: rgba(99, 76, 56, 0.2);
  --start-text: rgba(75, 116, 170, 0.8);  /* Grey-blue color for start consultation text */
  --primary-color: #4B74AA;
  --secondary-color: #607D8B;
  --accent-color: #634C38;
  --error-color: #f44336;
}

/* Dark theme */
[data-theme="dark"] {
  --color-bg1: #000000;
  --color-bg2: #00000063;
  --blending: hard-light;
  --bg-primary: #121212;
  --bg-secondary: #1e1e1e;
  --text-primary: rgba(255, 255, 255, 0.95);
  --text-secondary: rgba(255, 255, 255, 0.7);
  --accent-primary: #64B5F6;
  --accent-secondary: #8b5cf6;
  --gradient-start: #64B5F6;
  --gradient-end: #8b5cf6;
  --card-bg: rgba(18, 18, 20, 0.5);
  --card-hover: rgba(100, 181, 246, 0.1);
  --border-color: rgba(100, 181, 246, 0.2);
  --nav-bg: rgba(18, 18, 18, 0.95);
  --nav-text: #ffffff;
  --nav-hover: rgba(100, 181, 246, 0.1);
  --header-glow: rgba(100, 181, 246, 0.3);
  --start-text: rgba(100, 181, 246, 0.8);
  --primary-color: #64B5F6;
  --secondary-color: #8b5cf6;
  --accent-color: #64B5F6;
  --error-color: #ef4444;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --info-color: #2196f3;
  --transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  max-width: none;
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: transparent rgba(59, 130, 246, 0.9); /* Firefox: thumb and track color */
}

/* Global scrollbar styling */
::-webkit-scrollbar {
  width: 4px; /* Very thin scrollbar */
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: transparent; /* Completely transparent thumb */
  border: 1px solid rgba(59, 130, 246, 0.9); /* Thin bright blue border */
  border-radius: 10px;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.9); /* Bright glowing effect */
}

::-webkit-scrollbar-thumb:hover {
  background-color: transparent; /* Keep transparent on hover */
  border: 1px solid rgba(59, 130, 246, 1); /* Brighter border on hover */
  box-shadow: 0 0 12px rgba(59, 130, 246, 1); /* Enhanced glow on hover */
}

::-webkit-scrollbar-track {
  background-color: transparent; /* Transparent track */
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: transparent; /* Changed to transparent to allow animated background to show */
  color: var(--text-primary);
  overflow-x: hidden;
  width: 100vw;
  max-width: 100vw;
  margin: 0;
  padding: 0;
  transition: color 0.3s ease;
}

/* App wrapper - main container */
.app-wrapper {
  position: relative;
  min-height: 100vh;
  width: 100vw;
  overflow: hidden;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  z-index: 2;
  background-color: transparent; /* Ensure background is transparent */
}

/* Previous styling - keeping for compatibility */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  width: 100%;
  margin: 0;
  position: relative;
  background-color: #000;
}

.app-container.map-active {
  background-color: #1a1a1a;
  color: white;
}

/* Header styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background: linear-gradient(to right,
    var(--nav-bg) 0%,
    var(--nav-bg) 100%
  );
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1),
              0 0 50px var(--header-glow);
  position: relative;
  z-index: 1000; /* High z-index but lower than active menu */
  width: 100vw;
  box-sizing: border-box;
  height: 60px;
  border-bottom: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

/* Sign In Button Container - positioned to avoid navbar overlap */
.sign-in-button-container {
  position: absolute;
  top: 50%;
  right: 80px; /* Reduced from 120px to give more space */
  transform: translateY(-50%);
  z-index: 1001;
  display: flex;
  align-items: center;
}

/* Responsive adjustments for sign-in button */
@media (max-width: 1024px) {
  .sign-in-button-container {
    right: 60px; /* Move closer to edge on smaller screens */
  }
}

@media (max-width: 768px) {
  .sign-in-button-container {
    position: static; /* Remove absolute positioning on mobile */
    transform: none;
    margin-left: auto;
    margin-right: 10px;
  }
}

/* Fix the light background box showing in the header in dark mode */
[data-theme="dark"] .header {
  background: var(--nav-bg) !important;
  background-image: none !important;
}

[data-theme="dark"] .header > nav {
  background-color: transparent !important;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right,
    transparent,
    rgba(59, 130, 246, 0.3),
    transparent
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.header::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    rgba(75, 116, 170, 0),
    rgba(75, 116, 170, 0.5),
    rgba(75, 116, 170, 0.8),
    rgba(75, 116, 170, 0.5),
    rgba(75, 116, 170, 0)
  );
  opacity: 0.5;
  animation: glowPulse 4s ease-in-out infinite;
}

[data-theme="light"] .header::after {
  background: linear-gradient(90deg,
    rgba(99, 76, 56, 0),
    rgba(99, 76, 56, 0.5),
    rgba(99, 76, 56, 0.8),
    rgba(99, 76, 56, 0.5),
    rgba(99, 76, 56, 0)
  );
  opacity: 0.7;
}

@keyframes glowPulse {
  0% {
    opacity: 0.2;
    transform: scaleX(0.95);
  }
  50% {
    opacity: 0.5;
    transform: scaleX(1.05);
  }
  100% {
    opacity: 0.2;
    transform: scaleX(0.95);
  }
}

.header:hover::before {
  opacity: 1;
}

.full-width {
  width: 100vw;
  position: relative;
  left: 0;
  right: 0;
  margin: 0;
}

/* Main Content Layer */
.main-content-layer {
  position: relative;
  z-index: 5; /* Lower z-index to allow background to show through */
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start; /* Changed from center to flex-start to allow content to start from the top */
  width: 100%;
  padding: 20px;
  margin-top: 20px;
  background-color: transparent; /* Ensure background is transparent */
  overflow-y: auto; /* Allow vertical scrolling */
  max-height: calc(100vh - 80px); /* Set max height to viewport minus header */
}

/* Call Card Container - Centered on page */
.call-card-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: flex-start; /* Changed from center to flex-start to allow vertical expansion */
  width: 100%; /* Changed from 90% to 100% for full width */
  max-width: none; /* Remove max-width constraint */
  min-height: 500px; /* Minimum height instead of fixed height */
  max-height: calc(100vh - 120px); /* Max height to allow scrolling */
  margin: 0 auto 40px auto; /* Add bottom margin to allow content below */
  z-index: 5; /* Lower z-index to allow background to show through */
  background-color: transparent; /* Ensure background is transparent */
  overflow: visible; /* Allow content to overflow */
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

/* Force visibility for active call card container */
.call-card-container.active {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 1000 !important;
  position: relative !important;
}

/* Persistent call container styles */
#persistent-call-container {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 10000 !important;
  position: fixed !important;
  bottom: 20px !important;
  right: 20px !important;
  width: 400px !important;
  height: 200px !important;
  background-color: rgba(0, 0, 0, 0.8) !important;
  border-radius: 10px !important;
  padding: 10px !important;
  color: white !important;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  transition: all 0.3s ease !important;
  animation: fadeIn 0.5s ease-out !important;
}

/* Call Card - White container with rounded corners */
.call-card {
  position: relative;
  background-color: transparent; /* Change to transparent */
  border-radius: 20px;
  width: 100%; /* Full width of container */
  max-width: none; /* Remove max-width constraint */
  min-height: 500px; /* Minimum height */
  max-height: calc(100vh - 120px); /* Max height to allow scrolling */
  box-shadow: none; /* Remove shadow */
  overflow: visible; /* Allow content to overflow */
  display: flex;
  flex-direction: column;
  z-index: 5; /* Lower z-index to allow background to show through */
}

/* Start Button Container */
.start-button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  padding: 20px;
  padding-top: 150px; /* Add padding to move the button down */
  background-color: transparent; /* Ensure background is transparent */
  z-index: 5; /* Lower z-index to allow background to show through */
  position: relative; /* Ensure z-index works */
}

[data-theme="dark"] .start-button-container {
  color: var(--text-secondary);  /* Keep the existing color for dark mode */
}

/* Test Subdomains Container */
.test-subdomains-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 1000; /* Very high z-index to stay on top */
  background-color: rgba(0, 0, 0, 0.7);
  padding: 5px 10px;
}

/* Subdomain Toggle Button */
.subdomain-toggle-button {
  position: fixed;
  bottom: 10px;
  right: 10px;
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  z-index: 1000;
  transition: all 0.2s ease;
}

.subdomain-toggle-button:hover {
  background-color: rgba(0, 0, 0, 0.85);
  transform: translateY(-1px);
}

[data-theme="light"] .subdomain-toggle-button {
  background-color: rgba(75, 116, 170, 0.1);
  color: rgba(75, 116, 170, 0.8);
  border-color: rgba(75, 116, 170, 0.2);
}

[data-theme="light"] .subdomain-toggle-button:hover {
  background-color: rgba(75, 116, 170, 0.15);
}

/* Loading indicator */
.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  width: 100%;
  font-size: 16px;
  color: white;
}

/* Attorney info and call summary containers */
.attorney-info-container,
.call-summary-container {
  width: 100%;
  max-width: 1000px;
  background-color: #f7f9fc;
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  color: #333;
  margin: 20px auto;
}

.back-button {
  margin-top: 20px;
  padding: 10px 20px;
  background-color: #4B74AA;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.back-button:hover {
  background-color: #3A5D88;
}

/* Navigation Button Styles */
.nav-button {
  background: none;
  border: none;
  color: var(--nav-text);
  font-size: 14px;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  font-family: inherit;
  position: relative;
  overflow: hidden;
}

.nav-button:hover {
  background-color: var(--nav-hover);
  transform: translateY(-1px);
}

.nav-button .nav-icon {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.nav-button:hover .nav-icon {
  transform: scale(1.1);
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.nav-button:hover::before {
  left: 100%;
}

/* Existing styles below */
.logo-container {
  display: flex;
  align-items: center;
  margin-right: 1rem;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.logo-container a {
  display: flex;
  align-items: center;
  text-decoration: none;
  cursor: pointer;
}

.logo-container::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo-container:hover::before {
  transform: translate(-50%, -50%) scale(1.5);
}

.logo-container:hover {
  background-color: rgba(255, 255, 255, 0.05);
  transform: translateY(-1px);
}

.logo {
  width: auto;
  height: 32px;
  display: block;
  object-fit: contain;
  margin-right: 0.5rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  transition: transform 0.3s ease;
}

.logo-container:hover .logo {
  transform: scale(1.05);
}

/* Add white background specifically for the navigation logo */
img.logo[src*="nav_logo.webp"] {
  background-color: transparent !important;
  padding: 0 !important;
  box-shadow: none !important;
  filter: none !important;
  /* Ensure the logo appears as intended */
  opacity: 1 !important;
  visibility: visible !important;
}

.logo-text {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--text-primary);
}

.logo-legal {
  color: var(--text-primary);
}

.logo-scout {
  color: #4B74AA;
}

.logo-ai {
  color: #666;
  font-size: 0.9em;
}

/* Navigation styles */
.nav-container {
  position: relative;
  display: flex;
  align-items: center;
  flex-grow: 1;
  height: 100%;
  z-index: 5;
}

/* Ensure nav container has proper stacking context when menu is active */
/* Using :has() for modern browsers */
.nav-container:has(.hamburger-menu.active) {
  z-index: 10000; /* Highest z-index when menu is active */
}

/* Alternative for browsers that don't support :has() */
.nav-container.menu-active {
  z-index: 10000; /* Highest z-index when menu is active */
}

.main-nav {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.main-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
  justify-content: center;
}

.main-nav li {
  position: relative;
}

.main-nav a {
  position: relative;
  z-index: 10;
  color: var(--nav-text);
  text-decoration: none;
  font-size: 0.92rem;
  font-weight: 400;
  padding: 0.5rem 0.2rem;
  margin: 0 0.5rem;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  overflow: visible;
  letter-spacing: 0.02em;
  opacity: 0.85;
  background: none;
  border: none;
}

/* Tech-forward, sleek navigation links */
.main-nav a {
  position: relative;
  overflow: visible;
}

/* Sleek underline effect */
.main-nav a::before {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
  transition: width 0.4s ease;
  z-index: 1;
}

/* Special orange underline for Agent tab */
.main-nav a[data-text="Agent"]::before {
  background: linear-gradient(90deg, transparent, #D85722, transparent);
}

.main-nav a:hover::before,
.main-nav a.active::before {
  width: 100%;
}

/* Text glow effect */
.main-nav a::after {
  content: attr(data-text);
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  opacity: 0;
  color: var(--accent-primary);
  z-index: -1;
  filter: blur(8px);
  transform: scale(1.1);
  pointer-events: none;
  transition: opacity 0.3s ease, filter 0.3s ease;
  padding: 0.5rem 0.2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Special orange glow for Agent tab */
.main-nav a[data-text="Agent"]::after {
  color: #D85722;
}

.main-nav a:hover::after {
  opacity: 0.4;
  filter: blur(12px);
}

.main-nav a.active::after {
  opacity: 0.5;
  filter: blur(10px);
}

.main-nav a:hover,
.main-nav a.active {
  color: var(--accent-primary);
  opacity: 1;
  transform: translateY(-1px);
}

/* Special orange color for Agent tab when hovered/active */
.main-nav a[data-text="Agent"]:hover,
.main-nav a[data-text="Agent"].active {
  color: #D85722;
}

/* Playful entrance animation for nav items */
@keyframes nav-item-enter {
  0% { opacity: 0; transform: translateY(-5px); }
  100% { opacity: 1; transform: translateY(0); }
}

.main-nav li:nth-child(1) a {
  animation: nav-item-enter 0.3s ease-out 0.1s backwards;
}

.main-nav li:nth-child(2) a {
  animation: nav-item-enter 0.3s ease-out 0.2s backwards;
}

.main-nav li:nth-child(3) a {
  animation: nav-item-enter 0.3s ease-out 0.3s backwards;
}

/* Specific override for dark mode */
[data-theme="dark"] .main-nav a {
  opacity: 0.8;
}

[data-theme="dark"] .main-nav a:hover,
[data-theme="dark"] .main-nav a.active {
  color: var(--accent-primary);
  opacity: 1;
}

/* Special orange color for Agent tab in dark mode */
[data-theme="dark"] .main-nav a[data-text="Agent"]:hover,
[data-theme="dark"] .main-nav a[data-text="Agent"].active {
  color: #D85722;
  opacity: 1;
}

[data-theme="dark"] .main-nav a::before {
  background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
}

[data-theme="dark"] .main-nav a:hover::before,
[data-theme="dark"] .main-nav a.active::before {
  width: 100%;
}

[data-theme="dark"] .main-nav a::after {
  color: var(--accent-primary);
}

[data-theme="dark"] .main-nav a.active::after {
  opacity: 0.6;
  filter: blur(12px);
}

/* Specific override for light mode */
[data-theme="light"] .main-nav a:hover,
[data-theme="light"] .main-nav a.active {
  color: var(--accent-primary);
}

[data-theme="light"] .main-nav a::before {
  background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
}

[data-theme="light"] .main-nav a::after {
  color: var(--accent-primary);
}

[data-theme="light"] .main-nav a.active::after {
  opacity: 0.4;
  filter: blur(10px);
}

.nav-icon {
  font-size: 1rem;
  transition: all 0.3s ease;
  opacity: 0.85;
  position: relative;
  margin-right: 0.3rem;
}

.main-nav a:hover .nav-icon {
  transform: translateY(-2px) scale(1.1);
  opacity: 1;
  color: var(--accent-primary);
  filter: drop-shadow(0 0 3px var(--accent-primary));
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0% { transform: translateY(-2px); }
  50% { transform: translateY(0px); }
  100% { transform: translateY(-2px); }
}

/* Position theme toggle on the right side */
.theme-toggle {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

/* Base hamburger menu style - hidden by default */
.hamburger-menu {
  display: none;  /* Hide by default on all screen sizes */
}

/* Mobile menu styling for both modes - simplified and standardized */
@media (max-width: 768px) {
  /* Tech-forward hamburger menu */
  .hamburger-menu {
    display: block;  /* Only show on mobile */
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    margin-right: 1rem;
    z-index: 9999;
    width: 30px;
    height: 30px;
    position: relative;
  }

  /* Ensure hamburger menu is at the top layer when active */
  .hamburger-menu.active {
    z-index: 10000; /* Higher than any other element */
    position: relative;
  }

  /* Lines for the hamburger icon */
  .hamburger-menu span {
    display: block;
    position: absolute;
    width: 22px;
    height: 1px;
    background-color: var(--accent-primary);
    transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1), opacity 0.3s ease, width 0.3s ease, box-shadow 0.3s ease;
    left: 4px;
    border-radius: 0;
    box-shadow: 0 0 0px var(--accent-primary);
    opacity: 0.8;
  }

  /* Position the three lines */
  .hamburger-menu span:nth-child(1) {
    top: 10px;
    width: 18px;
  }

  .hamburger-menu span:nth-child(2) {
    top: 15px;
    width: 8px;
    left: 14px;
  }

  .hamburger-menu span:nth-child(3) {
    top: 20px;
    width: 12px;
    left: 10px;
  }

  /* Hover effect for hamburger */
  .hamburger-menu:hover span {
    opacity: 1;
    box-shadow: 0 0 5px var(--accent-primary);
  }

  .hamburger-menu:hover span:nth-child(2) {
    width: 8px;
    left: 14px;
  }

  .hamburger-menu:hover span:nth-child(3) {
    width: 12px;
    left: 10px;
  }

  /* X icon when menu is active - SHARED BASE STYLES */
  .hamburger-menu.active span {
    /* Reset any conflicting transforms first */
    transform: none;
    width: 18px;
    background-color: var(--accent-primary);
    box-shadow: 0 0 8px var(--accent-primary);
    opacity: 1;
    left: 6px;
  }

  /* LIGHT MODE X STYLES */
  [data-theme="light"] .hamburger-menu.active span:nth-child(1) {
    transform: rotate(45deg);
    top: 15px;
  }

  [data-theme="light"] .hamburger-menu.active span:nth-child(2) {
    opacity: 0;
    transform: translateX(-30px);
    width: 0;
  }

  [data-theme="light"] .hamburger-menu.active span:nth-child(3) {
    transform: rotate(-45deg);
    top: 15px;
  }

  /* DARK MODE X STYLES - simplified implementation */
  [data-theme="dark"] .hamburger-menu.active span {
    /* First hide all spans to prevent any overlap */
    opacity: 0;
    /* Make sure we don't have any transforms that might cause artifacts */
    transform: none;
    /* Reset any position values that might cause artifacts */
    left: 4px;
    background-color: var(--accent-primary);
    width: 0;
    visibility: hidden;
  }

  /* Then show just two spans for the X */
  [data-theme="dark"] .hamburger-menu.active span:nth-child(1) {
    opacity: 1;
    top: 15px;
    transform: rotate(45deg);
    width: 18px;
    visibility: visible;
  }

  [data-theme="dark"] .hamburger-menu.active span:nth-child(2) {
    opacity: 0;
    transform: translateX(-30px);
    width: 0;
    visibility: hidden;
  }

  [data-theme="dark"] .hamburger-menu.active span:nth-child(3) {
    opacity: 1;
    top: 15px;
    transform: rotate(-45deg);
    width: 18px;
    visibility: visible;
  }

  /* Ensure the same styling in both themes */
  [data-theme="dark"] .hamburger-menu span {
    background-color: var(--accent-primary);
    opacity: 0.85;
  }

  [data-theme="dark"] .hamburger-menu:hover span {
    opacity: 1;
    box-shadow: 0 0 8px var(--accent-primary);
  }

  [data-theme="dark"] .hamburger-menu.active span {
    box-shadow: 0 0 10px var(--accent-primary);
  }

  [data-theme="light"] .hamburger-menu span {
    background-color: var(--accent-primary); /* Use accent color for consistency */
  }

  /* Layout fixes for mobile */
  .call-card-container {
    width: 95%;
  }

  .call-card {
    width: 100%;
    min-height: 500px;
  }

  .attorney-info-container,
  .call-summary-container {
    max-width: 95%;
    padding: 20px;
  }

  .header {
    padding: 0 15px;
  }

  .nav-container {
    justify-content: flex-start;
  }

  .theme-toggle {
    position: static;
    transform: none;
  }

  /* Simple mobile menu styling - remove all !important flags */
  .main-nav {
    position: fixed;
    top: 60px;
    left: 0;
    width: 100%;
    height: calc(100vh - 60px);
    transform: translateX(-100%);
    background-color: var(--nav-bg);
    transition: transform 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 101;
  }

  .main-nav.active {
    transform: translateX(0);
    z-index: 9990; /* Very high z-index when active */
  }

  .main-nav ul {
    flex-direction: column;
    padding: 2rem;
    gap: 1.5rem;
    width: 100%;
    align-items: flex-start;
  }

  .main-nav li {
    width: 100%;
  }

  .main-nav a {
    font-size: 1.1rem;
    padding: 0.8rem 0.5rem;
    margin: 0.3rem 0;
    width: 100%;
    background-color: transparent;
    color: var(--nav-text);
    justify-content: flex-start;
    letter-spacing: 0.02em;
  }

  .main-nav a:hover {
    color: var(--accent-primary);
  }

  .main-nav a:hover::before,
  .main-nav a.active::before {
    width: 50%;
    left: 0;
  }

  .main-nav a::after {
    justify-content: flex-start;
    padding: 0.8rem 0.5rem;
  }

  .main-nav a:hover .nav-icon {
    transform: translateX(2px) scale(1.1);
    opacity: 1;
    color: var(--accent-primary);
    filter: drop-shadow(0 0 3px var(--accent-primary));
  }

  /* Simple overlay styling */
  .nav-overlay {
    display: none;
    position: fixed;
    top: 60px;
    left: 0;
    width: 100%;
    height: calc(100vh - 60px);
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 100;
    opacity: 0;
    transition: opacity 0.3s ease;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
  }

  .nav-overlay.active {
    display: block;
    opacity: 1;
    z-index: 9980; /* High z-index but below the menu */
  }
}

/* Fix dark theme for mobile menu - simplified approach */
[data-theme="dark"] .main-nav {
  background-color: transparent;
}

/* Clean up all the complex selectors that are interfering with normal behavior */
@media (max-width: 768px) {
  /* Disable bone-cursor completely in mobile mode */
  .bone-cursor {
    cursor: auto !important;
  }

  /* Remove the conflicting selectors completely */
  [data-theme="dark"] .bone-cursor .main-nav:not(.active),
  body:has([data-theme="dark"]) .bone-cursor .main-nav:not(.active),
  [data-theme="dark"] .bone-cursor + .main-nav,
  body:has([data-theme="dark"]) .bone-cursor + .main-nav,
  .nav-container .main-nav.active + .main-nav {
    display: none;
  }
}

/* Gradient Background */
.gradient-bg {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  background: linear-gradient(40deg, var(--color-bg1), var(--color-bg2));
  z-index: 1; /* Increased z-index but still behind content */
  opacity: 0.8; /* Increased opacity to make it more visible */
  pointer-events: none; /* Ensure it doesn't interfere with interactions */
}

.gradients-container {
  filter: blur(40px);
  width: 100%;
  height: 100%;
  opacity: 1; /* Full opacity for container */
}

[data-theme="light"] .gradients-container {
  filter: blur(20px);  /* Even less blur in light mode */
  opacity: 0.8; /* Slightly reduced opacity in light mode */
}

.bubble {
  position: absolute;
  mix-blend-mode: var(--blending);
  border-radius: 50%;
  opacity: 0.7; /* Increased opacity to make bubbles more visible */
  animation: moveInCircle 60s infinite linear;
  z-index: 1; /* Ensure bubbles are visible but behind content */
}

[data-theme="light"] .bubble {
  opacity: 0.6; /* Slightly reduced opacity in light mode but still visible */
}

@keyframes moveInCircle {
  0% {
    transform: rotate(0deg) translateX(100px) rotate(0deg) scale(1);
    opacity: 0.7;
  }
  25% {
    transform: rotate(90deg) translateX(100px) rotate(-90deg) scale(1.1);
    opacity: 0.9;
  }
  50% {
    transform: rotate(180deg) translateX(100px) rotate(-180deg) scale(1);
    opacity: 0.7;
  }
  75% {
    transform: rotate(270deg) translateX(100px) rotate(-270deg) scale(0.9);
    opacity: 0.5;
  }
  100% {
    transform: rotate(360deg) translateX(100px) rotate(-360deg) scale(1);
    opacity: 0.7;
  }
}

/* Add transition for smooth theme switching */
.app-wrapper,
.header,
.main-nav a,
.message-content,
.call-card {
  transition: all 0.3s ease;
}

.message-content {
  background-color: var(--nav-hover);
  color: var(--text-primary);
}

.message-timestamp {
  color: var(--text-secondary);
}

/* Button styles */
.button-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.button-label {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 1.1rem;
  font-weight: 400;  /* Lighter weight for modern look */
  letter-spacing: 0.3px;  /* Slight letter spacing for better readability */
  text-align: center;
  margin-top: 1rem;
  color: var(--text-secondary);
  text-transform: none;  /* Ensure no unwanted text transformation */
}

[data-theme="light"] .button-label {
  color: rgba(75, 116, 170, 0.8) !important;  /* Grey-blue color for light mode */
}

[data-theme="dark"] .button-label {
  color: var(--text-secondary);
}

/* About Page Styles */
.about-page-container {
  min-height: 100vh;
  padding: 2rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.about-page-container section {
  margin-bottom: 4rem;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
}

.about-page-container section:nth-child(2) {
  animation-delay: 0.2s;
}

.about-page-container section:nth-child(3) {
  animation-delay: 0.4s;
}

.about-page-container section:nth-child(4) {
  animation-delay: 0.6s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin: 4rem auto;
  padding: 2rem;
  max-width: 1200px;
}

.feature-card {
  background: var(--card-bg);
  border-radius: 1rem;
  padding: 2rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid var(--border-color);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  background: var(--card-hover);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--accent-primary);
}

.steps-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 4rem auto;
  padding: 2rem;
  max-width: 1200px;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  border-radius: 1rem;
}

.step-card {
  background: var(--card-bg);
  border-radius: 1rem;
  padding: 2rem;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: transform 0.3s ease;
}

.step-card:hover {
  transform: translateY(-5px);
}

.step-number {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 3rem;
  height: 3rem;
  background: var(--accent-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 4rem auto;
  padding: 2rem;
  max-width: 1200px;
}

.faq-card {
  background: var(--card-bg);
  border-radius: 1rem;
  padding: 2rem;
  transition: transform 0.3s ease;
  border: 1px solid var(--border-color);
}

.faq-card:hover {
  transform: translateY(-5px);
  background: var(--card-hover);
}

.cta-section {
  text-align: center;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  border-radius: 1rem;
  margin: 4rem auto;
  max-width: 1200px;
}

.cta-button {
  display: inline-block;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--accent-primary);
  background: white;
  border-radius: 0.5rem;
  text-decoration: none;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  margin-top: 1.5rem;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  background: var(--bg-primary);
}

/* Light mode */
.light-mode {
  --bg-primary: theme('colors.white');
  --bg-secondary: theme('colors.gray.50');
  --text-primary: theme('colors.gray.900');
  --text-secondary: theme('colors.gray.600');
  --accent-primary: theme('colors.blue.600');
  --accent-secondary: theme('colors.purple.600');
  --gradient-start: theme('colors.blue.500');
  --gradient-end: theme('colors.purple.500');
  --card-bg: theme('colors.white');
  --card-hover: theme('colors.gray.50');
}

.step-content {
  transform-style: preserve-3d;
}

/* Fix hamburger menu color in dark mode - Only color, no transformation */
[data-theme="dark"] .hamburger-menu span {
  background-color: #f5f5f5; /* Light color for dark mode */
}

/* Add stronger blur to dark mode mobile menu when open */
@media (max-width: 768px) {
  [data-theme="dark"] .main-nav.active {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background-color: rgba(0, 0, 0, 0.7); /* More opaque background */
  }

  /* Blur the overlay in dark mode when menu is open */
  [data-theme="dark"] .nav-overlay.active {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    background-color: rgba(0, 0, 0, 0.6); /* More opaque overlay */
  }

  /* Add blur to light mode menu when open as well */
  [data-theme="light"] .main-nav.active {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background-color: rgba(99, 76, 56, 0.9); /* More opaque background using nav-bg color */
  }

  /* Blur the overlay in light mode when menu is open */
  [data-theme="light"] .nav-overlay.active {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    background-color: rgba(99, 76, 56, 0.3); /* Lighter overlay */
  }
}

/* Fix mobile menu in dark mode - this is the main fix for the duplicate/inverted menu issue */
@media (max-width: 768px) {
  /* Ensure no menu items appear when collapsed in dark mode */
  [data-theme="dark"] .main-nav:not(.active) {
    transform: translateX(-100%) !important;
    visibility: hidden !important; /* Completely hide when not active */
    opacity: 0 !important;
  }

  /* Prevent hover from triggering the mobile menu */
  .bone-cursor {
    cursor: auto !important;
  }

  /* Only show mobile menu when active class is present */
  .nav-container:hover .main-nav:not(.active) {
    transform: translateX(-100%) !important;
    visibility: hidden !important;
    opacity: 0 !important;
  }

  .nav-container .main-nav:not(.active) {
    transform: translateX(-100%) !important;
    visibility: hidden !important;
    opacity: 0 !important;
  }
}

/* Mobile menu styles */
/* .hamburger-menu {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  z-index: 9999;
  width: 30px;
  height: 30px;
  position: relative;
}

.hamburger-menu span {
  display: block;
  position: absolute;
  width: 22px;
  height: 2px;
  background-color: var(--nav-text);
  transition: transform 0.3s ease-in-out,
              opacity 0.2s ease-in-out;
  left: 4px;
  transform-origin: center;
  border-radius: 1px;
}

.hamburger-menu span:first-child {
  top: 7px;
  transform-origin: top left;
}

.hamburger-menu span:nth-child(2) {
  top: 14px;
  opacity: 1;
}

.hamburger-menu span:last-child {
  top: 21px;
  transform-origin: bottom left;
} */

/* Hamburger menu visibility */
/* .hamburger-menu {
  display: none;
}

.hamburger-menu.visible {
  display: block !important;
} */

/* Remove conflicting hover styles */

/* Desktop navigation transitions */
@media (min-width: 769px) {
  [data-theme="dark"] .main-nav a {
    transition: color 0.3s ease;
  }
}

/* Fix mobile menu specifically for dark mode */
@media (max-width: 768px) {
  /* Ensure proper background for the active mobile menu in dark mode */
  [data-theme="dark"] .main-nav.active {
    background-color: rgba(18, 18, 18, 0.95);
    transform: translateX(0);
    z-index: 9990; /* Consistent with light mode */
  }
}

/* Ensure the main navigation bar has transparent background in dark mode */
[data-theme="dark"] .main-nav {
  background-color: transparent !important;
}

/* But when mobile menu is active, we need a background */
@media (max-width: 768px) {
  [data-theme="dark"] .main-nav {
    background-color: rgba(18, 18, 18, 0.95);
  }
}

/* Fix for the nav bar background in dark mode - targeting the actual light box element */
[data-theme="dark"] .header .main-nav {
  background-color: transparent !important;
  box-shadow: none !important;
  border: none !important;
}

[data-theme="dark"] .header .main-nav ul {
  background-color: transparent !important;
}

[data-theme="dark"] .main-nav a:not(:hover):not(.active) {
  background-color: transparent !important;
}

/* Fix for the light background on nav-container in dark mode */
[data-theme="dark"] .nav-container {
  background-color: transparent !important;
  box-shadow: none !important;
  border: none !important;
}

/* Ensure all nav elements in dark mode have transparent backgrounds by default */
[data-theme="dark"] .nav-container * {
  background-color: transparent;
}

/* Only apply styling to hovered or active links */
[data-theme="dark"] .main-nav a:hover,
[data-theme="dark"] .main-nav a.active {
  color: var(--accent-primary) !important;
  opacity: 1 !important;
}

/* Ensure the header border animation is visible in dark mode */
[data-theme="dark"] .header::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    rgba(75, 116, 170, 0),
    rgba(75, 116, 170, 0.5),
    rgba(75, 116, 170, 0.8),
    rgba(75, 116, 170, 0.5),
    rgba(75, 116, 170, 0)
  );
  opacity: 0.5;
  animation: glowPulse 4s ease-in-out infinite;
  z-index: 100;
}

/* REPLACEMENT: Direct fix for navigation element visibility */
/* Fix for dark mode navigation visibility */
[data-theme="dark"] .nav-container * {
  visibility: visible !important;
  opacity: 1 !important;
}

[data-theme="dark"] .main-nav {
  background-color: transparent !important;
}

[data-theme="dark"] .main-nav ul {
  display: flex !important;
  background-color: transparent !important;
}

[data-theme="dark"] .main-nav li {
  display: block !important;
  background-color: transparent !important;
}

[data-theme="dark"] .main-nav a {
  display: flex !important;
  background-color: transparent !important;
  color: var(--nav-text) !important;
}

[data-theme="dark"] .main-nav a:hover,
[data-theme="dark"] .main-nav a.active {
  background-color: rgba(30, 30, 30, 0.7) !important;
  color: var(--text-primary) !important;
  z-index: 20 !important;
}

/* Patch to prevent hover state from causing other elements to disappear */
[data-theme="dark"] .main-nav:hover ul,
[data-theme="dark"] .main-nav:hover li,
[data-theme="dark"] .main-nav:hover a {
  visibility: visible !important;
  opacity: 1 !important;
  display: flex !important;
}

/* Fix for navigation items disappearing on hover */
[data-theme="dark"] .main-nav a:hover ~ a,
[data-theme="dark"] .main-nav a:hover ~ li a,
[data-theme="dark"] .main-nav li:hover ~ li,
[data-theme="dark"] .main-nav li:hover ~ li a {
  visibility: visible !important;
  opacity: 1 !important;
  display: flex !important;
}

/* Ensure pointer events work correctly */
[data-theme="dark"] .main-nav ul,
[data-theme="dark"] .main-nav li,
[data-theme="dark"] .main-nav a {
  pointer-events: auto !important;
}

/* Fix z-index issues on hover */
[data-theme="dark"] .main-nav li:hover {
  z-index: 30 !important;
}

/* Reset any mix-blend-mode that might be causing visibility issues */
[data-theme="dark"] .nav-container,
[data-theme="dark"] .main-nav,
[data-theme="dark"] .main-nav ul,
[data-theme="dark"] .main-nav li,
[data-theme="dark"] .main-nav a {
  mix-blend-mode: normal !important;
  isolation: isolate !important;
}

/* Target any before/after pseudo-elements that might be interfering */
[data-theme="dark"] .main-nav::before,
[data-theme="dark"] .main-nav::after,
[data-theme="dark"] .main-nav *::before,
[data-theme="dark"] .main-nav *::after {
  mix-blend-mode: normal !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Create proper stacking context for navigation elements */
[data-theme="dark"] .nav-container {
  position: relative;
  z-index: 10;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Ensure each navigation item has its own stacking context */
[data-theme="dark"] .main-nav li {
  position: relative;
  z-index: 10;
  transform: translateZ(0);
}

/* Fix potential position:relative issues */
[data-theme="dark"] .main-nav a {
  isolation: isolate;
  position: relative;
  z-index: 10;
}

/* Remove any potential classes that might be changing display properties on hover */
.bone-cursor .main-nav:not(.active) {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Mobile menu active state - Same for both light and dark mode */
/* @media (max-width: 768px) {
  .hamburger-menu.active span:first-child {
    transform: rotate(45deg);
    top: 6px;
    left: 6px;
    width: 22px;
  }

  .hamburger-menu.active span:nth-child(2) {
    opacity: 0;
    transform: translateX(-20px);
    width: 22px;
  }

  .hamburger-menu.active span:last-child {
    transform: rotate(-45deg);
    top: 22px;
    left: 6px;
    width: 22px;
  }
} */

/* Make mobile menu text more visible in dark mode */
@media (max-width: 768px) {
  [data-theme="dark"] .main-nav a {
    color: rgba(255, 255, 255, 0.9); /* Slightly transparent white */
    font-weight: 400; /* Normal weight for minimalism */
    text-shadow: none; /* Remove text shadow for cleaner look */
  }
}

/* Complete fix for dark mode mobile menu - prevent any content from showing when collapsed */
@media (max-width: 768px) {
  [data-theme="dark"] .main-nav:not(.active) {
    display: none !important; /* Completely hide the menu when not active */
  }

  [data-theme="dark"] .main-nav:not(.active) * {
    display: none !important; /* Hide all children elements too */
  }

  /* When active, make sure it's properly displayed */
  [data-theme="dark"] .main-nav.active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    transform: translateX(0) !important;
  }
}

/* Remove navigation dots styles */
.button-text-control small {
  font-size: 0.8rem;
  color: rgba(60, 80, 100, 0.7);
}

[data-theme="dark"] .button-text-control small {
  color: rgba(255, 255, 255, 0.6);
}

/* Hide scrollbar for cleaner look but maintain scroll functionality */
.demo-page-container {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.demo-page-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, and Opera */
}

.start-option .input-group input,
.start-option .input-group label {
  text-align: center;
  width: 100%;
  max-width: 300px;
}

.start-option .begin-config {
  margin-left: auto;
  margin-right: auto;
  max-width: 200px;
}

/* Add styles for the CreateAgentButton container */

/* Call Card Container */
.call-card-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  padding: 20px;
  background-color: transparent; /* Ensure background is transparent */
  z-index: 5; /* Lower z-index to allow background to show through */
  position: relative; /* Ensure z-index works */
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.call-card-container.active {
  opacity: 1;
  transform: translateY(0);
}

.call-card {
  width: 100%;
  max-width: 1200px;
  height: 100%;
  max-height: 800px;
  border-radius: 12px;
  overflow: hidden;
  background-color: transparent;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

/* Hide redundant universal preview controls injected externally */
#universal-preview-controls,
.universal-preview-controls {
  display: none !important;
}
