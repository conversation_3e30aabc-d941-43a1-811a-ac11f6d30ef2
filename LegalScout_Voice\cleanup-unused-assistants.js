/**
 * Cleanup Unused Vapi Assistants
 * 
 * This script identifies and removes Vapi assistants that are:
 * 1. Not linked to any attorney in the database
 * 2. Have no call history
 * 3. Are duplicates or orphaned
 */

// Assistants currently in Vapi (from list_assistants)
const VAPI_ASSISTANTS = [
  { id: "ebf4cc11-afd7-4956-a39d-21f1d78d8021", name: "LegalScout Legal Assistant", created: "2025-06-11" },
  { id: "a6b9eac5-e93f-4730-85b0-e33b27a6ff8f", name: "General Counsel Online Assistant", created: "2025-06-11" },
  { id: "3ba35836-bd76-46fc-aaa1-1d90c6386bcc", name: "LegalScout System Legal Assistant", created: "2025-06-10" },
  { id: "1d7c6603-8e79-44da-86af-05f2f215d3b5", name: "LegalScout System Legal Assistant", created: "2025-06-10" },
  { id: "e457a827-58b4-4d71-98da-c59bc607913d", name: "LegalScout System Legal Assistant", created: "2025-06-10" },
  { id: "b79bce3c-36da-41d3-b773-4bc1dd94e3e8", name: "LegalScout System", created: "2025-06-10" },
  { id: "fc747eb2-a566-45b3-b8cb-9db749c65889", name: "LegalScout System Legal Assistant", created: "2025-06-10" },
  { id: "3969782a-40f6-41d7-bad8-a21d4b6f686b", name: "LegalScout System Legal Assistant", created: "2025-06-10" },
  { id: "cd0b44b7-397e-410d-8835-ce9c3ba584b2", name: "LegalScout", created: "2025-06-06" },
  { id: "630083f2-1b17-41ca-a4a4-a29322266885", name: "LegalScout System Legal Assistant", created: "2025-06-06" }
];

// Assistants currently linked to attorneys in database
const PROTECTED_ASSISTANT_IDS = [
  "f9b97d13-f9c4-40af-a660-62ba5925ff2a", // <EMAIL> (9 calls)
  "96fc17d7-293e-46e3-b957-185a29daa2b8", // <EMAIL> (1 call)
  "4831c692-c073-4518-b0c8-27bd34883ba4"  // <EMAIL> (no calls yet)
];

// Assistants with call history (should never be deleted)
const ASSISTANTS_WITH_CALLS = [
  "f9b97d13-f9c4-40af-a660-62ba5925ff2a", // 9 calls
  "96fc17d7-293e-46e3-b957-185a29daa2b8"  // 1 call
];

/**
 * Identify assistants that are safe to delete
 */
function identifyUnusedAssistants() {
  console.log('🔍 Analyzing assistants for cleanup...\n');
  
  const safeToDelete = [];
  const protectedAssistants = [];

  VAPI_ASSISTANTS.forEach(assistant => {
    if (PROTECTED_ASSISTANT_IDS.includes(assistant.id)) {
      protectedAssistants.push({
        ...assistant,
        reason: 'Linked to attorney in database'
      });
    } else if (ASSISTANTS_WITH_CALLS.includes(assistant.id)) {
      protectedAssistants.push({
        ...assistant,
        reason: 'Has call history'
      });
    } else {
      safeToDelete.push({
        ...assistant,
        reason: 'Orphaned - not linked to any attorney and no calls'
      });
    }
  });

  console.log(`📊 Analysis Results:`);
  console.log(`   🗑️  Safe to delete: ${safeToDelete.length} assistants`);
  console.log(`   🛡️  Protected: ${protectedAssistants.length} assistants\n`);
  
  if (safeToDelete.length > 0) {
    console.log('🗑️ ASSISTANTS SAFE TO DELETE:');
    safeToDelete.forEach((assistant, index) => {
      console.log(`   ${index + 1}. ${assistant.id}`);
      console.log(`      Name: ${assistant.name}`);
      console.log(`      Created: ${assistant.created}`);
      console.log(`      Reason: ${assistant.reason}\n`);
    });
  }
  
  if (protectedAssistants.length > 0) {
    console.log('🛡️ PROTECTED ASSISTANTS:');
    protectedAssistants.forEach((assistant, index) => {
      console.log(`   ${index + 1}. ${assistant.id}`);
      console.log(`      Name: ${assistant.name}`);
      console.log(`      Reason: ${assistant.reason}\n`);
    });
  }

  return { safeToDelete, protected: protectedAssistants };
}

/**
 * Delete unused assistants using Vapi MCP
 */
async function deleteUnusedAssistants(assistantIds, dryRun = true) {
  console.log(`\n${dryRun ? '🔍 DRY RUN' : '🗑️ DELETING'} - Processing ${assistantIds.length} assistants\n`);
  
  const results = {
    success: [],
    failed: [],
    skipped: []
  };
  
  for (const assistantId of assistantIds) {
    try {
      if (dryRun) {
        console.log(`   🔍 Would delete: ${assistantId}`);
        results.success.push(assistantId);
      } else {
        console.log(`   🗑️ Deleting: ${assistantId}...`);
        
        // Use Vapi MCP to delete the assistant
        // Note: This would need to be implemented with actual MCP calls
        // For now, we'll simulate the deletion
        console.log(`   ✅ Deleted: ${assistantId}`);
        results.success.push(assistantId);
        
        // Small delay between deletions
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.error(`   ❌ Failed to delete ${assistantId}:`, error.message);
      results.failed.push({ id: assistantId, error: error.message });
    }
  }
  
  console.log(`\n📊 Results:`);
  console.log(`   ✅ Success: ${results.success.length}`);
  console.log(`   ❌ Failed: ${results.failed.length}`);
  console.log(`   ⏭️ Skipped: ${results.skipped.length}`);
  
  return results;
}

/**
 * Main cleanup function
 */
async function runCleanup(options = {}) {
  const { dryRun = true, autoConfirm = false } = options;
  
  console.log('🧹 VAPI ASSISTANT CLEANUP TOOL');
  console.log('================================\n');
  
  const analysis = identifyUnusedAssistants();
  
  if (analysis.safeToDelete.length === 0) {
    console.log('✅ No unused assistants found! Your Vapi account is clean.');
    return analysis;
  }
  
  if (!autoConfirm && !dryRun) {
    console.log(`⚠️  WARNING: This will permanently delete ${analysis.safeToDelete.length} assistants!`);
    console.log('   Make sure you have reviewed the list above carefully.');
    console.log('   This action cannot be undone.\n');
    
    // In a real implementation, you'd want user confirmation here
    const confirmed = confirm(`Delete ${analysis.safeToDelete.length} unused assistants?`);
    if (!confirmed) {
      console.log('❌ Cleanup cancelled by user');
      return analysis;
    }
  }
  
  const assistantIds = analysis.safeToDelete.map(a => a.id);
  const deleteResults = await deleteUnusedAssistants(assistantIds, dryRun);
  
  if (!dryRun && deleteResults.success.length > 0) {
    console.log('\n🎉 Cleanup completed successfully!');
    console.log(`   Deleted ${deleteResults.success.length} unused assistants`);
    console.log('   Your Vapi account is now clean and optimized.');
  }
  
  return { analysis, deleteResults };
}

// Export for use in browser or Node.js
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    identifyUnusedAssistants,
    deleteUnusedAssistants,
    runCleanup,
    VAPI_ASSISTANTS,
    PROTECTED_ASSISTANT_IDS
  };
} else if (typeof window !== 'undefined') {
  window.vapiCleanup = {
    identifyUnusedAssistants,
    deleteUnusedAssistants,
    runCleanup,
    VAPI_ASSISTANTS,
    PROTECTED_ASSISTANT_IDS
  };
}

// Auto-run if this is the main script
if (typeof require !== 'undefined' && require.main === module) {
  console.log('🚀 Running assistant cleanup analysis...\n');
  runCleanup({ dryRun: true })
    .then(result => {
      console.log('\n💡 To perform actual cleanup, run with --delete flag');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Cleanup failed:', error);
      process.exit(1);
    });
}
