import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🔍 Environment Variables Debug:');
console.log('================================');

console.log('SUPABASE_URL:', process.env.SUPABASE_URL);
console.log('VITE_SUPABASE_URL:', process.env.VITE_SUPABASE_URL);
console.log('SUPABASE_ANON_KEY:', process.env.SUPABASE_ANON_KEY);
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY);
console.log('VITE_SUPABASE_KEY:', process.env.VITE_SUPABASE_KEY);

console.log('\nVAPI Keys:');
console.log('VAPI_TOKEN:', process.env.VAPI_TOKEN);
console.log('VAPI_SECRET_KEY:', process.env.VAPI_SECRET_KEY);
console.log('VITE_VAPI_SECRET_KEY:', process.env.VITE_VAPI_SECRET_KEY);

console.log('\nDerived values:');
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

console.log('Derived supabaseUrl:', supabaseUrl);
console.log('Derived supabaseKey:', supabaseKey ? 'present' : 'missing');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase configuration!');
  process.exit(1);
} else {
  console.log('✅ Supabase configuration looks good!');
}
