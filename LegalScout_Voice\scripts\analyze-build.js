#!/usr/bin/env node

/**
 * Build Analysis Script for LegalScout Voice
 * Analyzes bundle size, performance, and optimization opportunities
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const DIST_DIR = path.join(__dirname, '..', 'dist');
const ASSETS_DIR = path.join(DIST_DIR, 'assets');

// File size thresholds (in KB)
const THRESHOLDS = {
  JS_CHUNK_WARNING: 500,
  JS_CHUNK_ERROR: 1000,
  CSS_WARNING: 100,
  CSS_ERROR: 200,
  TOTAL_WARNING: 2000,
  TOTAL_ERROR: 5000
};

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getFileSize(filePath) {
  try {
    return fs.statSync(filePath).size;
  } catch (error) {
    return 0;
  }
}

function analyzeAssets() {
  if (!fs.existsSync(ASSETS_DIR)) {
    console.error('❌ Assets directory not found. Run build first.');
    return null;
  }

  const files = fs.readdirSync(ASSETS_DIR);
  const analysis = {
    js: [],
    css: [],
    images: [],
    fonts: [],
    other: [],
    total: 0
  };

  files.forEach(file => {
    const filePath = path.join(ASSETS_DIR, file);
    const size = getFileSize(filePath);
    const ext = path.extname(file).toLowerCase();
    
    analysis.total += size;

    const fileInfo = {
      name: file,
      size,
      sizeFormatted: formatBytes(size),
      path: filePath
    };

    if (ext === '.js') {
      analysis.js.push(fileInfo);
    } else if (ext === '.css') {
      analysis.css.push(fileInfo);
    } else if (['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg', '.ico'].includes(ext)) {
      analysis.images.push(fileInfo);
    } else if (['.woff', '.woff2', '.ttf', '.eot'].includes(ext)) {
      analysis.fonts.push(fileInfo);
    } else {
      analysis.other.push(fileInfo);
    }
  });

  // Sort by size (largest first)
  Object.keys(analysis).forEach(key => {
    if (Array.isArray(analysis[key])) {
      analysis[key].sort((a, b) => b.size - a.size);
    }
  });

  return analysis;
}

function generateReport(analysis) {
  console.log('\n🚀 LegalScout Voice - Build Analysis Report');
  console.log('=' .repeat(60));
  
  // Total bundle size
  const totalMB = analysis.total / (1024 * 1024);
  const totalStatus = totalMB > THRESHOLDS.TOTAL_ERROR / 1024 ? '🔴' : 
                     totalMB > THRESHOLDS.TOTAL_WARNING / 1024 ? '🟡' : '🟢';
  
  console.log(`\n📊 Total Bundle Size: ${totalStatus} ${formatBytes(analysis.total)}`);
  
  // JavaScript analysis
  console.log('\n📜 JavaScript Files:');
  if (analysis.js.length === 0) {
    console.log('  No JS files found');
  } else {
    analysis.js.forEach(file => {
      const sizeKB = file.size / 1024;
      const status = sizeKB > THRESHOLDS.JS_CHUNK_ERROR ? '🔴' : 
                    sizeKB > THRESHOLDS.JS_CHUNK_WARNING ? '🟡' : '🟢';
      console.log(`  ${status} ${file.name}: ${file.sizeFormatted}`);
    });
  }

  // CSS analysis
  console.log('\n🎨 CSS Files:');
  if (analysis.css.length === 0) {
    console.log('  No CSS files found');
  } else {
    analysis.css.forEach(file => {
      const sizeKB = file.size / 1024;
      const status = sizeKB > THRESHOLDS.CSS_ERROR ? '🔴' : 
                    sizeKB > THRESHOLDS.CSS_WARNING ? '🟡' : '🟢';
      console.log(`  ${status} ${file.name}: ${file.sizeFormatted}`);
    });
  }

  // Images analysis
  console.log('\n🖼️  Image Files:');
  if (analysis.images.length === 0) {
    console.log('  No image files found');
  } else {
    const totalImageSize = analysis.images.reduce((sum, file) => sum + file.size, 0);
    console.log(`  Total: ${formatBytes(totalImageSize)}`);
    analysis.images.slice(0, 5).forEach(file => {
      console.log(`  📷 ${file.name}: ${file.sizeFormatted}`);
    });
    if (analysis.images.length > 5) {
      console.log(`  ... and ${analysis.images.length - 5} more files`);
    }
  }

  // Fonts analysis
  console.log('\n🔤 Font Files:');
  if (analysis.fonts.length === 0) {
    console.log('  No font files found');
  } else {
    const totalFontSize = analysis.fonts.reduce((sum, file) => sum + file.size, 0);
    console.log(`  Total: ${formatBytes(totalFontSize)}`);
    analysis.fonts.forEach(file => {
      console.log(`  📝 ${file.name}: ${file.sizeFormatted}`);
    });
  }
}

function generateOptimizationSuggestions(analysis) {
  console.log('\n💡 Optimization Suggestions:');
  console.log('-'.repeat(40));

  const suggestions = [];

  // Check for large JS chunks
  const largeJSFiles = analysis.js.filter(file => file.size / 1024 > THRESHOLDS.JS_CHUNK_WARNING);
  if (largeJSFiles.length > 0) {
    suggestions.push('🔧 Consider code splitting for large JS chunks:');
    largeJSFiles.forEach(file => {
      suggestions.push(`   - ${file.name} (${file.sizeFormatted})`);
    });
  }

  // Check for large CSS files
  const largeCSSFiles = analysis.css.filter(file => file.size / 1024 > THRESHOLDS.CSS_WARNING);
  if (largeCSSFiles.length > 0) {
    suggestions.push('🎨 Consider CSS optimization for large stylesheets:');
    largeCSSFiles.forEach(file => {
      suggestions.push(`   - ${file.name} (${file.sizeFormatted})`);
    });
  }

  // Check for unoptimized images
  const largeImages = analysis.images.filter(file => file.size / 1024 > 100);
  if (largeImages.length > 0) {
    suggestions.push('🖼️  Consider image optimization:');
    largeImages.slice(0, 3).forEach(file => {
      suggestions.push(`   - ${file.name} (${file.sizeFormatted})`);
    });
  }

  // General suggestions
  if (analysis.total / (1024 * 1024) > 2) {
    suggestions.push('📦 Bundle size is large. Consider:');
    suggestions.push('   - Lazy loading for non-critical components');
    suggestions.push('   - Tree shaking unused dependencies');
    suggestions.push('   - Using dynamic imports for large libraries');
  }

  if (suggestions.length === 0) {
    console.log('✅ Bundle size looks good! No major optimizations needed.');
  } else {
    suggestions.forEach(suggestion => console.log(suggestion));
  }

  console.log('\n📈 Performance Tips:');
  console.log('   - Enable gzip/brotli compression on your server');
  console.log('   - Use CDN for static assets');
  console.log('   - Implement proper caching headers');
  console.log('   - Consider service worker for offline caching');
}

function checkBuildHealth() {
  console.log('\n🏥 Build Health Check:');
  console.log('-'.repeat(30));

  const checks = [
    {
      name: 'Index.html exists',
      check: () => fs.existsSync(path.join(DIST_DIR, 'index.html')),
      fix: 'Ensure Vite build completes successfully'
    },
    {
      name: 'Assets directory exists',
      check: () => fs.existsSync(ASSETS_DIR),
      fix: 'Check Vite configuration for asset handling'
    },
    {
      name: 'Source maps generated',
      check: () => fs.readdirSync(ASSETS_DIR).some(file => file.endsWith('.map')),
      fix: 'Enable sourcemap in Vite config for debugging'
    }
  ];

  checks.forEach(check => {
    const passed = check.check();
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${check.name}`);
    if (!passed) {
      console.log(`   Fix: ${check.fix}`);
    }
  });
}

// Main execution
function main() {
  console.log('🔍 Analyzing build output...\n');

  if (!fs.existsSync(DIST_DIR)) {
    console.error('❌ Build directory not found. Please run "npm run build" first.');
    process.exit(1);
  }

  const analysis = analyzeAssets();
  if (!analysis) {
    process.exit(1);
  }

  generateReport(analysis);
  generateOptimizationSuggestions(analysis);
  checkBuildHealth();

  console.log('\n🎯 Next Steps:');
  console.log('   1. Run "npm run build:analyze" to see detailed bundle analysis');
  console.log('   2. Check dist/stats.html for visual bundle breakdown');
  console.log('   3. Monitor bundle size in CI/CD pipeline');
  console.log('\n✨ Analysis complete!\n');
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { analyzeAssets, generateReport };
