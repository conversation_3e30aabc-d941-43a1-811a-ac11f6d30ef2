#!/usr/bin/env node

/**
 * Selective Merge Utility
 * 
 * Analyzes another repo and identifies components, patterns, and enhancements
 * worth merging without breaking your current clean architecture.
 * 
 * Usage: node selective-merge-utility.js <path-to-other-repo>
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

class SelectiveMergeUtility {
  constructor(otherRepoPath) {
    this.otherRepoPath = otherRepoPath;
    this.currentRepoPath = process.cwd();
    this.analysis = {
      timestamp: new Date().toISOString(),
      otherRepo: otherRepoPath,
      currentRepo: this.currentRepoPath,
      findings: {
        newComponents: [],
        enhancedComponents: [],
        newServices: [],
        configUpdates: [],
        uiEnhancements: [],
        backendImprovements: [],
        worthMerging: [],
        skipReasons: []
      }
    };
  }

  async analyze() {
    console.log('🔍 SELECTIVE MERGE ANALYSIS STARTING...\n');
    console.log(`📂 Other Repo: ${this.otherRepoPath}`);
    console.log(`📂 Current Repo: ${this.currentRepoPath}\n`);

    if (!fs.existsSync(this.otherRepoPath)) {
      throw new Error(`Other repo path does not exist: ${this.otherRepoPath}`);
    }

    // Analyze key directories
    await this.analyzeComponents();
    await this.analyzeServices();
    await this.analyzeConfigurations();
    await this.analyzePackageChanges();
    
    // Generate recommendations
    this.generateRecommendations();
    
    // Export results
    this.exportResults();
    
    return this.analysis;
  }

  async analyzeComponents() {
    console.log('📦 Analyzing Components...');
    
    const otherComponents = this.scanDirectory(path.join(this.otherRepoPath, 'src/components'));
    const currentComponents = this.scanDirectory(path.join(this.currentRepoPath, 'src/components'));
    
    otherComponents.forEach(component => {
      const currentVersion = currentComponents.find(c => c.name === component.name);
      
      if (!currentVersion) {
        // New component
        this.analysis.findings.newComponents.push({
          name: component.name,
          path: component.path,
          size: component.size,
          reason: 'New component not in current repo',
          recommendation: this.analyzeComponentValue(component)
        });
      } else if (component.hash !== currentVersion.hash) {
        // Enhanced component
        this.analysis.findings.enhancedComponents.push({
          name: component.name,
          currentPath: currentVersion.path,
          otherPath: component.path,
          sizeDiff: component.size - currentVersion.size,
          reason: 'Component has been modified',
          recommendation: this.analyzeEnhancement(component, currentVersion)
        });
      }
    });
  }

  async analyzeServices() {
    console.log('🛠️  Analyzing Services...');
    
    const otherServices = this.scanDirectory(path.join(this.otherRepoPath, 'src/services'));
    const currentServices = this.scanDirectory(path.join(this.currentRepoPath, 'src/services'));
    
    otherServices.forEach(service => {
      const currentVersion = currentServices.find(s => s.name === service.name);
      
      if (!currentVersion) {
        this.analysis.findings.newServices.push({
          name: service.name,
          path: service.path,
          size: service.size,
          reason: 'New service not in current repo',
          recommendation: this.analyzeServiceValue(service)
        });
      } else if (service.hash !== currentVersion.hash) {
        this.analysis.findings.backendImprovements.push({
          name: service.name,
          currentPath: currentVersion.path,
          otherPath: service.path,
          sizeDiff: service.size - currentVersion.size,
          recommendation: this.analyzeServiceEnhancement(service, currentVersion)
        });
      }
    });
  }

  async analyzeConfigurations() {
    console.log('⚙️  Analyzing Configurations...');
    
    const configFiles = [
      'package.json',
      'vite.config.js',
      '.env.example',
      'vercel.json',
      'src/config'
    ];
    
    configFiles.forEach(configPath => {
      const otherConfig = this.getFileInfo(path.join(this.otherRepoPath, configPath));
      const currentConfig = this.getFileInfo(path.join(this.currentRepoPath, configPath));
      
      if (otherConfig.exists && currentConfig.exists && otherConfig.hash !== currentConfig.hash) {
        this.analysis.findings.configUpdates.push({
          file: configPath,
          recommendation: this.analyzeConfigChange(otherConfig, currentConfig)
        });
      }
    });
  }

  async analyzePackageChanges() {
    console.log('📦 Analyzing Package Dependencies...');
    
    const otherPackage = this.readJsonFile(path.join(this.otherRepoPath, 'package.json'));
    const currentPackage = this.readJsonFile(path.join(this.currentRepoPath, 'package.json'));
    
    if (otherPackage && currentPackage) {
      const newDeps = this.findNewDependencies(otherPackage, currentPackage);
      const updatedDeps = this.findUpdatedDependencies(otherPackage, currentPackage);
      
      if (newDeps.length > 0 || updatedDeps.length > 0) {
        this.analysis.findings.configUpdates.push({
          file: 'package.json',
          newDependencies: newDeps,
          updatedDependencies: updatedDeps,
          recommendation: 'REVIEW_DEPENDENCIES'
        });
      }
    }
  }

  analyzeComponentValue(component) {
    const content = this.readFileContent(component.path);
    
    // Check for modern patterns
    const hasModernPatterns = [
      'useState', 'useEffect', 'useCallback', 'useMemo',
      'service-oriented', 'composition', 'error boundary'
    ].some(pattern => content.includes(pattern));
    
    // Check for Vapi integration
    const hasVapiIntegration = content.includes('vapi') || content.includes('Vapi');
    
    // Check for assistant management
    const hasAssistantFeatures = content.includes('assistant') || content.includes('Assistant');
    
    if (hasModernPatterns && (hasVapiIntegration || hasAssistantFeatures)) {
      return 'HIGH_VALUE - Modern component with Vapi/Assistant features';
    } else if (hasModernPatterns) {
      return 'MEDIUM_VALUE - Modern component, review for utility';
    } else {
      return 'LOW_VALUE - May contain legacy patterns';
    }
  }

  analyzeServiceValue(service) {
    const content = this.readFileContent(service.path);
    
    // Check for modern service patterns
    const hasModernServicePatterns = [
      'class', 'async', 'await', 'Promise', 'error handling'
    ].some(pattern => content.includes(pattern));
    
    // Check for MCP integration
    const hasMCPIntegration = content.includes('mcp') || content.includes('MCP');
    
    // Check for Vapi integration
    const hasVapiIntegration = content.includes('vapi') || content.includes('Vapi');
    
    if (hasModernServicePatterns && (hasMCPIntegration || hasVapiIntegration)) {
      return 'HIGH_VALUE - Modern service with MCP/Vapi integration';
    } else if (hasModernServicePatterns) {
      return 'MEDIUM_VALUE - Modern service, review for enhancements';
    } else {
      return 'LOW_VALUE - May contain legacy patterns';
    }
  }

  generateRecommendations() {
    console.log('🎯 Generating Recommendations...');
    
    // High-value components worth merging
    const highValueComponents = this.analysis.findings.newComponents
      .filter(c => c.recommendation.includes('HIGH_VALUE'));
    
    const highValueServices = this.analysis.findings.newServices
      .filter(s => s.recommendation.includes('HIGH_VALUE'));
    
    // Enhanced components worth reviewing
    const worthReviewing = this.analysis.findings.enhancedComponents
      .filter(c => c.recommendation.includes('WORTH_REVIEWING'));
    
    this.analysis.findings.worthMerging = [
      ...highValueComponents.map(c => ({ type: 'component', ...c })),
      ...highValueServices.map(s => ({ type: 'service', ...s })),
      ...worthReviewing.map(c => ({ type: 'enhancement', ...c }))
    ];
  }

  exportResults() {
    const reportPath = 'selective-merge-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(this.analysis, null, 2));
    
    console.log('\n✅ ANALYSIS COMPLETE!');
    console.log(`📄 Report exported to: ${reportPath}`);
    console.log(`📊 Found ${this.analysis.findings.worthMerging.length} items worth merging`);
    
    // Print summary
    this.printSummary();
  }

  printSummary() {
    console.log('\n🎯 MERGE RECOMMENDATIONS:');
    
    if (this.analysis.findings.worthMerging.length === 0) {
      console.log('  ✨ No high-value items found for merging');
      return;
    }
    
    this.analysis.findings.worthMerging.forEach(item => {
      console.log(`  🔥 ${item.type.toUpperCase()}: ${item.name}`);
      console.log(`     ${item.recommendation}`);
    });
    
    console.log('\n📋 NEXT STEPS:');
    console.log('  1. Review the detailed report in selective-merge-report.json');
    console.log('  2. Selectively copy high-value components/services');
    console.log('  3. Test integration without breaking existing patterns');
    console.log('  4. Update configurations as needed');
  }

  // Utility methods
  scanDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) return [];
    
    const files = [];
    const items = fs.readdirSync(dirPath, { withFileTypes: true });
    
    items.forEach(item => {
      if (item.isFile() && (item.name.endsWith('.js') || item.name.endsWith('.jsx'))) {
        const filePath = path.join(dirPath, item.name);
        const stats = fs.statSync(filePath);
        const content = fs.readFileSync(filePath, 'utf8');
        
        files.push({
          name: item.name,
          path: filePath,
          size: stats.size,
          hash: crypto.createHash('md5').update(content).digest('hex')
        });
      }
    });
    
    return files;
  }

  getFileInfo(filePath) {
    try {
      const stats = fs.statSync(filePath);
      const content = fs.readFileSync(filePath, 'utf8');
      return {
        exists: true,
        size: stats.size,
        hash: crypto.createHash('md5').update(content).digest('hex')
      };
    } catch {
      return { exists: false };
    }
  }

  readFileContent(filePath) {
    try {
      return fs.readFileSync(filePath, 'utf8');
    } catch {
      return '';
    }
  }

  readJsonFile(filePath) {
    try {
      return JSON.parse(fs.readFileSync(filePath, 'utf8'));
    } catch {
      return null;
    }
  }

  findNewDependencies(otherPackage, currentPackage) {
    const otherDeps = { ...otherPackage.dependencies, ...otherPackage.devDependencies };
    const currentDeps = { ...currentPackage.dependencies, ...currentPackage.devDependencies };
    
    return Object.keys(otherDeps).filter(dep => !currentDeps[dep]);
  }

  findUpdatedDependencies(otherPackage, currentPackage) {
    const otherDeps = { ...otherPackage.dependencies, ...otherPackage.devDependencies };
    const currentDeps = { ...currentPackage.dependencies, ...currentPackage.devDependencies };
    
    return Object.keys(otherDeps).filter(dep => 
      currentDeps[dep] && otherDeps[dep] !== currentDeps[dep]
    );
  }

  analyzeEnhancement(otherComponent, currentComponent) {
    // Simple heuristic based on size change and content
    const sizeDiff = otherComponent.size - currentComponent.size;
    
    if (sizeDiff > 1000) {
      return 'WORTH_REVIEWING - Significant additions';
    } else if (sizeDiff > 0) {
      return 'MINOR_ENHANCEMENT - Small improvements';
    } else {
      return 'REFACTORING - Code cleanup or optimization';
    }
  }

  analyzeServiceEnhancement(otherService, currentService) {
    return this.analyzeEnhancement(otherService, currentService);
  }

  analyzeConfigChange(otherConfig, currentConfig) {
    const sizeDiff = otherConfig.size - currentConfig.size;
    
    if (sizeDiff > 0) {
      return 'NEW_CONFIGURATION - Review for new settings';
    } else {
      return 'CONFIGURATION_UPDATE - Review for changes';
    }
  }
}

// CLI usage
if (process.argv[2]) {
  const otherRepoPath = process.argv[2];
  const utility = new SelectiveMergeUtility(otherRepoPath);
  
  utility.analyze().catch(error => {
    console.error('❌ Error:', error.message);
    process.exit(1);
  });
} else {
  console.log('Usage: node selective-merge-utility.js <path-to-other-repo>');
  console.log('\nThis utility analyzes another repo and identifies components,');
  console.log('services, and configurations worth merging into your current repo.');
}
