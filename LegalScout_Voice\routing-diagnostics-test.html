<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LegalScout Routing Diagnostics</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .diagnostic-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-ok { background: #22c55e; }
        .status-warning { background: #f59e0b; }
        .status-error { background: #ef4444; }
        .status-unknown { background: #6b7280; }
        
        .test-results {
            font-family: 'Courier New', monospace;
            background: #1f2937;
            color: #f3f4f6;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .summary-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
        }
        
        .summary-card h4 {
            margin: 0 0 10px 0;
            color: #1f2937;
        }
        
        .button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        
        .button:hover {
            background: #2563eb;
        }
        
        .button.secondary {
            background: #6b7280;
        }
        
        .button.secondary:hover {
            background: #4b5563;
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #3b82f6;
            transition: width 0.3s ease;
        }
        
        .timestamp {
            color: #6b7280;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <h1>🔍 LegalScout Routing Diagnostics</h1>
    
    <div class="diagnostic-panel">
        <h2>Environment Detection</h2>
        <div id="environment-info">
            <p><strong>Current URL:</strong> <span id="current-url"></span></p>
            <p><strong>Environment:</strong> <span id="environment-type"></span></p>
            <p><strong>Expected Path:</strong> <span id="expected-path"></span></p>
            <p><strong>Actual Path:</strong> <span id="actual-path"></span></p>
        </div>
    </div>

    <div class="diagnostic-panel">
        <h2>Test Controls</h2>
        <button class="button" onclick="runAllDiagnostics()">🚀 Run All Diagnostics</button>
        <button class="button secondary" onclick="runRoutingTests()">🛣️ Routing Only</button>
        <button class="button secondary" onclick="runEnvironmentTests()">🌍 Environment Only</button>
        <button class="button secondary" onclick="runComponentTests()">🧩 Components Only</button>
        <button class="button secondary" onclick="clearResults()">🗑️ Clear Results</button>
        
        <div class="progress-bar" id="progress-bar" style="display: none;">
            <div class="progress-fill" id="progress-fill"></div>
        </div>
    </div>

    <div class="diagnostic-panel">
        <h2>Quick Summary</h2>
        <div class="summary-grid" id="summary-grid">
            <!-- Summary cards will be populated here -->
        </div>
    </div>

    <div class="diagnostic-panel">
        <h2>Detailed Results</h2>
        <div class="test-results" id="test-results">
            Click "Run All Diagnostics" to start testing...
        </div>
    </div>

    <div class="diagnostic-panel">
        <h2>Raw Data</h2>
        <button class="button secondary" onclick="showRawData()">📊 Show Raw Diagnostic Data</button>
        <button class="button secondary" onclick="exportResults()">💾 Export Results</button>
        <div class="test-results" id="raw-data" style="display: none;">
            <!-- Raw data will be shown here -->
        </div>
    </div>

    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            updateEnvironmentInfo();
            
            // Auto-run diagnostics after a short delay
            setTimeout(() => {
                console.log('🚀 Auto-running diagnostics...');
                runAllDiagnostics();
            }, 1000);
        });

        function updateEnvironmentInfo() {
            const hostname = window.location.hostname;
            const isProduction = hostname.includes('legalscout.net');
            const isLocal = hostname.includes('localhost');
            const isVercel = hostname.includes('vercel.app');
            
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('environment-type').textContent = 
                isProduction ? '🏭 Production' : 
                isLocal ? '💻 Local Development' : 
                isVercel ? '☁️ Vercel Preview' : '❓ Unknown';
            document.getElementById('expected-path').textContent = isProduction ? '/' : '/dashboard';
            document.getElementById('actual-path').textContent = window.location.pathname;
        }

        async function runAllDiagnostics() {
            showProgress();
            logMessage('🚀 Starting comprehensive diagnostics...\n');
            
            try {
                // Load and run all diagnostic scripts
                await loadDiagnosticScripts();
                updateProgress(25);
                
                await runRoutingDiagnostics();
                updateProgress(50);
                
                await runComponentAnalysis();
                updateProgress(75);
                
                await runEnvironmentDiagnostics();
                updateProgress(100);
                
                generateSummary();
                logMessage('\n✅ All diagnostics completed successfully!');
                
            } catch (error) {
                logMessage(`\n❌ Diagnostic failed: ${error.message}`);
                console.error('Diagnostic error:', error);
            } finally {
                hideProgress();
            }
        }

        async function loadDiagnosticScripts() {
            logMessage('📦 Loading diagnostic scripts...\n');
            
            const scripts = [
                'diagnostic-routing-tests.js',
                'routing-component-analyzer.js',
                'environment-timing-diagnostics.js'
            ];
            
            for (const script of scripts) {
                try {
                    await loadScript(script);
                    logMessage(`✅ Loaded ${script}\n`);
                } catch (error) {
                    logMessage(`❌ Failed to load ${script}: ${error.message}\n`);
                }
            }
        }

        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }

        async function runRoutingDiagnostics() {
            logMessage('🛣️ Running routing diagnostics...\n');
            
            if (window.routingDiagnostics) {
                await window.routingDiagnostics.runAllTests();
                logMessage('✅ Routing diagnostics completed\n');
            } else {
                logMessage('⚠️ Routing diagnostics not available\n');
            }
        }

        async function runComponentAnalysis() {
            logMessage('🧩 Running component analysis...\n');
            
            if (window.routingAnalyzer) {
                await window.routingAnalyzer.analyze();
                logMessage('✅ Component analysis completed\n');
            } else {
                logMessage('⚠️ Component analyzer not available\n');
            }
        }

        async function runEnvironmentDiagnostics() {
            logMessage('🌍 Running environment diagnostics...\n');
            
            if (window.envTimingDiagnostics) {
                await window.envTimingDiagnostics.runDiagnostics();
                logMessage('✅ Environment diagnostics completed\n');
            } else {
                logMessage('⚠️ Environment diagnostics not available\n');
            }
        }

        function generateSummary() {
            const summaryGrid = document.getElementById('summary-grid');
            summaryGrid.innerHTML = '';
            
            // Collect results from all diagnostics
            const results = {
                routing: window.routingDiagnostics?.results,
                components: window.routingAnalysis,
                environment: window.environmentDiagnostics
            };
            
            // Generate summary cards
            Object.entries(results).forEach(([key, data]) => {
                if (data) {
                    const card = createSummaryCard(key, data);
                    summaryGrid.appendChild(card);
                }
            });
        }

        function createSummaryCard(title, data) {
            const card = document.createElement('div');
            card.className = 'summary-card';
            
            const status = getOverallStatus(data);
            const statusClass = `status-${status}`;
            
            card.innerHTML = `
                <h4><span class="status-indicator ${statusClass}"></span>${title.charAt(0).toUpperCase() + title.slice(1)}</h4>
                <div class="timestamp">Last updated: ${new Date().toLocaleTimeString()}</div>
                <div style="margin-top: 10px;">
                    ${generateStatusDetails(data)}
                </div>
            `;
            
            return card;
        }

        function getOverallStatus(data) {
            if (!data) return 'unknown';
            
            // Check for critical issues
            if (data.issues?.some(i => i.severity === 'critical') || 
                data.summary?.criticalIssues > 0) {
                return 'error';
            }
            
            // Check for warnings
            if (data.issues?.some(i => i.severity === 'warning') || 
                data.summary?.warnings > 0) {
                return 'warning';
            }
            
            return 'ok';
        }

        function generateStatusDetails(data) {
            if (data.summary) {
                return Object.entries(data.summary)
                    .map(([key, value]) => `<div><strong>${key}:</strong> ${value}</div>`)
                    .join('');
            }
            return '<div>No summary available</div>';
        }

        function runRoutingTests() {
            logMessage('🛣️ Running routing tests only...\n');
            if (window.routingDiagnostics) {
                window.routingDiagnostics.runAllTests();
            }
        }

        function runEnvironmentTests() {
            logMessage('🌍 Running environment tests only...\n');
            if (window.envTimingDiagnostics) {
                window.envTimingDiagnostics.runDiagnostics();
            }
        }

        function runComponentTests() {
            logMessage('🧩 Running component tests only...\n');
            if (window.routingAnalyzer) {
                window.routingAnalyzer.analyze();
            }
        }

        function showRawData() {
            const rawDataDiv = document.getElementById('raw-data');
            const allData = {
                routing: window.routingDiagnostics?.results,
                components: window.routingAnalysis,
                environment: window.environmentDiagnostics,
                timestamp: new Date().toISOString()
            };
            
            rawDataDiv.textContent = JSON.stringify(allData, null, 2);
            rawDataDiv.style.display = rawDataDiv.style.display === 'none' ? 'block' : 'none';
        }

        function exportResults() {
            const allData = {
                routing: window.routingDiagnostics?.results,
                components: window.routingAnalysis,
                environment: window.environmentDiagnostics,
                url: window.location.href,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(allData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `legalscout-diagnostics-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function clearResults() {
            document.getElementById('test-results').textContent = 'Results cleared. Click "Run All Diagnostics" to start testing...';
            document.getElementById('summary-grid').innerHTML = '';
            document.getElementById('raw-data').style.display = 'none';
        }

        function logMessage(message) {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.textContent += message;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function showProgress() {
            document.getElementById('progress-bar').style.display = 'block';
            updateProgress(0);
        }

        function updateProgress(percent) {
            document.getElementById('progress-fill').style.width = percent + '%';
        }

        function hideProgress() {
            setTimeout(() => {
                document.getElementById('progress-bar').style.display = 'none';
            }, 500);
        }

        // Console override to capture logs
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            if (args[0] && typeof args[0] === 'string' && args[0].includes('[')) {
                logMessage(args.join(' ') + '\n');
            }
        };
    </script>
</body>
</html>
