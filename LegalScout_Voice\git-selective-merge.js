#!/usr/bin/env node

/**
 * Git-Based Selective Merge Utility
 * 
 * Uses Git to analyze your other repo and identify valuable components,
 * patterns, and enhancements worth merging without breaking your clean architecture.
 * 
 * Usage: 
 *   node git-selective-merge.js <git-repo-url>
 *   node git-selective-merge.js <local-repo-path>
 *   node git-selective-merge.js --branch <branch-name> <repo>
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

class GitSelectiveMerge {
  constructor(repoSource, options = {}) {
    this.repoSource = repoSource;
    this.branch = options.branch || 'main';
    this.tempDir = path.join(process.cwd(), '.temp-analysis');
    this.currentRepo = process.cwd();
    this.analysis = {
      timestamp: new Date().toISOString(),
      source: repoSource,
      branch: this.branch,
      findings: {
        newComponents: [],
        enhancedComponents: [],
        newServices: [],
        configChanges: [],
        worthMerging: [],
        gitStats: {}
      }
    };
  }

  async analyze() {
    console.log('🔍 GIT SELECTIVE MERGE ANALYSIS STARTING...\n');
    console.log(`📂 Source: ${this.repoSource}`);
    console.log(`🌿 Branch: ${this.branch}\n`);

    try {
      // Clone or update the other repo
      await this.setupOtherRepo();
      
      // Get Git statistics
      await this.getGitStats();
      
      // Analyze differences
      await this.analyzeDifferences();
      
      // Generate recommendations
      this.generateRecommendations();
      
      // Export results
      this.exportResults();
      
      // Cleanup
      this.cleanup();
      
      return this.analysis;
      
    } catch (error) {
      console.error('❌ Error:', error.message);
      this.cleanup();
      throw error;
    }
  }

  async setupOtherRepo() {
    console.log('📥 Setting up other repo...');
    
    // Clean up any existing temp directory
    if (fs.existsSync(this.tempDir)) {
      this.execCommand(`rm -rf "${this.tempDir}"`);
    }

    // Clone the repo
    if (this.repoSource.startsWith('http') || this.repoSource.startsWith('git@')) {
      // Remote repo
      console.log('🌐 Cloning remote repository...');
      this.execCommand(`git clone --depth 1 --branch ${this.branch} "${this.repoSource}" "${this.tempDir}"`);
    } else {
      // Local repo path
      console.log('📁 Copying local repository...');
      this.execCommand(`cp -r "${this.repoSource}" "${this.tempDir}"`);
    }
  }

  async getGitStats() {
    console.log('📊 Analyzing Git statistics...');
    
    const originalCwd = process.cwd();
    process.chdir(this.tempDir);
    
    try {
      // Get recent commits
      const recentCommits = this.execCommand('git log --oneline -10').split('\n').filter(line => line.trim());
      
      // Get file change statistics
      const fileStats = this.execCommand('git diff --name-status HEAD~5 HEAD 2>/dev/null || echo "No recent changes"');
      
      // Get contributors
      const contributors = this.execCommand('git shortlog -sn --all').split('\n').filter(line => line.trim());
      
      // Get branch info
      const branches = this.execCommand('git branch -a').split('\n').filter(line => line.trim());
      
      this.analysis.findings.gitStats = {
        recentCommits: recentCommits.slice(0, 5),
        fileChanges: fileStats.split('\n').filter(line => line.trim()),
        contributors: contributors.slice(0, 5),
        branches: branches.slice(0, 10),
        lastCommitDate: this.execCommand('git log -1 --format=%cd --date=short')
      };
      
    } finally {
      process.chdir(originalCwd);
    }
  }

  async analyzeDifferences() {
    console.log('🔍 Analyzing component differences...');
    
    // Compare key directories
    await this.compareDirectory('src/components');
    await this.compareDirectory('src/services');
    await this.compareDirectory('src/hooks');
    await this.compareDirectory('src/pages');
    
    // Compare configuration files
    await this.compareConfigFiles();
  }

  async compareDirectory(dirPath) {
    const otherDir = path.join(this.tempDir, dirPath);
    const currentDir = path.join(this.currentRepo, dirPath);
    
    if (!fs.existsSync(otherDir)) {
      console.log(`⚠️  Directory ${dirPath} not found in other repo`);
      return;
    }
    
    if (!fs.existsSync(currentDir)) {
      console.log(`📁 New directory found: ${dirPath}`);
      // Analyze all files in new directory
      this.analyzeNewDirectory(otherDir, dirPath);
      return;
    }
    
    // Compare files in directory
    const otherFiles = this.getJsFiles(otherDir);
    const currentFiles = this.getJsFiles(currentDir);
    
    otherFiles.forEach(file => {
      const relativePath = path.relative(otherDir, file);
      const currentFile = path.join(currentDir, relativePath);
      
      if (!fs.existsSync(currentFile)) {
        // New file
        this.analyzeNewFile(file, path.join(dirPath, relativePath));
      } else {
        // Compare existing file
        this.compareFiles(file, currentFile, path.join(dirPath, relativePath));
      }
    });
  }

  analyzeNewFile(filePath, relativePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const analysis = this.analyzeFileContent(content);
    
    const fileInfo = {
      path: relativePath,
      size: content.length,
      lines: content.split('\n').length,
      analysis: analysis,
      recommendation: this.getRecommendation(analysis)
    };
    
    if (relativePath.includes('components')) {
      this.analysis.findings.newComponents.push(fileInfo);
    } else if (relativePath.includes('services')) {
      this.analysis.findings.newServices.push(fileInfo);
    }
  }

  compareFiles(otherFile, currentFile, relativePath) {
    const otherContent = fs.readFileSync(otherFile, 'utf8');
    const currentContent = fs.readFileSync(currentFile, 'utf8');
    
    if (otherContent === currentContent) {
      return; // Files are identical
    }
    
    const otherAnalysis = this.analyzeFileContent(otherContent);
    const currentAnalysis = this.analyzeFileContent(currentContent);
    
    const enhancement = {
      path: relativePath,
      sizeDiff: otherContent.length - currentContent.length,
      linesDiff: otherContent.split('\n').length - currentContent.split('\n').length,
      otherAnalysis: otherAnalysis,
      currentAnalysis: currentAnalysis,
      recommendation: this.getEnhancementRecommendation(otherAnalysis, currentAnalysis)
    };
    
    this.analysis.findings.enhancedComponents.push(enhancement);
  }

  analyzeFileContent(content) {
    return {
      hasModernReact: /use[A-Z]\w+|useState|useEffect|useCallback|useMemo/.test(content),
      hasVapiIntegration: /vapi|Vapi/.test(content),
      hasAssistantFeatures: /assistant|Assistant/.test(content),
      hasMCPIntegration: /mcp|MCP/.test(content),
      hasServicePattern: /class \w+Service|export.*Service/.test(content),
      hasErrorHandling: /try\s*{|catch\s*\(|\.catch\(/.test(content),
      hasAsyncPatterns: /async|await|Promise/.test(content),
      hasOAuthIntegration: /oauth|OAuth|auth\./.test(content),
      hasConfigManagement: /config|Config|\.env/.test(content),
      linesOfCode: content.split('\n').length,
      complexity: this.calculateComplexity(content)
    };
  }

  calculateComplexity(content) {
    // Simple complexity metric based on control structures
    const complexityPatterns = [
      /if\s*\(/g,
      /for\s*\(/g,
      /while\s*\(/g,
      /switch\s*\(/g,
      /catch\s*\(/g,
      /function\s+\w+/g,
      /=>\s*{/g
    ];
    
    return complexityPatterns.reduce((total, pattern) => {
      const matches = content.match(pattern);
      return total + (matches ? matches.length : 0);
    }, 0);
  }

  getRecommendation(analysis) {
    let score = 0;
    let reasons = [];
    
    if (analysis.hasModernReact) { score += 3; reasons.push('Modern React patterns'); }
    if (analysis.hasVapiIntegration) { score += 4; reasons.push('Vapi integration'); }
    if (analysis.hasAssistantFeatures) { score += 3; reasons.push('Assistant features'); }
    if (analysis.hasMCPIntegration) { score += 4; reasons.push('MCP integration'); }
    if (analysis.hasServicePattern) { score += 2; reasons.push('Service architecture'); }
    if (analysis.hasErrorHandling) { score += 2; reasons.push('Error handling'); }
    if (analysis.hasAsyncPatterns) { score += 1; reasons.push('Async patterns'); }
    
    if (score >= 8) {
      return { level: 'HIGH_VALUE', score, reasons: reasons.join(', ') };
    } else if (score >= 4) {
      return { level: 'MEDIUM_VALUE', score, reasons: reasons.join(', ') };
    } else {
      return { level: 'LOW_VALUE', score, reasons: reasons.join(', ') };
    }
  }

  getEnhancementRecommendation(otherAnalysis, currentAnalysis) {
    const otherRec = this.getRecommendation(otherAnalysis);
    const currentRec = this.getRecommendation(currentAnalysis);
    
    if (otherRec.score > currentRec.score) {
      return {
        level: 'WORTH_REVIEWING',
        improvement: otherRec.score - currentRec.score,
        newFeatures: otherRec.reasons,
        reason: 'Enhanced version with better patterns/features'
      };
    } else if (otherAnalysis.linesOfCode > currentAnalysis.linesOfCode * 1.2) {
      return {
        level: 'SIGNIFICANT_ADDITIONS',
        improvement: 0,
        reason: 'Substantial code additions'
      };
    } else {
      return {
        level: 'MINOR_CHANGES',
        improvement: 0,
        reason: 'Minor modifications or refactoring'
      };
    }
  }

  async compareConfigFiles() {
    console.log('⚙️  Comparing configuration files...');
    
    const configFiles = [
      'package.json',
      'vite.config.js',
      '.env.example',
      'vercel.json'
    ];
    
    configFiles.forEach(file => {
      const otherFile = path.join(this.tempDir, file);
      const currentFile = path.join(this.currentRepo, file);
      
      if (fs.existsSync(otherFile) && fs.existsSync(currentFile)) {
        const otherContent = fs.readFileSync(otherFile, 'utf8');
        const currentContent = fs.readFileSync(currentFile, 'utf8');
        
        if (otherContent !== currentContent) {
          this.analysis.findings.configChanges.push({
            file: file,
            hasChanges: true,
            recommendation: this.analyzeConfigChange(file, otherContent, currentContent)
          });
        }
      } else if (fs.existsSync(otherFile)) {
        this.analysis.findings.configChanges.push({
          file: file,
          isNew: true,
          recommendation: 'NEW_CONFIG_FILE'
        });
      }
    });
  }

  analyzeConfigChange(filename, otherContent, currentContent) {
    if (filename === 'package.json') {
      try {
        const otherPkg = JSON.parse(otherContent);
        const currentPkg = JSON.parse(currentContent);
        
        const newDeps = Object.keys(otherPkg.dependencies || {})
          .filter(dep => !currentPkg.dependencies?.[dep]);
        
        const newDevDeps = Object.keys(otherPkg.devDependencies || {})
          .filter(dep => !currentPkg.devDependencies?.[dep]);
        
        if (newDeps.length > 0 || newDevDeps.length > 0) {
          return `NEW_DEPENDENCIES: ${[...newDeps, ...newDevDeps].join(', ')}`;
        }
      } catch (e) {
        return 'PACKAGE_JSON_CHANGES';
      }
    }
    
    return 'CONFIG_UPDATED';
  }

  generateRecommendations() {
    console.log('🎯 Generating merge recommendations...');
    
    // High-value items
    const highValueItems = [
      ...this.analysis.findings.newComponents.filter(c => c.recommendation.level === 'HIGH_VALUE'),
      ...this.analysis.findings.newServices.filter(s => s.recommendation.level === 'HIGH_VALUE'),
      ...this.analysis.findings.enhancedComponents.filter(e => e.recommendation.level === 'WORTH_REVIEWING')
    ];
    
    this.analysis.findings.worthMerging = highValueItems.map(item => ({
      ...item,
      mergeStrategy: this.getMergeStrategy(item)
    }));
  }

  getMergeStrategy(item) {
    if (item.recommendation?.level === 'HIGH_VALUE') {
      return 'DIRECT_COPY - High value, modern patterns';
    } else if (item.recommendation?.level === 'WORTH_REVIEWING') {
      return 'SELECTIVE_MERGE - Review and merge specific improvements';
    } else {
      return 'MANUAL_REVIEW - Requires careful analysis';
    }
  }

  exportResults() {
    const reportPath = 'git-merge-analysis.json';
    fs.writeFileSync(reportPath, JSON.stringify(this.analysis, null, 2));
    
    console.log('\n✅ GIT ANALYSIS COMPLETE!');
    console.log(`📄 Report exported to: ${reportPath}`);
    
    this.printSummary();
  }

  printSummary() {
    console.log('\n📊 ANALYSIS SUMMARY:');
    console.log(`  📦 New Components: ${this.analysis.findings.newComponents.length}`);
    console.log(`  🛠️  New Services: ${this.analysis.findings.newServices.length}`);
    console.log(`  🔄 Enhanced Components: ${this.analysis.findings.enhancedComponents.length}`);
    console.log(`  ⚙️  Config Changes: ${this.analysis.findings.configChanges.length}`);
    
    console.log('\n🎯 WORTH MERGING:');
    if (this.analysis.findings.worthMerging.length === 0) {
      console.log('  ✨ No high-value items found');
    } else {
      this.analysis.findings.worthMerging.forEach(item => {
        console.log(`  🔥 ${item.path}`);
        console.log(`     ${item.mergeStrategy}`);
        if (item.recommendation?.reasons) {
          console.log(`     Features: ${item.recommendation.reasons}`);
        }
      });
    }
    
    console.log('\n📋 NEXT STEPS:');
    console.log('  1. Review git-merge-analysis.json for detailed findings');
    console.log('  2. Use git commands to selectively merge high-value items');
    console.log('  3. Test integration without breaking existing patterns');
  }

  cleanup() {
    if (fs.existsSync(this.tempDir)) {
      this.execCommand(`rm -rf "${this.tempDir}"`);
    }
  }

  execCommand(command) {
    try {
      return execSync(command, { encoding: 'utf8', stdio: 'pipe' }).trim();
    } catch (error) {
      throw new Error(`Command failed: ${command}\n${error.message}`);
    }
  }

  getJsFiles(dir) {
    const files = [];
    
    function scanDir(currentDir) {
      const items = fs.readdirSync(currentDir, { withFileTypes: true });
      
      items.forEach(item => {
        const fullPath = path.join(currentDir, item.name);
        
        if (item.isDirectory() && !item.name.startsWith('.')) {
          scanDir(fullPath);
        } else if (item.isFile() && (item.name.endsWith('.js') || item.name.endsWith('.jsx'))) {
          files.push(fullPath);
        }
      });
    }
    
    if (fs.existsSync(dir)) {
      scanDir(dir);
    }
    
    return files;
  }

  analyzeNewDirectory(dirPath, relativePath) {
    const files = this.getJsFiles(dirPath);
    
    files.forEach(file => {
      const fileRelativePath = path.relative(this.tempDir, file);
      this.analyzeNewFile(file, fileRelativePath);
    });
  }
}

// CLI usage
const args = process.argv.slice(2);
let repoSource, branch;

if (args.includes('--branch')) {
  const branchIndex = args.indexOf('--branch');
  branch = args[branchIndex + 1];
  repoSource = args[args.length - 1];
} else {
  repoSource = args[0];
  branch = 'main';
}

if (repoSource) {
  const analyzer = new GitSelectiveMerge(repoSource, { branch });
  
  analyzer.analyze().catch(error => {
    console.error('❌ Analysis failed:', error.message);
    process.exit(1);
  });
} else {
  console.log('Usage:');
  console.log('  node git-selective-merge.js <git-repo-url>');
  console.log('  node git-selective-merge.js <local-repo-path>');
  console.log('  node git-selective-merge.js --branch <branch-name> <repo>');
  console.log('\nExamples:');
  console.log('  node git-selective-merge.js https://github.com/user/repo.git');
  console.log('  node git-selective-merge.js ../other-legalscout-repo');
  console.log('  node git-selective-merge.js --branch feature-branch ./other-repo');
}
