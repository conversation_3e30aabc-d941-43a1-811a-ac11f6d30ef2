﻿import { createClient } from '@supabase/supabase-js';
import { createStubClient } from '../utils/mockSupabase.js';

// Get environment variables with fallbacks
const getSupabaseUrl = () => {
  return import.meta.env.VITE_SUPABASE_URL ||
         'https://utopqxsvudgrtiwenlzl.supabase.co';
};

const getSupabaseKey = () => {
  return import.meta.env.VITE_SUPABASE_KEY ||
         import.meta.env.VITE_SUPABASE_ANON_KEY ||
         'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
};

// Create Supabase client with browser-ready initialization
let supabaseClient = null;
let initializationAttempted = false;

const initializeSupabaseClient = () => {
  if (initializationAttempted) {
    return supabaseClient;
  }

  initializationAttempted = true;

  try {
    const url = getSupabaseUrl();
    const key = getSupabaseKey();

    console.log('🔧 [Supabase] Initializing real Supabase client...');
    supabaseClient = createClient(url, key);
    console.log('✅ [Supabase] Real client initialized successfully');
    return supabaseClient;
  } catch (error) {
    console.error('❌ [Supabase] Failed to initialize real client, using stub:', error);
    supabaseClient = createStubClient();
    return supabaseClient;
  }
};

// Create real Supabase client for authentication operations
const createRealSupabaseClient = async () => {
  try {
    const url = getSupabaseUrl();
    const key = getSupabaseKey();

    console.log('Creating real Supabase client for authentication...');

    // SIMPLE: Just create the client with default config
    const realClient = createClient(url, key);
    console.log('Real Supabase client created successfully');
    return realClient;
  } catch (error) {
    console.error('Error creating real Supabase client:', error);
    throw error;
  }
};

export const getSupabaseClient = async () => {
  if (!supabaseClient) {
    return initializeSupabaseClient();
  }
  return supabaseClient;
};

// Get real Supabase client for authentication operations (bypasses stub)
export const getRealSupabaseClient = async () => {
  return createRealSupabaseClient();
};

export const supabase = new Proxy({}, {
  get(target, prop) {
    const client = supabaseClient || initializeSupabaseClient();
    if (!client || typeof client[prop] === 'undefined') {
      console.warn('Supabase client not available, using stub');
      return createStubClient()[prop];
    }
    return client[prop];
  }
});

// EMERGENCY: Direct authentication that bypasses broken Supabase client
export const emergencyAuth = {
  signInWithGoogle: async () => {
    try {
      console.log('🚨 [EmergencyAuth] Starting direct Google OAuth...');
      console.log('🚨 [EmergencyAuth] Current origin:', window.location.origin);

      const supabaseUrl = getSupabaseUrl();
      const supabaseKey = getSupabaseKey();
      // Use current origin (port-agnostic)
      const redirectUrl = `${window.location.origin}/auth/callback`;
      console.log('🚨 [EmergencyAuth] Redirect URL:', redirectUrl);

      const oauthUrl = `${supabaseUrl}/auth/v1/authorize?` + new URLSearchParams({
        provider: 'google',
        redirect_to: redirectUrl,
        apikey: supabaseKey
      });

      console.log('🚨 [EmergencyAuth] Redirecting to:', oauthUrl);
      window.location.href = oauthUrl;
      return { data: { url: oauthUrl }, error: null };
    } catch (error) {
      console.error('🚨 [EmergencyAuth] Error:', error);
      throw error;
    }
  },

  getCurrentUser: async () => {
    try {
      console.log('🚨 [EmergencyAuth] Getting current user...');

      // Check if we're on the callback URL with tokens
      const urlParams = new URLSearchParams(window.location.search);
      const hashParams = new URLSearchParams(window.location.hash.substring(1));

      // Look for access token in URL (OAuth callback)
      const accessToken = urlParams.get('access_token') || hashParams.get('access_token');
      const refreshToken = urlParams.get('refresh_token') || hashParams.get('refresh_token');

      if (accessToken) {
        console.log('🚨 [EmergencyAuth] Found access token in URL, getting user data...');

        const supabaseUrl = getSupabaseUrl();
        const response = await fetch(`${supabaseUrl}/auth/v1/user`, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'apikey': getSupabaseKey()
          }
        });

        if (response.ok) {
          const user = await response.json();
          console.log('🚨 [EmergencyAuth] Got user from OAuth callback:', user.email);

          // Store tokens for future use
          localStorage.setItem('supabase.auth.token', JSON.stringify({
            access_token: accessToken,
            refresh_token: refreshToken,
            user: user
          }));

          // Clean up URL
          window.history.replaceState({}, document.title, window.location.pathname);

          return { user, error: null };
        }
      }

      // Try to get user from stored token
      const storedAuth = localStorage.getItem('supabase.auth.token');
      if (storedAuth) {
        try {
          const authData = JSON.parse(storedAuth);
          console.log('🚨 [EmergencyAuth] Found stored auth, verifying...');

          const supabaseUrl = getSupabaseUrl();
          const response = await fetch(`${supabaseUrl}/auth/v1/user`, {
            headers: {
              'Authorization': `Bearer ${authData.access_token}`,
              'apikey': getSupabaseKey()
            }
          });

          if (response.ok) {
            const user = await response.json();
            console.log('🚨 [EmergencyAuth] Verified stored auth for:', user.email);
            return { user, error: null };
          } else {
            console.log('🚨 [EmergencyAuth] Stored token expired, clearing...');
            localStorage.removeItem('supabase.auth.token');
          }
        } catch (parseError) {
          console.error('🚨 [EmergencyAuth] Error parsing stored auth:', parseError);
          localStorage.removeItem('supabase.auth.token');
        }
      }

      console.log('🚨 [EmergencyAuth] No valid authentication found');
      return { user: null, error: 'Not authenticated' };
    } catch (error) {
      console.error('🚨 [EmergencyAuth] Error getting user:', error);
      return { user: null, error: error.message };
    }
  }
};

export const signInWithGoogle = async () => {
  console.log('🔐 [Supabase] Starting Google sign-in with emergency auth...');

  // Use emergency auth directly since the regular client is stubbed
  return await emergencyAuth.signInWithGoogle();
};

// Make emergency auth globally available for console testing
if (typeof window !== 'undefined') {
  window.emergencyAuth = emergencyAuth;
  console.log('🚨 [EmergencyAuth] Available globally as window.emergencyAuth');
}

export const isSupabaseConfigured = () => {
  const url = getSupabaseUrl();
  const key = getSupabaseKey();
  return !!(url && key && url !== 'your-supabase-url' && key !== 'your-anon-key');
};

export { getSupabaseUrl, getSupabaseKey };
