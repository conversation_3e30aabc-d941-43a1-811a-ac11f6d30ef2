eventTrap.bundle.js:26 🚀 [LegalScout] Initializing environment...
eventTrap.bundle.js:48 ✅ [LegalScout] Environment initialized
companionBubble.js:137 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
assistantDataService.js:224 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
main.jsx:111 [vite] connecting...
main.jsx:111 [vite] connected.
384.js:200 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
AssistantAwareShare.css:30 [VapiLoader] Starting Vapi SDK loading process
AssistantAwareShare.css:34 [VapiLoader] Attempting to import @vapi-ai/web package
defaultTemplates.js:94 [VapiMcpService] Created clean fetch from iframe
defaultTemplates.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized Object
CallsTab.jsx:7 [VapiAssistantService Constructor] Attempting to load secret<PERSON>ey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
ConsultationCardView.jsx:219 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
ConsultationCardView.jsx:12 [AttorneyProfileManager] Auto-initializing from localStorage
ConsultationCardView.jsx:18 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
overlay.ts:9 🚀 [LegalScout] Starting React app...
overlay.ts:9 🔥 [main.jsx] Environment variables check: Object
overlay.ts:9 🔥 [main.jsx] Critical imports check: Object
overlay.ts:9 ✅ [LegalScout] React app rendered successfully
leaflet.css:19 [ErrorBoundary] Creating React placeholder
leaflet.css:23 [ErrorBoundary] Adding createContext placeholder
leaflet.css:55 [ErrorBoundary] Adding useState placeholder
leaflet.css:55 [ErrorBoundary] Adding useEffect placeholder
leaflet.css:55 [ErrorBoundary] Adding useLayoutEffect placeholder
leaflet.css:55 [ErrorBoundary] Adding useRef placeholder
leaflet.css:55 [ErrorBoundary] Adding useCallback placeholder
leaflet.css:55 [ErrorBoundary] Adding useMemo placeholder
leaflet.css:55 [ErrorBoundary] Adding useContext placeholder
leaflet.css:55 [ErrorBoundary] Adding forwardRef placeholder
leaflet.css:55 [ErrorBoundary] Adding createElement placeholder
leaflet.css:55 [ErrorBoundary] Adding cloneElement placeholder
leaflet.css:55 [ErrorBoundary] Adding createRef placeholder
leaflet.css:55 [ErrorBoundary] Adding Component placeholder
leaflet.css:55 [ErrorBoundary] Adding PureComponent placeholder
leaflet.css:55 [ErrorBoundary] Adding Fragment placeholder
leaflet.css:55 [ErrorBoundary] Adding Children placeholder
leaflet.css:55 [ErrorBoundary] Adding isValidElement placeholder
leaflet.css:61 [ErrorBoundary] React polyfills applied
prepareInjection.js:1 [ErrorBoundary] React polyfills applied
react_jsx-dev-runtime.js?v=7a56b667:457 🔥 [App.jsx] App component is starting!
react_jsx-dev-runtime.js?v=7a56b667:460 🔥 [App.jsx] Auth state: Object
prepareInjection.js:1 🔥 [App.jsx] App component is starting!
prepareInjection.js:1 🔥 [App.jsx] Auth state: Object
react-toastify.js?v=7a56b667:63 🔐 [AuthContext] Starting auth initialization...
react-toastify.js?v=7a56b667:67 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
384.js:31 🔧 [Supabase] Initializing real Supabase client...
384.js:33 ✅ [Supabase] Real client initialized successfully
react-toastify.js?v=7a56b667:356 Using real authentication in all environments
react-toastify.js?v=7a56b667:63 🔐 [AuthContext] Starting auth initialization...
react-toastify.js?v=7a56b667:67 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
react-toastify.js?v=7a56b667:356 Using real authentication in all environments
AssistantAwareShare.css:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
AssistantAwareShare.css:51 [VapiLoader] ✅ Vapi SDK validation successful
react-toastify.js?v=7a56b667:149 Auth state changed: SIGNED_IN
react-toastify.js?v=7a56b667:151 OAuth user data (auth change): Object
react-toastify.js?v=7a56b667:152 OAuth user data details (auth change): Object
react-toastify.js?v=7a56b667:161 Found OAuth email (auth change): <EMAIL>
react-toastify.js?v=7a56b667:170 AuthContext: Handling auth state change for event: SIGNED_IN
VM88:116 SyncContext: Handling auth state for action: login
VM88:117 SyncContext: Auth data: Object
EnhancedVapiAssistantManager.js:69 Loaded contentScript
dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
VM88:137 SyncContext: Auth state result: Object
CallDebugPanel.jsx:292 Development mode: Using mock consistency check result
react-toastify.js?v=7a56b667:175 AuthContext: Auth state sync result: Object
react-toastify.js?v=7a56b667:82 🔐 [AuthContext] OAuth user data: Object
react-toastify.js?v=7a56b667:83 🔐 [AuthContext] OAuth user data details: Object
react-toastify.js?v=7a56b667:92 🔐 [AuthContext] Found OAuth email: <EMAIL>
react-toastify.js?v=7a56b667:101 🔐 [AuthContext] Handling auth state for refresh...
VM88:116 SyncContext: Handling auth state for action: refresh
VM88:117 SyncContext: Auth data: Object
react-toastify.js?v=7a56b667:82 🔐 [AuthContext] OAuth user data: Object
react-toastify.js?v=7a56b667:83 🔐 [AuthContext] OAuth user data details: Object
react-toastify.js?v=7a56b667:92 🔐 [AuthContext] Found OAuth email: <EMAIL>
react-toastify.js?v=7a56b667:101 🔐 [AuthContext] Handling auth state for refresh...
VM88:116 SyncContext: Handling auth state for action: refresh
VM88:117 SyncContext: Auth data: Object
react-toastify.js?v=7a56b667:149 Auth state changed: INITIAL_SESSION
react-toastify.js?v=7a56b667:151 OAuth user data (auth change): Object
react-toastify.js?v=7a56b667:152 OAuth user data details (auth change): Object
react-toastify.js?v=7a56b667:161 Found OAuth email (auth change): <EMAIL>
VM88:137 SyncContext: Auth state result: Object
CallDebugPanel.jsx:292 Development mode: Using mock consistency check result
VM88:137 SyncContext: Auth state result: Object
CallDebugPanel.jsx:292 Development mode: Using mock consistency check result
react-toastify.js?v=7a56b667:110 🔐 [AuthContext] Auth state sync result: Object
react-toastify.js?v=7a56b667:139 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
ConsultationCardView.jsx:126 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
react-toastify.js?v=7a56b667:110 🔐 [AuthContext] Auth state sync result: Object
react-toastify.js?v=7a56b667:139 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
ConsultationCardView.jsx:20 [AttorneyProfileManager] Refreshed attorney data for auto-sync: Object
ConsultationCardView.jsx:219 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
ConsultationCardView.jsx:23 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
EnhancedVapiAssistantManager.js:28 received intentional event
EnhancedVapiAssistantManager.js:28 updating page active status
EnhancedVapiAssistantManager.js:28 received intentional event
EnhancedVapiAssistantManager.js:28 received intentional event
EnhancedVapiAssistantManager.js:28 updating page active status
EnhancedVapiAssistantManager.js:28 received intentional event
schemaGenerator.js:3674 🔐 [App] Starting Google sign-in...
384.js:191 🔐 [Supabase] Starting Google sign-in with emergency auth...
384.js:87 🚨 [EmergencyAuth] Starting direct Google OAuth...
384.js:88 🚨 [EmergencyAuth] Current origin: http://localhost:5175
384.js:94 🚨 [EmergencyAuth] Redirect URL: http://localhost:5175/auth/callback
384.js:102 🚨 [EmergencyAuth] Redirecting to: https://utopqxsvudgrtiwenlzl.supabase.co/auth/v1/authorize?provider=google&redirect_to=http%3A%2F%2Flocalhost%3A5175%2Fauth%2Fcallback&apikey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU
Navigated to https://accounts.google.com/o/oauth2/v2/auth?apikey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU&client_id=************-0b877bga5m3retigi7ntb3nufq6csolo.apps.googleusercontent.com&redirect_to=http%3A%2F%2Flocalhost%3A5175%2Fauth%2Fcallback&redirect_uri=https%3A%2F%2Futopqxsvudgrtiwenlzl.supabase.co%2Fauth%2Fv1%2Fcallback&response_type=code&scope=email+profile&state=eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09WK3BUamYiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************.XexUnwYAs63NxxgDTt9TSq5lI-CuAAHgI5gyBz-XSBs

                
          
          
          
         Chrome is moving towards a new experience that allows users to choose to browse without third-party cookies.
chunk-Q72EVS5P.js?v=7a56b667:69 Loaded contentScript
App.css:126 ChatGPT Assistant content script loaded
chunk-Q72EVS5P.js?v=7a56b667:28 received intentional event
chunk-Q72EVS5P.js?v=7a56b667:28 updating page active status
Navigated to http://localhost:5175/auth/callback
callback:26 🚀 [LegalScout] Initializing environment...
callback:48 ✅ [LegalScout] Environment initialized
callback:137 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
callback:224 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
client.ts:19 [vite] connecting...
client.ts:155 [vite] connected.
supabase.js:200 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js:1482 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
main.jsx:26 🚀 [LegalScout] Starting React app...
main.jsx:42 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
main.jsx:57 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
main.jsx:88 ✅ [LegalScout] React app rendered successfully
ProductionErrorBoundary.jsx:22 [ErrorBoundary] Creating React placeholder
ProductionErrorBoundary.jsx:28 [ErrorBoundary] Adding createContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useState placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useLayoutEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useCallback placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useMemo placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding forwardRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding cloneElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Component placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding PureComponent placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Fragment placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Children placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding isValidElement placeholder
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
App.jsx:811 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:841 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
App.jsx:811 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:841 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
AssistantAwareContext.jsx:32 🔍 [AssistantAwareContext] Current assistant ID: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, resolved: undefined, attorneyId: undefined}
AssistantAwareContext.jsx:112 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: undefined, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false}
AssistantAwareContext.jsx:122 ⚠️ [AssistantAwareContext] No assistant selected or no assistant subdomain - using placeholder
AssistantAwareContext.jsx:32 🔍 [AssistantAwareContext] Current assistant ID: {forceAssistantId: null, current_assistant_id: undefined, vapi_assistant_id: undefined, resolved: undefined, attorneyId: undefined}
AssistantAwareContext.jsx:112 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: undefined, assistantSubdomain: null, attorneySubdomain: undefined, hasAssistantSelected: false}
AssistantAwareContext.jsx:122 ⚠️ [AssistantAwareContext] No assistant selected or no assistant subdomain - using placeholder
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
AuthCallback.jsx:15 🔐 [AuthCallback] Starting OAuth callback handling...
AuthCallback.jsx:34 🔐 [AuthCallback] Checking for OAuth tokens...
AuthCallback.jsx:35 🔐 [AuthCallback] Hash params: #access_token=eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09WK3BUamYiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1JhYAk-A0IsUPVIWTzE7NP4aYgjzqhISDs26ohrK5zI&expires_at=**********&expires_in=3600&provider_token=*********************************************************************************************************************************************************************************************************************************&refresh_token=5idugkygdcq6&token_type=bearer
AuthCallback.jsx:36 🔐 [AuthCallback] Query params: 
AuthCallback.jsx:37 🔐 [AuthCallback] Access token found: true
AuthCallback.jsx:70 🔐 [AuthCallback] Found OAuth tokens in URL
AuthCallback.jsx:91 🔐 [AuthCallback] Processing authentication for: <EMAIL>
AuthCallback.jsx:94 🔧 [AuthCallback] Calling fixAuthProfile...
authProfileFixer.js:17 [AuthProfileFixer] Starting profile fix for user: <EMAIL>
authProfileFixer.js:18 [AuthProfileFixer] User ID: 571390ac-5a83-46b2-ad3a-18b9cf39d701
authProfileFixer.js:19 [AuthProfileFixer] Access token length: 1297
authProfileFixer.js:32 [AuthProfileFixer] API Config: {baseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co', hasAuth: true, hasApiKey: true}
authProfileFixer.js:40 [AuthProfileFixer] Step 1: Looking for existing attorney profile...
authProfileFixer.js:218 [AuthProfileFixer] 🔍 Searching for existing attorney profile...
authProfileFixer.js:222 [AuthProfileFixer] 🔍 Method 1: Searching by user_id: 571390ac-5a83-46b2-ad3a-18b9cf39d701
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:34 [useStandaloneAttorney] Setting up retry mechanism for manager...
useStandaloneAttorney.js:42 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-16T15:24:59.301937+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
AssistantAwareContext.jsx:44 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: undefined, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
AssistantAwareContext.jsx:55 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
App.jsx:624 🚀 [App] Initializing with safe subdomain detection...
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:79 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
supabase.js:31 🔧 [Supabase] Initializing real Supabase client...
supabase.js:33 ✅ [Supabase] Real client initialized successfully
AuthContext.jsx:485 Using real authentication in all environments
AuthCallback.jsx:15 🔐 [AuthCallback] Starting OAuth callback handling...
AuthCallback.jsx:34 🔐 [AuthCallback] Checking for OAuth tokens...
AuthCallback.jsx:35 🔐 [AuthCallback] Hash params: #access_token=eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09WK3BUamYiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1JhYAk-A0IsUPVIWTzE7NP4aYgjzqhISDs26ohrK5zI&expires_at=**********&expires_in=3600&provider_token=*********************************************************************************************************************************************************************************************************************************&refresh_token=5idugkygdcq6&token_type=bearer
AuthCallback.jsx:36 🔐 [AuthCallback] Query params: 
AuthCallback.jsx:37 🔐 [AuthCallback] Access token found: true
AuthCallback.jsx:70 🔐 [AuthCallback] Found OAuth tokens in URL
AuthCallback.jsx:91 🔐 [AuthCallback] Processing authentication for: <EMAIL>
AuthCallback.jsx:94 🔧 [AuthCallback] Calling fixAuthProfile...
authProfileFixer.js:17 [AuthProfileFixer] Starting profile fix for user: <EMAIL>
authProfileFixer.js:18 [AuthProfileFixer] User ID: 571390ac-5a83-46b2-ad3a-18b9cf39d701
authProfileFixer.js:19 [AuthProfileFixer] Access token length: 1297
authProfileFixer.js:32 [AuthProfileFixer] API Config: {baseUrl: 'https://utopqxsvudgrtiwenlzl.supabase.co', hasAuth: true, hasApiKey: true}
authProfileFixer.js:40 [AuthProfileFixer] Step 1: Looking for existing attorney profile...
authProfileFixer.js:218 [AuthProfileFixer] 🔍 Searching for existing attorney profile...
authProfileFixer.js:222 [AuthProfileFixer] 🔍 Method 1: Searching by user_id: 571390ac-5a83-46b2-ad3a-18b9cf39d701
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:34 [useStandaloneAttorney] Setting up retry mechanism for manager...
useStandaloneAttorney.js:42 [useStandaloneAttorney] Using stored attorney as fallback: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-16T15:24:59.301937+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
AssistantAwareContext.jsx:44 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: undefined, attorneyId: undefined, hasCurrentAssistant: false, hasAttorney: false}
AssistantAwareContext.jsx:55 ⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney
App.jsx:624 🚀 [App] Initializing with safe subdomain detection...
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:79 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
AuthContext.jsx:485 Using real authentication in all environments
AssistantAwareContext.jsx:32 🔍 [AssistantAwareContext] Current assistant ID: {forceAssistantId: null, current_assistant_id: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', vapi_assistant_id: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', resolved: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71'}
AssistantAwareContext.jsx:112 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true}
AssistantAwareContext.jsx:122 ⚠️ [AssistantAwareContext] No assistant selected or no assistant subdomain - using placeholder
AssistantAwareContext.jsx:32 🔍 [AssistantAwareContext] Current assistant ID: {forceAssistantId: null, current_assistant_id: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', vapi_assistant_id: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', resolved: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71'}
AssistantAwareContext.jsx:112 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', assistantSubdomain: null, attorneySubdomain: 'damon', hasAssistantSelected: true}
AssistantAwareContext.jsx:122 ⚠️ [AssistantAwareContext] No assistant selected or no assistant subdomain - using placeholder
index.ts:5 Loaded contentScript
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
AssistantAwareContext.jsx:44 🔄 [AssistantAwareContext] useEffect triggered: {currentAssistantId: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', attorneyId: '87756a2c-a398-43f2-889a-b8815684df71', hasCurrentAssistant: true, hasAttorney: true}
AssistantAwareContext.jsx:52 ✅ [AssistantAwareContext] Loading assistant data for: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
App.jsx:639 ✅ Production environment initialized
App.jsx:70 Supabase config initialization (fallback)
App.jsx:75 Supabase config verification (fallback)
App.jsx:649 ✅ Supabase configured successfully
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
App.jsx:669 🔍 [App] Subdomain detected: default
App.jsx:677 🏠 [App] Localhost detected - treating as main domain
App.jsx:732 🏁 [App] Initialization complete
productionEnvironment.js:134 🔧 [ProductionEnvironment] Initializing production environment...
productionEnvironment.js:145 ✅ [ProductionEnvironment] Environment variables initialized in window object
App.jsx:639 ✅ Production environment initialized
App.jsx:70 Supabase config initialization (fallback)
App.jsx:75 Supabase config verification (fallback)
App.jsx:649 ✅ Supabase configured successfully
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
App.jsx:669 🔍 [App] Subdomain detected: default
App.jsx:677 🏠 [App] Localhost detected - treating as main domain
App.jsx:732 🏁 [App] Initialization complete
authProfileFixer.js:228 [AuthProfileFixer] 🔍 Method 1 response status: 200
authProfileFixer.js:228 [AuthProfileFixer] 🔍 Method 1 response status: 200
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
App.jsx:811 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:841 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
App.jsx:811 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:841 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
authProfileFixer.js:232 [AuthProfileFixer] 🔍 Method 1 response data length: 1
authProfileFixer.js:234 [AuthProfileFixer] ✅ Found attorney by user_id: <EMAIL>
authProfileFixer.js:44 [AuthProfileFixer] ✅ Found existing attorney profile: {id: '87756a2c-a398-43f2-889a-b8815684df71', email: '<EMAIL>', subdomain: 'damon', firm_name: 'LegalScout', user_id: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
AuthCallback.jsx:96 🔧 [AuthCallback] fixAuthProfile result: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-16T15:24:59.301937+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
AuthCallback.jsx:100 ✅ [AuthCallback] Attorney profile found/created: {id: '87756a2c-a398-43f2-889a-b8815684df71', firm_name: 'LegalScout', subdomain: 'damon', email: '<EMAIL>'}
AuthCallback.jsx:109 💾 [AuthCallback] Attorney profile stored in localStorage
AuthCallback.jsx:122 🚀 [AuthCallback] Redirecting to dashboard: http://localhost:5175/dashboard
authProfileFixer.js:232 [AuthProfileFixer] 🔍 Method 1 response data length: 1
authProfileFixer.js:234 [AuthProfileFixer] ✅ Found attorney by user_id: <EMAIL>
authProfileFixer.js:44 [AuthProfileFixer] ✅ Found existing attorney profile: {id: '87756a2c-a398-43f2-889a-b8815684df71', email: '<EMAIL>', subdomain: 'damon', firm_name: 'LegalScout', user_id: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
AuthCallback.jsx:96 🔧 [AuthCallback] fixAuthProfile result: {id: '87756a2c-a398-43f2-889a-b8815684df71', created_at: '2025-06-06T17:01:56.837565+00:00', updated_at: '2025-06-16T15:24:59.301937+00:00', subdomain: 'damon', firm_name: 'LegalScout', …}
AuthCallback.jsx:100 ✅ [AuthCallback] Attorney profile found/created: {id: '87756a2c-a398-43f2-889a-b8815684df71', firm_name: 'LegalScout', subdomain: 'damon', email: '<EMAIL>'}
AuthCallback.jsx:109 💾 [AuthCallback] Attorney profile stored in localStorage
AuthCallback.jsx:122 🚀 [AuthCallback] Redirecting to dashboard: http://localhost:5175/dashboard
callback#access_token=eyJhbGciOiJIUzI1NiIsImtpZCI6IjBvMER3VDhFU09WK3BUamYiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1JhYAk-A0IsUPVIWTzE7NP4aYgjzqhISDs26ohrK5zI&expires_at=**********&expires_in=3600&provider_token=*********************************************************************************************************************************************************************************************************************************&refresh_token=5idugkygdcq6&token_type=bearer:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
AttorneyProfileManager.js:417 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
App.jsx:811 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:841 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/auth/callback', isAuthenticated: false, hasSession: false}
App.jsx:811 🔍 [App.jsx] ROOT ROUTE DECISION: {isAttorneySubdomain: false, user: false, userEmail: undefined, subdomain: 'default', hostname: 'localhost'}
App.jsx:841 🔍 [App.jsx] REDIRECT DECISION: {hasUser: false, userEmail: undefined, redirectingTo: '/home'}
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
AuthContext.jsx:190 Auth state changed: SIGNED_IN
AuthContext.jsx:193 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:196 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:211 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:224 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
AuthContext.jsx:85 🔐 [AuthContext] ⚠️ FORCING loading to false after timeout
AuthContext.jsx:85 🔐 [AuthContext] ⚠️ FORCING loading to false after timeout
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
AuthContext.jsx:100 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:103 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:118 🔐 [AuthContext] Found OAuth email: <EMAIL>
AuthContext.jsx:131 🔐 [AuthContext] Handling auth state for refresh...
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
AuthContext.jsx:100 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:103 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:118 🔐 [AuthContext] Found OAuth email: <EMAIL>
AuthContext.jsx:131 🔐 [AuthContext] Handling auth state for refresh...
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
AuthContext.jsx:190 Auth state changed: INITIAL_SESSION
AuthContext.jsx:193 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:196 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:211 Found OAuth email (auth change): <EMAIL>
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', assistantSubdomain: 'assistant1test', attorneySubdomain: 'damon', hasAssistantSelected: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://assistant1test.legalscout.net', embedUrl: 'https://assistant1test.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=assistant1test&loadFromS…&assistantId=d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://assistant1test.legalscout.net'}
 🔗 [AssistantAwareContext] Generating URLs: {currentAssistantId: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', assistantSubdomain: 'assistant1test', attorneySubdomain: 'damon', hasAssistantSelected: true}
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: {shareUrl: 'https://assistant1test.legalscout.net', embedUrl: 'https://assistant1test.legalscout.net/embed', previewUrl: '/simple-preview?subdomain=assistant1test&loadFromS…&assistantId=d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d', webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call', baseUrl: 'https://assistant1test.legalscout.net'}
 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Auth state managed successfully'}
 Development mode: Using mock consistency check result
 API error, falling back to client-side implementation: TypeError: Failed to fetch
    at Object.manageAuthState (src/hooks/useSyncTools.js:237:32)
    at Object.handleAuthState (src/contexts/SyncContext.jsx:136:40)
    at initAuth (src/contexts/AuthContext.jsx:102:45)
overrideMethod @ chrome-extension://f…ld/installHook.js:1
(anonymous) @ useSyncTools.js:273
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:102
await in initAuth
(anonymous) @ AuthContext.jsx:143
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
commitPassiveMountOnFiber @ chunk-Q72EVS5P.js:18184
commitPassiveMountEffects_complete @ chunk-Q72EVS5P.js:18157
commitPassiveMountEffects_begin @ chunk-Q72EVS5P.js:18147
commitPassiveMountEffects @ chunk-Q72EVS5P.js:18137
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19518
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Using client-side fallback for auth state management
 API error, falling back to client-side implementation: TypeError: Failed to fetch
    at Object.manageAuthState (src/hooks/useSyncTools.js:237:32)
    at Object.handleAuthState (src/contexts/SyncContext.jsx:136:40)
    at initAuth (src/contexts/AuthContext.jsx:102:45)
overrideMethod @ chrome-extension://f…ld/installHook.js:1
(anonymous) @ useSyncTools.js:273
await in (anonymous)
(anonymous) @ SyncContext.jsx:136
initAuth @ AuthContext.jsx:102
await in initAuth
(anonymous) @ AuthContext.jsx:143
commitHookEffectListMount @ chunk-Q72EVS5P.js:16936
invokePassiveEffectMountInDEV @ chunk-Q72EVS5P.js:18352
invokeEffectsInDev @ chunk-Q72EVS5P.js:19729
commitDoubleInvokeEffectsInDEV @ chunk-Q72EVS5P.js:19714
flushPassiveEffectsImpl @ chunk-Q72EVS5P.js:19531
flushPassiveEffects @ chunk-Q72EVS5P.js:19475
(anonymous) @ chunk-Q72EVS5P.js:19356
workLoop @ chunk-Q72EVS5P.js:197
flushWork @ chunk-Q72EVS5P.js:176
performWorkUntilDeadline @ chunk-Q72EVS5P.js:384
 Using client-side fallback for auth state management
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
 Development mode: Using mock consistency check result
 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Client-side fallback: Session refreshed successfully'}
 Development mode: Using mock consistency check result
 [AttorneyProfileManager] Error loading attorney by id: {message: 'TypeError: Failed to fetch', details: 'TypeError: Failed to fetch\n    at http://localhost…deps/@supabase_supabase-js.js?v=7a56b667:3873:24)', hint: '', code: ''}
overrideMethod @ chrome-extension://f…ld/installHook.js:1
loadAttorneyById @ AttorneyProfileManager.js:435
await in loadAttorneyById
(anonymous) @ AttorneyProfileManager.js:62
setTimeout
autoInitializeFromLocalStorage @ AttorneyProfileManager.js:56
AttorneyProfileManager @ AttorneyProfileManager.js:34
(anonymous) @ AttorneyProfileManager.js:1589
 [AttorneyProfileManager] Could not refresh attorney data for auto-sync: {message: 'TypeError: Failed to fetch', details: 'TypeError: Failed to fetch\n    at http://localhost…deps/@supabase_supabase-js.js?v=7a56b667:3873:24)', hint: '', code: ''}
overrideMethod @ chrome-extension://f…ld/installHook.js:1
(anonymous) @ AttorneyProfileManager.js:73
setTimeout
autoInitializeFromLocalStorage @ AttorneyProfileManager.js:56
AttorneyProfileManager @ AttorneyProfileManager.js:34
(anonymous) @ AttorneyProfileManager.js:1589
 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
dashboard:26 🚀 [LegalScout] Initializing environment...
dashboard:48 ✅ [LegalScout] Environment initialized
dashboard:137 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
dashboard:224 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
client.ts:19 [vite] connecting...
Navigated to http://localhost:5175/dashboard
client.ts:155 [vite] connected.
supabase.js:200 🚨 [EmergencyAuth] Available globally as window.emergencyAuth
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
vapiAssistantService.js:24 [VapiAssistantService Constructor] Attempting to load secretKey from mcpConfig: 6734febc-fc65-4669-93b0-929b31ff6564
AttorneyProfileManager.js:1482 [AttorneyProfileManager] Loaded attorney from localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:42 [AttorneyProfileManager] Auto-initializing from localStorage
AttorneyProfileManager.js:52 [AttorneyProfileManager] Preview mode detected, skipping Realtime subscription
main.jsx:26 🚀 [LegalScout] Starting React app...
main.jsx:42 🔥 [main.jsx] Environment variables check: {NODE_ENV: undefined, DEV: true, PROD: false, VITE_SUPABASE_URL: true, VITE_SUPABASE_KEY: true, …}
main.jsx:57 🔥 [main.jsx] Critical imports check: {React: true, ReactDOM: true, App: true, BrowserRouter: true, ProductionErrorBoundary: true, …}
main.jsx:88 ✅ [LegalScout] React app rendered successfully
ProductionErrorBoundary.jsx:22 [ErrorBoundary] Creating React placeholder
ProductionErrorBoundary.jsx:28 [ErrorBoundary] Adding createContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useState placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useLayoutEffect placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useCallback placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useMemo placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding useContext placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding forwardRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding cloneElement placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding createRef placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Component placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding PureComponent placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Fragment placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding Children placeholder
ProductionErrorBoundary.jsx:47 [ErrorBoundary] Adding isValidElement placeholder
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
ProductionErrorBoundary.jsx:54 [ErrorBoundary] React polyfills applied
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard', isAuthenticated: false, hasSession: false}
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: {user: false, userEmail: undefined, pathname: '/dashboard', isAuthenticated: false, hasSession: false}
vapiLoader.js:41 [VapiLoader] ✅ Successfully loaded Vapi SDK from installed package
vapiLoader.js:51 [VapiLoader] ✅ Vapi SDK validation successful
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:79 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
supabase.js:31 🔧 [Supabase] Initializing real Supabase client...
supabase.js:33 ✅ [Supabase] Real client initialized successfully
AuthContext.jsx:485 Using real authentication in all environments
AuthContext.jsx:72 🔐 [AuthContext] Starting auth initialization...
AuthContext.jsx:79 🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing
AuthContext.jsx:485 Using real authentication in all environments
index.ts:5 Loaded contentScript
AuthContext.jsx:190 Auth state changed: SIGNED_IN
AuthContext.jsx:193 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:196 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:211 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:224 AuthContext: Handling auth state change for event: SIGNED_IN
SyncContext.jsx:178 SyncContext: Handling auth state for action: login
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'login', hasAttorney: true, message: 'Auth state managed successfully'}
useSyncTools.js:559 Development mode: Using mock consistency check result
AttorneyProfileManager.js:417 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
AuthContext.jsx:231 AuthContext: Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Auth state managed successfully'}
AuthContext.jsx:100 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:103 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:118 🔐 [AuthContext] Found OAuth email: <EMAIL>
AuthContext.jsx:131 🔐 [AuthContext] Handling auth state for refresh...
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
AuthContext.jsx:100 🔐 [AuthContext] OAuth user data: {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:103 🔐 [AuthContext] OAuth user data details: {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:118 🔐 [AuthContext] Found OAuth email: <EMAIL>
AuthContext.jsx:131 🔐 [AuthContext] Handling auth state for refresh...
SyncContext.jsx:178 SyncContext: Handling auth state for action: refresh
SyncContext.jsx:179 SyncContext: Auth data: {hasUser: true, hasSession: true, userEmail: '<EMAIL>', userId: '571390ac-5a83-46b2-ad3a-18b9cf39d701'}
AuthContext.jsx:190 Auth state changed: INITIAL_SESSION
AuthContext.jsx:193 OAuth user data (auth change): {id: '571390ac-5a83-46b2-ad3a-18b9cf39d701', aud: 'authenticated', role: 'authenticated', email: '<EMAIL>', email_confirmed_at: '2025-04-21T21:08:15.841729Z', …}
AuthContext.jsx:196 OAuth user data details (auth change): {directEmail: '<EMAIL>', metadataEmail: '<EMAIL>', identityEmail: '<EMAIL>', rawMetadata: {…}, rawAppMetadata: {…}, …}
AuthContext.jsx:211 Found OAuth email (auth change): <EMAIL>
AuthContext.jsx:85 🔐 [AuthContext] ⚠️ FORCING loading to false after timeout
AuthContext.jsx:85 🔐 [AuthContext] ⚠️ FORCING loading to false after timeout
AttorneyProfileManager.js:64 [AttorneyProfileManager] Refreshed attorney data for auto-sync: {id: '87756a2c-a398-43f2-889a-b8815684df71', vapi_assistant_id: 'd1be86a2-49b3-4ff1-b39c-86f41c2b2f3d'}
AttorneyProfileManager.js:1469 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:78 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Auth state managed successfully'}
useSyncTools.js:559 Development mode: Using mock consistency check result
SyncContext.jsx:203 SyncContext: Auth state result: {success: true, action: 'refresh', hasAttorney: true, message: 'Auth state managed successfully'}
useSyncTools.js:559 Development mode: Using mock consistency check result
AuthContext.jsx:145 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Auth state managed successfully'}
AuthContext.jsx:177 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
AuthContext.jsx:145 🔐 [AuthContext] Auth state sync result: {success: true, hasAttorney: true, fallback: false, message: 'Auth state managed successfully'}
AuthContext.jsx:177 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
