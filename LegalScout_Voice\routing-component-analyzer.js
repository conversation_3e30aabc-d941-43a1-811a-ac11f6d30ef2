/**
 * Routing Component Analyzer
 * Deep dive into React Router configuration and component loading
 */

class RoutingComponentAnalyzer {
    constructor() {
        this.findings = {
            reactSetup: {},
            routerConfig: {},
            componentTree: {},
            loadingSequence: [],
            errors: []
        };
    }

    async analyze() {
        console.log('🔬 [RoutingAnalyzer] Starting deep component analysis...');
        
        try {
            await this.analyzeReactSetup();
            await this.analyzeRouterConfig();
            await this.analyzeComponentTree();
            await this.analyzeLoadingSequence();
            await this.generateFindings();
        } catch (error) {
            console.error('❌ [RoutingAnalyzer] Analysis failed:', error);
            this.findings.errors.push(error);
        }
    }

    async analyzeReactSetup() {
        console.log('⚛️ [RoutingAnalyzer] Analyzing React setup...');
        
        const reactSetup = {
            reactExists: !!window.React,
            reactVersion: window.React?.version,
            reactDOMExists: !!window.ReactDOM,
            reactRouterExists: !!window.ReactRouter,
            devMode: process?.env?.NODE_ENV === 'development',
            
            // Check for React root
            reactRoot: this.findReactRoot(),
            
            // Check for React components in DOM
            reactComponents: this.findReactComponents(),
            
            // Check for React Router components
            routerComponents: this.findRouterComponents()
        };

        this.findings.reactSetup = reactSetup;
        console.log('✅ [RoutingAnalyzer] React setup analysis complete');
    }

    findReactRoot() {
        const possibleRoots = [
            document.getElementById('root'),
            document.getElementById('app'),
            document.querySelector('[data-reactroot]'),
            document.querySelector('[data-testid="app-root"]'),
            document.querySelector('.app-root')
        ].filter(Boolean);

        return {
            found: possibleRoots.length > 0,
            elements: possibleRoots.length,
            ids: possibleRoots.map(el => el.id || el.className || el.tagName)
        };
    }

    findReactComponents() {
        // Look for React component indicators
        const indicators = [
            '[data-reactroot]',
            '[data-react-component]',
            '[class*="react-"]',
            '[id*="react-"]'
        ];

        const components = {};
        indicators.forEach(selector => {
            components[selector] = document.querySelectorAll(selector).length;
        });

        return components;
    }

    findRouterComponents() {
        const routerSelectors = [
            '[data-testid*="router"]',
            '[class*="router"]',
            '[class*="route"]',
            'router',
            'route'
        ];

        const found = {};
        routerSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) {
                found[selector] = Array.from(elements).map(el => ({
                    tag: el.tagName,
                    id: el.id,
                    className: el.className,
                    textContent: el.textContent?.substring(0, 50)
                }));
            }
        });

        return found;
    }

    async analyzeRouterConfig() {
        console.log('🛣️ [RoutingAnalyzer] Analyzing router configuration...');
        
        const config = {
            // Check URL structure
            url: {
                full: window.location.href,
                pathname: window.location.pathname,
                search: window.location.search,
                hash: window.location.hash,
                expectedForEnv: this.getExpectedURL()
            },
            
            // Check routing method
            routingMethod: this.detectRoutingMethod(),
            
            // Check for route definitions
            routeDefinitions: this.findRouteDefinitions(),
            
            // Check for navigation elements
            navigation: this.findNavigationElements()
        };

        this.findings.routerConfig = config;
        console.log('✅ [RoutingAnalyzer] Router configuration analysis complete');
    }

    getExpectedURL() {
        const hostname = window.location.hostname;
        const isProduction = hostname.includes('legalscout.net');
        const isLocal = hostname.includes('localhost');
        
        return {
            isProduction,
            isLocal,
            expectedPath: isProduction ? '/' : '/dashboard',
            currentMatches: window.location.pathname === (isProduction ? '/' : '/dashboard')
        };
    }

    detectRoutingMethod() {
        return {
            hashRouting: window.location.hash.includes('#/'),
            browserRouting: !window.location.hash.includes('#/') && window.history?.pushState,
            serverRouting: !window.React, // If no React, likely server-side routing
            
            // Check for specific router implementations
            reactRouter: !!window.ReactRouter,
            reachRouter: !!window.ReachRouter,
            nextRouter: !!window.next?.router
        };
    }

    findRouteDefinitions() {
        // Look for route definitions in various forms
        const routes = {
            // JSX route components (if visible in DOM)
            jsxRoutes: document.querySelectorAll('route, Route').length,
            
            // Data attributes indicating routes
            dataRoutes: Array.from(document.querySelectorAll('[data-route]')).map(el => el.dataset.route),
            
            // Navigation links
            navLinks: Array.from(document.querySelectorAll('a[href]')).map(a => a.href),
            
            // Route-like class names
            routeClasses: Array.from(document.querySelectorAll('[class*="route"]')).map(el => el.className)
        };

        return routes;
    }

    findNavigationElements() {
        return {
            links: Array.from(document.querySelectorAll('a')).map(a => ({
                href: a.href,
                text: a.textContent?.trim(),
                internal: a.href.includes(window.location.hostname)
            })),
            
            buttons: Array.from(document.querySelectorAll('button[onclick], button[data-navigate]')).map(btn => ({
                onclick: btn.onclick?.toString(),
                dataNavigate: btn.dataset.navigate,
                text: btn.textContent?.trim()
            })),
            
            forms: Array.from(document.querySelectorAll('form')).map(form => ({
                action: form.action,
                method: form.method
            }))
        };
    }

    async analyzeComponentTree() {
        console.log('🌳 [RoutingAnalyzer] Analyzing component tree...');
        
        const tree = {
            // Document structure
            documentStructure: this.getDocumentStructure(),
            
            // Main containers
            containers: this.findMainContainers(),
            
            // Conditional rendering indicators
            conditionalElements: this.findConditionalElements(),
            
            // Loading states
            loadingStates: this.findLoadingStates()
        };

        this.findings.componentTree = tree;
        console.log('✅ [RoutingAnalyzer] Component tree analysis complete');
    }

    getDocumentStructure() {
        return {
            title: document.title,
            bodyClasses: document.body.className,
            bodyId: document.body.id,
            headElements: Array.from(document.head.children).map(el => ({
                tag: el.tagName,
                content: el.textContent?.substring(0, 100) || el.outerHTML.substring(0, 100)
            })),
            scriptCount: document.scripts.length,
            styleCount: document.styleSheets.length
        };
    }

    findMainContainers() {
        const containerSelectors = [
            '#root',
            '#app',
            '.app',
            '.main',
            '.container',
            '[data-testid="main-app"]',
            '[data-testid="dashboard"]'
        ];

        const containers = {};
        containerSelectors.forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
                containers[selector] = {
                    exists: true,
                    children: element.children.length,
                    innerHTML: element.innerHTML.length,
                    visible: element.offsetHeight > 0 && element.offsetWidth > 0
                };
            }
        });

        return containers;
    }

    findConditionalElements() {
        // Look for elements that might be conditionally rendered
        return {
            hiddenElements: document.querySelectorAll('[style*="display: none"], .hidden, [hidden]').length,
            emptyElements: Array.from(document.querySelectorAll('div, section, main')).filter(el => 
                el.children.length === 0 && el.textContent.trim() === ''
            ).length,
            loadingElements: document.querySelectorAll('[class*="loading"], [class*="spinner"], [data-testid*="loading"]').length
        };
    }

    findLoadingStates() {
        const loadingIndicators = [
            '[class*="loading"]',
            '[class*="spinner"]',
            '[class*="loader"]',
            '[data-testid*="loading"]',
            '.skeleton'
        ];

        const states = {};
        loadingIndicators.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) {
                states[selector] = Array.from(elements).map(el => ({
                    visible: el.offsetHeight > 0,
                    className: el.className,
                    textContent: el.textContent?.trim()
                }));
            }
        });

        return states;
    }

    async analyzeLoadingSequence() {
        console.log('⏳ [RoutingAnalyzer] Analyzing loading sequence...');
        
        const sequence = {
            domReadyState: document.readyState,
            windowLoaded: document.readyState === 'complete',
            
            // Script loading
            scripts: Array.from(document.scripts).map(script => ({
                src: script.src,
                loaded: script.readyState || 'unknown',
                async: script.async,
                defer: script.defer
            })),
            
            // Resource loading
            resources: this.getResourceLoadingInfo(),
            
            // Timing
            timing: this.getTimingInfo()
        };

        this.findings.loadingSequence = sequence;
        console.log('✅ [RoutingAnalyzer] Loading sequence analysis complete');
    }

    getResourceLoadingInfo() {
        if (!window.performance || !window.performance.getEntriesByType) {
            return { available: false };
        }

        const resources = window.performance.getEntriesByType('resource');
        return {
            available: true,
            total: resources.length,
            scripts: resources.filter(r => r.name.includes('.js')).length,
            styles: resources.filter(r => r.name.includes('.css')).length,
            failed: resources.filter(r => r.transferSize === 0).length
        };
    }

    getTimingInfo() {
        if (!window.performance || !window.performance.timing) {
            return { available: false };
        }

        const timing = window.performance.timing;
        return {
            available: true,
            navigationStart: timing.navigationStart,
            domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
            loadComplete: timing.loadEventEnd - timing.navigationStart,
            domInteractive: timing.domInteractive - timing.navigationStart
        };
    }

    async generateFindings() {
        console.log('📋 [RoutingAnalyzer] Generating findings report...');
        
        const issues = this.identifyIssues();
        const recommendations = this.generateRecommendations(issues);
        
        const report = {
            summary: this.generateSummary(),
            issues,
            recommendations,
            rawFindings: this.findings,
            timestamp: new Date().toISOString()
        };

        // Store globally
        window.routingAnalysis = report;
        
        console.log('🎯 [RoutingAnalyzer] ANALYSIS COMPLETE');
        console.log('📊 Summary:', report.summary);
        console.log('⚠️ Issues found:', issues.length);
        console.log('💡 Recommendations:', recommendations.length);
        
        return report;
    }

    identifyIssues() {
        const issues = [];
        const { reactSetup, routerConfig, componentTree } = this.findings;
        
        // React issues
        if (!reactSetup.reactExists) {
            issues.push({ type: 'critical', message: 'React not found in window object' });
        }
        
        if (!reactSetup.reactRoot.found) {
            issues.push({ type: 'critical', message: 'No React root element found' });
        }
        
        // Router issues
        if (!routerConfig.routingMethod.reactRouter && !routerConfig.routingMethod.browserRouting) {
            issues.push({ type: 'critical', message: 'No routing method detected' });
        }
        
        // URL mismatch
        if (!routerConfig.url.expectedForEnv.currentMatches) {
            issues.push({ 
                type: 'warning', 
                message: `URL mismatch: expected ${routerConfig.url.expectedForEnv.expectedPath}, got ${routerConfig.url.pathname}` 
            });
        }
        
        // Component tree issues
        if (Object.keys(componentTree.containers).length === 0) {
            issues.push({ type: 'critical', message: 'No main containers found' });
        }
        
        return issues;
    }

    generateRecommendations(issues) {
        const recommendations = [];
        
        issues.forEach(issue => {
            switch (issue.message) {
                case 'React not found in window object':
                    recommendations.push('Ensure React is properly loaded before routing components');
                    break;
                case 'No React root element found':
                    recommendations.push('Add a root element with id="root" or similar for React to mount');
                    break;
                case 'No routing method detected':
                    recommendations.push('Configure React Router or similar routing solution');
                    break;
                default:
                    if (issue.message.includes('URL mismatch')) {
                        recommendations.push('Fix routing configuration to match expected paths for each environment');
                    }
            }
        });
        
        return recommendations;
    }

    generateSummary() {
        const { reactSetup, routerConfig, componentTree } = this.findings;
        
        return {
            reactStatus: reactSetup.reactExists ? 'OK' : 'MISSING',
            routerStatus: routerConfig.routingMethod.reactRouter ? 'OK' : 'MISSING',
            containerStatus: Object.keys(componentTree.containers).length > 0 ? 'OK' : 'MISSING',
            urlStatus: routerConfig.url.expectedForEnv.currentMatches ? 'OK' : 'MISMATCH'
        };
    }
}

// Auto-run analysis
console.log('🚀 [RoutingAnalyzer] Starting routing component analysis...');
const analyzer = new RoutingComponentAnalyzer();
analyzer.analyze();

// Make available globally
window.routingAnalyzer = analyzer;
