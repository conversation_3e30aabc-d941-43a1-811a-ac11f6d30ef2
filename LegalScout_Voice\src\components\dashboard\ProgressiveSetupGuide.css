/* Contextual Setup Shepherd - 2025 Design */

.contextual-shepherd {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 12px;
  animation: slideInUp 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.shepherd-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 16px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(20px);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  max-width: 320px;
}

.shepherd-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 4px 16px rgba(0, 0, 0, 0.1);
}

.shepherd-card.high {
  border-left: 4px solid #ef4444;
}

.shepherd-card.medium {
  border-left: 4px solid #f59e0b;
}

.shepherd-card.low {
  border-left: 4px solid #22c55e;
}

.shepherd-content {
  flex: 1;
  min-width: 0;
}

.shepherd-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.shepherd-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.2;
}

.shepherd-content p {
  margin: 0;
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
}

.priority-icon {
  font-size: 12px;
  flex-shrink: 0;
}

.priority-icon.high {
  color: #ef4444;
}

.priority-icon.medium {
  color: #f59e0b;
}

.priority-icon.low {
  color: #22c55e;
}

.shepherd-action {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.shepherd-action:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.shepherd-action svg {
  font-size: 11px;
  transition: transform 0.2s ease;
}

.shepherd-action:hover svg {
  transform: translateX(2px);
}

/* Progress dots */
.progress-dots {
  display: flex;
  justify-content: center;
  gap: 6px;
  padding: 8px;
}

.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #e5e7eb;
  transition: all 0.3s ease;
}

.dot.complete {
  background: #22c55e;
  box-shadow: 0 0 8px rgba(34, 197, 94, 0.3);
}

.dot.pending {
  background: #d1d5db;
}

/* Dark theme support */
.dashboard-container.dark .shepherd-card {
  background: rgba(30, 30, 30, 0.95);
  border-color: rgba(255, 255, 255, 0.1);
}

.dashboard-container.dark .shepherd-header h4 {
  color: rgba(255, 255, 255, 0.9);
}

.dashboard-container.dark .shepherd-content p {
  color: rgba(255, 255, 255, 0.6);
}

.dashboard-container.dark .dot {
  background: #374151;
}

.dashboard-container.dark .dot.complete {
  background: #22c55e;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .contextual-shepherd {
    bottom: 16px;
    right: 16px;
    left: 16px;
  }

  .shepherd-card {
    max-width: none;
    padding: 14px 16px;
  }

  .shepherd-header h4 {
    font-size: 13px;
  }

  .shepherd-content p {
    font-size: 12px;
  }

  .shepherd-action {
    padding: 7px 12px;
    font-size: 12px;
  }
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Micro-interaction for step completion */
.setup-pill.completing {
  animation: pulse 0.6s ease-in-out;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Glassmorphism effect for modern look */
.setup-pill::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: inherit;
  pointer-events: none;
}

/* Subtle glow effect */
.setup-pill::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
  filter: blur(8px);
}

.setup-pill:hover::after {
  opacity: 0.3;
}
