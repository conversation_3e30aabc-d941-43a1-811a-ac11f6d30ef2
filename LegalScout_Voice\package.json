{"name": "legalscout", "private": true, "version": "0.1.1", "type": "module", "scripts": {"start": "vite", "dev": "cross-env NODE_ENV=development vite --port 5174 --host", "dev:api": "cross-env NODE_ENV=development node dev-server.js", "dev:full": "concurrently \"npm run dev:api\" \"npm run dev\"", "dev:quiet": "cross-env NODE_ENV=development VITE_LOGGING=error vite --port 5174 --host", "test:call-sync": "node scripts/run-call-sync-tests.js", "check:vapi-config": "node scripts/check-vapi-config.js", "diagnose:call-sync": "npm run check:vapi-config && npm run test:call-sync", "dev:profile": "cross-env REACT_PROFILER=1 vite --port 5174 --host", "dev:debug": "cross-env REACT_DEBUG_TOOLS=1 vite --port 5174 --host", "build": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build", "build:profile": "cross-env REACT_PROFILER=1 NODE_OPTIONS=--max-old-space-size=4096 vite build && (node scripts/copy-fix-scripts.js || echo 'Fix scripts copy failed, continuing build') && node scripts/production-environment-injector.cjs", "build:analyze": "npm run build && node scripts/analyze-build.js && open dist/stats.html", "analyze": "node scripts/analyze-build.js", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "clean": "rimraf dist node_modules/.vite", "reset": "npm run clean && npm install", "test": "vitest run", "test:vapi-mcp": "node scripts/test-vapi-mcp-connection.js", "test:vapi-key": "node scripts/test-vapi-api-key.js", "test:assistant-propagation": "vitest run tests/assistant-propagation.test.js", "test:live-propagation": "node scripts/test-assistant-propagation.js", "test:data-refresh": "node scripts/test-assistant-data-refresh.js", "setup:assistant-assignments": "node scripts/setup-assistant-assignments.js", "test:oauth-filtering": "node scripts/test-oauth-assistant-filtering.js", "test:assistant-images": "node scripts/test-assistant-images.js", "test:dropdown-errors": "node scripts/test-dropdown-error-handling.js", "test:critical-issues": "node tests/critical-issues-test-suite.js", "test:react-context": "node tests/react-context-fix-test.js", "test:vapi-sdk": "node tests/vapi-sdk-loading-test.js", "test:all-critical": "npm run test:critical-issues && npm run test:react-context && npm run test:vapi-sdk", "test:system": "echo 'Opening system test page...' && start http://localhost:5174/system-test.html", "test:system-quick": "node scripts/quick-health-check.js", "test:system-production": "echo 'Opening production test page...' && start https://dashboard.legalscout.net/system-test.html", "test:system-verbose": "node scripts/quick-health-check.js --verbose", "debug:app": "node scripts/debug-app-suite.js", "debug:quick": "node scripts/debug-app-suite.js --quick", "debug:full": "node scripts/debug-app-suite.js --full", "debug:browser": "echo 'Opening debug test page...' && start http://localhost:5174/debug-test.html", "test:runner": "node scripts/test-runner.js", "test:simple": "node scripts/simple-app-test.js", "test:simple-local": "node scripts/simple-app-test.js --local", "test:simple-prod": "node scripts/simple-app-test.js --production", "fix:black-screen": "node scripts/fix-black-screen.js fix", "restore:app": "node scripts/fix-black-screen.js restore", "fix:deployment": "node scripts/fix-deployment.js fix", "clean:deps": "node scripts/fix-deployment.js clean", "check:deployment": "node scripts/fix-deployment.js check", "test:local-deployment": "node test-local-deployment.cjs", "test:production-fixes": "node scripts/test-production-fixes.js", "deploy": "node deploy.js", "deploy:enhanced": "node deploy-enhanced.cjs", "deploy:test": "npm run test:local-deployment && npm run deploy:enhanced", "build:production": "node scripts/production-build.js", "vercel-build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 vite build --mode production", "vercel-build-simple": "vite build --mode production && node scripts/production-environment-injector.cjs", "vercel-build-legacy": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 VITE_DISABLE_FRAMER_MOTION=true vite build --mode production && (test -f scripts/copy-fix-scripts.js && node scripts/copy-fix-scripts.js || echo 'Fix scripts copy skipped - script not found or failed') && node scripts/production-environment-injector.cjs"}, "dependencies": {"@agentdeskai/browser-tools-mcp": "^1.2.1", "@auth0/auth0-react": "^2.2.4", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@modelcontextprotocol/sdk": "^1.12.1", "@mui/material": "^7.1.0", "@react-three/drei": "^10.0.4", "@react-three/fiber": "^9.1.0", "@supabase/supabase-js": "^2.49.1", "@vapi-ai/mcp-server": "^0.0.6", "@vapi-ai/web": "^2.3.1", "@vercel/mcp-adapter": "^0.6.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "formidable": "^3.5.4", "framer-motion": "^10.16.4", "isomorphic-fetch": "^3.0.0", "jose": "^6.0.11", "jsdom": "^22.1.0", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "papaparse": "^5.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-leaflet": "^4.2.1", "react-markdown": "^10.1.0", "react-router-dom": "^7.3.0", "react-toastify": "^11.0.5", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "three": "^0.174.0", "three-globe": "^2.42.1", "typewriter-effect": "^2.21.0", "uuid": "^11.1.0", "ws": "^8.18.2", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "engines": {"node": ">=18.0.0"}, "devDependencies": {"@babel/plugin-transform-react-jsx": "^7.25.9", "@browsermcp/mcp": "^0.1.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/react-hooks": "^8.0.1", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.20", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "eventsource": "^4.0.0", "node-fetch": "^3.3.2", "postcss": "^8.5.3", "rimraf": "^5.0.10", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.3.3", "vite": "^4.4.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-pwa": "^1.0.0", "vitest": "^3.0.8"}, "description": "LegalScout is a voice-guided legal consultation platform that connects users with attorneys based on their legal needs. The platform uses Vapi.ai for voice interactions and provides interactive map visualizations for attorney recommendations.", "main": "checkSupabaseTables.js", "directories": {"doc": "docs", "test": "tests"}, "keywords": [], "author": "", "license": "ISC"}