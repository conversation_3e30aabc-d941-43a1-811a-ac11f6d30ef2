#!/usr/bin/env node

/**
 * Quick Health Check for LegalScout Voice
 *
 * This script performs a basic health check without requiring a browser.
 * It tests environment variables and basic API connectivity.
 *
 * Usage:
 *   node scripts/quick-health-check.js [options]
 *
 * Options:
 *   --verbose          Verbose output
 *   --production       Check production environment
 *   --help             Show help
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { config } from 'dotenv';

// ES module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from .env file
config();

// Debug: Check if script is running
console.log('🧪 LegalScout Voice - Quick Health Check starting...');

// Configuration
const DEFAULT_CONFIG = {
  verbose: false,
  production: false
};

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const config = { ...DEFAULT_CONFIG };

  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--verbose':
        config.verbose = true;
        break;
      case '--production':
        config.production = true;
        break;
      case '--help':
        showHelp();
        process.exit(0);
        break;
      default:
        console.error(`Unknown option: ${args[i]}`);
        process.exit(1);
    }
  }

  return config;
}

// Show help
function showHelp() {
  console.log(`
LegalScout Voice - Quick Health Check

Usage: node scripts/quick-health-check.js [options]

Options:
  --verbose          Verbose output
  --production       Check production environment
  --help             Show this help

Examples:
  node scripts/quick-health-check.js
  node scripts/quick-health-check.js --verbose
  node scripts/quick-health-check.js --production
`);
}

// Log function
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [HealthCheck]`;
  
  switch(type) {
    case 'success': console.log(`✅ ${prefix} ${message}`); break;
    case 'error': console.error(`❌ ${prefix} ${message}`); break;
    case 'warning': console.warn(`⚠️ ${prefix} ${message}`); break;
    default: console.log(`ℹ️ ${prefix} ${message}`);
  }
}

// Check environment variables
function checkEnvironmentVariables(config) {
  log('Checking environment variables...', 'info');
  
  const requiredVars = {
    'VITE_SUPABASE_URL': 'https://utopqxsvudgrtiwenlzl.supabase.co',
    'VITE_SUPABASE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9',
    'VITE_VAPI_PUBLIC_KEY': '310f0d43-27c2-47a5-a76d-e55171d024f7',
    'VITE_VAPI_SECRET_KEY': '6734febc-fc65-4669-93b0-929b31ff6564'
  };

  const results = {
    passed: 0,
    failed: 0,
    issues: []
  };

  for (const [varName, expectedPrefix] of Object.entries(requiredVars)) {
    const value = process.env[varName];
    
    if (!value) {
      results.failed++;
      results.issues.push(`Missing ${varName}`);
      log(`Missing environment variable: ${varName}`, 'error');
    } else if (!value.startsWith(expectedPrefix.substring(0, 10))) {
      results.failed++;
      results.issues.push(`Incorrect ${varName}`);
      log(`Incorrect environment variable: ${varName}`, 'warning');
      if (config.verbose) {
        log(`  Expected prefix: ${expectedPrefix.substring(0, 10)}...`, 'info');
        log(`  Actual prefix: ${value.substring(0, 10)}...`, 'info');
      }
    } else {
      results.passed++;
      log(`Environment variable OK: ${varName}`, 'success');
      if (config.verbose) {
        log(`  Value: ${value.substring(0, 10)}...`, 'info');
      }
    }
  }

  return results;
}

// Check file system
function checkFileSystem(config) {
  log('Checking critical files...', 'info');
  
  const criticalFiles = [
    'index.html',
    'public/comprehensive-system-test.js',
    'public/system-test-integration.js',
    'public/system-test.html',
    'src/lib/supabase.js',
    'src/config/vapiConfig.js'
  ];

  const results = {
    passed: 0,
    failed: 0,
    issues: []
  };

  for (const file of criticalFiles) {
    const filePath = path.join(process.cwd(), file);
    
    if (fs.existsSync(filePath)) {
      results.passed++;
      log(`File exists: ${file}`, 'success');
    } else {
      results.failed++;
      results.issues.push(`Missing file: ${file}`);
      log(`Missing file: ${file}`, 'error');
    }
  }

  return results;
}

// Test basic API connectivity (if fetch is available)
async function checkAPIConnectivity(config) {
  log('Checking API connectivity...', 'info');
  
  const results = {
    passed: 0,
    failed: 0,
    issues: []
  };

  // Test Supabase URL
  const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
  
  try {
    // Use dynamic import for fetch in Node.js
    const { default: fetch } = await import('node-fetch');
    
    const response = await fetch(`${supabaseUrl}/rest/v1/`, {
      method: 'HEAD',
      timeout: 5000
    });
    
    if (response.ok || response.status === 401) { // 401 is expected without API key
      results.passed++;
      log('Supabase URL accessible', 'success');
    } else {
      results.failed++;
      results.issues.push(`Supabase URL returned ${response.status}`);
      log(`Supabase URL returned status ${response.status}`, 'warning');
    }
  } catch (error) {
    if (error.code === 'MODULE_NOT_FOUND') {
      log('Skipping API connectivity test (node-fetch not available)', 'info');
    } else {
      results.failed++;
      results.issues.push(`Supabase connectivity failed: ${error.message}`);
      log(`Supabase connectivity failed: ${error.message}`, 'error');
    }
  }

  return results;
}

// Main health check function
async function runHealthCheck(config) {
  log('🚀 Starting LegalScout Voice Health Check', 'info');
  
  if (config.verbose) {
    log('Configuration:', 'info');
    console.log(JSON.stringify(config, null, 2));
  }

  const allResults = {
    timestamp: new Date().toISOString(),
    environment: config.production ? 'production' : 'development',
    passed: 0,
    failed: 0,
    issues: []
  };

  try {
    // Check environment variables
    const envResults = checkEnvironmentVariables(config);
    allResults.passed += envResults.passed;
    allResults.failed += envResults.failed;
    allResults.issues.push(...envResults.issues);

    // Check file system
    const fileResults = checkFileSystem(config);
    allResults.passed += fileResults.passed;
    allResults.failed += fileResults.failed;
    allResults.issues.push(...fileResults.issues);

    // Check API connectivity
    const apiResults = await checkAPIConnectivity(config);
    allResults.passed += apiResults.passed;
    allResults.failed += apiResults.failed;
    allResults.issues.push(...apiResults.issues);

    // Summary
    const total = allResults.passed + allResults.failed;
    const successRate = total > 0 ? Math.round((allResults.passed / total) * 100) : 0;

    log('📊 Health Check Summary:', 'info');
    log(`  Total checks: ${total}`, 'info');
    log(`  Passed: ${allResults.passed}`, 'success');
    log(`  Failed: ${allResults.failed}`, allResults.failed > 0 ? 'error' : 'info');
    log(`  Success rate: ${successRate}%`, successRate >= 80 ? 'success' : 'warning');

    if (allResults.issues.length > 0) {
      log('🚨 Issues detected:', 'warning');
      allResults.issues.forEach(issue => {
        log(`  • ${issue}`, 'warning');
      });
    }

    // Determine overall status
    let status = 'healthy';
    if (allResults.failed > 0) {
      status = allResults.failed >= 3 ? 'critical' : 'warning';
    }

    log(`🎯 Overall status: ${status.toUpperCase()}`, 
        status === 'healthy' ? 'success' : 
        status === 'warning' ? 'warning' : 'error');

    // Exit code
    const exitCode = allResults.failed > 0 ? 1 : 0;
    
    if (exitCode === 0) {
      log('✅ Health check completed successfully', 'success');
    } else {
      log('❌ Health check detected issues', 'error');
    }

    return { success: exitCode === 0, results: allResults };

  } catch (error) {
    log(`💥 Health check failed: ${error.message}`, 'error');
    return { success: false, error: error.message };
  }
}

// Main function
async function main() {
  const config = parseArgs();
  const result = await runHealthCheck(config);
  
  process.exit(result.success ? 0 : 1);
}

// Handle uncaught errors
process.on('unhandledRejection', (error) => {
  log(`Unhandled rejection: ${error.message}`, 'error');
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  log(`Uncaught exception: ${error.message}`, 'error');
  process.exit(1);
});

// Always run the main function when this script is executed
main().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});

export { runHealthCheck, parseArgs };
