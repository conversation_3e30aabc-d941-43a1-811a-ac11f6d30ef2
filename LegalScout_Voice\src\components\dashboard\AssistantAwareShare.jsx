/**
 * Assistant-Aware Share Component
 * 
 * Provides assistant-aware sharing functionality that automatically adapts
 * to the currently selected assistant. Replaces all hardcoded share/copy buttons.
 */

import React, { useState } from 'react';
import { FaLink, FaCode, FaShare, FaEnvelope, FaTwitter, FaSms, FaCopy, FaCheck, FaExclamationTriangle } from 'react-icons/fa';
import { useAssistantAwareCopy } from '../../hooks/useAssistantAwareCopy';
import './AssistantAwareShare.css';

const AssistantAwareShare = ({ 
  variant = 'full', // 'full', 'compact', 'buttons-only', 'url-only'
  showTitle = true,
  className = '',
  onCopySuccess,
  onCopyError
}) => {
  const [expandedSection, setExpandedSection] = useState(null);
  
  const {
    copyStatus,
    isLoading,
    statusMessage,
    copyShareUrl,
    copyIframeEmbed,
    copyWidgetEmbed,
    copyButtonEmbed,
    copyForPlatform,
    shareUrl,
    embedCodes,
    isAssistantSelected,
    assistantName,
    subdomain,
    clearStatus
  } = useAssistantAwareCopy();

  // Handle copy with callbacks
  const handleCopy = async (copyFunction, type) => {
    try {
      const success = await copyFunction();
      if (success && onCopySuccess) {
        onCopySuccess(type);
      } else if (!success && onCopyError) {
        onCopyError(type);
      }
    } catch (error) {
      if (onCopyError) {
        onCopyError(type, error);
      }
    }
  };

  // Status indicator
  const StatusIndicator = () => {
    if (!copyStatus) return null;

    return (
      <div className={`copy-status ${copyStatus.success ? 'success' : 'error'}`}>
        {copyStatus.success ? <FaCheck /> : <FaExclamationTriangle />}
        <span>{statusMessage}</span>
        <button onClick={clearStatus} className="status-close">×</button>
      </div>
    );
  };

  // Warning for no assistant selected
  const NoAssistantWarning = () => {
    if (isAssistantSelected) return null;

    return (
      <div className="no-assistant-warning">
        <FaExclamationTriangle />
        <span>Select an assistant to generate specific links</span>
      </div>
    );
  };

  // URL only variant
  if (variant === 'url-only') {
    return (
      <div className={`assistant-aware-share url-only ${className}`}>
        <NoAssistantWarning />
        <div className="url-field">
          <input
            type="text"
            value={shareUrl}
            readOnly
            className="share-url-input"
            placeholder="Select an assistant to generate URL"
          />
          <button
            onClick={() => handleCopy(copyShareUrl, 'URL')}
            disabled={isLoading || !isAssistantSelected}
            className="copy-button"
            title="Copy URL"
          >
            {isLoading ? '...' : <FaCopy />}
          </button>
        </div>
        <StatusIndicator />
      </div>
    );
  }

  // Buttons only variant
  if (variant === 'buttons-only') {
    return (
      <div className={`assistant-aware-share buttons-only ${className}`}>
        <NoAssistantWarning />
        <div className="share-buttons">
          <button
            onClick={() => handleCopy(copyShareUrl, 'URL')}
            disabled={isLoading || !isAssistantSelected}
            className="share-button primary"
          >
            <FaLink /> Copy Link
          </button>
          <button
            onClick={() => handleCopy(copyIframeEmbed, 'Embed')}
            disabled={isLoading || !isAssistantSelected}
            className="share-button secondary"
          >
            <FaCode /> Copy Embed
          </button>
        </div>
        <StatusIndicator />
      </div>
    );
  }

  // Compact variant
  if (variant === 'compact') {
    return (
      <div className={`assistant-aware-share compact ${className}`}>
        {showTitle && (
          <h4>
            <FaShare /> Share {assistantName || 'Assistant'}
            {subdomain && <span className="subdomain">({subdomain})</span>}
          </h4>
        )}
        
        <NoAssistantWarning />
        
        <div className="compact-content">
          <div className="url-field">
            <input
              type="text"
              value={shareUrl}
              readOnly
              className="share-url-input"
            />
            <button
              onClick={() => handleCopy(copyShareUrl, 'URL')}
              disabled={isLoading || !isAssistantSelected}
              className="copy-button"
            >
              <FaCopy />
            </button>
          </div>
          
          <div className="quick-actions">
            <button
              onClick={() => handleCopy(copyIframeEmbed, 'Embed')}
              disabled={isLoading || !isAssistantSelected}
              className="quick-action"
              title="Copy iframe embed code"
            >
              <FaCode />
              <span>Copy Embed</span>
            </button>
            <button
              onClick={() => handleCopy(() => copyForPlatform('email'), 'Email')}
              disabled={isLoading || !isAssistantSelected}
              className="quick-action"
              title="Copy email content"
            >
              <FaEnvelope />
              <span>Email Share</span>
            </button>
            <button
              onClick={() => handleCopy(() => copyForPlatform('social'), 'Social')}
              disabled={isLoading || !isAssistantSelected}
              className="quick-action"
              title="Copy social media content"
            >
              <FaTwitter />
              <span>Social Share</span>
            </button>
          </div>
        </div>
        
        <StatusIndicator />
      </div>
    );
  }

  // Full variant (default)
  return (
    <div className={`assistant-aware-share full ${className}`}>
      {showTitle && (
        <div className="share-header">
          <h3>
            <FaShare /> Share Your Assistant
            {assistantName && <span className="assistant-name">- {assistantName}</span>}
          </h3>
          {subdomain && (
            <div className="subdomain-info">
              <code>{subdomain}.legalscout.net</code>
            </div>
          )}
        </div>
      )}

      <NoAssistantWarning />

      {/* Direct URL Section */}
      <div className="share-section">
        <h4>Direct Link</h4>
        <p>Share this URL with clients for direct access to your assistant.</p>
        <div className="url-field">
          <input
            type="text"
            value={shareUrl}
            readOnly
            className="share-url-input"
          />
          <button
            onClick={() => handleCopy(copyShareUrl, 'URL')}
            disabled={isLoading || !isAssistantSelected}
            className="copy-button"
          >
            <FaCopy /> Copy
          </button>
        </div>
      </div>

      {/* Embed Codes Section */}
      <div className="share-section">
        <h4>Embed on Website</h4>
        <div className="embed-options">
          <div className="embed-option">
            <label>iframe Embed</label>
            <div className="code-field">
              <textarea
                value={embedCodes.iframe}
                readOnly
                className="code-textarea"
                rows="3"
              />
              <button
                onClick={() => handleCopy(copyIframeEmbed, 'iframe embed')}
                disabled={isLoading || !isAssistantSelected}
                className="copy-button"
              >
                <FaCopy />
              </button>
            </div>
          </div>

          <div className="embed-option">
            <label>Widget Script</label>
            <div className="code-field">
              <textarea
                value={embedCodes.widget}
                readOnly
                className="code-textarea"
                rows="4"
              />
              <button
                onClick={() => handleCopy(copyWidgetEmbed, 'widget script')}
                disabled={isLoading || !isAssistantSelected}
                className="copy-button"
              >
                <FaCopy />
              </button>
            </div>
          </div>

          <div className="embed-option">
            <label>Button Link</label>
            <div className="code-field">
              <textarea
                value={embedCodes.button}
                readOnly
                className="code-textarea"
                rows="2"
              />
              <button
                onClick={() => handleCopy(copyButtonEmbed, 'button code')}
                disabled={isLoading || !isAssistantSelected}
                className="copy-button"
              >
                <FaCopy />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Platform-Specific Sharing */}
      <div className="share-section">
        <h4>Share on Platforms</h4>
        <div className="platform-buttons">
          <button
            onClick={() => handleCopy(() => copyForPlatform('email'), 'Email content')}
            disabled={isLoading || !isAssistantSelected}
            className="platform-button email"
          >
            <FaEnvelope /> Email
          </button>
          <button
            onClick={() => handleCopy(() => copyForPlatform('social'), 'Social media content')}
            disabled={isLoading || !isAssistantSelected}
            className="platform-button social"
          >
            <FaTwitter /> Social Media
          </button>
          <button
            onClick={() => handleCopy(() => copyForPlatform('sms'), 'SMS content')}
            disabled={isLoading || !isAssistantSelected}
            className="platform-button sms"
          >
            <FaSms /> SMS
          </button>
        </div>
      </div>

      <StatusIndicator />
    </div>
  );
};

export default AssistantAwareShare;
