/**
 * Minimal App Component
 * 
 * This is a stripped-down version of App.jsx to test if the issue is
 * in the complex App logic or somewhere else.
 */

import React, { useState, useEffect } from 'react';

const MinimalApp = () => {
  const [message, setMessage] = useState('Loading...');
  const [logs, setLogs] = useState([]);

  const addLog = (msg) => {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const logEntry = `[${timestamp}] ${msg}`;
    setLogs(prev => [...prev, logEntry]);
    console.log(logEntry);
  };

  useEffect(() => {
    addLog('🚀 MinimalApp mounted');
    
    // Test basic React functionality
    setTimeout(() => {
      setMessage('✅ React is working!');
      addLog('✅ React state update successful');
    }, 1000);

    // Test environment variables
    try {
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      addLog(`Environment test: ${supabaseUrl ? '✅ Env vars loaded' : '❌ Env vars missing'}`);
    } catch (error) {
      addLog(`❌ Environment error: ${error.message}`);
    }

    // Test subdomain detection
    try {
      const hostname = window.location.hostname;
      addLog(`Hostname: ${hostname}`);
      
      if (hostname === 'localhost' || hostname === '127.0.0.1') {
        addLog('✅ Localhost detected correctly');
      } else {
        addLog(`ℹ️ Non-localhost: ${hostname}`);
      }
    } catch (error) {
      addLog(`❌ Subdomain test error: ${error.message}`);
    }

    return () => {
      addLog('🧹 MinimalApp unmounting');
    };
  }, []);

  return (
    <div style={{
      padding: '20px',
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#f0f0f0',
      minHeight: '100vh'
    }}>
      <h1 style={{ color: '#333' }}>🧪 Minimal App Test</h1>
      
      <div style={{
        padding: '20px',
        backgroundColor: '#fff',
        borderRadius: '8px',
        marginBottom: '20px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <h2 style={{ color: '#2c3e50' }}>{message}</h2>
        <p>If you can see this, React is rendering successfully!</p>
        
        <div style={{ marginTop: '20px' }}>
          <h3>Environment Info:</h3>
          <ul>
            <li>URL: {window.location.href}</li>
            <li>Hostname: {window.location.hostname}</li>
            <li>Mode: {import.meta.env.MODE}</li>
            <li>Dev: {import.meta.env.DEV ? 'true' : 'false'}</li>
          </ul>
        </div>
      </div>

      <div style={{
        padding: '20px',
        backgroundColor: '#fff',
        borderRadius: '8px',
        fontFamily: 'monospace',
        fontSize: '12px'
      }}>
        <h3>Execution Logs:</h3>
        <div style={{
          maxHeight: '300px',
          overflowY: 'auto',
          backgroundColor: '#f8f8f8',
          padding: '10px',
          borderRadius: '4px'
        }}>
          {logs.map((log, index) => (
            <div key={index} style={{ marginBottom: '2px' }}>
              {log}
            </div>
          ))}
        </div>
      </div>

      <div style={{ marginTop: '20px' }}>
        <button
          onClick={() => {
            addLog('🔄 Manual test button clicked');
            setMessage('🎉 Button click successful!');
          }}
          style={{
            padding: '10px 20px',
            backgroundColor: '#3498db',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          Test Button
        </button>
      </div>
    </div>
  );
};

export default MinimalApp;
