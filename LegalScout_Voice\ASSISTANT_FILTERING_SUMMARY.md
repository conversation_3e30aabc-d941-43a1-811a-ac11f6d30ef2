# Assistant-Based Call & Consultation Filtering

## ✅ Implementation Complete

Your dashboard now filters call records and consultations by the **currently selected assistant ID** on both load and refresh.

## 🎯 Current Assistant ID

Based on your database records:
- **Your Attorney ID**: `87756a2c-a398-43f2-889a-b8815684df71`
- **Current Assistant ID**: `89257374-3725-4fa2-ba8b-08d2204be538` (LegalScout - older assistant)
- **Vapi Assistant ID**: `cd0b44b7-397e-410d-8835-ce9c3ba584b2` (LegalScout - newer assistant)

## 📊 Call Distribution

From Vapi MCP server data:
- **Current Assistant** (`89257374-3725-4fa2-ba8b-08d2204be538`): **0 calls**
- **Vapi Assistant** (`cd0b44b7-397e-410d-8835-ce9c3ba584b2`): **5 calls**
  - `23b43046-5354-455e-a124-72299cba04b2` (ended - customer ended)
  - `9fa86c40-2a11-4b23-8ba0-30550c8de553` (ended - customer ended)  
  - `85da57e7-8078-469b-b945-620fcc1387ec` (ended - customer ended)
  - `9c25e09b-0052-40f8-9df6-aa2a348d9d8d` (ended - silence timeout)
  - `41e853b3-42b3-4b27-8c33-57e76553f2a5` (ended - customer ended)

## 🔧 How It Works

### On Page Load
1. **CallsTab** gets the attorney's `current_assistant_id` or falls back to `vapi_assistant_id`
2. **CallRecordsTable** receives `currentAssistantId` as prop
3. Query filters: `WHERE attorney_id = ? AND assistant_id = ?`
4. **ConsultationsTab** filters by `metadata.assistant_id = current_assistant_id`

### On Assistant Switch
1. **EnhancedAssistantDropdown** calls `assistantUIConfigService.switchToAssistant()`
2. Updates `current_assistant_id` in database
3. Calls `assistantDataRefreshService.refreshAssistantData()`
4. **Both tabs refresh automatically** due to useEffect dependencies

### On Manual Refresh
1. **CallRecordsTable** useEffect depends on `[attorneyId, currentAssistantId]`
2. **ConsultationsTab** useEffect depends on `[attorney, attorney?.current_assistant_id, attorney?.vapi_assistant_id]`
3. **assistantDataRefreshService** listener triggers refresh in both components

## 📝 Code Changes Made

### 1. CallRecordsTable.jsx
```javascript
// Added currentAssistantId prop and filtering
const CallRecordsTable = ({ attorneyId, assistantData, currentAssistantId }) => {
  
  // Filter by assistant ID in query
  let query = supabase
    .from('call_records')
    .select('*')
    .eq('attorney_id', attorneyId);

  if (currentAssistantId) {
    query = query.eq('assistant_id', currentAssistantId);
  }
  
  // Refresh when assistant changes
  }, [attorneyId, currentAssistantId]);
```

### 2. CallsTab.jsx
```javascript
// Pass current assistant ID to CallRecordsTable
<CallRecordsTable
  attorneyId={attorney?.id}
  assistantData={assistantData}
  currentAssistantId={attorney?.current_assistant_id || attorney?.vapi_assistant_id}
/>
```

### 3. ConsultationsTab.jsx
```javascript
// Filter consultations by assistant ID
const currentAssistantId = attorney.current_assistant_id || attorney.vapi_assistant_id;

// Filter by metadata.assistant_id
let filteredConsultations = consultationsWithParsedMetadata;
if (currentAssistantId) {
  filteredConsultations = consultationsWithParsedMetadata.filter(consultation => {
    return consultation.metadata?.assistant_id === currentAssistantId;
  });
}

// Refresh when assistant IDs change
}, [attorney, attorney?.current_assistant_id, attorney?.vapi_assistant_id]);
```

### 4. Webhook (api/webhook/vapi-call/index.js)
```javascript
// Updated attorney lookup to check both assistant ID fields
const { data, error } = await supabase
  .from('attorneys')
  .select('id')
  .or(`vapi_assistant_id.eq.${assistantId},current_assistant_id.eq.${assistantId}`)
  .single();
```

## 🧪 Testing Results

✅ **Filtering Logic**: Verified with mock data
✅ **Assistant Switching**: Triggers proper refreshes  
✅ **Database Queries**: Optimized for assistant-specific filtering
✅ **Webhook Integration**: Handles both assistant ID fields
✅ **UI Dependencies**: Proper useEffect dependencies for auto-refresh

## 🚀 What You'll See

### Current State (Assistant: 89257374-3725-4fa2-ba8b-08d2204be538)
- **Calls Tab**: Shows 0 call records
- **Briefs Tab**: Shows 0 consultations

### If You Switch to Vapi Assistant (cd0b44b7-397e-410d-8835-ce9c3ba584b2)
- **Calls Tab**: Will show 5 call records (once synced from Vapi)
- **Briefs Tab**: Will show consultations created from those calls

## 🔄 Next Steps

1. **Test Assistant Switching**: Use the assistant dropdown to switch between assistants
2. **Verify Call Sync**: Make a test call to see it appear in the correct assistant's view
3. **Check Webhook**: Ensure new calls are properly attributed to the correct assistant

## 📋 Database Schema Note

The `call_records` table has a trigger that automatically sets `attorney_id` based on `assistant_id`. The trigger function needs to be updated to handle the new dual-field lookup:

```sql
-- Current trigger looks for: vapi_assistant_id = NEW.assistant_id
-- Should be updated to: vapi_assistant_id = NEW.assistant_id OR current_assistant_id = NEW.assistant_id
```

## 🎉 Summary

Your dashboard now properly filters call records and consultations by the currently selected assistant ID. When you switch assistants, you'll see different sets of calls and consultations, allowing you to manage multiple assistants with separate data views.

The implementation handles:
- ✅ Page load filtering
- ✅ Assistant switching
- ✅ Manual refresh
- ✅ Webhook integration
- ✅ Automatic UI updates
