// Dashboard Iframe Manager
(function() {
  console.log('🖼️ [IFRAME] Initializing dashboard iframe manager...');
  
  // Handle iframe communication
  window.addEventListener('message', function(event) {
    if (event.origin !== window.location.origin) return;
    
    if (event.data.type === 'IFRAME_READY') {
      console.log('✅ [IFRAME] Dashboard iframe ready');
    }
  });
  
  // Iframe utilities
  window.iframeManager = {
    postMessage: function(message, targetOrigin = '*') {
      const iframes = document.querySelectorAll('iframe');
      iframes.forEach(iframe => {
        if (iframe.contentWindow) {
          iframe.contentWindow.postMessage(message, targetOrigin);
        }
      });
    }
  };
  
  console.log('✅ [IFRAME] Dashboard iframe manager ready');
})();