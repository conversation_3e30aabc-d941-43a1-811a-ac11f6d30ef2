import React, { useState, useEffect } from 'react';
import { supabase, isSupabaseConfigured, signInWithGoogle } from '../lib/supabase';
import { vapiAssistantService } from '../services/vapiAssistantService';
import './AuthOverlay.css';

const AuthOverlay = ({ isOpen, onClose, onSuccess }) => {
  const [authMethod, setAuthMethod] = useState(null);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firmName, setFirmName] = useState('');
  const [subdomain, setSubdomain] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [step, setStep] = useState('methods'); // methods, email, success

  useEffect(() => {
    // Reset state when overlay is opened
    if (isOpen) {
      console.log('AuthOverlay opened');
      setAuthMethod(null);
      setEmail('');
      setPassword('');
      setFirmName('');
      setSubdomain('');
      setError(null);
      setStep('methods');
    }
  }, [isOpen]);

  // Handle Google sign-in
  const handleGoogleSignIn = () => {
    console.log('🔴 BUTTON CLICKED - Google sign-in starting');
    alert('Google button clicked!'); // Immediate feedback

    setLoading(true);
    setError(null);

    // Check if we're in development mode
    const isDev = import.meta.env.DEV || import.meta.env.MODE === 'development';
    console.log('🔴 Development mode:', isDev);

    // Check if Supabase is configured
    const configured = isSupabaseConfigured();
    console.log('🔴 Supabase configured:', configured);

    if (!configured && !isDev) {
      console.error('Supabase not configured for Google sign-in');
      setError('Authentication is not configured. Please contact the administrator.');
      setLoading(false);
      return;
    }

    console.log('🔴 About to call signInWithGoogle...');
    // Call signInWithGoogle without await - let it redirect immediately
    signInWithGoogle().catch(err => {
      console.error('🔴 Google sign-in error:', err);
      alert('Error: ' + err.message);
      setError('Failed to sign in with Google. Please try again.');
      setLoading(false);
    });
  };

  // Handle email sign-up
  const handleEmailSignUp = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    // Check if Supabase is configured
    if (!isSupabaseConfigured()) {
      setError('Authentication is not configured. Please contact the administrator.');
      setLoading(false);
      return;
    }

    try {
      // Validate inputs
      if (!email || !password || !firmName || !subdomain) {
        throw new Error('All fields are required');
      }

      // Validate subdomain format (letters, numbers, hyphens only)
      if (!/^[a-z0-9-]+$/.test(subdomain)) {
        throw new Error('Subdomain can only contain lowercase letters, numbers, and hyphens');
      }

      // Check if subdomain is available
      if (import.meta.env.DEV) console.log('AuthOverlay: checking subdomain availability:', subdomain);
      const { data: existingAttorney, error: lookupError } = await supabase
        .from('attorneys')
        .select('id')
        .eq('subdomain', subdomain)
        .single();
      if (import.meta.env.DEV) console.log('AuthOverlay: existingAttorney, lookupError:', existingAttorney, lookupError);

      if (existingAttorney) {
        throw new Error('This subdomain is already taken. Please choose another one.');
      }

      // Sign up with email
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
      });
      console.log('AuthOverlay: supabase.auth.signUp result:', { authData, authError });

      if (authError) throw authError;

      // Create attorney record in the database
      const { data: attorneyData, error: attorneyError } = await supabase
        .from('attorneys')
        .insert([
          {
            subdomain,
            firm_name: firmName,
            email,
            is_active: true,
            user_id: authData.user.id,
            address: '',
            vapi_instructions: `You are a legal assistant for ${firmName}. Help potential clients understand their legal needs and collect relevant information for consultation.`
          }
        ])
        .select('*')
        .single();
      console.log('AuthOverlay: supabase.from("attorneys").insert result:', { attorneyData, attorneyError });

      if (attorneyError) throw attorneyError;

      // Create a Vapi assistant for the attorney
      try {
        const assistant = await vapiAssistantService.createAssistantForAttorney(attorneyData);
        console.log('Created Vapi assistant for attorney:', assistant);
      } catch (assistantError) {
        console.error('Error creating Vapi assistant:', assistantError);
        // Continue with account creation even if assistant creation fails
        // We can create the assistant later
      }

      // Store attorney data in localStorage
      localStorage.setItem('attorney', JSON.stringify(attorneyData));
      // Also persist the attorney ID for retrieval
      localStorage.setItem('attorney_id', attorneyData.id);
      localStorage.setItem('currentAttorneyId', attorneyData.id);

      // Call the success callback with the attorney data
      if (onSuccess) {
        onSuccess(attorneyData);
      } else {
        // Redirect directly to dashboard if no callback
        window.location.href = '/dashboard';
      }
    } catch (err) {
      console.error('Email sign-up error:', err);
      setError(err.message || 'Failed to create account. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle overlay click (close if clicking outside the content)
  const handleOverlayClick = (e) => {
    if (e.target.classList.contains('auth-overlay')) {
      onClose();
    }
  };

  if (!isOpen) return null;

  // Check if we're in development mode
  const isDev = import.meta.env.DEV || import.meta.env.MODE === 'development';

  // Show a message if Supabase is not configured and we're not in development mode
  const supabaseNotConfigured = !isSupabaseConfigured() && !isDev;

  return (
    <div className="auth-overlay" onClick={handleOverlayClick}>
      <div className="auth-overlay-content">
        <button className="close-button" onClick={onClose}>×</button>

        {supabaseNotConfigured && (
          <div className="error-message">
            <h3>Authentication Not Configured</h3>
            <p>The authentication system is not properly configured. This is expected in development mode.</p>
            <div className="dev-mode-options">
              <h4>Development Options:</h4>
              <button
                className="dev-mode-button"
                onClick={() => {
                  // Simulate successful authentication for development
                  console.log('Using mock authentication for development');
                  // Create mock attorney data
                  const mockAttorneyData = {
                    id: 'dev-' + Date.now(),
                    firm_name: 'Development Law Firm',
                    subdomain: 'devmode',
                    email: '<EMAIL>',
                    is_active: true,
                    created_at: new Date().toISOString(),
                    vapi_instructions: 'You are a legal assistant for Development Law Firm. Help potential clients understand their legal needs and collect relevant information for consultation.'
                  };
                  // Store mock data in localStorage
                  localStorage.setItem('attorney', JSON.stringify(mockAttorneyData));
                  // Call the success callback with mock data and redirect to dashboard
                  if (onSuccess) {
                    onSuccess(mockAttorneyData);
                  } else {
                    // Redirect directly to dashboard if no callback
                    window.location.href = '/dashboard';
                  }
                }}
              >
                Continue in Development Mode
              </button>
              <div className="setup-instructions">
                <p><strong>To configure Supabase:</strong></p>
                <ol>
                  <li>Create a <a href="https://supabase.com/" target="_blank" rel="noopener noreferrer">Supabase</a> account and project</li>
                  <li>Create a <code>.env</code> file in the project root with:</li>
                  <pre>
                    VITE_SUPABASE_URL=your-supabase-url<br/>
                    VITE_SUPABASE_KEY=your-supabase-anon-key
                  </pre>
                  <li>Create an <code>attorneys</code> table in Supabase with the required fields</li>
                </ol>
              </div>
            </div>
          </div>
        )}

        {!supabaseNotConfigured && step === 'methods' && (
          <>
            <h2>Create Your Attorney Account</h2>
            <p>Choose a sign-up method to get started with your customized legal assistant.</p>

            <div className="auth-methods">
              <button
                className="auth-method-button google-button"
                onClick={handleGoogleSignIn}
                disabled={loading}
              >
                <img src="/google-icon.svg" alt="Google" />
                Continue with Google
              </button>

              <div className="divider">
                <span>or</span>
              </div>

              <button
                className="auth-method-button email-button"
                onClick={() => setStep('email')}
                disabled={loading}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="2" y="4" width="20" height="16" rx="2" />
                  <path d="M22 7l-10 7L2 7" />
                </svg>
                Continue with Email
              </button>
            </div>
          </>
        )}

        {!supabaseNotConfigured && step === 'email' && (
          <>
            <h2>Create Your Attorney Account</h2>
            <p>Fill in your details to set up your customized legal assistant.</p>

            <form onSubmit={handleEmailSignUp} className="auth-form">
              <div className="form-group">
                <label htmlFor="firmName">Law Firm Name</label>
                <input
                  type="text"
                  id="firmName"
                  value={firmName}
                  onChange={(e) => setFirmName(e.target.value)}
                  placeholder="Your Law Firm, LLC"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="subdomain">Subdomain</label>
                <div className="subdomain-input">
                  <input
                    type="text"
                    id="subdomain"
                    value={subdomain}
                    onChange={(e) => setSubdomain(e.target.value.toLowerCase())}
                    placeholder="yourfirm"
                    required
                  />
                  <span className="subdomain-suffix">.legalscout.ai</span>
                </div>
                <small>This will be your unique URL: https://{subdomain || 'yourfirm'}.legalscout.ai</small>
              </div>

              <div className="form-group">
                <label htmlFor="email">Email Address</label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="password">Password</label>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Create a secure password"
                  required
                  minLength="8"
                />
              </div>

              {error && <div className="error-message">{error}</div>}

              <div className="form-actions">
                <button
                  type="button"
                  className="back-button"
                  onClick={() => setStep('methods')}
                  disabled={loading}
                >
                  Back
                </button>
                <button
                  type="submit"
                  className="submit-button"
                  disabled={loading}
                >
                  {loading ? 'Creating Account...' : 'Create Account'}
                </button>
              </div>
            </form>
          </>
        )}

        {!supabaseNotConfigured && step === 'success' && (
          <div className="success-message">
            <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            <h2>Account Created Successfully!</h2>
            <p>Redirecting to your dashboard...</p>
            <div className="loading-spinner"></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthOverlay;
