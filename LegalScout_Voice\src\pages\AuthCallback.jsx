import React, { useEffect, useState } from 'react';
import { getSupabaseClient, isSupabaseConfigured } from '../lib/supabase';
import { useNavigate } from 'react-router-dom';
import { fixAuthProfile } from '../utils/authProfileFixer';
import { notificationManager } from '../components/SubtleNotification';
import './AuthCallback.css';

const AuthCallback = () => {
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const handleCallback = async () => {
      console.log('🔐 [AuthCallback] Starting OAuth callback handling...');

      // Check if Supabase is configured
      if (!isSupabaseConfigured()) {
        setError('Supabase is not properly configured. Please check your environment variables.');
        setLoading(false);
        return;
      }

      try {
        // Parse OAuth callback parameters from URL hash AND query params
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        const queryParams = new URLSearchParams(window.location.search);

        // Try both hash and query params (emergency auth might use query params)
        const accessToken = hashParams.get('access_token') || queryParams.get('access_token');
        const refreshToken = hashParams.get('refresh_token') || queryParams.get('refresh_token');
        const expiresAt = hashParams.get('expires_at') || queryParams.get('expires_at');

        console.log('🔐 [AuthCallback] Checking for OAuth tokens...');
        console.log('🔐 [AuthCallback] Hash params:', window.location.hash);
        console.log('🔐 [AuthCallback] Query params:', window.location.search);
        console.log('🔐 [AuthCallback] Access token found:', !!accessToken);

        if (!accessToken) {
          // Try using emergency auth to get user from URL
          console.log('🔐 [AuthCallback] No access token in URL, trying emergency auth...');
          const { emergencyAuth } = await import('../lib/supabase');
          const authResult = await emergencyAuth.getCurrentUser();

          if (authResult.user) {
            console.log('🔐 [AuthCallback] Emergency auth found user:', authResult.user.email);

            // Use the enhanced auth profile fixer
            const attorneyProfile = await fixAuthProfile(authResult.user, authResult.access_token);

            if (attorneyProfile) {
              localStorage.setItem('attorney', JSON.stringify(attorneyProfile));
              notificationManager.success(
                `${attorneyProfile.firm_name} assistant configured successfully!`,
                { duration: 4000, position: 'top-center' }
              );
              setTimeout(() => {
                window.location.href = `${window.location.origin}/dashboard`;
              }, 1500);
              return;
            } else {
              navigate('/complete-profile');
              return;
            }
          }

          throw new Error('No access token found in callback URL. Authentication may have failed.');
        }

        console.log('🔐 [AuthCallback] Found OAuth tokens in URL');

        // Create session object from URL parameters
        const sessionData = {
          access_token: accessToken,
          refresh_token: refreshToken,
          expires_at: parseInt(expiresAt),
          token_type: 'bearer'
        };

        // Decode the JWT to get user info
        const tokenPayload = JSON.parse(atob(accessToken.split('.')[1]));
        const userData = {
          user: {
            id: tokenPayload.sub,
            email: tokenPayload.email,
            user_metadata: tokenPayload.user_metadata || {},
            app_metadata: tokenPayload.app_metadata || {}
          }
        };

        console.log('🔐 [AuthCallback] Processing authentication for:', userData.user.email);

        // Use the enhanced auth profile fixer with access token
        console.log('🔧 [AuthCallback] Calling fixAuthProfile...');
        const attorneyProfile = await fixAuthProfile(userData.user, accessToken);
        console.log('🔧 [AuthCallback] fixAuthProfile result:', attorneyProfile);

        if (attorneyProfile) {
          // User has an attorney profile, store and redirect to dashboard
          console.log('✅ [AuthCallback] Attorney profile found/created:', {
            id: attorneyProfile.id,
            firm_name: attorneyProfile.firm_name,
            subdomain: attorneyProfile.subdomain,
            email: attorneyProfile.email
          });

          // Store in localStorage for dashboard access
          localStorage.setItem('attorney', JSON.stringify(attorneyProfile));
          console.log('💾 [AuthCallback] Attorney profile stored in localStorage');

          // Show subtle success notification
          notificationManager.success(
            `${attorneyProfile.firm_name} assistant configured successfully! Your subdomain is ready.`,
            {
              duration: 4000,
              position: 'top-center'
            }
          );

          // Force navigation to dashboard with full URL
          const dashboardUrl = `${window.location.origin}/dashboard`;
          console.log('🚀 [AuthCallback] Redirecting to dashboard:', dashboardUrl);

          // Delay redirect slightly to show notification
          setTimeout(() => {
            window.location.href = dashboardUrl;
          }, 1500);
        } else {
          // User needs to complete profile setup
          console.log('📝 [AuthCallback] No attorney profile found, redirecting to complete profile');
          navigate('/complete-profile');
        }
      } catch (err) {
        console.error('Auth callback error:', err);

        // Provide more specific error messages based on the error type
        let errorMessage = 'Authentication failed. Please try again.';

        if (err.message?.includes('manage-auth-state')) {
          errorMessage = 'Authentication service temporarily unavailable. Please try signing in again.';
        } else if (err.message?.includes('Vapi')) {
          errorMessage = 'Voice service connection issue. Your account is being set up, please wait a moment and try again.';
        } else if (err.message?.includes('network') || err.message?.includes('fetch')) {
          errorMessage = 'Network connection issue. Please check your internet connection and try again.';
        } else if (err.message?.includes('token') || err.message?.includes('access')) {
          errorMessage = 'Authentication token issue. Please try signing in again.';
        } else if (err.message) {
          errorMessage = err.message;
        }

        setError(errorMessage);

        // Add a retry mechanism for certain types of errors
        if (err.message?.includes('manage-auth-state') || err.message?.includes('network')) {
          console.log('🔄 [AuthCallback] Retryable error detected, will show retry option');
          // You could add a retry button here in the future
        }
      } finally {
        setLoading(false);
      }
    };

    handleCallback();
  }, [navigate]);



  if (loading) {
    return (
      <div className="auth-callback-container">
        <div className="loading-spinner"></div>
        <p>Completing authentication...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="auth-callback-container">
        <div className="error-message">
          <h2>Authentication Error</h2>
          <p>{error}</p>
          <button onClick={() => navigate('/')}>Return to Home</button>
        </div>
      </div>
    );
  }

  return (
    <div className="auth-callback-container">
      <div className="success-message">
        <h2>Authentication Successful</h2>
        <p>Redirecting to your dashboard...</p>
      </div>
    </div>
  );
};

export default AuthCallback;
