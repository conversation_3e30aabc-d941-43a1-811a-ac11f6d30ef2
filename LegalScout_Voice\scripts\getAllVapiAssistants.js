/**
 * Get All Vapi Assistants Script
 * 
 * This script fetches ALL assistants from your Vapi account using direct API calls.
 * It bypasses the MCP server limitations and gets complete data.
 */

// Configuration
const VAPI_API_URL = 'https://api.vapi.ai';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564'; // Your private key from the codebase
const MAX_LIMIT = 1000; // Maximum allowed by Vapi API

/**
 * Make a request to the Vapi API
 */
async function makeVapiRequest(endpoint, options = {}) {
  const url = `${VAPI_API_URL}${endpoint}`;
  
  const response = await fetch(url, {
    ...options,
    headers: {
      'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
      'Content-Type': 'application/json',
      ...options.headers
    }
  });

  if (!response.ok) {
    throw new Error(`Vapi API Error: ${response.status} ${response.statusText}`);
  }

  return await response.json();
}

/**
 * Get all assistants with pagination support
 */
async function getAllAssistants() {
  console.log('🔍 Fetching ALL assistants from Vapi...');
  
  try {
    // First, try to get up to 1000 assistants in one call
    const assistants = await makeVapiRequest(`/assistant?limit=${MAX_LIMIT}`);
    
    console.log(`✅ Found ${assistants.length} assistants in your Vapi account`);
    
    return assistants;
  } catch (error) {
    console.error('❌ Error fetching assistants:', error.message);
    throw error;
  }
}

/**
 * Get detailed information for each assistant
 */
async function getAssistantDetails(assistantIds) {
  console.log(`🔍 Getting detailed information for ${assistantIds.length} assistants...`);
  
  const detailedAssistants = [];
  
  for (let i = 0; i < assistantIds.length; i++) {
    const assistantId = assistantIds[i];
    try {
      console.log(`📋 Getting details for assistant ${i + 1}/${assistantIds.length}: ${assistantId}`);
      const assistant = await makeVapiRequest(`/assistant/${assistantId}`);
      detailedAssistants.push(assistant);
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      console.error(`❌ Error getting details for assistant ${assistantId}:`, error.message);
      // Continue with other assistants
    }
  }
  
  return detailedAssistants;
}

/**
 * Analyze assistants for associations
 */
function analyzeAssistants(assistants) {
  console.log('\n📊 ASSISTANT ANALYSIS');
  console.log('='.repeat(50));
  
  const analysis = {
    total: assistants.length,
    byName: {},
    byCreationDate: {},
    withWebhooks: [],
    withTools: [],
    withPhoneNumbers: [],
    duplicateNames: []
  };
  
  assistants.forEach(assistant => {
    // Group by name
    if (!analysis.byName[assistant.name]) {
      analysis.byName[assistant.name] = [];
    }
    analysis.byName[assistant.name].push(assistant);
    
    // Group by creation date
    const creationDate = assistant.createdAt.split('T')[0];
    if (!analysis.byCreationDate[creationDate]) {
      analysis.byCreationDate[creationDate] = 0;
    }
    analysis.byCreationDate[creationDate]++;
    
    // Check for webhooks/server URLs
    if (assistant.server?.url) {
      analysis.withWebhooks.push({
        id: assistant.id,
        name: assistant.name,
        webhookUrl: assistant.server.url
      });
    }
    
    // Check for tools
    if (assistant.toolIds && assistant.toolIds.length > 0) {
      analysis.withTools.push({
        id: assistant.id,
        name: assistant.name,
        toolCount: assistant.toolIds.length,
        toolIds: assistant.toolIds
      });
    }
  });
  
  // Find duplicate names
  Object.entries(analysis.byName).forEach(([name, assistants]) => {
    if (assistants.length > 1) {
      analysis.duplicateNames.push({
        name,
        count: assistants.length,
        assistants: assistants.map(a => ({ id: a.id, createdAt: a.createdAt }))
      });
    }
  });
  
  return analysis;
}

/**
 * Print analysis results
 */
function printAnalysis(analysis) {
  console.log(`📈 Total Assistants: ${analysis.total}`);
  
  console.log('\n🏷️  Assistants by Name:');
  Object.entries(analysis.byName).forEach(([name, assistants]) => {
    console.log(`  • ${name}: ${assistants.length} assistant(s)`);
    if (assistants.length > 1) {
      assistants.forEach(a => {
        console.log(`    - ${a.id} (created: ${a.createdAt})`);
      });
    }
  });
  
  console.log('\n📅 Assistants by Creation Date:');
  Object.entries(analysis.byCreationDate)
    .sort(([a], [b]) => b.localeCompare(a))
    .forEach(([date, count]) => {
      console.log(`  • ${date}: ${count} assistant(s)`);
    });
  
  if (analysis.withWebhooks.length > 0) {
    console.log('\n🔗 Assistants with Webhooks:');
    analysis.withWebhooks.forEach(assistant => {
      console.log(`  • ${assistant.name} (${assistant.id})`);
      console.log(`    Webhook: ${assistant.webhookUrl}`);
    });
  }
  
  if (analysis.withTools.length > 0) {
    console.log('\n🛠️  Assistants with Tools:');
    analysis.withTools.forEach(assistant => {
      console.log(`  • ${assistant.name} (${assistant.id})`);
      console.log(`    Tools: ${assistant.toolCount} (${assistant.toolIds.join(', ')})`);
    });
  }
  
  if (analysis.duplicateNames.length > 0) {
    console.log('\n⚠️  Duplicate Assistant Names:');
    analysis.duplicateNames.forEach(duplicate => {
      console.log(`  • "${duplicate.name}" appears ${duplicate.count} times:`);
      duplicate.assistants.forEach(a => {
        console.log(`    - ${a.id} (created: ${a.createdAt})`);
      });
    });
  }
}

/**
 * Main execution function
 */
async function main() {
  try {
    console.log('🚀 Starting Vapi Assistant Analysis...\n');
    
    // Get all assistants
    const assistants = await getAllAssistants();
    
    if (assistants.length === 0) {
      console.log('ℹ️  No assistants found in your Vapi account.');
      return;
    }
    
    // Get detailed information for each assistant
    const assistantIds = assistants.map(a => a.id);
    const detailedAssistants = await getAssistantDetails(assistantIds);
    
    // Analyze the assistants
    const analysis = analyzeAssistants(detailedAssistants);
    
    // Print the analysis
    printAnalysis(analysis);
    
    // Save detailed data to file
    const fs = await import('fs');
    const outputFile = 'vapi-assistants-analysis.json';
    
    const outputData = {
      timestamp: new Date().toISOString(),
      summary: analysis,
      assistants: detailedAssistants
    };
    
    fs.writeFileSync(outputFile, JSON.stringify(outputData, null, 2));
    console.log(`\n💾 Detailed data saved to: ${outputFile}`);
    
    console.log('\n✅ Analysis complete!');
    
  } catch (error) {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { getAllAssistants, getAssistantDetails, analyzeAssistants };
