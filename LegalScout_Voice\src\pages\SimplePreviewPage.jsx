import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import SimplifiedPreview from '../components/preview/SimplifiedPreview';
import EnhancedPreviewNew from '../components/preview/EnhancedPreviewNew';

/**
 * SimplePreviewPage Component
 *
 * This component renders a preview of the attorney's agent with the specified configuration.
 * It accepts URL parameters for all configuration options and passes them to the preview component.
 *
 * @returns {JSX.Element} The SimplePreviewPage component
 */
const SimplePreviewPage = () => {
  const [searchParams] = useSearchParams();
  const [isLoading, setIsLoading] = useState(true);
  const [config, setConfig] = useState({
    // Firm details
    firmName: 'Your Law Firm',
    titleText: '',  // Add titleText field
    attorneyName: 'Your Name',
    practiceAreas: [],
    state: '',
    practiceDescription: "Your AI legal assistant is ready to help",

    // Visual customization
    primaryColor: '#4B74AA',
    secondaryColor: '#2C3E50',
    buttonColor: '#D85722',
    backgroundColor: '#1a1a1a',
    backgroundOpacity: 0.9,
    buttonText: 'Start Consultation',
    buttonOpacity: 1,
    practiceAreaBackgroundOpacity: 0.1,
    textBackgroundColor: '#634C38',

    // Content
    welcomeMessage: "Hello! I'm Scout, your legal assistant. How can I help you today?",
    informationGathering: "Tell me about your situation, and I'll help find the right solution for you.",

    // Assets
    logoUrl: '/PRIMARY CLEAR.png',
    mascot: '/PRIMARY CLEAR.png',

    // Vapi configuration
    vapiInstructions: '',
    vapiContext: '',

    // Theme
    theme: 'dark',

    // Preview type
    useEnhancedPreview: false
  });

  // OPTIMAL: Load assistant config using simple subdomain service
  const loadAssistantConfig = async (subdomain) => {
    try {
      console.log('SimplePreviewPage: Loading assistant config using simple service for subdomain:', subdomain);

      const { simpleSubdomainService } = await import('../services/simpleSubdomainService');
      const config = await simpleSubdomainService.getSubdomainConfig(subdomain);

      if (!config) {
        console.error('SimplePreviewPage: No assistant config found for subdomain:', subdomain);
        return null;
      }

      console.log('SimplePreviewPage: Successfully loaded assistant config:', {
        subdomain: config.subdomain,
        firmName: config.firmName,
        assistant_id: config.assistant_id,
        loadedVia: config.loadedVia
      });

      return config;
    } catch (error) {
      console.error('SimplePreviewPage: Error loading assistant config:', error);
      return null;
    }
  };

  // Parse URL parameters on component mount
  useEffect(() => {
    const loadConfig = async () => {
      console.log('SimplePreviewPage: Starting config load...');
      console.log('SimplePreviewPage: URL search params:', Object.fromEntries(searchParams.entries()));

      const newConfig = { ...config };

      // Extract parameters from URL
      for (const [key, value] of searchParams.entries()) {
        if (key === 'practiceAreas') {
          try {
            newConfig[key] = JSON.parse(value);
          } catch (e) {
            newConfig[key] = value.split(',');
          }
        } else if (key === 'backgroundOpacity' || key === 'buttonOpacity' || key === 'practiceAreaBackgroundOpacity') {
          newConfig[key] = parseFloat(value);
        } else if (key === 'useEnhancedPreview' || key === 'loadFromSupabase') {
          newConfig[key] = value === 'true';
        } else {
          newConfig[key] = value;
        }
      }

      // OPTIMAL: Load assistant config using simple service (always for subdomains)
      if (newConfig.subdomain && newConfig.subdomain !== 'default') {
        console.log('SimplePreviewPage: Loading assistant config for subdomain:', newConfig.subdomain);

        const assistantConfig = await loadAssistantConfig(newConfig.subdomain);

        if (assistantConfig) {
          console.log('SimplePreviewPage: Assistant config loaded successfully:', {
            firmName: assistantConfig.firmName,
            assistant_id: assistantConfig.assistant_id,
            loadedVia: assistantConfig.loadedVia
          });

          // Store original config for comparison
          const originalConfig = { ...newConfig };

          // Merge the assistant config with URL parameters
          // Assistant config takes precedence for consistency
          Object.assign(newConfig, {
            firmName: assistantConfig.firmName,
            primaryColor: assistantConfig.primaryColor,
            secondaryColor: assistantConfig.secondaryColor,
            buttonColor: assistantConfig.buttonColor,
            backgroundColor: assistantConfig.backgroundColor,
            backgroundOpacity: assistantConfig.backgroundOpacity,
            buttonOpacity: assistantConfig.buttonOpacity,
            practiceAreaBackgroundOpacity: assistantConfig.practiceAreaBackgroundOpacity,
            textBackgroundColor: assistantConfig.textBackgroundColor,
            welcomeMessage: assistantConfig.welcomeMessage,
            informationGathering: assistantConfig.informationGathering,
            practiceDescription: assistantConfig.practiceDescription,
            logoUrl: assistantConfig.logoUrl,
            mascot: assistantConfig.mascot || assistantConfig.buttonImage,
            vapiInstructions: assistantConfig.vapiInstructions,
            vapiContext: assistantConfig.vapiContext,
            vapi_assistant_id: assistantConfig.vapi_assistant_id,
            voiceId: assistantConfig.voiceId,
            aiModel: assistantConfig.aiModel,
            officeAddress: assistantConfig.officeAddress,
            schedulingLink: assistantConfig.schedulingLink,
            practiceAreas: assistantConfig.practiceAreas || []
          });

          console.log('SimplePreviewPage: Config merged with assistant data:', {
            firmName: newConfig.firmName,
            primaryColor: newConfig.primaryColor,
            vapi_assistant_id: newConfig.vapi_assistant_id
          });
        } else {
          console.warn('SimplePreviewPage: No assistant config found for subdomain:', newConfig.subdomain);
        }
      } else {
        console.log('SimplePreviewPage: Using default config (no subdomain or default subdomain)');
      }

      console.log('SimplePreviewPage: Final config:', {
        firmName: newConfig.firmName,
        primaryColor: newConfig.primaryColor,
        vapi_assistant_id: newConfig.vapi_assistant_id,
        subdomain: newConfig.subdomain
      });

      setConfig(newConfig);
      setIsLoading(false);
    };

    loadConfig();
  }, [searchParams]);

  // Listen for messages from parent window (for iframe communication)
  useEffect(() => {
    const handleMessage = (event) => {
      console.log('🎯 [SimplePreviewPage] Received message from parent:', event.data);

      // Handle both message types for compatibility
      if (event.data && (event.data.type === 'UPDATE_PREVIEW_CONFIG' || event.data.type === 'PREVIEW_CONFIG_UPDATE')) {
        console.log('🎯 [SimplePreviewPage] Processing config update from parent:', event.data.config);
        setConfig(prevConfig => ({ ...prevConfig, ...event.data.config }));
        console.log('🎯 [SimplePreviewPage] Config updated successfully');

        // Send response back to parent for PREVIEW_CONFIG_UPDATE messages
        if (event.data.type === 'PREVIEW_CONFIG_UPDATE' && window !== window.parent) {
          try {
            window.parent.postMessage({
              type: 'PREVIEW_CONFIG_UPDATE_RESPONSE',
              success: true,
              timestamp: event.data.timestamp
            }, '*');
            console.log('🎯 [SimplePreviewPage] Sent response back to parent');
          } catch (e) {
            console.error('🚨 [SimplePreviewPage] Error sending response to parent:', e);
          }
        }
      }
    };

    window.addEventListener('message', handleMessage);

    // Send ready message to parent with retry mechanism
    if (window !== window.parent) {
      const sendReadyMessage = () => {
        try {
          window.parent.postMessage({ type: 'PREVIEW_READY' }, '*');
          console.log('🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent');
        } catch (e) {
          console.error('🚨 [SimplePreviewPage] Error sending PREVIEW_READY message to parent:', e);
        }
      };

      // Send immediately
      sendReadyMessage();

      // Also send after a short delay to ensure parent is ready
      setTimeout(sendReadyMessage, 100);
      setTimeout(sendReadyMessage, 500);
    }

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  if (isLoading) {
    return (
      <div className="simple-preview-page" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <div>Loading attorney information...</div>
      </div>
    );
  }

  return (
    <div className="simple-preview-page">
      {config.useEnhancedPreview ? (
        <EnhancedPreviewNew
          firmName={config.firmName}
          titleText={config.titleText}
          attorneyName={config.attorneyName}
          practiceAreas={config.practiceAreas}
          state={config.state}
          practiceDescription={config.practiceDescription}
          primaryColor={config.primaryColor}
          secondaryColor={config.secondaryColor}
          backgroundColor={config.backgroundColor}
          backgroundOpacity={config.backgroundOpacity}
          buttonText={config.buttonText}
          buttonOpacity={config.buttonOpacity}
          practiceAreaBackgroundOpacity={config.practiceAreaBackgroundOpacity}
          textBackgroundColor={config.textBackgroundColor}
          welcomeMessage={config.welcomeMessage}
          informationGathering={config.informationGathering}
          logoUrl={config.logoUrl}
          mascot={config.mascot}
          vapiInstructions={config.vapiInstructions}
          vapiContext={config.vapiContext}
          theme={config.theme}
          subdomain={config.subdomain}
          vapiAssistantId={config.vapi_assistant_id}
        />
      ) : (
        <SimplifiedPreview
          firmName={config.firmName}
          titleText={config.titleText}
          primaryColor={config.primaryColor}
          secondaryColor={config.secondaryColor}
          buttonColor={config.buttonColor}
          backgroundColor={config.backgroundColor}
          backgroundOpacity={config.backgroundOpacity}
          practiceDescription={config.practiceDescription}
          welcomeMessage={config.welcomeMessage}
          informationGathering={config.informationGathering}
          theme={config.theme}
          logoUrl={config.logoUrl}
          buttonText={config.buttonText}
          mascot={config.mascot || config.buttonImageUrl || config.logoUrl}
          vapiInstructions={config.vapiInstructions}
          vapiContext={config.vapiContext}
          buttonOpacity={config.buttonOpacity}
          practiceAreaBackgroundOpacity={config.practiceAreaBackgroundOpacity}
          textBackgroundColor={config.textBackgroundColor}
          vapi_assistant_id={config.vapi_assistant_id}
          subdomain={config.subdomain}
        />
      )}
    </div>
  );
};

export default SimplePreviewPage;
