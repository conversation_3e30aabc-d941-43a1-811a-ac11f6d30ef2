/**
 * Test Production Fixes
 * 
 * This script tests the production fixes to ensure they work correctly
 * before deployment.
 */

const fs = require('fs');
const path = require('path');

// Test environment variable injection
const testEnvironmentInjection = () => {
  console.log('\n🧪 Testing Environment Variable Injection...');
  
  const distPath = path.join(process.cwd(), 'dist');
  const indexPath = path.join(distPath, 'index.html');
  const envInjectionPath = path.join(distPath, 'env-injection.js');
  
  let passed = 0;
  let total = 0;
  
  // Test 1: Check if dist directory exists
  total++;
  if (fs.existsSync(distPath)) {
    console.log('✅ dist directory exists');
    passed++;
  } else {
    console.log('❌ dist directory does not exist');
  }
  
  // Test 2: Check if index.html exists
  total++;
  if (fs.existsSync(indexPath)) {
    console.log('✅ index.html exists');
    passed++;
  } else {
    console.log('❌ index.html does not exist');
  }
  
  // Test 3: Check if environment injection script exists
  total++;
  if (fs.existsSync(envInjectionPath)) {
    console.log('✅ env-injection.js exists');
    passed++;
  } else {
    console.log('❌ env-injection.js does not exist');
  }
  
  // Test 4: Check if index.html contains environment variables
  if (fs.existsSync(indexPath)) {
    total++;
    const indexContent = fs.readFileSync(indexPath, 'utf8');
    if (indexContent.includes('VITE_SUPABASE_URL') && indexContent.includes('VITE_VAPI_PUBLIC_KEY')) {
      console.log('✅ index.html contains environment variable injection');
      passed++;
    } else {
      console.log('❌ index.html does not contain environment variable injection');
    }
  }
  
  console.log(`\n📊 Environment Injection Tests: ${passed}/${total} passed`);
  return { passed, total, success: passed === total };
};

// Test file structure
const testFileStructure = () => {
  console.log('\n🧪 Testing File Structure...');
  
  const requiredFiles = [
    'src/utils/environmentVerifier.js',
    'src/utils/productionRoutingFix.js',
    'src/utils/productionInitializer.js',
    'scripts/production-environment-injector.js',
    'vite.config.js',
    'vercel.json',
    'package.json'
  ];
  
  let passed = 0;
  let total = requiredFiles.length;
  
  requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file} exists`);
      passed++;
    } else {
      console.log(`❌ ${file} does not exist`);
    }
  });
  
  console.log(`\n📊 File Structure Tests: ${passed}/${total} passed`);
  return { passed, total, success: passed === total };
};

// Test package.json scripts
const testPackageScripts = () => {
  console.log('\n🧪 Testing Package.json Scripts...');
  
  const packagePath = path.join(process.cwd(), 'package.json');
  
  if (!fs.existsSync(packagePath)) {
    console.log('❌ package.json does not exist');
    return { passed: 0, total: 1, success: false };
  }
  
  const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  const scripts = packageContent.scripts || {};
  
  let passed = 0;
  let total = 0;
  
  // Test required scripts
  const requiredScripts = [
    'vercel-build-safe',
    'build'
  ];
  
  requiredScripts.forEach(script => {
    total++;
    if (scripts[script] && scripts[script].includes('production-environment-injector.js')) {
      console.log(`✅ ${script} includes environment injector`);
      passed++;
    } else {
      console.log(`❌ ${script} does not include environment injector`);
    }
  });
  
  console.log(`\n📊 Package Scripts Tests: ${passed}/${total} passed`);
  return { passed, total, success: passed === total };
};

// Test Vite configuration
const testViteConfig = () => {
  console.log('\n🧪 Testing Vite Configuration...');
  
  const viteConfigPath = path.join(process.cwd(), 'vite.config.js');
  
  if (!fs.existsSync(viteConfigPath)) {
    console.log('❌ vite.config.js does not exist');
    return { passed: 0, total: 1, success: false };
  }
  
  const viteContent = fs.readFileSync(viteConfigPath, 'utf8');
  
  let passed = 0;
  let total = 0;
  
  // Test environment variable definitions
  const requiredEnvVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_KEY',
    'VITE_VAPI_PUBLIC_KEY',
    'VITE_VAPI_SECRET_KEY'
  ];
  
  requiredEnvVars.forEach(envVar => {
    total++;
    if (viteContent.includes(`'import.meta.env.${envVar}'`)) {
      console.log(`✅ ${envVar} is defined in vite.config.js`);
      passed++;
    } else {
      console.log(`❌ ${envVar} is not defined in vite.config.js`);
    }
  });
  
  console.log(`\n📊 Vite Config Tests: ${passed}/${total} passed`);
  return { passed, total, success: passed === total };
};

// Test Vercel configuration
const testVercelConfig = () => {
  console.log('\n🧪 Testing Vercel Configuration...');
  
  const vercelConfigPath = path.join(process.cwd(), 'vercel.json');
  
  if (!fs.existsSync(vercelConfigPath)) {
    console.log('❌ vercel.json does not exist');
    return { passed: 0, total: 1, success: false };
  }
  
  const vercelContent = JSON.parse(fs.readFileSync(vercelConfigPath, 'utf8'));
  
  let passed = 0;
  let total = 0;
  
  // Test build command
  total++;
  if (vercelContent.buildCommand && vercelContent.buildCommand.includes('vercel-build-safe')) {
    console.log('✅ Vercel uses vercel-build-safe command');
    passed++;
  } else {
    console.log('❌ Vercel does not use vercel-build-safe command');
  }
  
  // Test environment variables
  const requiredEnvVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_KEY',
    'VITE_VAPI_PUBLIC_KEY',
    'VITE_VAPI_SECRET_KEY'
  ];
  
  requiredEnvVars.forEach(envVar => {
    total++;
    if (vercelContent.env && vercelContent.env[envVar]) {
      console.log(`✅ ${envVar} is defined in vercel.json`);
      passed++;
    } else {
      console.log(`❌ ${envVar} is not defined in vercel.json`);
    }
  });
  
  // Test rewrites
  total++;
  if (vercelContent.rewrites && vercelContent.rewrites.length > 0) {
    console.log('✅ Vercel rewrites are configured');
    passed++;
  } else {
    console.log('❌ Vercel rewrites are not configured');
  }
  
  console.log(`\n📊 Vercel Config Tests: ${passed}/${total} passed`);
  return { passed, total, success: passed === total };
};

// Main test function
const runAllTests = () => {
  console.log('🚀 Running Production Fixes Tests...\n');
  
  const results = [];
  
  // Run all tests
  results.push(testFileStructure());
  results.push(testPackageScripts());
  results.push(testViteConfig());
  results.push(testVercelConfig());
  results.push(testEnvironmentInjection());
  
  // Calculate overall results
  const totalPassed = results.reduce((sum, result) => sum + result.passed, 0);
  const totalTests = results.reduce((sum, result) => sum + result.total, 0);
  const allPassed = results.every(result => result.success);
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 OVERALL TEST RESULTS');
  console.log('='.repeat(50));
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${totalPassed}`);
  console.log(`Failed: ${totalTests - totalPassed}`);
  console.log(`Success Rate: ${Math.round((totalPassed / totalTests) * 100)}%`);
  
  if (allPassed) {
    console.log('\n✅ ALL TESTS PASSED! Production fixes are ready for deployment.');
  } else {
    console.log('\n❌ SOME TESTS FAILED! Please fix the issues before deploying.');
  }
  
  return allPassed;
};

// Run tests if called directly
if (require.main === module) {
  const success = runAllTests();
  process.exit(success ? 0 : 1);
}

module.exports = {
  runAllTests,
  testEnvironmentInjection,
  testFileStructure,
  testPackageScripts,
  testViteConfig,
  testVercelConfig
};
