/**
 * Browser-based test for assistant switching and custom fields fixes
 * Run this in the browser console to test the fixes
 */

console.log('🧪 Testing Assistant Switching and Custom Fields Fixes - UPDATED');
console.log('================================================================');
console.log('🔧 Fixed Issues:');
console.log('  1. Custom fields now clear when switching assistants');
console.log('  2. Briefs tab now updates when switching assistants');
console.log('  3. Fixed parameter order in assistantDataRefreshService calls');
console.log('  4. Added better error handling for Vapi 404 responses');
console.log('================================================================');

// Test 1: Check if custom fields clear when switching assistants
function testCustomFieldsClearing() {
  console.log('\n🔍 Test 1: Custom Fields Clearing on Assistant Switch');

  // Look for custom fields inputs
  const customFieldInputs = document.querySelectorAll('input[placeholder*="field"], input[name*="custom"]');
  console.log(`Found ${customFieldInputs.length} custom field inputs`);

  // Look for assistant dropdown (enhanced version)
  const assistantDropdowns = document.querySelectorAll('.dropdown-trigger, select');
  const assistantDropdown = Array.from(assistantDropdowns).find(element =>
    element.textContent?.includes('Assistant') ||
    element.textContent?.includes('agent') ||
    element.innerHTML?.includes('Assistant') ||
    element.innerHTML?.includes('agent')
  );

  if (assistantDropdown) {
    console.log('✅ Found assistant dropdown');
    console.log('Current content:', assistantDropdown.textContent?.substring(0, 100));

    // Check for enhanced dropdown structure
    const dropdownOptions = assistantDropdown.querySelectorAll('.dropdown-option, option');
    console.log(`Available options: ${dropdownOptions.length}`);
    dropdownOptions.forEach((opt, i) => {
      if (i < 5) { // Show first 5 options
        console.log(`  Option ${i}: ${opt.textContent?.substring(0, 50)}`);
      }
    });
  } else {
    console.log('⚠️ Assistant dropdown not found');
  }

  return { customFieldInputs: customFieldInputs.length, hasDropdown: !!assistantDropdown };
}

// Test 2: Check for assistant creation errors
function testAssistantCreationErrors() {
  console.log('\n🔍 Test 2: Assistant Creation Error Prevention');
  
  // Monitor console errors
  const originalError = console.error;
  let errorCount = 0;
  const errors = [];
  
  console.error = function(...args) {
    const errorMessage = args.join(' ');
    if (errorMessage.includes('Cannot read properties of null') && 
        errorMessage.includes('firm_name')) {
      errorCount++;
      errors.push(errorMessage);
      console.log('❌ Caught assistant creation error:', errorMessage);
    }
    originalError.apply(console, args);
  };
  
  // Restore after 10 seconds
  setTimeout(() => {
    console.error = originalError;
    console.log(`\n📊 Error monitoring complete. Found ${errorCount} assistant creation errors.`);
    if (errors.length > 0) {
      console.log('Errors found:', errors);
    }
  }, 10000);
  
  return { monitoring: true };
}

// Test 3: Check name field behavior
function testNameFieldBehavior() {
  console.log('\n🔍 Test 3: Name Field Update Behavior');
  
  // Find name/title inputs
  const nameInputs = document.querySelectorAll('input[name="titleText"], input[placeholder*="name"], input[placeholder*="firm"]');
  console.log(`Found ${nameInputs.length} name-related inputs`);
  
  nameInputs.forEach((input, index) => {
    console.log(`Input ${index}:`, {
      name: input.name,
      value: input.value,
      placeholder: input.placeholder
    });
  });
  
  return { nameInputs: nameInputs.length };
}

// Test 4: Check assistant context
function testAssistantContext() {
  console.log('\n🔍 Test 4: Assistant Context State');
  
  // Check if React DevTools or context is available
  if (window.React) {
    console.log('✅ React is available');
  }
  
  // Check for assistant-related state in localStorage
  const storedData = {};
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key.includes('assistant') || key.includes('attorney')) {
      try {
        storedData[key] = JSON.parse(localStorage.getItem(key));
      } catch {
        storedData[key] = localStorage.getItem(key);
      }
    }
  }
  
  console.log('Stored assistant/attorney data:', storedData);
  
  return { storedData };
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Running All Tests...\n');
  
  const results = {
    customFields: testCustomFieldsClearing(),
    assistantCreation: testAssistantCreationErrors(),
    nameField: testNameFieldBehavior(),
    context: testAssistantContext()
  };
  
  console.log('\n📊 Test Results Summary:', results);
  
  // Instructions for manual testing
  console.log('\n📋 Manual Testing Instructions:');
  console.log('1. Switch between assistants using the dropdown');
  console.log('2. Check if custom fields data changes/clears');
  console.log('3. Try changing the name field in Agent tab');
  console.log('4. Verify no new assistants are created accidentally');
  console.log('5. Check browser console for any errors');
  
  return results;
}

// Auto-run tests
runAllTests();

// Make functions available globally for manual testing
window.testAssistantFixes = {
  runAllTests,
  testCustomFieldsClearing,
  testAssistantCreationErrors,
  testNameFieldBehavior,
  testAssistantContext
};

console.log('\n💡 Tests complete! Use window.testAssistantFixes to run individual tests.');
