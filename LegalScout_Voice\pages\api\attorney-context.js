// API endpoint for attorney_context_api tool (TRUE API Request)
// Provides attorney-specific functionality with automatic context detection

import { supabase } from '../../lib/supabase';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false,
      error: 'Method not allowed' 
    });
  }

  try {
    // For API Request tools, data comes directly in req.body
    const { assistantId, callId, action, clientInfo, caseInfo } = req.body;
    
    if (!assistantId) {
      return res.status(400).json({ 
        success: false,
        error: 'Assistant ID is required' 
      });
    }

    if (!action) {
      return res.status(400).json({ 
        success: false,
        error: 'Action is required' 
      });
    }

    // Get attorney context from assistant ID
    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .or(`vapi_assistant_id.eq.${assistantId},current_assistant_id.eq.${assistantId}`)
      .single();
    
    if (attorneyError || !attorney) {
      return res.status(200).json({
        success: false,
        message: "I'm having trouble accessing attorney information right now. Please try again or contact us directly.",
        data: { assistantId, action }
      });
    }

    let result;
    
    switch (action) {
      case 'get_attorney_info':
        result = `I'm an AI assistant for ${attorney.firm_name}. ${
          attorney.name ? `I work with ${attorney.name}. ` : ''
        }${
          attorney.office_address || attorney.address ? 
          `We're located at ${attorney.office_address || attorney.address}. ` : ''
        }${
          attorney.practice_areas?.length ? 
          `We specialize in ${attorney.practice_areas.join(', ')}.` : 
          'We handle various legal matters.'
        }`;
        break;
        
      case 'get_practice_areas':
        const areas = attorney.practice_areas?.length ? 
          attorney.practice_areas.join(', ') : 
          'General Legal Services';
        result = `${attorney.firm_name} specializes in: ${areas}. We're here to help with any questions you might have about these areas of law.`;
        break;
        
      case 'get_contact_info':
        result = `You can reach ${attorney.firm_name} at:${
          attorney.phone ? `\nPhone: ${attorney.phone}` : ''
        }\nEmail: ${attorney.email}${
          attorney.office_address || attorney.address ? 
          `\nAddress: ${attorney.office_address || attorney.address}` : ''
        }${
          attorney.scheduling_link ? 
          `\nSchedule online: ${attorney.scheduling_link}` : ''
        }`;
        break;
        
      case 'get_office_hours':
        result = `Our office hours are Monday through Friday, 9 AM to 5 PM. For urgent matters outside these hours, please leave a message and we'll get back to you as soon as possible.${
          attorney.phone ? ` You can reach us at ${attorney.phone}` : ''
        } or email us at ${attorney.email}.`;
        break;
        
      case 'schedule_consultation':
        if (!clientInfo?.name || !clientInfo?.email) {
          result = "To schedule a consultation, I'll need your name and email address. Could you please provide those?";
          break;
        }
        
        // Create consultation record
        const { data: consultation, error: consultationError } = await supabase
          .from('consultations')
          .insert({
            attorney_id: attorney.id,
            client_name: clientInfo.name,
            client_email: clientInfo.email,
            client_phone: clientInfo.phone,
            legal_issue: caseInfo?.legalIssue,
            urgency: caseInfo?.urgency || 'medium',
            status: 'pending',
            created_at: new Date().toISOString()
          })
          .select()
          .single();
          
        if (consultationError) {
          console.error('Error creating consultation:', consultationError);
          result = `I encountered an issue scheduling your consultation. Please call ${attorney.firm_name} directly${attorney.phone ? ` at ${attorney.phone}` : ''} or email ${attorney.email}.`;
        } else {
          result = `I've scheduled a consultation for ${clientInfo.name} with ${attorney.firm_name}. You'll receive a confirmation email at ${clientInfo.email} with the details. Our team will contact you within 24 hours to confirm the appointment time.`;
        }
        break;
        
      case 'create_case_file':
        if (!clientInfo?.name) {
          result = "To create a case file, I'll need at least your name. Could you please provide that?";
          break;
        }
        
        // Create case file record
        const { data: caseFile, error: caseError } = await supabase
          .from('case_files')
          .insert({
            attorney_id: attorney.id,
            client_name: clientInfo.name,
            client_email: clientInfo.email,
            client_phone: clientInfo.phone,
            case_description: caseInfo?.legalIssue,
            urgency_level: caseInfo?.urgency || 'medium',
            additional_details: caseInfo?.details,
            status: 'new',
            created_at: new Date().toISOString()
          })
          .select()
          .single();
          
        if (caseError) {
          console.error('Error creating case file:', caseError);
          result = `I've noted your information for ${attorney.firm_name} and will make sure our team follows up with you.`;
        } else {
          const caseRef = `CF-${caseFile.id.toString().slice(-8)}`;
          result = `I've created a case file for ${clientInfo.name} with ${attorney.firm_name}. Your case reference number is ${caseRef}. Our legal team will review your case and contact you within 2 business days.`;
        }
        break;
        
      case 'transfer_to_attorney':
        // Log the transfer request
        console.log(`Transfer requested for ${attorney.firm_name}, urgency: ${caseInfo?.urgency}`);
        
        if (caseInfo?.urgency === 'emergency') {
          result = `This appears to be an emergency. Please call our office immediately${attorney.phone ? ` at ${attorney.phone}` : ''} or contact emergency services if this is a life-threatening situation.`;
        } else {
          result = `I'm connecting you with ${attorney.firm_name}. Please hold while I transfer your call.${attorney.phone ? ` If the transfer fails, you can reach us directly at ${attorney.phone}.` : ''}`;
        }
        break;
        
      default:
        result = `I can help you with attorney information, practice areas, contact details, scheduling consultations, and creating case files for ${attorney.firm_name}. What would you like to know?`;
    }

    return res.status(200).json({
      success: true,
      message: result,
      data: {
        attorney: attorney.firm_name,
        action: action,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Attorney context API error:', error);
    
    return res.status(500).json({
      success: false,
      error: "Internal server error",
      message: "I encountered an error processing your request. Please try again or contact us directly."
    });
  }
}
