/* Tool Canvas Styles */
.tool-canvas {
  position: fixed;
  top: 60px;
  right: 0;
  width: 400px;
  height: calc(100vh - 60px);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-left: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  transition: transform 0.3s ease, width 0.3s ease;
}

.tool-canvas.collapsed {
  width: 60px;
}

.tool-canvas.collapsed .tool-canvas-content,
.tool-canvas.collapsed .tool-canvas-tabs {
  display: none;
}

/* Dark mode */
body.dark-mode .tool-canvas {
  background: rgba(20, 20, 20, 0.95);
  border-left-color: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Header */
.tool-canvas-header {
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
}

body.dark-mode .tool-canvas-header {
  background: rgba(20, 20, 20, 0.8);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.header-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.result-count {
  font-size: 12px;
  color: #666;
  margin-left: 8px;
}

body.dark-mode .result-count {
  color: #aaa;
}

.expand-toggle {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.expand-toggle:hover {
  background: rgba(0, 0, 0, 0.1);
}

body.dark-mode .expand-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Tabs */
.tool-canvas-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(248, 249, 250, 0.8);
}

body.dark-mode .tool-canvas-tabs {
  background: rgba(30, 30, 30, 0.8);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.tab-button {
  padding: 6px 12px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  background: white;
  border-radius: 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tab-button:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

.tab-button.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

body.dark-mode .tab-button {
  background: rgba(40, 40, 40, 0.8);
  border-color: rgba(255, 255, 255, 0.2);
  color: white;
}

body.dark-mode .tab-button:hover {
  background: rgba(59, 130, 246, 0.2);
}

.tab-count {
  background: rgba(255, 255, 255, 0.3);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  min-width: 16px;
  text-align: center;
}

.tab-button.active .tab-count {
  background: rgba(255, 255, 255, 0.3);
}

/* Content */
.tool-canvas-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* Empty state */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
  color: #666;
}

body.dark-mode .empty-state {
  color: #aaa;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-message {
  font-size: 14px;
  line-height: 1.5;
  max-width: 280px;
}

/* Tool results */
.tool-results-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tool-result-item {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 12px;
  transition: all 0.3s ease;
}

.tool-result-item.recent {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  animation: pulse-border 2s ease-in-out;
}

@keyframes pulse-border {
  0%, 100% { border-color: #3b82f6; }
  50% { border-color: #60a5fa; }
}

body.dark-mode .tool-result-item {
  background: rgba(40, 40, 40, 0.8);
  border-color: rgba(255, 255, 255, 0.1);
}

/* Tool result header */
.tool-result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.tool-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.tool-name {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
}

body.dark-mode .tool-name {
  color: #f9fafb;
}

.tool-timestamp {
  font-size: 11px;
  color: #6b7280;
}

body.dark-mode .tool-timestamp {
  color: #9ca3af;
}

.tool-status {
  display: flex;
  align-items: center;
  font-size: 16px;
}

/* Spinner for running tools */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Tool content */
.tool-result-content {
  font-size: 13px;
  line-height: 1.4;
}

.content-summary {
  font-weight: 500;
  margin-bottom: 8px;
  color: #374151;
}

body.dark-mode .content-summary {
  color: #d1d5db;
}

/* Category-specific styles */
.tool-result-item.dossier {
  border-left: 4px solid #10b981;
}

.tool-result-item.attorney {
  border-left: 4px solid #8b5cf6;
}

.tool-result-item.legal {
  border-left: 4px solid #f59e0b;
}

.tool-result-item.citations {
  border-left: 4px solid #ef4444;
}

.tool-result-item.scheduling {
  border-left: 4px solid #06b6d4;
}

/* Specific content styles */
.dossier-details,
.attorney-message,
.legal-findings,
.citations-list,
.appointment-details {
  margin-top: 8px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
  font-size: 12px;
}

body.dark-mode .dossier-details,
body.dark-mode .attorney-message,
body.dark-mode .legal-findings,
body.dark-mode .citations-list,
body.dark-mode .appointment-details {
  background: rgba(255, 255, 255, 0.05);
}

.detail-item {
  margin-bottom: 4px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.citation-item {
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.citation-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

body.dark-mode .citation-item {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.citation-title {
  font-weight: 500;
  margin-bottom: 2px;
}

.citation-source {
  color: #6b7280;
  font-size: 11px;
}

body.dark-mode .citation-source {
  color: #9ca3af;
}

/* Responsive */
@media (max-width: 1200px) {
  .tool-canvas {
    width: 320px;
  }
}

@media (max-width: 768px) {
  .tool-canvas {
    width: 100%;
    right: 0;
    transform: translateX(100%);
  }
  
  .tool-canvas.expanded {
    transform: translateX(0);
  }
}
