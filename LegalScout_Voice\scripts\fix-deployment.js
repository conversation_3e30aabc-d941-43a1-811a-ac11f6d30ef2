#!/usr/bin/env node

/**
 * LegalScout Voice - Deployment Fix
 * 
 * This script fixes common deployment issues including:
 * - Package dependency conflicts
 * - Vercel build configuration
 * - Environment variable setup
 */

import fs from 'fs';
import path from 'path';
import { config } from 'dotenv';

// Load environment variables
config();

function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [DeploymentFix]`;
  
  switch(type) {
    case 'success': console.log(`✅ ${prefix} ${message}`); break;
    case 'error': console.error(`❌ ${prefix} ${message}`); break;
    case 'warning': console.warn(`⚠️ ${prefix} ${message}`); break;
    default: console.log(`ℹ️ ${prefix} ${message}`);
  }
}

function createVercelConfig() {
  const vercelConfig = {
    "version": 2,
    "buildCommand": "npm run vercel-build-safe",
    "outputDirectory": "dist",
    "installCommand": "npm install --legacy-peer-deps",
    "framework": null,
    "functions": {
      "api/index.js": {
        "runtime": "nodejs18.x"
      }
    },
    "rewrites": [
      {
        "source": "/api/(.*)",
        "destination": "/api/index.js"
      },
      {
        "source": "/(.*)",
        "destination": "/index.html"
      }
    ],
    "headers": [
      {
        "source": "/(.*)",
        "headers": [
          {
            "key": "X-Content-Type-Options",
            "value": "nosniff"
          },
          {
            "key": "X-Frame-Options",
            "value": "DENY"
          },
          {
            "key": "X-XSS-Protection",
            "value": "1; mode=block"
          }
        ]
      }
    ],
    "env": {
      "NODE_ENV": "production"
    }
  };

  const vercelPath = path.join(process.cwd(), 'vercel.json');
  
  try {
    fs.writeFileSync(vercelPath, JSON.stringify(vercelConfig, null, 2));
    log('Created optimized vercel.json', 'success');
    return true;
  } catch (error) {
    log(`Failed to create vercel.json: ${error.message}`, 'error');
    return false;
  }
}

function createNpmrc() {
  const npmrcContent = `legacy-peer-deps=true
fund=false
audit=false
progress=false
loglevel=error
`;

  const npmrcPath = path.join(process.cwd(), '.npmrc');
  
  try {
    fs.writeFileSync(npmrcPath, npmrcContent);
    log('Created .npmrc with legacy peer deps', 'success');
    return true;
  } catch (error) {
    log(`Failed to create .npmrc: ${error.message}`, 'error');
    return false;
  }
}

function updatePackageJson() {
  const packagePath = path.join(process.cwd(), 'package.json');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // Add safe build script for Vercel
    packageJson.scripts['vercel-build-safe'] = 'cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 VITE_DISABLE_FRAMER_MOTION=true vite build --mode production && node scripts/production-environment-injector.cjs';
    
    // Update engines to be more specific
    packageJson.engines = {
      "node": "18.x",
      "npm": ">=8.0.0"
    };
    
    // Add resolution for problematic packages
    if (!packageJson.overrides) {
      packageJson.overrides = {};
    }
    
    packageJson.overrides = {
      ...packageJson.overrides,
      "express": "^5.1.0",
      "@modelcontextprotocol/sdk": "^1.12.1"
    };
    
    fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
    log('Updated package.json with deployment optimizations', 'success');
    return true;
  } catch (error) {
    log(`Failed to update package.json: ${error.message}`, 'error');
    return false;
  }
}

function cleanDependencies() {
  const nodeModulesPath = path.join(process.cwd(), 'node_modules');
  const packageLockPath = path.join(process.cwd(), 'package-lock.json');
  
  try {
    // Remove node_modules if it exists
    if (fs.existsSync(nodeModulesPath)) {
      fs.rmSync(nodeModulesPath, { recursive: true, force: true });
      log('Removed node_modules directory', 'success');
    }
    
    // Remove package-lock.json if it exists
    if (fs.existsSync(packageLockPath)) {
      fs.unlinkSync(packageLockPath);
      log('Removed package-lock.json', 'success');
    }
    
    return true;
  } catch (error) {
    log(`Failed to clean dependencies: ${error.message}`, 'error');
    return false;
  }
}

function createDeploymentReadinessCheck() {
  const checkScript = `#!/usr/bin/env node

/**
 * Deployment Readiness Check
 */

import fs from 'fs';
import path from 'path';

function checkFile(filePath, description) {
  if (fs.existsSync(filePath)) {
    console.log(\`✅ \${description} exists\`);
    return true;
  } else {
    console.log(\`❌ \${description} missing\`);
    return false;
  }
}

function main() {
  console.log('🚀 Deployment Readiness Check\\n');
  
  let allGood = true;
  
  // Check critical files
  allGood &= checkFile('vercel.json', 'Vercel configuration');
  allGood &= checkFile('.npmrc', 'NPM configuration');
  allGood &= checkFile('src/App.jsx', 'Main application file');
  allGood &= checkFile('api/index.js', 'API handler');
  allGood &= checkFile('package.json', 'Package configuration');
  
  // Check environment variables
  const requiredEnvVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_KEY',
    'VITE_VAPI_PUBLIC_KEY',
    'VITE_VAPI_SECRET_KEY'
  ];
  
  console.log('\\n🔍 Environment Variables:');
  for (const envVar of requiredEnvVars) {
    if (process.env[envVar]) {
      console.log(\`✅ \${envVar} is set\`);
    } else {
      console.log(\`❌ \${envVar} is missing\`);
      allGood = false;
    }
  }
  
  console.log(\`\\n\${allGood ? '✅' : '❌'} Deployment readiness: \${allGood ? 'READY' : 'NOT READY'}\`);
  
  if (!allGood) {
    console.log('\\n💡 Run "npm run fix:deployment" to fix issues');
  }
  
  process.exit(allGood ? 0 : 1);
}

main();
`;

  const checkPath = path.join(process.cwd(), 'scripts/deployment-readiness-check.js');
  
  try {
    fs.writeFileSync(checkPath, checkScript);
    log('Created deployment readiness check script', 'success');
    return true;
  } catch (error) {
    log(`Failed to create readiness check: ${error.message}`, 'error');
    return false;
  }
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  log('🚀 Starting Deployment Fix', 'info');

  switch (command) {
    case 'fix':
      log('Applying deployment fixes...', 'info');
      
      let success = true;
      success &= createVercelConfig();
      success &= createNpmrc();
      success &= updatePackageJson();
      success &= createDeploymentReadinessCheck();
      
      if (success) {
        log('✅ All deployment fixes applied successfully!', 'success');
        log('Next steps:', 'info');
        log('1. Run "npm run clean" to clean dependencies', 'info');
        log('2. Run "npm install --legacy-peer-deps" to reinstall', 'info');
        log('3. Run "npm run build" to test build', 'info');
        log('4. Commit and push to trigger Vercel deployment', 'info');
      } else {
        log('❌ Some fixes failed to apply', 'error');
        process.exit(1);
      }
      break;
      
    case 'clean':
      log('Cleaning dependencies...', 'info');
      if (cleanDependencies()) {
        log('✅ Dependencies cleaned successfully!', 'success');
        log('Run "npm install --legacy-peer-deps" to reinstall', 'info');
      } else {
        log('❌ Failed to clean dependencies', 'error');
        process.exit(1);
      }
      break;
      
    case 'check':
      log('Running deployment readiness check...', 'info');
      try {
        const { spawn } = await import('child_process');
        const child = spawn('node', ['scripts/deployment-readiness-check.js'], {
          stdio: 'inherit'
        });
        
        child.on('close', (code) => {
          process.exit(code);
        });
      } catch (error) {
        log(`Failed to run readiness check: ${error.message}`, 'error');
        process.exit(1);
      }
      break;
      
    default:
      console.log(\`
LegalScout Voice - Deployment Fix

Usage: node scripts/fix-deployment.js [command]

Commands:
  fix      - Apply all deployment fixes
  clean    - Clean node_modules and package-lock.json
  check    - Run deployment readiness check

Examples:
  node scripts/fix-deployment.js fix
  node scripts/fix-deployment.js clean
  node scripts/fix-deployment.js check
\`);
      break;
  }
}

// Handle errors
process.on('unhandledRejection', (error) => {
  log(\`Unhandled rejection: \${error.message}\`, 'error');
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  log(\`Uncaught exception: \${error.message}\`, 'error');
  process.exit(1);
});

// Run if called directly
if (process.argv[1] && process.argv[1].endsWith('fix-deployment.js')) {
  main();
}

export { createVercelConfig, createNpmrc, updatePackageJson, cleanDependencies };
