#!/usr/bin/env node

/**
 * Command Line System Test Runner for LegalScout Voice
 *
 * This script runs the comprehensive system test from the command line
 * and can be used in CI/CD pipelines.
 *
 * Usage:
 *   node scripts/run-system-test.js [options]
 *
 * Options:
 *   --url <url>        URL to test (default: http://localhost:5174)
 *   --production       Test production environment
 *   --quick            Run quick health check only
 *   --timeout <ms>     Test timeout in milliseconds (default: 30000)
 *   --output <file>    Save results to JSON file
 *   --verbose          Verbose output
 *   --help             Show help
 */

import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// ES module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const DEFAULT_CONFIG = {
  url: 'http://localhost:5174',
  timeout: 30000,
  headless: true,
  verbose: false,
  quick: false,
  output: null
};

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const config = { ...DEFAULT_CONFIG };

  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--url':
        config.url = args[++i];
        break;
      case '--production':
        config.url = 'https://dashboard.legalscout.net';
        break;
      case '--quick':
        config.quick = true;
        break;
      case '--timeout':
        config.timeout = parseInt(args[++i]);
        break;
      case '--output':
        config.output = args[++i];
        break;
      case '--verbose':
        config.verbose = true;
        break;
      case '--headless':
        config.headless = args[++i] !== 'false';
        break;
      case '--help':
        showHelp();
        process.exit(0);
        break;
      default:
        console.error(`Unknown option: ${args[i]}`);
        process.exit(1);
    }
  }

  return config;
}

// Show help
function showHelp() {
  console.log(`
LegalScout Voice - System Test Runner

Usage: node scripts/run-system-test.js [options]

Options:
  --url <url>        URL to test (default: http://localhost:5175)
  --production       Test production environment
  --quick            Run quick health check only
  --timeout <ms>     Test timeout in milliseconds (default: 30000)
  --output <file>    Save results to JSON file
  --verbose          Verbose output
  --headless <bool>  Run in headless mode (default: true)
  --help             Show this help

Examples:
  node scripts/run-system-test.js
  node scripts/run-system-test.js --production --output results.json
  node scripts/run-system-test.js --quick --verbose
  node scripts/run-system-test.js --url http://localhost:3000 --headless false
`);
}

// Log function
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [SystemTestRunner]`;
  
  switch(type) {
    case 'success': console.log(`✅ ${prefix} ${message}`); break;
    case 'error': console.error(`❌ ${prefix} ${message}`); break;
    case 'warning': console.warn(`⚠️ ${prefix} ${message}`); break;
    default: console.log(`ℹ️ ${prefix} ${message}`);
  }
}

// Run system test
async function runSystemTest(config) {
  let browser = null;
  let page = null;

  try {
    log(`Starting system test for ${config.url}`, 'info');

    // Launch browser
    browser = await puppeteer.launch({
      headless: config.headless,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    page = await browser.newPage();

    // Set viewport
    await page.setViewport({ width: 1200, height: 800 });

    // Enable console logging if verbose
    if (config.verbose) {
      page.on('console', msg => {
        const type = msg.type();
        const text = msg.text();
        if (text.includes('[SystemTest]')) {
          console.log(`[Browser Console] ${text}`);
        }
      });
    }

    // Navigate to the application
    log(`Navigating to ${config.url}`, 'info');
    await page.goto(config.url, { 
      waitUntil: 'networkidle2',
      timeout: config.timeout 
    });

    // Wait for the page to load
    await page.waitForTimeout(2000);

    // Inject the system test script
    log('Injecting system test script', 'info');
    const testScriptPath = path.join(__dirname, '../public/comprehensive-system-test.js');
    const testScript = fs.readFileSync(testScriptPath, 'utf8');
    await page.evaluate(testScript);

    // Wait for test script to initialize
    await page.waitForTimeout(1000);

    // Run the appropriate test
    let results;
    if (config.quick) {
      log('Running quick health check', 'info');
      results = await page.evaluate(async () => {
        if (typeof window.runQuickHealthCheck === 'function') {
          return await window.runQuickHealthCheck();
        } else {
          throw new Error('Quick health check function not available');
        }
      });
    } else {
      log('Running comprehensive system test', 'info');
      results = await page.evaluate(async () => {
        if (typeof window.runComprehensiveTest === 'function') {
          return await window.runComprehensiveTest();
        } else {
          throw new Error('Comprehensive test function not available');
        }
      });
    }

    // Process results
    if (results.error) {
      log(`Test failed: ${results.error}`, 'error');
      return { success: false, error: results.error };
    }

    // Log summary
    if (config.quick) {
      log(`Health check completed: ${results.status}`, 
          results.status === 'healthy' ? 'success' : 'warning');
      
      if (results.issues && results.issues.length > 0) {
        log(`Issues found: ${results.issues.join(', ')}`, 'warning');
      }
    } else {
      const { passed, failed, warnings, successRate } = results.summary;
      log(`Test completed: ${passed} passed, ${failed} failed, ${warnings} warnings (${successRate}% success rate)`,
          failed === 0 ? 'success' : 'warning');
    }

    // Save results to file if requested
    if (config.output) {
      const outputPath = path.resolve(config.output);
      fs.writeFileSync(outputPath, JSON.stringify(results, null, 2));
      log(`Results saved to ${outputPath}`, 'success');
    }

    // Determine exit code
    const success = config.quick ? 
      (results.status === 'healthy') : 
      (results.summary && results.summary.failed === 0);

    return { success, results };

  } catch (error) {
    log(`Test runner failed: ${error.message}`, 'error');
    return { success: false, error: error.message };
  } finally {
    if (page) await page.close();
    if (browser) await browser.close();
  }
}

// Main function
async function main() {
  const config = parseArgs();

  if (config.verbose) {
    log('Configuration:', 'info');
    console.log(JSON.stringify(config, null, 2));
  }

  const result = await runSystemTest(config);

  if (result.success) {
    log('System test completed successfully', 'success');
    process.exit(0);
  } else {
    log('System test failed', 'error');
    if (result.error) {
      console.error(result.error);
    }
    process.exit(1);
  }
}

// Handle uncaught errors
process.on('unhandledRejection', (error) => {
  log(`Unhandled rejection: ${error.message}`, 'error');
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  log(`Uncaught exception: ${error.message}`, 'error');
  process.exit(1);
});

// Run if called directly (ES module check)
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { runSystemTest, parseArgs };
