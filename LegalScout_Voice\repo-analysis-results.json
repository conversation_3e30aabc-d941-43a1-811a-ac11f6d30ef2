{"timestamp": "2025-06-11T21:48:55.126Z", "status": "completed", "steps": ["Analysis started", "Git available: git version 2.39.0.windows.2", "Repository cloned successfully", "Analysis completed successfully"], "findings": {"newComponents": [], "newServices": [], "configChanges": [{"file": "package.json", "newDependencies": ["next", "react-spinners", "styled-jsx", "@types/node"]}], "recommendations": []}, "otherRepo": {"directories": ["api", "dist", "pages", "public", "serverless", "src", "styles", "tests"], "files": [".giti<PERSON>re", "function.js", "index.css", "index.html", "jsconfig.json", "LICENSE", "Make.com Configuration", "next.config.js", "package-lock.json", "package.json", "README.md", "subdomain_config.json", "testWebhook.js", "updateAttorney.js", "vercel.json", "vite.config.js", "vite.config.js.bak", "yarn.lock"]}, "currentRepo": {"directories": ["ai-meta-mcp-server", "api", "app", "backup_files", "convex", "dist", "docs", "legal", "lib", "LOGS", "node_modules", "old_version", "pages", "preview_interface_enhancements", "public", "scripts", "serverless", "sql", "src", "styles", "supabase", "temp-repo", "temp_clone", "temp_commit_files", "temp_files", "temp_repo", "tests"], "files": [".dockerignore", ".env", ".env.development", ".env.example", ".env.local", ".env.production", ".giti<PERSON>re", ".npmrc", ".vercelignore", "add_voice_provider_column.sql", "all-assistants.json", "analysis.log", "analyze-vapi-assistants.ps1", "ASSISTANT_FILTERING_SUMMARY.md", "BUG_REPORTER_SETUP.md", "bulk-cleanup-vapi.ps1", "callfailsindash.log", "CALL_TERMINATION_FIXES_IMPLEMENTED.md", "CALL_TERMINATION_FIX_PLAN.md", "check-site.js", "check-supabase-status.js", "checkSupabaseTables.js", "cleanup.bat", "CLEAN_AUTH_SOLUTION.md", "clear-test-subdomain.html", "clear-test-subdomain.js", "codebase-analysis-export.js", "codebase-analysis-report.json", "codebase_diagram.py", "commit_calendar.py", "commit_stats_analyzer.py", "comprehensive-label-test.js", "console-consultation-sync-diagnostic.js", "console-vapi-diagnostic.js", "copyAssets.js", "courtlistener-api.yaml", "create-clean-assistant.js", "create_attorneys_table.sql", "create_list_tables_function.sql", "create_robert_attorney.sql", "create_test_attorney.sql", "Dashboard preview load loags", "debug-data-collection.js", "debug-preview.html", "delete-assistants.ps1", "delete-current-orphaned.ps1", "deploy.js", "deployment-summary.json", "dev-server.js", "development_guidelines.md", "docker-compose.yml", "Dockerfile", "DOCKER_README.md", "ed <PERSON>", "elegant-enhancement-plan.md", "ELEGANT_ASSISTANT_SOLUTION.md", "fix-contamination-now.js", "fix-localStorage.html", "fix-voice-config.js", "fix_rls_policies.sql", "function.js", "get-pip.py", "git-selective-merge.js", "GOOGLE_OAUTH_SETUP.md", "h origin main", "how 1a6e25136c2b30c3ba4b73e9c1b357ddd2ed73afApp.jsx", "how 6d508c0 -n 1  Select-String -Pattern IssueFixBugError -SimpleMatch", "HTML_FOR_SHEECH_PARTICLES", "index.css", "index.html", "jsconfig.json", "label-monitor.js", "LAUNCH_CHECKLIST.md", "legalscout.stainless.yaml", "LegalScout_Voice_Project_Documentation.md", "LICENSE", "LONG_TERM_PLAN.md", "MAKE_VAPI_WORK.md", "map_component.txt", "memory.md", "merge-analysis.js", "minimal-api-server.js", "modern-patterns-implementation.js", "modified_files.txt", "Multiagent collab", "nginx.conf", "output.log", "package-courtlistener-mcp.json", "package-lock.json", "package.json", "postcss.config.cjs", "PreviewInterface.backup.tsx", "PRE_DEPLOY_CHECKLIST.md", "project_status.md", "quarantine", "quick-label-test.js", "quick-repo-compare.js", "README-courtlistener-mcp.md", "README.md", "ROBUST_STATE_HANDLER_SAFEGUARDS.md", "selective-merge-utility.js", "server-package.json", "server.js", "server.mjs", "set-damon-subdomain.html", "setupSupabase.js", "simple-analysis.js", "simple-repo-analyzer.js", "SimpleVapiCall_original.jsx", "SPEECH_PARTICLES_CSS", "SPEECH_PARTICLES_JS", "STAINLESS_SDK_README.md", "STARTUP_GUIDE.md", "subdomain_config.json", "supabase-test.html", "SUPABASE_MIGRATION.md", "SUPABASE_SETUP.md", "SUPABASE_VAPI_INTEGRATION_PLAN.md", "super-simple-server.js", "tatus", "temp_app.jsx", "temp_button.jsx", "temp_controller.jsx", "test-api-imports.js", "test-assistant-call-sync.js", "test-assistant-dropdown-debug.js", "test-assistant-dropdown-fix.js", "test-assistant-dropdown.html", "test-assistant-mcp.js", "test-assistant-switching.js", "test-complete-assistant-functionality.js", "test-consultation-filtering.js", "test-git-analysis.js", "test-individual-imports.js", "test-joes<PERSON>zza-fix.js", "test-label-accessibility.html", "test-logo-upload.html", "test-mcp-endpoint.html", "test-merge.js", "test-node.js", "test-real-auth.md", "test-subdomain-consistency.html", "test-subdomain-editor.html", "test-supabase-connection.js", "test-supabase-direct.js", "test-supabase.html", "test-three.html", "test-ui-filtering.js", "test-vapi-api.js", "test-vapi-fix.html", "test-vite.js", "test-website-import.js", "testSupabaseAttorney.js", "testWebhook.js", "todo.md", "tsconfig.json", "update-supabase-key.html", "updateAttorney.js", "VAPI-INTEGRATION.md", "VAPI-SETUP.md", "VapiIntegrationDocumentation.md", "VAPI_CONFIG_REPORT.md", "VAPI_INTEGRATION_STATUS.md", "VAPI_SETUP_GUIDE.md", "vercel.json", "vercel.json.bak", "verify-supabase-key.js", "vite-plugin-exclude-framer-motion.js", "vite.config.js", "vite.config.js.bak", "vite.config.js.timestamp-1749676086275-00316fd15c55a.mjs", "vite.config.ts", "vite.svg", "vitest.config.js", "vitest.setup.js", "weekly_hours_calculator.py", "working-api-server.js"]}}