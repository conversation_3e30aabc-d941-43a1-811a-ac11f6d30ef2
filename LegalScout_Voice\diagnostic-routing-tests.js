/**
 * Comprehensive Routing Diagnostic Tests
 * Identifies routing issues between local development and production
 */

class RoutingDiagnostics {
    constructor() {
        this.results = {
            environment: {},
            routing: {},
            components: {},
            timing: {},
            errors: []
        };
        this.startTime = Date.now();
    }

    async runAllTests() {
        console.log('🔍 [RoutingDiagnostics] Starting comprehensive routing diagnostics...');
        
        try {
            await this.testEnvironment();
            await this.testRouting();
            await this.testComponents();
            await this.testTiming();
            await this.generateReport();
        } catch (error) {
            console.error('❌ [RoutingDiagnostics] Fatal error:', error);
            this.results.errors.push({ type: 'fatal', error: error.message, stack: error.stack });
        }
    }

    async testEnvironment() {
        console.log('🌍 [RoutingDiagnostics] Testing environment configuration...');
        
        const env = {
            hostname: window.location.hostname,
            pathname: window.location.pathname,
            search: window.location.search,
            hash: window.location.hash,
            origin: window.location.origin,
            protocol: window.location.protocol,
            port: window.location.port,
            isProduction: window.location.hostname.includes('legalscout.net'),
            isLocal: window.location.hostname.includes('localhost'),
            isVercel: window.location.hostname.includes('vercel.app'),
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString()
        };

        // Check for environment variables
        const envVars = {
            hasSupabaseUrl: !!window.SUPABASE_URL,
            hasSupabaseKey: !!window.SUPABASE_ANON_KEY,
            hasVapiKeys: !!window.VAPI_PRIVATE_KEY && !!window.VAPI_PUBLIC_KEY,
            processEnv: typeof process !== 'undefined' ? Object.keys(process.env || {}) : [],
            windowEnv: Object.keys(window).filter(key => key.includes('SUPABASE') || key.includes('VAPI'))
        };

        this.results.environment = { ...env, envVars };
        console.log('✅ [RoutingDiagnostics] Environment test complete:', this.results.environment);
    }

    async testRouting() {
        console.log('🛣️ [RoutingDiagnostics] Testing routing configuration...');
        
        const routing = {
            currentPath: window.location.pathname,
            expectedPath: this.getExpectedPath(),
            routerExists: !!window.React && !!document.querySelector('[data-testid="router"]'),
            hashRouter: window.location.hash.length > 0,
            historyAPI: !!(window.history && window.history.pushState),
            baseTag: document.querySelector('base')?.href,
            metaRouting: Array.from(document.querySelectorAll('meta')).filter(m => 
                m.name?.includes('route') || m.content?.includes('route')
            ).map(m => ({ name: m.name, content: m.content }))
        };

        // Test route resolution
        const routes = [
            '/',
            '/dashboard',
            '/dashboard/profile',
            '/dashboard/agent',
            '/dashboard/briefs',
            '/dashboard/sessions'
        ];

        routing.routeTests = {};
        for (const route of routes) {
            routing.routeTests[route] = await this.testRoute(route);
        }

        this.results.routing = routing;
        console.log('✅ [RoutingDiagnostics] Routing test complete:', this.results.routing);
    }

    async testRoute(route) {
        try {
            // Test if route components exist
            const routeSelectors = [
                `[data-route="${route}"]`,
                `[data-path="${route}"]`,
                `.route-${route.replace(/\//g, '-')}`,
                `#route-${route.replace(/\//g, '-')}`
            ];

            const elements = routeSelectors.map(selector => 
                document.querySelector(selector)
            ).filter(Boolean);

            return {
                exists: elements.length > 0,
                elements: elements.length,
                selectors: routeSelectors,
                accessible: route === window.location.pathname
            };
        } catch (error) {
            return { error: error.message };
        }
    }

    getExpectedPath() {
        const hostname = window.location.hostname;
        const isProduction = hostname.includes('legalscout.net');
        const isLocal = hostname.includes('localhost');

        // Check if user is authenticated by looking for auth indicators
        const hasUser = !!(window.supabaseClient?.auth?.getUser ||
                          window.user ||
                          localStorage.getItem('supabase.auth.token') ||
                          sessionStorage.getItem('supabase.auth.token'));

        if (isProduction) {
            // Production: authenticated users go to dashboard, others to home
            return hasUser ? '/dashboard' : '/';
        } else if (isLocal) {
            // Local: authenticated users go to dashboard, others to /home
            return hasUser ? '/dashboard' : '/home';
        }

        return window.location.pathname;
    }

    async testComponents() {
        console.log('🧩 [RoutingDiagnostics] Testing component loading...');
        
        const components = {
            react: {
                exists: !!window.React,
                version: window.React?.version,
                devTools: !!window.__REACT_DEVTOOLS_GLOBAL_HOOK__
            },
            router: {
                reactRouter: !!window.ReactRouter,
                hashRouter: !!document.querySelector('[data-testid="hash-router"]'),
                browserRouter: !!document.querySelector('[data-testid="browser-router"]'),
                routerProvider: !!document.querySelector('[data-testid="router-provider"]')
            },
            app: {
                mainApp: !!document.querySelector('[data-testid="main-app"]'),
                dashboard: !!document.querySelector('[data-testid="dashboard"]'),
                preview: !!document.querySelector('[data-testid="preview"]'),
                iframe: document.querySelectorAll('iframe').length
            },
            authentication: {
                authProvider: !!document.querySelector('[data-testid="auth-provider"]'),
                loginForm: !!document.querySelector('[data-testid="login-form"]'),
                userProfile: !!document.querySelector('[data-testid="user-profile"]')
            }
        };

        // Test for missing critical components
        const criticalComponents = [
            'main-app',
            'router',
            'auth-provider'
        ];

        components.missing = criticalComponents.filter(component => 
            !document.querySelector(`[data-testid="${component}"]`)
        );

        this.results.components = components;
        console.log('✅ [RoutingDiagnostics] Component test complete:', this.results.components);
    }

    async testTiming() {
        console.log('⏱️ [RoutingDiagnostics] Testing timing and loading sequence...');
        
        const timing = {
            startTime: this.startTime,
            currentTime: Date.now(),
            elapsed: Date.now() - this.startTime,
            domReady: document.readyState,
            windowLoaded: document.readyState === 'complete',
            scriptsLoaded: Array.from(document.scripts).length,
            stylesLoaded: Array.from(document.styleSheets).length
        };

        // Test loading sequence
        const loadingSequence = [];
        
        // Check for loading indicators
        const loadingElements = [
            document.querySelector('[data-testid="loading"]'),
            document.querySelector('.loading'),
            document.querySelector('#loading'),
            document.querySelector('[class*="spinner"]'),
            document.querySelector('[class*="loader"]')
        ].filter(Boolean);

        timing.loadingElements = loadingElements.length;
        timing.hasLoadingState = loadingElements.length > 0;

        // Performance timing if available
        if (window.performance && window.performance.timing) {
            const perf = window.performance.timing;
            timing.performance = {
                navigationStart: perf.navigationStart,
                domContentLoaded: perf.domContentLoadedEventEnd - perf.navigationStart,
                loadComplete: perf.loadEventEnd - perf.navigationStart,
                firstPaint: window.performance.getEntriesByType?.('paint')?.[0]?.startTime
            };
        }

        this.results.timing = timing;
        console.log('✅ [RoutingDiagnostics] Timing test complete:', this.results.timing);
    }

    async generateReport() {
        console.log('📊 [RoutingDiagnostics] Generating diagnostic report...');
        
        const report = {
            summary: this.generateSummary(),
            recommendations: this.generateRecommendations(),
            fullResults: this.results,
            timestamp: new Date().toISOString()
        };

        // Store results globally for inspection
        window.routingDiagnostics = report;
        
        console.log('🎯 [RoutingDiagnostics] DIAGNOSTIC SUMMARY:');
        console.log(report.summary);
        console.log('💡 [RoutingDiagnostics] RECOMMENDATIONS:');
        report.recommendations.forEach((rec, i) => {
            console.log(`${i + 1}. ${rec}`);
        });
        
        return report;
    }

    generateSummary() {
        const { environment, routing, components, timing } = this.results;
        
        return {
            environment: `${environment.isProduction ? 'PRODUCTION' : 'DEVELOPMENT'} on ${environment.hostname}`,
            currentPath: routing.currentPath,
            expectedPath: routing.expectedPath,
            pathMismatch: routing.currentPath !== routing.expectedPath,
            criticalComponentsMissing: components.missing?.length || 0,
            loadingTime: `${timing.elapsed}ms`,
            hasErrors: this.results.errors.length > 0
        };
    }

    generateRecommendations() {
        const recommendations = [];
        const { environment, routing, components, timing } = this.results;
        
        // Path mismatch
        if (routing.currentPath !== routing.expectedPath) {
            recommendations.push(`PATH MISMATCH: Currently on '${routing.currentPath}' but expected '${routing.expectedPath}'`);
        }
        
        // Missing components
        if (components.missing?.length > 0) {
            recommendations.push(`MISSING COMPONENTS: ${components.missing.join(', ')} - these are critical for routing`);
        }
        
        // Environment variables
        if (!environment.envVars.hasSupabaseUrl || !environment.envVars.hasSupabaseKey) {
            recommendations.push('MISSING ENV VARS: Supabase configuration not found in window object');
        }
        
        // Router issues
        if (!components.router.browserRouter && !components.router.hashRouter) {
            recommendations.push('NO ROUTER: Neither BrowserRouter nor HashRouter detected');
        }
        
        // Timing issues
        if (timing.elapsed > 5000) {
            recommendations.push(`SLOW LOADING: ${timing.elapsed}ms is too slow for initial load`);
        }
        
        // Production-specific issues
        if (environment.isProduction && routing.currentPath === '/dashboard') {
            recommendations.push('PRODUCTION ROUTING: Should load home page (/) not dashboard in production');
        }
        
        return recommendations;
    }
}

// Auto-run diagnostics
console.log('🚀 [RoutingDiagnostics] Initializing comprehensive routing diagnostics...');
const diagnostics = new RoutingDiagnostics();

// Run immediately and also after DOM is fully loaded
diagnostics.runAllTests();

if (document.readyState !== 'complete') {
    window.addEventListener('load', () => {
        console.log('🔄 [RoutingDiagnostics] Re-running after window load...');
        diagnostics.runAllTests();
    });
}

// Make available globally
window.routingDiagnostics = diagnostics;
