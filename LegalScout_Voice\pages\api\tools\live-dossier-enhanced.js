// API endpoint for live_dossier_api tool (TRUE API Request)
// This replaces the Make.com webhook with direct API integration

import { supabase } from '../../../lib/supabase';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    // For API Request tools, data comes directly in req.body
    const { assistantId, callId, dossier, conversation } = req.body;

    if (!assistantId) {
      return res.status(400).json({
        success: false,
        error: 'Assistant ID is required'
      });
    }

    if (!dossier) {
      return res.status(400).json({
        success: false,
        error: 'Dossier data is required'
      });
    }
    // Get attorney context from assistant ID
    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .or(`vapi_assistant_id.eq.${assistantId},current_assistant_id.eq.${assistantId}`)
      .single();

    if (attorneyError || !attorney) {
      console.error('Attorney not found for assistant:', assistantId);

      // Still process the dossier update but without attorney context
      return res.status(200).json({
        success: true,
        message: "I've updated your case information. Our team will review the details and follow up with you.",
        data: { assistantId, timestamp: new Date().toISOString() }
      });
    }

    // Save dossier update to database
    const dossierData = {
      attorney_id: attorney.id,
      assistant_id: assistantId,
      call_id: callId || `call-${Date.now()}`,
      status: dossier.status,
      jurisdiction: dossier.jurisdiction,
      client_background: dossier.clientBackground,
      legal_issues: dossier.legalIssues,
      statement_of_facts: dossier.statementOfFacts,
      objectives: dossier.objectives,
      user_message: conversation?.userMessage,
      scout_message: conversation?.scoutMessage,
      user_location: conversation?.userLocation,
      created_at: new Date().toISOString()
    };

    const { data: dossierRecord, error: dossierError } = await supabase
      .from('dossier_updates')
      .insert(dossierData)
      .select()
      .single();

    if (dossierError) {
      console.error('Error saving dossier:', dossierError);

      // Return success anyway to not break the conversation
      return res.status(200).json({
        success: true,
        message: `I've noted your information for ${attorney.firm_name}. Our team will review your case and follow up with you.`,
        data: { attorney: attorney.firm_name, error: 'database_save_failed' }
      });
    }

    // Create a personalized response
    const response = `I've updated your case file for ${attorney.firm_name}. ${
      dossier.status ? `Current status: ${dossier.status}. ` : ''
    }${
      dossier.jurisdiction?.address ? `Location: ${dossier.jurisdiction.address}. ` : ''
    }Our legal team will review the details and contact you soon.`;

    // TODO: Trigger real-time updates for dashboard
    // await broadcastDossierUpdate(attorney.id, dossierRecord);

    return res.status(200).json({
      success: true,
      message: response,
      data: {
        dossierUpdateId: dossierRecord.id,
        attorney: attorney.firm_name,
        timestamp: dossierRecord.created_at
      }
    });

  } catch (error) {
    console.error('Live dossier API error:', error);

    return res.status(500).json({
      success: false,
      error: "Internal server error",
      message: "I've noted your information and our team will follow up with you."
    });
  }
}
