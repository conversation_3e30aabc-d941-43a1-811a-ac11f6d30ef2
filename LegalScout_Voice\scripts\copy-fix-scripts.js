/**
 * Copy Fix Scripts
 *
 * This script copies our fix scripts to the dist folder during the build process.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Add error handling for module resolution
try {

// Paths - handle both local and Vercel build environments
const publicDir = path.resolve(process.cwd(), 'public');
const distDir = path.resolve(process.cwd(), 'dist');

// Verify paths exist
if (!fs.existsSync(publicDir)) {
  console.log(`Public directory not found: ${publicDir}`);
  console.log('Skipping fix scripts copy...');
  process.exit(0);
}

// Create the dist directory if it doesn't exist
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
  console.log(`Created dist directory: ${distDir}`);
}

// Copy only essential fix scripts from public to dist
function copyFixScripts() {
  console.log('Copying essential fix scripts from public to dist...');

  // Only copy essential fix scripts to reduce build size and time
  const essentialFixScripts = [
    'vm2-eval-polyfill-new.js',
    'production-cors-fix.js',
    'production-csp-eval-fix.js',
    'dashboard-iframe-manager.js',
    'clean-auth-solution.js',
    'system-test-integration.js'
  ];

  // Get all files in the public directory
  const files = fs.readdirSync(publicDir);

  // Filter for essential fix scripts only - exact matches only
  const fixScripts = files.filter(file => essentialFixScripts.includes(file));

  // Copy each essential fix script to the dist directory
  let copyCount = 0;
  for (const script of fixScripts) {
    const sourcePath = path.join(publicDir, script);
    const destPath = path.join(distDir, script);

    // Skip directories
    if (fs.statSync(sourcePath).isDirectory()) {
      continue;
    }

    // Copy the file
    fs.copyFileSync(sourcePath, destPath);
    console.log(`Copied ${script} to dist`);
    copyCount++;
  }

  console.log(`Copied ${copyCount} essential fix scripts to dist (reduced from ${files.filter(f => f.includes('fix')).length} total)`);
  return copyCount;
}

  // Copy the fix scripts
  const scriptsCopied = copyFixScripts();

  if (scriptsCopied > 0) {
    console.log('Successfully copied fix scripts to dist');
  } else {
    console.log('No fix scripts found to copy');
  }

} catch (error) {
  console.error('Error in copy-fix-scripts.js:', error.message);
  console.log('Continuing build process despite copy script error...');
  process.exit(0); // Exit successfully to not break the build
}
