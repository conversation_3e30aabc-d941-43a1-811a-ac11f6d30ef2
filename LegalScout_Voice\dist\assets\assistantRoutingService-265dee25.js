import{h as d,i as g,k as l}from"./index-dd4c5999.js";class f{constructor(){this.baseUrl=d(),this.apiKey=g(),this.cache=new Map}async directQuery(i,t={}){try{const{select:n="*",eq:o,single:a=!1}=t;let r=`${this.baseUrl}/rest/v1/${i}?select=${n}`;o&&Object.entries(o).forEach(([c,u])=>{r+=`&${c}=eq.${encodeURIComponent(u)}`});const s=await fetch(r,{method:"GET",headers:{apikey:this.apiKey,Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json"}});if(!s.ok)throw new Error(`HTTP ${s.status}: ${await s.text()}`);const e=await s.json();return{data:a?e[0]||null:e,error:null}}catch(n){return{data:null,error:n}}}async getAssistantConfig(i=null){try{const t=i||l()||"default";if(console.log(`🎯 [AssistantRouting] Loading config for subdomain: ${t}`),t==="default"||t==="www")return console.log("📭 [AssistantRouting] Skipping default subdomain"),null;console.log("🔍 [AssistantRouting] Environment check:",{hasWindow:typeof window<"u",hasFetch:typeof fetch<"u",hasHeaders:typeof Headers<"u",baseUrl:this.baseUrl,hasApiKey:!!this.apiKey});const n=`assistant_config_${t}`;if(this.cache.has(n))return console.log(`💾 [AssistantRouting] Using cached config for ${t}`),this.cache.get(n);const o=await this.directQuery("v_subdomain_assistant_lookup",{select:"*",eq:{subdomain:t,is_active:!0},single:!0});if(o.error||!o.data)return console.log(`📭 [AssistantRouting] No assistant mapping found for: ${t}`),null;const a=o.data;console.log("✅ [AssistantRouting] Found assistant mapping:",{assistant_id:a.assistant_id,attorney_id:a.attorney_id,firm_name:a.firm_name});const r=await this.directQuery("attorneys",{select:"*",eq:{id:a.attorney_id},single:!0});if(r.error||!r.data)return console.log(`❌ [AssistantRouting] Attorney not found: ${a.attorney_id}`),null;const s=r.data,e={id:s.id,assistant_id:a.assistant_id,attorney_id:a.attorney_id,subdomain:a.subdomain,firmName:s.firm_name||a.firm_name||"LegalScout",name:s.name||s.firm_name,email:s.email,vapi_assistant_id:a.assistant_id,current_assistant_id:a.assistant_id,is_primary_assistant:a.is_primary,welcomeMessage:s.welcome_message||"Hello! How can I help you today?",primaryColor:s.primary_color||"#3B82F6",logoUrl:s.logo_url,vapiInstructions:s.vapi_instructions||"You are a helpful legal assistant.",aiModel:s.ai_model||"gpt-4o",voiceId:s.voice_id||"alloy",isActive:!0,loadedVia:"assistant_routing_service",vapiSyncStatus:"assistant_subdomain_mapped"};return this.cache.set(n,e),console.log("🎯 [AssistantRouting] Assistant config loaded:",{firmName:e.firmName,assistant_id:e.assistant_id,subdomain:e.subdomain}),e}catch(t){return console.error("❌ [AssistantRouting] Error loading assistant config:",t),null}}async hasAssistant(i=null){return!!await this.getAssistantConfig(i)}async getAssistantStatus(i=null){const t=i||l()||"default";if(t==="default"||t==="www")return{hasAssistant:!1,isMainDomain:!0,subdomain:t};const n=await this.getAssistantConfig(t);return{hasAssistant:!!n,isMainDomain:!1,subdomain:t,assistant_id:n?.assistant_id,firmName:n?.firmName}}clearCache(i=null){i?this.cache.delete(`assistant_config_${i}`):this.cache.clear()}async preloadConfig(i){return this.getAssistantConfig(i)}}const _=new f;export{_ as assistantRoutingService,_ as default};
