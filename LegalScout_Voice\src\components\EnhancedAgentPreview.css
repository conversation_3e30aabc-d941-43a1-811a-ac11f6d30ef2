.enhanced-agent-preview {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  min-height: 500px;
  background-color: var(--preview-background, #ffffff);
  border-radius: 0.75rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
  border: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
}

/* Dark theme */
.enhanced-agent-preview.dark {
  --preview-background: #1a1a1a;
  --text-primary: #f3f4f6;
  --text-secondary: #d1d5db;
  --text-tertiary: #9ca3af;
  --border-color: rgba(255, 255, 255, 0.1);
  --header-background: rgba(255, 255, 255, 0.05);
  --card-background: #2a2a2a;
  --error-background: rgba(239, 68, 68, 0.2);
  --message-background: rgba(255, 255, 255, 0.05);
  --assistant-background: rgba(59, 130, 246, 0.1);
  --user-background: rgba(16, 185, 129, 0.1);
  --placeholder-background: rgba(255, 255, 255, 0.05);
}

/* Header */
.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
  background-color: var(--header-background, rgba(0, 0, 0, 0.02));
}

.preview-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
}

.preview-controls {
  display: flex;
  gap: 0.5rem;
}

.preview-mode-toggle {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  background-color: var(--primary-color, #3b82f6);
  color: white;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.preview-mode-toggle:hover {
  background-color: var(--primary-color-dark, #2563eb);
}

/* Error message */
.preview-error {
  padding: 1rem;
  background-color: var(--error-background, rgba(239, 68, 68, 0.1));
  border-left: 4px solid var(--error-color, #ef4444);
  margin: 1rem;
  border-radius: 0.5rem;
}

.preview-error p {
  margin: 0;
  color: var(--error-text, #b91c1c);
  font-size: 0.875rem;
}

/* Content */
.preview-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 1rem;
}

/* Placeholder */
.preview-placeholder {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  background-color: var(--placeholder-background, rgba(0, 0, 0, 0.02));
  border-radius: 0.5rem;
  text-align: center;
}

.preview-placeholder p {
  margin: 0.5rem 0;
  color: var(--text-secondary, #4b5563);
  font-size: 0.875rem;
}

/* Controller preview */
.controller-preview {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
}

/* Full call preview */
.full-call-preview {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Footer */
.preview-footer {
  padding: 1rem;
  border-top: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
  background-color: var(--header-background, rgba(0, 0, 0, 0.02));
}

.preview-note {
  margin: 0;
  font-size: 0.75rem;
  color: var(--text-tertiary, #6b7280);
  text-align: center;
}

.preview-note p {
  margin: 0 0 8px 0;
}

.assistant-info-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-top: 8px;
}

.assistant-id {
  font-family: monospace;
  font-size: 0.75rem;
  color: var(--text-tertiary, #6b7280);
  background: var(--border-color, rgba(0, 0, 0, 0.1));
  padding: 2px 6px;
  border-radius: 3px;
}

.delete-assistant-btn-preview {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.delete-assistant-btn-preview:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.delete-assistant-btn-preview svg {
  width: 10px;
  height: 10px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .enhanced-agent-preview {
    min-height: 400px;
  }
  
  .preview-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
  
  .preview-content {
    padding: 0.5rem;
  }
}
