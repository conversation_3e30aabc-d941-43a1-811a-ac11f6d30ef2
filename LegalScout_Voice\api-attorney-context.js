
// File: pages/api/tools/attorney-context-enhanced.js
// API endpoint for attorney_context_enhanced tool

import { supabase } from '../../lib/supabase';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    const { assistantId, action, clientInfo, caseInfo } = req.body;
    
    // Get attorney context
    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .or(`vapi_assistant_id.eq.${assistantId},current_assistant_id.eq.${assistantId}`)
      .single();
    
    if (attorneyError || !attorney) {
      return res.status(404).json({ 
        success: false, 
        error: 'Attorney not found' 
      });
    }

    let result;
    switch (action) {
      case 'get_attorney_info':
        result = `I'm an AI assistant for ${attorney.firm_name}. We're located at ${attorney.office_address || attorney.address} and specialize in ${attorney.practice_areas?.join(', ') || 'various legal matters'}.`;
        break;
        
      case 'get_practice_areas':
        result = `${attorney.firm_name} specializes in: ${attorney.practice_areas?.join(', ') || 'General Legal Services'}.`;
        break;
        
      case 'get_contact_info':
        result = `You can reach ${attorney.firm_name} at ${attorney.phone || 'our main number'} or email ${attorney.email}. ${attorney.scheduling_link ? `You can also schedule online at ${attorney.scheduling_link}` : ''}`;
        break;
        
      case 'get_office_hours':
        result = `Our office hours are Monday through Friday, 9 AM to 5 PM. For urgent matters, please call ${attorney.phone}.`;
        break;
        
      case 'schedule_consultation':
        // Create consultation record
        const { data: consultation } = await supabase
          .from('consultations')
          .insert({
            attorney_id: attorney.id,
            client_name: clientInfo.name,
            client_email: clientInfo.email,
            client_phone: clientInfo.phone,
            legal_issue: caseInfo.legalIssue,
            status: 'pending'
          })
          .select()
          .single();
          
        result = `I've scheduled a consultation for ${clientInfo.name}. You'll receive confirmation at ${clientInfo.email}.`;
        break;
        
      default:
        result = 'I can help you with attorney information, practice areas, contact details, and scheduling consultations.';
    }

    return res.status(200).json({
      success: true,
      message: result,
      data: {
        attorney: attorney.firm_name,
        action: action
      }
    });

  } catch (error) {
    console.error('Attorney context error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}
