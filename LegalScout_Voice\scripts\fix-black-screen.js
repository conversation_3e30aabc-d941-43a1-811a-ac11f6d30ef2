#!/usr/bin/env node

/**
 * LegalScout Voice - Black Screen Fix
 * 
 * This script fixes the black screen issue by creating a simplified App.jsx
 * that bypasses the complex loading logic causing the problem.
 */

import fs from 'fs';
import path from 'path';
import { config } from 'dotenv';

// Load environment variables
config();

function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [BlackScreenFix]`;
  
  switch(type) {
    case 'success': console.log(`✅ ${prefix} ${message}`); break;
    case 'error': console.error(`❌ ${prefix} ${message}`); break;
    case 'warning': console.warn(`⚠️ ${prefix} ${message}`); break;
    default: console.log(`ℹ️ ${prefix} ${message}`);
  }
}

function createBackup() {
  const appPath = path.join(process.cwd(), 'src/App.jsx');
  const backupPath = path.join(process.cwd(), 'src/App.jsx.backup');
  
  try {
    if (fs.existsSync(appPath)) {
      fs.copyFileSync(appPath, backupPath);
      log('Created backup of App.jsx', 'success');
      return true;
    } else {
      log('App.jsx not found', 'error');
      return false;
    }
  } catch (error) {
    log(`Failed to create backup: ${error.message}`, 'error');
    return false;
  }
}

function createMinimalWorkingApp() {
  const minimalApp = `import React, { useState, useEffect, useRef, Suspense } from 'react'
import { Routes, Route, Navigate, useLocation } from 'react-router-dom'
import { ToastContainer, toast } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import './App.css'
import { useAuth } from './contexts/AuthContext'
import { AssistantAwareProvider } from './contexts/AssistantAwareContext'

// Import all the components from the original app
import Dashboard from './pages/DashboardNew.jsx'
import AnimatedBackground from './components/AnimatedBackground.jsx'
import ThemeToggle from './components/ThemeToggle.jsx'
import Navbar from './components/Navbar.jsx'
import AuthOverlay from './components/AuthOverlay'
import AuthCallback from './pages/AuthCallback'
import SimpleCompleteProfile from './pages/SimpleCompleteProfile'
import LoginPage from './pages/LoginPage'
import AboutPage from './pages/AboutPage.jsx'
import TestComponent from './components/TestComponent.jsx'
import SubdomainTestPage from './pages/SubdomainTestPage.jsx'
import AttorneyProfileTest from './components/AttorneyProfileTest.jsx'
import CrmDemo from './pages/CrmDemo.jsx'
import CallControl from './pages/CallControl.jsx'
import SimpleDemoPage from './pages/SimpleDemoPage.jsx'
import PreviewPage from './pages/PreviewPage.jsx'
import SimplifiedPreview from './components/SimplifiedPreview.jsx'
import SimplePreviewPage from './pages/SimplePreviewPage.jsx'
import MobileActivateAssistant from './components/mobile/MobileActivateAssistant.jsx'

// Import utilities but with error handling
import { getCurrentSubdomain, checkIsAttorneySubdomain } from './utils/subdomainUtils.js'
import { getAttorneyConfigAsync } from './utils/getAttorneyConfig.js'
import { storeImage } from './utils/imageStorage.js'
import { withDevTools } from './utils/debugConfig.js'
import { debug } from './utils/loggerUtils.js'
import { initializeSupabaseConfig, verifySupabaseConfig, logSupabaseConfig } from './lib/supabase.js'
import { notificationManager } from './components/SubtleNotification.jsx'

// CRITICAL FIX: Simplified Home component that doesn't break
const SimpleHome = ({ isDarkTheme, setShowAuthOverlay }) => (
  <div style={{
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100vh',
    color: '#fff',
    textAlign: 'center',
    padding: '20px'
  }}>
    <h1 style={{ marginBottom: '20px', fontSize: '2.5rem' }}>Welcome to LegalScout</h1>
    <p style={{ marginBottom: '30px', fontSize: '1.2rem', maxWidth: '600px' }}>
      Your AI-powered legal assistant platform. Connect with attorneys and get legal guidance.
    </p>
    <button
      onClick={() => setShowAuthOverlay(true)}
      style={{
        padding: '15px 30px',
        fontSize: '1.1rem',
        backgroundColor: '#3498db',
        color: 'white',
        border: 'none',
        borderRadius: '8px',
        cursor: 'pointer'
      }}
    >
      Get Started
    </button>
  </div>
);

// CRITICAL FIX: Lazy load the complex Home component with fallback
const LazyHome = React.lazy(() =>
  import('./pages/Home.jsx').catch(() => ({
    default: SimpleHome
  }))
);

// CRITICAL FIX: Lazy load PreviewFrameLoader with fallback
const LazyPreviewFrameLoader = React.lazy(() =>
  import('./components/preview/PreviewFrameLoader.jsx').catch(() => ({
    default: () => <div>Preview loading...</div>
  }))
);

function App() {
  const location = useLocation();
  const { user } = useAuth();

  // CRITICAL FIX: Initialize all state with safe defaults
  const [callActive, setCallActive] = useState(false)
  const [showAttorneyInfo, setShowAttorneyInfo] = useState(false)
  const [showCallSummary, setShowCallSummary] = useState(false)
  const [callData, setCallData] = useState(null)
  const [subdomain, setSubdomain] = useState('default') // FIXED: Default to 'default' instead of null
  const [isAttorneySubdomain, setIsAttorneySubdomain] = useState(false)
  const [attorneyProfile, setAttorneyProfile] = useState(null)
  const [isDevelopment, setIsDevelopment] = useState(false)
  const [isLoading, setIsLoading] = useState(false) // FIXED: Start with false instead of true
  const [availableSubdomains, setAvailableSubdomains] = useState(['default'])
  const [isDarkTheme, setIsDarkTheme] = useState(true)
  const [showSubdomains, setShowSubdomains] = useState(false)
  const [selectedPracticeArea, setSelectedPracticeArea] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [activeConfigTab, setActiveConfigTab] = useState('firm');
  const [showAuthOverlay, setShowAuthOverlay] = useState(false);
  const [configMode, setConfigMode] = useState('url');

  // Add missing state variables with safe defaults
  const [firmName, setFirmName] = useState('Smith & Associates, LLP');
  const [logoUrl, setLogoUrl] = useState('');
  const [state, setState] = useState('');
  const [primaryColor, setPrimaryColor] = useState('#2c3e50');
  const [secondaryColor, setSecondaryColor] = useState('#3498db');
  const [buttonColor, setButtonColor] = useState('#3498db');
  const [backgroundColor, setBackgroundColor] = useState('#f0f4f8');
  const [backgroundOpacity, setBackgroundOpacity] = useState(0.3);
  const [buttonText, setButtonText] = useState('Start Consultation');
  const [buttonOpacity, setButtonOpacity] = useState(1);
  const [previewHeight, setPreviewHeight] = useState(600);
  const [practiceDescription, setPracticeDescription] = useState('**Welcome to our legal practice**\\n\\nOur team of experienced attorneys is dedicated to providing you with exceptional legal representation.');
  const [welcomeMessage, setWelcomeMessage] = useState('Hello, I\\'m an AI assistant from Smith & Associates. How can I help you today?');
  const [informationGathering, setInformationGathering] = useState('To better assist you, I\\'ll need a few details about your situation.');
  const [attorneyName, setAttorneyName] = useState('John Smith');
  const [practiceAreaBackgroundOpacity, setPracticeAreaBackgroundOpacity] = useState(0.2);
  const [textBackgroundColor, setTextBackgroundColor] = useState('#634C38');
  const [firmUrl, setFirmUrl] = useState('');
  const [isUrlLoading, setIsUrlLoading] = useState(false);

  const iframeRef = useRef(null);

  // CRITICAL FIX: Simplified practice areas
  const practiceAreas = {
    'Personal Injury': {
      questions: "I want to know the circumstances of their injury, including the date, location, and how it occurred.",
      practiceDescription: "**Our firm specializes in personal injury law**",
      welcomeMessage: "Welcome to our personal injury consultation.",
      informationGathering: "I want to know the circumstances of your injury."
    },
    'Family Law': {
      questions: "I need to understand the nature of their family law issue.",
      practiceDescription: "**Our firm is dedicated to helping families**",
      welcomeMessage: "Welcome to our family law consultation.",
      informationGathering: "I need to understand the nature of your family law issue."
    },
    'Criminal Defense': {
      questions: "I need to know the charges against the client.",
      practiceDescription: "## Criminal Defense Experts",
      welcomeMessage: "Thank you for considering our firm for your criminal defense needs.",
      informationGathering: "Please tell me about the charges you're facing."
    }
  };

  // CRITICAL FIX: Safe call management functions
  const startCall = (config) => {
    console.log('Starting call with config:', config);
    setCallActive(true);
    setCallData(config);
  };

  const endCall = () => {
    console.log('Ending call');
    setCallActive(false);
    setCallData(null);
  };

  // CRITICAL FIX: Safe Vapi call key
  const vapiCallKey = import.meta.env.VITE_VAPI_PUBLIC_KEY || '310f0d43-27c2-47a5-a76d-e55171d024f7';`;

  // Continue the App component with all the original functionality but safer loading
  const appContinuation = `
  // Handle practice area selection
  const handlePracticeAreaChange = (e) => {
    const area = e.target.value;
    setSelectedPracticeArea(area);
    if (area && practiceAreas[area]) {
      setWelcomeMessage(practiceAreas[area].welcomeMessage);
      setInformationGathering(practiceAreas[area].informationGathering);
      setPracticeDescription(practiceAreas[area].practiceDescription);
    }
  };

  // Handle file upload for logo
  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        try {
          const imageId = storeImage(reader.result);
          setLogoUrl(imageId);
        } catch (error) {
          console.error('Failed to store image:', error);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveLogo = () => setLogoUrl('');
  const goToPreview = () => setShowPreview(true);

  // CRITICAL FIX: Simplified URL submit without complex scraping
  const handleUrlSubmit = async (e) => {
    if (e && e.preventDefault) e.preventDefault();

    let urlToProcess = firmUrl;
    if (e && e.detail && e.detail.url) {
      urlToProcess = e.detail.url;
      setFirmUrl(urlToProcess);
    }

    if (!urlToProcess) {
      alert('Please enter a URL');
      return;
    }

    setIsUrlLoading(true);

    try {
      // Simple domain extraction without complex scraping
      const domain = urlToProcess.replace(/^https?:\\/\\//, '').replace(/^www\\./, '').split('/')[0];
      const generatedFirmName = domain
        .split(/[.-]/)
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ') + " Law";

      setFirmName(generatedFirmName);
      setWelcomeMessage('Hello, I\\'m an AI assistant from ' + generatedFirmName + '. How can I help you today?');
      setConfigMode('manual');

      toast.success('Basic configuration extracted from URL!');
    } catch (error) {
      console.error('Error processing URL:', error);
      toast.error('Failed to process URL');
    } finally {
      setIsUrlLoading(false);
    }
  };

  // Helper functions
  const hexToRgb = (hex) => {
    hex = hex.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    return \`\${r}, \${g}, \${b}\`;
  };

  const getContrastColor = (hexColor) => {
    let hex = hexColor.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    return luminance > 0.5 ? '#000000' : '#ffffff';
  };

  // Theme management
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', isDarkTheme ? 'dark' : 'light');
    if (isDarkTheme) {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }
  }, [isDarkTheme]);

  const toggleTheme = () => setIsDarkTheme(prev => !prev);
  const handleAuthSuccess = () => setShowAuthOverlay(false);

  // CRITICAL FIX: Safe subdomain detection with timeout and fallbacks
  useEffect(() => {
    console.log('🚀 [App] Initializing with safe subdomain detection...');

    // Set a maximum timeout to prevent infinite loading
    const maxLoadingTimeout = setTimeout(() => {
      console.log('🚀 [App] ⚠️ Maximum loading timeout reached, forcing app to load');
      setIsLoading(false);
      setSubdomain('default');
    }, 2000); // 2 second maximum

    const initializeApp = async () => {
      try {
        // Initialize Supabase safely
        try {
          await initializeSupabaseConfig();
          const result = await verifySupabaseConfig();
          if (result.success) {
            console.log('✅ Supabase configured successfully');
          }
        } catch (error) {
          console.warn('⚠️ Supabase initialization failed, continuing with fallback:', error);
        }

        // Determine environment
        const isDev = import.meta.env.MODE === 'development' ||
                     window.location.hostname === 'localhost' ||
                     window.location.hostname === '127.0.0.1';
        setIsDevelopment(isDev);

        // Get subdomain safely
        let subdomainValue = 'default';
        try {
          subdomainValue = getCurrentSubdomain() || 'default';
        } catch (error) {
          console.warn('⚠️ Subdomain detection failed, using default:', error);
        }

        console.log('🔍 [App] Subdomain detected:', subdomainValue);
        setSubdomain(subdomainValue);

        // Check if attorney subdomain
        const hostname = window.location.hostname;
        const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1';

        if (isLocalhost && subdomainValue === 'default') {
          console.log('🏠 [App] Localhost detected - treating as main domain');
          setIsAttorneySubdomain(false);
          setAttorneyProfile(null);
          setIsLoading(false);
          clearTimeout(maxLoadingTimeout);
          return;
        }

        // Check if this is an attorney subdomain
        let isAttorneySub = false;
        try {
          isAttorneySub = checkIsAttorneySubdomain(subdomainValue);
        } catch (error) {
          console.warn('⚠️ Attorney subdomain check failed:', error);
        }

        setIsAttorneySubdomain(isAttorneySub);

        // Load attorney profile if needed
        if (isAttorneySub && subdomainValue !== 'default') {
          try {
            console.log('📞 [App] Loading attorney profile for:', subdomainValue);
            const profile = await getAttorneyConfigAsync(subdomainValue);

            if (profile && profile.firmName) {
              console.log('✅ [App] Attorney profile loaded:', profile.firmName);
              setAttorneyProfile(profile);
            } else {
              console.log('⚠️ [App] No valid profile found, treating as main domain');
              setIsAttorneySubdomain(false);
              setAttorneyProfile(null);
            }
          } catch (error) {
            console.error('❌ [App] Error loading attorney profile:', error);
            setIsAttorneySubdomain(false);
            setAttorneyProfile(null);
          }
        }
      } catch (error) {
        console.error('❌ [App] App initialization failed:', error);
      } finally {
        setIsLoading(false);
        clearTimeout(maxLoadingTimeout);
        console.log('🏁 [App] Initialization complete');
      }
    };

    initializeApp();

    return () => clearTimeout(maxLoadingTimeout);
  }, []);

  // CRITICAL FIX: Show loading only briefly, then render app
  if (isLoading) {
    return (
      <AssistantAwareProvider>
        <div className="app-wrapper">
          <AnimatedBackground />
          <div className="loading-container" style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100vh',
            color: '#fff'
          }}>
            <div className="loading-spinner" style={{
              border: '4px solid #f3f3f3',
              borderTop: '4px solid #3498db',
              borderRadius: '50%',
              width: '40px',
              height: '40px',
              animation: 'spin 2s linear infinite',
              marginBottom: '20px'
            }}></div>
            <p>Loading LegalScout...</p>
          </div>
        </div>
      </AssistantAwareProvider>
    );
  }

  return (
    <AssistantAwareProvider>
      <div className="app-wrapper">
        <AnimatedBackground />
        <ToastContainer
          position="top-center"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme={isDarkTheme ? 'dark' : 'light'}
        />

        {/* Header - show except on dashboard */}
        {location.pathname !== '/dashboard' && (
          <header className="header">
            <div className="logo-container">
              <img src="/nav_logo.webp" alt="LegalScout Logo" className="logo" />
            </div>
            <Navbar isDarkTheme={isDarkTheme} />
            <ThemeToggle isDark={isDarkTheme} onToggle={toggleTheme} />
          </header>
        )}

        <main className="main-content-layer">
          <Routes>
            {/* CRITICAL FIX: Safe routing with fallbacks */}
            <Route path="/" element={
              isAttorneySubdomain ? (
                <Suspense fallback={<SimpleHome isDarkTheme={isDarkTheme} setShowAuthOverlay={setShowAuthOverlay} />}>
                  <LazyHome
                    isLoading={false}
                    callActive={callActive}
                    showAttorneyInfo={showAttorneyInfo}
                    showCallSummary={showCallSummary}
                    attorneyProfile={attorneyProfile}
                    startCall={startCall}
                    endCall={endCall}
                    callData={callData}
                    subdomain={subdomain}
                    setShowAttorneyInfo={setShowAttorneyInfo}
                    setShowCallSummary={setShowCallSummary}
                    buttonText={buttonText}
                    isAttorneySubdomain={isAttorneySubdomain}
                    hideCreateAgentButton={true}
                    isDarkTheme={isDarkTheme}
                    vapiCallKey={vapiCallKey}
                  />
                </Suspense>
              ) : (
                user ? <Navigate to="/dashboard" replace /> : <Navigate to="/home" replace />
              )
            } />

            {/* Home route */}
            <Route path="/home" element={
              <Suspense fallback={<SimpleHome isDarkTheme={isDarkTheme} setShowAuthOverlay={setShowAuthOverlay} />}>
                <LazyHome
                  isLoading={false}
                  callActive={callActive}
                  showAttorneyInfo={showAttorneyInfo}
                  showCallSummary={showCallSummary}
                  attorneyProfile={attorneyProfile}
                  startCall={startCall}
                  endCall={endCall}
                  callData={callData}
                  subdomain={subdomain}
                  setShowAttorneyInfo={setShowAttorneyInfo}
                  setShowCallSummary={setShowCallSummary}
                  buttonText={buttonText}
                  isAttorneySubdomain={isAttorneySubdomain}
                  hideCreateAgentButton={true}
                  isDarkTheme={isDarkTheme}
                  vapiCallKey={vapiCallKey}
                />
              </Suspense>
            } />

            {/* All other routes preserved */}
            <Route path="/about" element={<AboutPage />} />
            <Route path="/contact" element={<div>Contact Page Coming Soon</div>} />
            <Route path="/auth/callback" element={<AuthCallback />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/test" element={<TestComponent />} />
            <Route path="/subdomain-test" element={<SubdomainTestPage />} />
            <Route path="/attorney-profile-test" element={<AttorneyProfileTest />} />
            <Route path="/complete-profile" element={<SimpleCompleteProfile />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/crm-demo" element={<CrmDemo />} />

            <Route path="/call-control" element={
              <Suspense fallback={<div className="loading-container"><p>Loading call control...</p></div>}>
                <CallControl />
              </Suspense>
            } />

            <Route path="/demo" element={
              <Suspense fallback={<div className="loading-container"><p>Loading demo...</p></div>}>
                <SimpleDemoPage
                  firmName={firmName}
                  logoUrl={logoUrl}
                  state={state}
                  primaryColor={primaryColor}
                  secondaryColor={secondaryColor}
                  buttonColor={buttonColor}
                  setButtonColor={setButtonColor}
                  backgroundColor={backgroundColor}
                  backgroundOpacity={backgroundOpacity}
                  welcomeMessage={welcomeMessage}
                  informationGathering={informationGathering}
                  practiceDescription={practiceDescription}
                  previewHeight={previewHeight}
                  setPreviewHeight={setPreviewHeight}
                  attorneyName={attorneyName}
                  selectedPracticeArea={selectedPracticeArea}
                  handlePracticeAreaChange={handlePracticeAreaChange}
                  showPreview={showPreview}
                  setShowPreview={setShowPreview}
                  handleLogoUpload={handleLogoUpload}
                  handleRemoveLogo={handleRemoveLogo}
                  practiceAreas={practiceAreas}
                  activeConfigTab={activeConfigTab}
                  setActiveConfigTab={setActiveConfigTab}
                  buttonText={buttonText}
                  setButtonText={setButtonText}
                  buttonOpacity={buttonOpacity}
                  setButtonOpacity={setButtonOpacity}
                  practiceAreaBackgroundOpacity={practiceAreaBackgroundOpacity}
                  setPracticeAreaBackgroundOpacity={setPracticeAreaBackgroundOpacity}
                  textBackgroundColor={textBackgroundColor}
                  setTextBackgroundColor={setTextBackgroundColor}
                  goToPreview={goToPreview}
                  setFirmName={setFirmName}
                  setAttorneyName={setAttorneyName}
                  setPracticeDescription={setPracticeDescription}
                  setState={setState}
                  setWelcomeMessage={setWelcomeMessage}
                  setInformationGathering={setInformationGathering}
                  setPrimaryColor={setPrimaryColor}
                  setSecondaryColor={setSecondaryColor}
                  setBackgroundColor={setBackgroundColor}
                  setBackgroundOpacity={setBackgroundOpacity}
                  iframeRef={iframeRef}
                  firmUrl={firmUrl}
                  setFirmUrl={setFirmUrl}
                  isLoading={isUrlLoading}
                  handleUrlSubmit={handleUrlSubmit}
                  isDarkTheme={isDarkTheme}
                  handleGetStarted={() => setShowAuthOverlay(true)}
                />
              </Suspense>
            } />

            <Route path="/demo/preview" element={
              <Suspense fallback={<div className="loading-container"><p>Loading preview...</p></div>}>
                <PreviewPage
                  firmName={firmName}
                  attorneyName={attorneyName}
                  darkMode={isDarkTheme}
                  onToggleDarkMode={() => setIsDarkTheme(!isDarkTheme)}
                />
              </Suspense>
            } />

            <Route path="/preview" element={
              <Suspense fallback={<div className="loading-container"><p>Loading preview...</p></div>}>
                <SimplifiedPreview
                  firmName={firmName}
                  primaryColor={primaryColor}
                  secondaryColor={secondaryColor}
                  buttonColor={buttonColor}
                  backgroundColor={backgroundColor}
                  backgroundOpacity={backgroundOpacity}
                  practiceDescription={practiceDescription}
                  welcomeMessage={welcomeMessage}
                  informationGathering={informationGathering}
                  theme={isDarkTheme ? 'dark' : 'light'}
                  logoUrl={logoUrl || "/PRIMARY CLEAR.png"}
                  buttonText={buttonText || "Start Consultation"}
                  buttonOpacity={buttonOpacity}
                  practiceAreaBackgroundOpacity={practiceAreaBackgroundOpacity}
                  textBackgroundColor={textBackgroundColor}
                  mascot="/PRIMARY CLEAR.png"
                  vapiInstructions="You are a general legal assistant."
                />
              </Suspense>
            } />

            <Route path="/simple-preview" element={<SimplePreviewPage />} />

            <Route path="/preview-frame" element={
              <Suspense fallback={<div>Loading preview frame...</div>}>
                <LazyPreviewFrameLoader />
              </Suspense>
            } />

            {/* Test routes */}
            <Route path="/preview-frame-test" element={
              <div style={{ padding: '20px', textAlign: 'center' }}>
                <h1>Simple Preview Test</h1>
                <p>This page is for testing the simple preview route directly.</p>
              </div>
            } />

            <Route path="/test-route" element={
              <div style={{ padding: '20px', textAlign: 'center', color: '#fff' }}>
                <h1>✅ Test Route Works!</h1>
                <p>If you can see this, routing is working correctly.</p>
                <p>Current user: {user ? user.email : 'Not logged in'}</p>
                <button onClick={() => setShowAuthOverlay(true)}>
                  {user ? 'Switch Account' : 'Sign In'}
                </button>
              </div>
            } />
          </Routes>
        </main>

        {/* Auth Overlay */}
        <AuthOverlay
          isOpen={showAuthOverlay}
          onClose={() => setShowAuthOverlay(false)}
          onSuccess={handleAuthSuccess}
        />

        {/* Mobile Activate Assistant */}
        <Suspense fallback={null}>
          <MobileActivateAssistant
            onActivated={(config) => {
              console.log('[App] Assistant activated:', config);
              if (config && config.id) {
                setAttorneyProfile(config);
              }
            }}
          />
        </Suspense>
      </div>
    </AssistantAwareProvider>
  );
}

export default withDevTools(App, {
  displayName: 'LegalScoutApp',
  type: 'container',
  description: 'Main application container with safe loading',
  responsibleFor: ['call initiation', 'layout management', 'state control']
});
`;

  const appPath = path.join(process.cwd(), 'src/App.jsx');

  try {
    fs.writeFileSync(appPath, minimalApp + appContinuation);
    log('Created improved App.jsx with preserved functionality', 'success');
    return true;
  } catch (error) {
    log(`Failed to create improved App.jsx: ${error.message}`, 'error');
    return false;
  }
}

function restoreBackup() {
  const appPath = path.join(process.cwd(), 'src/App.jsx');
  const backupPath = path.join(process.cwd(), 'src/App.jsx.backup');
  
  try {
    if (fs.existsSync(backupPath)) {
      fs.copyFileSync(backupPath, appPath);
      log('Restored App.jsx from backup', 'success');
      return true;
    } else {
      log('No backup found', 'error');
      return false;
    }
  } catch (error) {
    log(`Failed to restore backup: ${error.message}`, 'error');
    return false;
  }
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  log('🚀 Starting Black Screen Fix', 'info');

  switch (command) {
    case 'fix':
      log('Creating improved App.jsx with preserved functionality...', 'info');
      if (createBackup() && createMinimalWorkingApp()) {
        log('✅ Black screen fix applied successfully!', 'success');
        log('✅ All original functionality preserved!', 'success');
        log('Your original App.jsx is backed up as App.jsx.backup', 'info');
        log('Run "npm run dev:full" to test the fix', 'info');
      } else {
        log('❌ Failed to apply fix', 'error');
        process.exit(1);
      }
      break;
      
    case 'restore':
      log('Restoring original App.jsx...', 'info');
      if (restoreBackup()) {
        log('✅ Original App.jsx restored successfully!', 'success');
      } else {
        log('❌ Failed to restore original App.jsx', 'error');
        process.exit(1);
      }
      break;
      
    default:
      console.log(`
LegalScout Voice - Black Screen Fix

Usage: node scripts/fix-black-screen.js [command]

Commands:
  fix      - Apply the black screen fix (creates backup)
  restore  - Restore the original App.jsx from backup

Examples:
  node scripts/fix-black-screen.js fix
  node scripts/fix-black-screen.js restore
`);
      break;
  }
}

// Handle errors
process.on('unhandledRejection', (error) => {
  log(`Unhandled rejection: ${error.message}`, 'error');
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  log(`Uncaught exception: ${error.message}`, 'error');
  process.exit(1);
});

// Run if called directly
if (process.argv[1] && process.argv[1].endsWith('fix-black-screen.js')) {
  main();
}

export { createBackup, createMinimalWorkingApp, restoreBackup };
