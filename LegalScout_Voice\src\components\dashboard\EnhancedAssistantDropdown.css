/* Enhanced Assistant Dropdown - Ultra Minimal */
.enhanced-assistant-dropdown {
  padding: 0;
  background: transparent;
  border: none;
}

.enhanced-assistant-dropdown h3 {
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
  font-weight: 500;
  color: #666;
}

.enhanced-assistant-dropdown .card-description {
  font-size: 0.65rem;
  margin-bottom: 0.25rem;
  line-height: 1.2;
  color: #888;
}

/* Custom Dropdown - Modern & Minimal */
.custom-dropdown {
  position: relative;
  width: 100%;
}

.dropdown-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(75, 116, 170, 0.2);
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.95);
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 3rem;
  backdrop-filter: blur(12px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.dropdown-trigger:hover:not(.disabled) {
  border-color: rgba(75, 116, 170, 0.4);
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.dropdown-trigger.disabled {
  background-color: rgba(249, 250, 251, 0.6);
  cursor: not-allowed;
  opacity: 0.5;
  border-color: rgba(0, 0, 0, 0.1);
}

.selected-assistant {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  flex: 1;
}

.assistant-mini-image {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid rgba(0, 0, 0, 0.06);
  flex-shrink: 0;
}

/* Updated assistant image for vertical dropdown */
.assistant-image {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid rgba(0, 0, 0, 0.06);
  flex-shrink: 0;
}

.assistant-mini-image.placeholder,
.assistant-image.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: white;
  font-size: 9px;
  font-weight: 600;
}

.assistant-image.placeholder {
  font-size: 14px;
}

.assistant-mini-image.create-icon,
.assistant-image.create-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #10b981;
  color: white;
  font-size: 7px;
}

.assistant-image.create-icon {
  font-size: 14px;
}

.assistant-name {
  font-size: 0.85rem;
  color: var(--text-primary);
  font-weight: 400;
}

.dropdown-arrow {
  color: rgba(75, 116, 170, 0.6);
  font-size: 0.875rem;
  transition: all 0.2s ease;
  margin-left: 0.5rem;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
  color: rgba(75, 116, 170, 0.8);
}

/* Selected Icon */
.selected-icon {
  color: rgba(75, 116, 170, 0.8);
  font-size: 1rem;
  margin-left: auto;
  flex-shrink: 0;
}

/* Dropdown Menu - Modern Vertical */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(75, 116, 170, 0.15);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  max-height: 320px;
  overflow-y: auto;
  margin-top: 8px;
  padding: 0.5rem;
  backdrop-filter: blur(16px);
}

/* New Vertical Elegant Layout */
.dropdown-menu.vertical-elegant {
  background-color: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(75, 116, 170, 0.15);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  margin-top: 8px;
  padding: 0.5rem;
  max-height: 320px;
  overflow-y: auto;
  backdrop-filter: blur(16px);
}

/* Modern Dropdown Items */
.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 8px;
  margin-bottom: 0.25rem;
  position: relative;
  min-height: 3.5rem;
}

.dropdown-item:last-child {
  margin-bottom: 0;
}

.dropdown-item:hover {
  background-color: rgba(75, 116, 170, 0.08);
  transform: translateX(2px);
}

.dropdown-item.selected {
  background-color: rgba(75, 116, 170, 0.12);
  border: 1px solid rgba(75, 116, 170, 0.3);
  box-shadow: 0 2px 8px rgba(75, 116, 170, 0.15);
}

.dropdown-item.create-new {
  background-color: rgba(16, 185, 129, 0.05);
  border: 1px dashed rgba(16, 185, 129, 0.3);
  margin-top: 0.5rem;
}

.dropdown-item.create-new:hover {
  background-color: rgba(16, 185, 129, 0.1);
  border-style: solid;
}

/* Assistant Avatar with Status Dot */
.assistant-avatar {
  position: relative;
  flex-shrink: 0;
}

.assistant-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.assistant-image.placeholder,
.assistant-image.create-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: white;
  font-size: 16px;
  font-weight: 600;
}

.assistant-image.create-icon {
  background-color: #10b981;
}

/* Status Dot */
.status-dot {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  z-index: 1;
  transition: all 0.2s ease;
}

/* Status dot animations for configured assistants */
.status-dot[style*="#10b981"] {
  animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
  0%, 100% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 0 0 rgba(16, 185, 129, 0.4);
  }
  50% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 0 4px rgba(16, 185, 129, 0.1);
  }
}

.dropdown-divider {
  height: 1px;
  background-color: rgba(75, 116, 170, 0.1);
  margin: 0.5rem 0;
  border-radius: 1px;
}

/* Assistant Info - Modern Typography */
.assistant-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
  min-width: 0;
}

.assistant-name {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-primary, #1a202c);
  line-height: 1.3;
  margin: 0;
}

.assistant-subdomain {
  font-size: 0.8rem;
  color: var(--text-secondary, #718096);
  font-family: 'SF Mono', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  opacity: 0.8;
  line-height: 1.2;
}

/* Vertical dropdown assistant info */
.dropdown-menu.vertical-compact .assistant-info {
  gap: 0.25rem;
}

.dropdown-menu.vertical-compact .assistant-info .assistant-name {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}

.assistant-subdomain {
  font-size: 0.7rem;
  color: var(--text-secondary);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  opacity: 0.8;
}

/* Selected Assistant in Trigger */
.selected-assistant {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

/* Mini avatar and status dot for trigger */
.assistant-image-mini {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.status-dot-mini {
  position: absolute;
  bottom: -1px;
  right: -1px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.selected-assistant .assistant-info {
  gap: 0.25rem;
}

.selected-assistant .assistant-info .assistant-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary, #1a202c);
  line-height: 1.3;
}

.selected-assistant .assistant-subdomain {
  font-size: 0.75rem;
  color: var(--text-secondary, #718096);
  font-family: 'SF Mono', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  opacity: 0.7;
  line-height: 1.2;
}

.dropdown-item .assistant-name {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.assistant-details {
  font-size: 0.6rem;
  color: var(--text-secondary);
  font-weight: 400;
  line-height: 1.1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  opacity: 0.8;
}

/* Legacy horizontal compact styles removed - now using vertical-elegant */

/* Legacy styles removed - using vertical-elegant layout */

/* Selected icon for vertical layout */
.selected-icon {
  color: var(--primary-color);
  font-size: 0.875rem;
  margin-left: auto;
}

.assistant-divider {
  width: 1px;
  height: 40px;
  background-color: #e2e8f0;
  margin: 0 0.25rem;
  flex-shrink: 0;
}

.assistant-item .assistant-name {
  font-size: 0.7rem;
  font-weight: 500;
  color: var(--text-primary);
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
  line-height: 1.2;
}

/* Dark Theme Support */
[data-theme="dark"] .dropdown-trigger {
  background-color: rgba(24, 24, 28, 0.8);
  border-color: rgba(100, 181, 246, 0.3);
  color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .dropdown-trigger:hover:not(.disabled) {
  border-color: rgba(100, 181, 246, 0.5);
  background-color: rgba(24, 24, 28, 0.95);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .dropdown-menu.vertical-elegant {
  background-color: rgba(18, 18, 20, 0.95);
  border-color: rgba(100, 181, 246, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

/* Legacy horizontal compact styles removed */

[data-theme="dark"] .dropdown-item {
  color: rgba(255, 255, 255, 0.95);
}

[data-theme="dark"] .dropdown-item:hover {
  background-color: rgba(100, 181, 246, 0.12);
}

[data-theme="dark"] .dropdown-item.selected {
  background-color: rgba(100, 181, 246, 0.18);
  border-color: rgba(100, 181, 246, 0.4);
  box-shadow: 0 2px 8px rgba(100, 181, 246, 0.2);
}

[data-theme="dark"] .dropdown-item.create-new {
  background-color: rgba(16, 185, 129, 0.08);
  border-color: rgba(16, 185, 129, 0.3);
}

[data-theme="dark"] .dropdown-item.create-new:hover {
  background-color: rgba(16, 185, 129, 0.15);
}

[data-theme="dark"] .assistant-name {
  color: rgba(255, 255, 255, 0.95);
}

[data-theme="dark"] .assistant-subdomain {
  color: rgba(255, 255, 255, 0.7);
}

[data-theme="dark"] .dropdown-divider {
  background-color: rgba(100, 181, 246, 0.15);
}

[data-theme="dark"] .assistant-details {
  color: var(--dark-text-secondary, rgba(255, 255, 255, 0.7));
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .dropdown-trigger {
    padding: 0.875rem 1rem;
    min-height: 3.5rem;
  }

  .dropdown-menu.vertical-elegant {
    margin-top: 4px;
    border-radius: 16px;
    max-height: 60vh;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  .dropdown-item {
    padding: 1rem;
    min-height: 4rem;
    gap: 1rem;
  }
}

/* Status Dot Compact for Header Dropdown */
.status-dot-compact {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
  margin-left: auto;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Status colors for deployment status */
.status-dot[style*="#10b981"],
.status-dot-mini[style*="#10b981"],
.status-dot-compact[style*="#10b981"] {
  /* Green - Deployed */
  background-color: #10b981;
}

.status-dot[style*="#ef4444"],
.status-dot-mini[style*="#ef4444"],
.status-dot-compact[style*="#ef4444"] {
  /* Red - No subdomain assigned */
  background-color: #ef4444;
}

.status-dot[style*="#9ca3af"],
.status-dot-mini[style*="#9ca3af"],
.status-dot-compact[style*="#9ca3af"] {
  /* Gray - Incomplete setup */
  background-color: #9ca3af;
}

/* Pulse animation for deployed assistants */
.status-dot[style*="#10b981"],
.status-dot-mini[style*="#10b981"] {
  animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
  0%, 100% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 0 0 rgba(16, 185, 129, 0.4);
  }
  50% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 0 4px rgba(16, 185, 129, 0.1);
  }
}

  .assistant-image {
    width: 44px;
    height: 44px;
  }

  .status-dot {
    width: 14px;
    height: 14px;
    bottom: -3px;
    right: -3px;
    border-width: 3px;
  }

  .assistant-name {
    font-size: 1rem;
  }

  .assistant-subdomain {
    font-size: 0.85rem;
  }

  .selected-assistant .assistant-info .assistant-name {
    font-size: 0.95rem;
  }

  .selected-assistant .assistant-subdomain {
    font-size: 0.8rem;
  }
}

/* Touch-friendly hover states for mobile */
@media (hover: none) and (pointer: coarse) {
  .dropdown-item:hover {
    transform: none;
  }

  .dropdown-trigger:hover:not(.disabled) {
    transform: none;
  }
}

/* Header-specific compact overrides */
.header-assistant-selector-wrapper .selected-assistant {
  gap: 0.5rem !important;
}

.header-assistant-selector-wrapper .assistant-avatar {
  flex-shrink: 0;
}

/* Ensure header dropdown menu uses vertical-elegant layout */
.header-assistant-selector-wrapper .dropdown-menu.vertical-elegant {
  background-color: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(75, 116, 170, 0.15);
  border-radius: 8px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(12px);
  max-height: 240px;
}

/* Header dropdown divider */
.header-assistant-selector-wrapper .dropdown-divider {
  margin: 0.25rem 0;
  background-color: rgba(75, 116, 170, 0.1);
}

/* Header create new item */
.header-assistant-selector-wrapper .dropdown-item.create-new {
  margin-top: 0.25rem;
  border-color: rgba(16, 185, 129, 0.2);
}

.dropdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.375rem;
}

.dropdown-header label {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-weight: var(--font-weight-medium);
  font-size: 0.8rem;
  color: var(--text-primary);
  margin: 0;
}

.label-icon {
  color: var(--primary-color);
  font-size: 0.875rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.loading-spinner {
  color: var(--primary-color);
  font-size: 0.75rem;
}

.create-assistant-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.375rem 0.75rem;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(16, 185, 129, 0.2);
}

.create-assistant-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(16, 185, 129, 0.3);
}

.create-assistant-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.create-assistant-btn svg {
  font-size: 0.7rem;
}

.spinning {
  animation: smoothSpin 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes smoothSpin {
  0% {
    transform: rotate(0deg) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: rotate(180deg) scale(1.1);
    opacity: 1;
  }
  100% {
    transform: rotate(360deg) scale(1);
    opacity: 0.8;
  }
}

.assistant-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  background-color: white;
  color: var(--text-primary);
  font-size: 0.95rem;
  cursor: pointer;
  transition: var(--transition-default);
}

.assistant-select:hover {
  border-color: var(--primary-color);
}

.assistant-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(75, 116, 170, 0.1);
}

.assistant-select:disabled {
  background-color: var(--bg-secondary, #f9fafb);
  color: var(--text-secondary);
  cursor: not-allowed;
  opacity: 0.6;
}

/* Dark theme support */
[data-theme="dark"] .assistant-select {
  background-color: var(--dark-input-bg, rgba(24, 24, 28, 0.4));
  border-color: var(--dark-border, rgba(100, 181, 246, 0.2));
  color: var(--dark-text-primary, rgba(255, 255, 255, 0.95));
}

.assistant-details {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background-color: rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(75, 116, 170, 0.1);
  border-radius: var(--radius-small);
}

.assistant-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.assistant-name {
  font-weight: var(--font-weight-medium);
  font-size: 0.925rem;
  color: var(--text-primary);
}

.assistant-description {
  font-size: 0.8rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.config-status {
  margin-top: 0.5rem;
}

.config-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
  color: var(--success-color);
  background-color: rgba(76, 175, 80, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-small);
  border: 1px solid rgba(76, 175, 80, 0.2);
}

/* Dark theme support */
[data-theme="dark"] .assistant-details {
  background-color: rgba(255, 255, 255, 0.02);
  border-color: rgba(100, 181, 246, 0.1);
}

[data-theme="dark"] .assistant-name {
  color: var(--dark-text-primary, rgba(255, 255, 255, 0.95));
}

[data-theme="dark"] .assistant-description {
  color: var(--dark-text-secondary, rgba(255, 255, 255, 0.7));
}

.dropdown-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
  padding: 0.75rem;
  background-color: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.2);
  border-radius: var(--radius-small);
  color: var(--error-color);
  font-size: 0.875rem;
}

/* Dark theme support for error */
[data-theme="dark"] .dropdown-error {
  background-color: rgba(244, 67, 54, 0.15);
  border-color: rgba(244, 67, 54, 0.3);
}

.dropdown-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid var(--border-color);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem;
  background-color: transparent;
  border: 1px solid var(--primary-color);
  border-radius: var(--radius-small);
  color: var(--primary-color);
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: white;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Dark theme support for actions */
[data-theme="dark"] .action-btn {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

[data-theme="dark"] .action-btn:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: white;
}

/* Responsive design */

/* Responsive design */
@media (max-width: 768px) {
  .enhanced-assistant-dropdown {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .dropdown-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .dropdown-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .action-btn {
    justify-content: center;
    width: 100%;
  }
}
