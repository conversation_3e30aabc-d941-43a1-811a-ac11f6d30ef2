import React, { useState, useEffect } from 'react';
import { FaEdit, FaTimes, FaCheck, FaSpinner, FaExclamationTriangle } from 'react-icons/fa';
import './SubdomainEditor.css';

const SubdomainEditor = ({
  currentSubdomain,
  firmName,
  onUpdate,
  disabled = false,
  // NEW: Assistant-specific props
  currentAssistantId,
  attorneyId,
  assistantName
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [newSubdomain, setNewSubdomain] = useState('');
  const [isChecking, setIsChecking] = useState(false);
  const [isAvailable, setIsAvailable] = useState(null);
  const [error, setError] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [assistantSubdomains, setAssistantSubdomains] = useState([]);

  // Generate suggested subdomains from firm name with multiple variants
  const generateSuggestedSubdomains = (name) => {
    if (!name) return [];

    const cleanName = name
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '') // Remove special characters except spaces
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
      .substring(0, 30); // Limit length

    const suggestions = [cleanName];

    // Add variants for better options
    if (cleanName.includes('-')) {
      // Remove common words and create shorter versions
      const withoutCommon = cleanName
        .replace(/-law$|-legal$|-firm$|-group$|-llc$|-pc$|-pllc$|-attorneys?$|-lawyer?s?$/g, '')
        .replace(/^-|-$/g, '');
      if (withoutCommon && withoutCommon !== cleanName) {
        suggestions.push(withoutCommon);
      }

      // Use just the first part if it's meaningful
      const firstPart = cleanName.split('-')[0];
      if (firstPart.length >= 3 && firstPart !== cleanName) {
        suggestions.push(firstPart);
        suggestions.push(firstPart + '-law');
        suggestions.push(firstPart + '-legal');
      }
    }

    // Add professional suffixes if not already present
    if (!cleanName.includes('law') && !cleanName.includes('legal')) {
      suggestions.push(cleanName + '-law');
      suggestions.push(cleanName + '-legal');
    }

    // Remove duplicates and empty strings
    return [...new Set(suggestions)].filter(s => s && s.length >= 3);
  };

  // Generate single suggested subdomain (for backward compatibility)
  const generateSuggestedSubdomain = (name) => {
    const suggestions = generateSuggestedSubdomains(name);
    return suggestions[0] || '';
  };

  // Check subdomain availability
  const checkAvailability = async (subdomain) => {
    if (!subdomain || subdomain === currentSubdomain) {
      setIsAvailable(null);
      return;
    }

    // Validate subdomain format
    const subdomainRegex = /^[a-z0-9-]+$/;
    if (!subdomainRegex.test(subdomain)) {
      setError('Subdomain can only contain lowercase letters, numbers, and hyphens');
      setIsAvailable(false);
      return;
    }

    if (subdomain.length < 3) {
      setError('Subdomain must be at least 3 characters long');
      setIsAvailable(false);
      return;
    }

    if (subdomain.length > 30) {
      setError('Subdomain must be 30 characters or less');
      setIsAvailable(false);
      return;
    }

    if (subdomain.startsWith('-') || subdomain.endsWith('-')) {
      setError('Subdomain cannot start or end with a hyphen');
      setIsAvailable(false);
      return;
    }

    setIsChecking(true);
    setError('');

    try {
      console.log('Checking availability for subdomain:', subdomain);

      // Get real Supabase client for database operations (consistent with authService pattern)
      const { getRealSupabaseClient } = await import('../../lib/supabase');
      const supabase = await getRealSupabaseClient();

      // NEW: Check assistant_subdomains table first
      const { data: assistantSubdomainData, error: assistantQueryError } = await supabase
        .from('assistant_subdomains')
        .select('subdomain, assistant_id')
        .eq('subdomain', subdomain)
        .eq('is_active', true);

      console.log('Assistant subdomain query result:', { assistantSubdomainData, assistantQueryError });

      if (assistantQueryError) {
        console.error('Assistant subdomain query error:', assistantQueryError);
        throw assistantQueryError;
      }

      // Check if subdomain is taken by another assistant
      const takenByOtherAssistant = assistantSubdomainData &&
        assistantSubdomainData.length > 0 &&
        assistantSubdomainData[0].assistant_id !== currentAssistantId;

      // FALLBACK: Also check legacy attorneys table
      const { data: attorneyData, error: attorneyQueryError } = await supabase
        .from('attorneys')
        .select('subdomain, id')
        .eq('subdomain', subdomain);

      if (attorneyQueryError) {
        console.error('Attorney query error:', attorneyQueryError);
        throw attorneyQueryError;
      }

      // Check if subdomain is taken by an attorney (and not migrated yet)
      const takenByAttorney = attorneyData && attorneyData.length > 0 &&
        attorneyData[0].id !== attorneyId;

      const subdomainTaken = takenByOtherAssistant || takenByAttorney;
      const available = !subdomainTaken;

      console.log('Subdomain availability check:', {
        subdomain,
        takenByOtherAssistant,
        takenByAttorney,
        available,
        currentAssistantId
      });

      setIsAvailable(available);

      if (!available) {
        if (takenByOtherAssistant) {
          setError('This subdomain is already assigned to another assistant');
        } else if (takenByAttorney) {
          setError('This subdomain is already taken by another attorney');
        } else {
          setError('This subdomain is already taken');
        }
      }
    } catch (err) {
      console.error('Error checking subdomain availability:', err);
      setError('Error checking availability. Please try again.');
      setIsAvailable(false);
    } finally {
      setIsChecking(false);
    }
  };

  // Handle subdomain input change
  const handleSubdomainChange = (e) => {
    const value = e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, '');
    setNewSubdomain(value);
  };

  // Handle save
  const handleSave = async () => {
    console.log('SubdomainEditor: handleSave called with state:', {
      isAvailable,
      newSubdomain,
      currentAssistantId,
      attorneyId,
      isSaving
    });

    if (!isAvailable || !newSubdomain || !currentAssistantId || !attorneyId) {
      console.log('SubdomainEditor: Early return due to missing requirements:', {
        isAvailable,
        newSubdomain,
        currentAssistantId,
        attorneyId
      });
      return;
    }

    setIsSaving(true);
    try {
      console.log('SubdomainEditor: Attempting to save assistant subdomain:', {
        subdomain: newSubdomain,
        assistantId: currentAssistantId,
        attorneyId: attorneyId
      });

      // Import the assistant subdomain service
      const { assistantSubdomainService } = await import('../../services/assistantSubdomainService');

      // Assign subdomain to assistant
      await assistantSubdomainService.assignSubdomainToAssistant(
        currentAssistantId,
        newSubdomain,
        attorneyId,
        false // Not necessarily primary - let user decide
      );

      // Update assistant webhook URL
      await assistantSubdomainService.updateAssistantWebhookUrl(currentAssistantId, newSubdomain);

      console.log('SubdomainEditor: Assistant subdomain save completed successfully');

      // Call the parent update handler if provided
      if (onUpdate) {
        await onUpdate({
          subdomain: newSubdomain,
          assistantId: currentAssistantId,
          type: 'assistant_subdomain'
        });
      }

      setIsEditing(false);
      setNewSubdomain('');
      setIsAvailable(null);
      setError('');

      // Show success feedback
      alert(`Assistant subdomain successfully updated!\n\n${assistantName || 'Assistant'} is now available at:\n${newSubdomain}.legalscout.net`);

    } catch (err) {
      console.error('SubdomainEditor: Error updating assistant subdomain:', err);

      if (err.message && err.message.includes('already assigned')) {
        setError('This subdomain is already assigned to another assistant.');
      } else if (err.message && err.message.includes('Failed to update')) {
        setError('Failed to update subdomain. Please try again.');
      } else {
        setError('Subdomain updated, but there was an issue syncing with voice assistant. Your new URL should still work.');

        // Still close the editor since the main update likely succeeded
        setTimeout(() => {
          setIsEditing(false);
          setNewSubdomain('');
          setIsAvailable(null);
          setError('');
        }, 3000);
      }
    } finally {
      setIsSaving(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    setIsEditing(false);
    setNewSubdomain('');
    setIsAvailable(null);
    setError('');
  };

  // Load existing assistant subdomains
  const loadAssistantSubdomains = async () => {
    if (!attorneyId) return;

    try {
      const { assistantSubdomainService } = await import('../../services/assistantSubdomainService');
      const subdomains = await assistantSubdomainService.getSubdomainsForAttorney(attorneyId);
      setAssistantSubdomains(subdomains);
      console.log('Loaded assistant subdomains:', subdomains);
    } catch (error) {
      console.error('Error loading assistant subdomains:', error);
    }
  };

  // Handle edit click
  const handleEditClick = () => {
    setIsEditing(true);
    // Generate suggestion based on assistant name or firm name
    const baseName = assistantName || firmName;
    setNewSubdomain(generateSuggestedSubdomain(baseName));
  };

  // Load assistant subdomains on mount
  useEffect(() => {
    loadAssistantSubdomains();
  }, [attorneyId]);

  // Debounced availability check
  useEffect(() => {
    if (!newSubdomain) {
      setIsAvailable(null);
      setError('');
      return;
    }

    const timeoutId = setTimeout(() => {
      checkAvailability(newSubdomain);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [newSubdomain, currentAssistantId, attorneyId]);

  if (!isEditing) {
    return (
      <div className="subdomain-display">
        <div className="subdomain-info">
          <div className="subdomain-url">
            <strong>{currentSubdomain || 'not-set'}</strong>.legalscout.net
          </div>
          <div className="subdomain-label">
            {assistantName ? `${assistantName} Assistant URL` : 'Assistant URL'}
          </div>
          {currentAssistantId && (
            <div className="assistant-id-info">
              Assistant ID: {currentAssistantId.substring(0, 8)}...
            </div>
          )}
        </div>
        <button
          type="button"
          className="edit-subdomain-btn"
          onClick={handleEditClick}
          disabled={disabled}
          title="Edit assistant subdomain"
        >
          <FaEdit />
        </button>
      </div>
    );
  }

  return (
    <div className="subdomain-editor">
      <div className="subdomain-editor-header">
        <h4>Edit Assistant Subdomain</h4>
        <p>
          Choose a unique subdomain for <strong>{assistantName || 'this assistant'}</strong>.
          This will be the dedicated URL for this specific assistant.
        </p>
        {currentAssistantId && (
          <div className="assistant-context">
            <small>Assistant ID: {currentAssistantId}</small>
          </div>
        )}
      </div>

      <div className="subdomain-input-group">
        <div className="subdomain-input-wrapper">
          <input
            type="text"
            value={newSubdomain}
            onChange={handleSubdomainChange}
            placeholder="your-firm-name"
            className={`subdomain-input ${
              isAvailable === true ? 'available' :
              isAvailable === false ? 'unavailable' : ''
            }`}
            disabled={isSaving}
          />
          <span className="subdomain-suffix">.legalscout.net</span>
        </div>

        <div className="availability-status">
          {isChecking && (
            <div className="status checking">
              <FaSpinner className="spinning" />
              <span>Checking availability...</span>
            </div>
          )}

          {!isChecking && isAvailable === true && (
            <div className="status available">
              <FaCheck />
              <span>Available!</span>
            </div>
          )}

          {!isChecking && isAvailable === false && (
            <div className="status unavailable">
              <FaExclamationTriangle />
              <span>Not available</span>
            </div>
          )}
        </div>
      </div>

      {error && (
        <div className="subdomain-error">
          <FaExclamationTriangle />
          <span>{error}</span>
        </div>
      )}

      <div className="subdomain-preview">
        <strong>Preview:</strong> https://{newSubdomain || 'your-subdomain'}.legalscout.net
      </div>

      <div className="subdomain-actions">
        <button
          type="button"
          className="cancel-btn"
          onClick={handleCancel}
          disabled={isSaving}
        >
          <FaTimes />
          Cancel
        </button>

        <button
          type="button"
          className="save-btn"
          onClick={() => {
            console.log('SubdomainEditor: Save button clicked');
            handleSave();
          }}
          disabled={!isAvailable || isSaving || !newSubdomain}
        >
          {isSaving ? (
            <>
              <FaSpinner className="spinning" />
              Saving...
            </>
          ) : (
            <>
              <FaCheck />
              Save Subdomain
            </>
          )}
        </button>
      </div>

      <div className="subdomain-warning">
        <FaExclamationTriangle />
        <div>
          <strong>Important:</strong> Changing this assistant's subdomain will update its dedicated URL.
          Any existing links to this specific assistant will no longer work.
          Other assistants will keep their own subdomains.
        </div>
      </div>
    </div>
  );
};

export default SubdomainEditor;
