/**
 * Shared subdomain extraction utilities
 * 
 * This file contains utilities for extracting subdomains that work both
 * client-side (from window.location.hostname) and server-side (from request headers).
 */

/**
 * Extract subdomain from a hostname string
 * @param {string} hostname - The hostname to extract subdomain from
 * @returns {string} The subdomain or 'default' if none found
 */
export function extractSubdomainFromHostname(hostname) {
  if (!hostname) return 'default';
  
  const parts = hostname.split('.');
  
  // Handle main domain cases (example.com or www.example.com)
  if (parts.length <= 2) {
    return 'default';
  }
  
  // Handle www subdomain as default
  if (parts[0] === 'www') {
    return 'default';
  }
  
  // Handle special subdomains that should be treated as default
  const defaultSubdomains = ['app', 'dashboard', 'api', 'mail', 'admin'];
  if (defaultSubdomains.includes(parts[0].toLowerCase())) {
    return 'default';
  }
  
  // Return the subdomain
  return parts[0].toLowerCase();
}

/**
 * Extract subdomain from request host header (server-side)
 * @param {string} host - The host header value from request
 * @returns {string} The subdomain or 'default' if none found
 */
export function extractSubdomainFromHost(host) {
  return extractSubdomainFromHostname(host);
}

/**
 * Extract subdomain from window.location (client-side)
 * @returns {string} The subdomain or 'default' if none found
 */
export function extractSubdomainFromWindow() {
  if (typeof window === 'undefined') return 'default';
  return extractSubdomainFromHostname(window.location.hostname);
}

/**
 * Check if a subdomain should be treated as an assistant subdomain
 * @param {string} subdomain - The subdomain to check
 * @returns {boolean} Whether this is an assistant subdomain
 */
export function isAttorneySubdomain(subdomain) {
  // Keep the same function name for backward compatibility
  // but now it checks for assistant subdomains
  return subdomain !== 'default' &&
         subdomain !== 'www' &&
         subdomain !== '' &&
         subdomain !== null &&
         subdomain !== undefined;
}

/**
 * Check if a subdomain should be treated as an assistant subdomain
 * @param {string} subdomain - The subdomain to check
 * @returns {boolean} Whether this is an assistant subdomain
 */
export function isAssistantSubdomain(subdomain) {
  return isAttorneySubdomain(subdomain); // Same logic for now
}

/**
 * Async check if a subdomain exists in the assistant mapping system
 * @param {string} subdomain - The subdomain to check
 * @returns {Promise<boolean>} Whether the subdomain exists in the system
 */
export async function subdomainExistsInSystem(subdomain) {
  if (!isAssistantSubdomain(subdomain)) {
    return false;
  }

  try {
    // Import and check the assistant subdomain service
    const { AssistantSubdomainService } = await import('../services/assistantSubdomainService');
    const service = new AssistantSubdomainService();
    const mapping = await service.getAssistantBySubdomain(subdomain);
    return !!mapping;
  } catch (error) {
    console.warn('Error checking subdomain existence:', error);
    return false;
  }
}
