api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71&assistant_id=eq.87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: the server responded with a status of 400 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71&assistant_id=eq.d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d:1  Failed to load resource: the server responded with a status of 400 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71&assistant_id=eq.50e13a9e-22dd-4fe8-a03e-de627c5206c1:1  Failed to load resource: the server responded with a status of 400 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71&assistant_id=eq.87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: the server responded with a status of 400 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71&assistant_id=eq.d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d:1  Failed to load resource: the server responded with a status of 400 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71&assistant_id=eq.50e13a9e-22dd-4fe8-a03e-de627c5206c1:1  Failed to load resource: the server responded with a status of 400 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71&assistant_id=eq.d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d:1  Failed to load resource: the server responded with a status of 400 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71&assistant_id=eq.50e13a9e-22dd-4fe8-a03e-de627c5206c1:1  Failed to load resource: the server responded with a status of 400 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71&assistant_id=eq.87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: the server responded with a status of 400 ()
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api.vapi.ai/assistant/87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: the server responded with a status of 404 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?assistant_id=eq.87756a2c-a398-43f2-889a-b8815684df71&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: the server responded with a status of 400 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71&assistant_id=eq.50e13a9e-22dd-4fe8-a03e-de627c5206c1:1  Failed to load resource: the server responded with a status of 400 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/consultations?select=duration%2Ccreated_at%2Cstatus&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71&assistant_id=eq.d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d:1  Failed to load resource: the server responded with a status of 400 ()
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*:1  Failed to load resource: the server responded with a status of 409 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*:1  Failed to load resource: the server responded with a status of 409 ()
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/vapi-proxy/calls:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/vapi-proxy/calls:1  Failed to load resource: the server responded with a status of 404 (Not Found)
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*:1  Failed to load resource: the server responded with a status of 409 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*:1  Failed to load resource: the server responded with a status of 409 ()
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/vapi-proxy/calls:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/vapi-proxy/calls:1  Failed to load resource: the server responded with a status of 404 (Not Found)
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*:1  Failed to load resource: the server responded with a status of 409 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*:1  Failed to load resource: the server responded with a status of 409 ()
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*:1  Failed to load resource: the server responded with a status of 409 ()
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*:1  Failed to load resource: the server responded with a status of 409 ()
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*:1  Failed to load resource: the server responded with a status of 409 ()
api/vapi-proxy/assistant/87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/vapi-proxy/assistant/87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api.vapi.ai/assistant/87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: the server responded with a status of 404 ()
api.vapi.ai/assistant/87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: the server responded with a status of 404 ()
dashboard:1 Access to fetch at 'https://dashboard.vapi.ai/api/assistant/87756a2c-a398-43f2-889a-b8815684df71' from origin 'http://localhost:5175' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: It does not have HTTP ok status.
dashboard.vapi.ai/api/assistant/87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: net::ERR_FAILED
dashboard:1 Access to fetch at 'https://dashboard.vapi.ai/api/assistant/87756a2c-a398-43f2-889a-b8815684df71' from origin 'http://localhost:5175' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: It does not have HTTP ok status.
dashboard.vapi.ai/api/assistant/87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: net::ERR_FAILED
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=custom_fields&assistant_id=eq.87756a2c-a398-43f2-889a-b8815684df71&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: the server responded with a status of 406 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=custom_fields&assistant_id=eq.87756a2c-a398-43f2-889a-b8815684df71&attorney_id=eq.87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: the server responded with a status of 406 ()
api/vapi-proxy/assistant/87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api.vapi.ai/assistant/87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: the server responded with a status of 404 ()
dashboard:1 Access to fetch at 'https://dashboard.vapi.ai/api/assistant/87756a2c-a398-43f2-889a-b8815684df71' from origin 'http://localhost:5175' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: It does not have HTTP ok status.
dashboard.vapi.ai/api/assistant/87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: net::ERR_FAILED
api/vapi-proxy/assistant/87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: the server responded with a status of 400 (Bad Request)
api/vapi-proxy/assistant/87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api.vapi.ai/assistant/87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: the server responded with a status of 404 ()
api/vapi-proxy/assistant/87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: the server responded with a status of 404 (Not Found)
dashboard:1 Access to fetch at 'https://dashboard.vapi.ai/api/assistant/87756a2c-a398-43f2-889a-b8815684df71' from origin 'http://localhost:5175' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: It does not have HTTP ok status.
dashboard.vapi.ai/api/assistant/87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: net::ERR_FAILED
api.vapi.ai/assistant/87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: the server responded with a status of 404 ()
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
dashboard:1 Access to fetch at 'https://dashboard.vapi.ai/api/assistant/87756a2c-a398-43f2-889a-b8815684df71' from origin 'http://localhost:5175' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: It does not have HTTP ok status.
dashboard.vapi.ai/api/assistant/87756a2c-a398-43f2-889a-b8815684df71:1  Failed to load resource: net::ERR_FAILED
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/vapi-proxy/calls:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/vapi-proxy/calls:1  Failed to load resource: the server responded with a status of 404 (Not Found)
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*:1  Failed to load resource: the server responded with a status of 409 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*:1  Failed to load resource: the server responded with a status of 409 ()
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/vapi-proxy/calls:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/vapi-proxy/calls:1  Failed to load resource: the server responded with a status of 404 (Not Found)
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*:1  Failed to load resource: the server responded with a status of 409 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*:1  Failed to load resource: the server responded with a status of 409 ()
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*:1  Failed to load resource: the server responded with a status of 409 ()
api/vapi-proxy/calls:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/vapi-proxy/calls:1  Failed to load resource: the server responded with a status of 404 (Not Found)
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*:1  Failed to load resource: the server responded with a status of 409 ()
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*:1  Failed to load resource: the server responded with a status of 409 ()
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
api/vapi-proxy/calls:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/vapi-proxy/calls:1  Failed to load resource: the server responded with a status of 404 (Not Found)
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*:1  Failed to load resource: the server responded with a status of 409 ()
 [AgentTab] Loaded assistant-specific configuration: Object
 [AgentTab] ✅ Banner image loaded from assistant config: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Updated preview config: Object
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Only non-Vapi fields changed: logo_url, mascot
 [DashboardNew] Skipping Vapi sync for UI-only changes
 [DashboardNew] Calling updateAttorney with: Object
 [useStandaloneAttorney] Standalone attorney manager not found
(anonymous) @ useStandaloneAttorney.js:151
 [AgentTab] Form data initialized from assistant configuration
 [DashboardNew] Attorney updated successfully: Object
 [DashboardNew] 🔍 useAuth() results: Object
 [DashboardNew] 🔍 useAuth() results: Object
 [DashboardNew] Attorney state updated, updating previewConfig.
 [DashboardNew] Attorney Vapi Assistant ID: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 [DashboardNew] 🔍 useAuth() results: Object
 [DashboardNew] Updated preview config with assistant ID: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: Object
 [DashboardNew] 🔍 useAuth() results: Object
 [DashboardNew] Updated preview config with assistant ID: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: Object
 🌐 Loaded assistant subdomain: damon
 [useStandaloneAttorney] Manager not ready, will retry...
 [VapiMcpService] Successfully got assistant from: /api/vapi-proxy/assistant/50e13a9e-22dd-4fe8-a03e-de627c5206c1
utopqxsvudgrtiwenlzl.supabase.co/rest/v1/assistant_ui_configs?select=*:1  Failed to load resource: the server responded with a status of 409 ()
 Error saving assistant config: Object
saveAssistantConfig @ assistantUIConfigService.js:283
 [AssistantDataService] Could not sync assistant name from Vapi: duplicate key value violates unique constraint "assistant_ui_configs_attorney_id_assistant_id_key"
syncAssistantNameFromVapi @ assistantDataService.js:212
 ✅ [AssistantAwareContext] Loaded assistant data: Object
 [useStandaloneAttorney] Manager still not ready after 5 seconds
(anonymous) @ useStandaloneAttorney.js:64
 [useStandaloneAttorney] Manager not ready, will retry...
 [useStandaloneAttorney] Manager still not ready after 5 seconds
(anonymous) @ useStandaloneAttorney.js:64
 Refreshing preview iframe
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: Object
 [DashboardNew] Assistant ID in config: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: Object
 [DashboardNew] Assistant ID in config: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: Object
 [DashboardNew] Assistant ID in config: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: Object
 [DashboardNew] Assistant ID in config: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 🎯 [SimplePreviewPage] Received message from parent: [object Object]
 🎯 [SimplePreviewPage] Processing config update from parent: [object Object]
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: [object Object]
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: [object Object]
 [EnhancedPreview] Assistant ID in customizations: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 [EnhancedPreview] All assistant ID related fields: [object Object]
 EnhancedPreview: Updated Vapi assistant ID to: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 Updated properties: firmName, titleText, attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, logoUrl, mascot, vapiContext, vapiAssistantId, theme, voiceId, voiceProvider, buttonColor
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: Object
 [DashboardNew] Assistant ID in config: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: Object
 [DashboardNew] Assistant ID in config: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: Object
 [DashboardNew] Assistant ID in config: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: Object
 [DashboardNew] Assistant ID in config: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 🎯 [SimplePreviewPage] Received message from parent: [object Object]
 🎯 [SimplePreviewPage] Processing config update from parent: [object Object]
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: [object Object]
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: [object Object]
 [EnhancedPreview] Assistant ID in customizations: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 [EnhancedPreview] All assistant ID related fields: [object Object]
 EnhancedPreview: Updated Vapi assistant ID to: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 Updated properties: firmName, titleText, attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, logoUrl, mascot, vapiContext, vapiAssistantId, theme, voiceId, voiceProvider, buttonColor
 🎯 [SimplePreviewPage] Received message from parent: [object Object]
 🎯 [SimplePreviewPage] Processing config update from parent: [object Object]
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: [object Object]
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: [object Object]
 [EnhancedPreview] Assistant ID in customizations: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 [EnhancedPreview] All assistant ID related fields: [object Object]
 EnhancedPreview: Updated Vapi assistant ID to: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 Updated properties: firmName, titleText, attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, logoUrl, mascot, vapiContext, vapiAssistantId, theme, voiceId, voiceProvider, buttonColor
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: Object
 [DashboardNew] Assistant ID in config: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: Object
 [DashboardNew] Assistant ID in config: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 🎯 [SimplePreviewPage] Received message from parent: [object Object]
 🎯 [SimplePreviewPage] Processing config update from parent: [object Object]
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: [object Object]
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: [object Object]
 [EnhancedPreview] Assistant ID in customizations: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 [EnhancedPreview] All assistant ID related fields: [object Object]
 EnhancedPreview: Updated Vapi assistant ID to: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 Updated properties: firmName, titleText, attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, logoUrl, mascot, vapiContext, vapiAssistantId, theme, voiceId, voiceProvider, buttonColor
 [EnhancedPreviewNew] Component initializing with props: [object Object]
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d/logo_1750094742670.png
 [EnhancedPreviewNew] Component initializing with props: [object Object]
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d/logo_1750094742670.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d/logo_1750094742670.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d/logo_1750094742670.png
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d/logo_1750094742670.png
 Processing image URL or ID: [144 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d/logo_1750094742670.png
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout Legal Assistant
 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d/logo_1750094742670.png
 primaryColor: #2563eb
 secondaryColor: #1e40af
 vapiInstructions: You are Scout, a helpful legal assistant for LegalScout. Help clients understand their legal options and guide them through initial consultations.
 vapiAssistantId: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 voiceId: echo
 voiceProvider: 11labs
 chatActive: false
 🎯 [SimplePreviewPage] Received message from parent: [object Object]
 🎯 [SimplePreviewPage] Processing config update from parent: [object Object]
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Processing config update from parent: Object
 🎯 [SimplePreviewPage] Config updated successfully
 🎯 [SimplePreviewPage] Received message from parent: Object
 🎯 [SimplePreviewPage] Processing config update from parent: Object
 🎯 [SimplePreviewPage] Config updated successfully
 ✅ [SimpleSubdomain] Vapi assistant loaded: LegalScout Legal Assistant
 ✅ [SimpleSubdomain] Config loaded for damon: Object
 SimplePreviewPage: Successfully loaded assistant config: Object
 SimplePreviewPage: Assistant config loaded successfully: Object
 SimplePreviewPage: Config merged with assistant data: Object
 SimplePreviewPage: Final config: Object
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 primaryColor: #10b981
 secondaryColor: #059669
 vapiInstructions: You are Scout, a helpful legal assistant for LegalScout. Help clients understand their legal options and guide them through initial consultations.
 vapiAssistantId: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreview] Component mounted and ready to receive messages
 [EnhancedPreview] Initial assistant ID: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: LegalScout
 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 primaryColor: #10b981
 secondaryColor: #059669
 vapiInstructions: You are Scout, a helpful legal assistant for LegalScout. Help clients understand their legal options and guide them through initial consultations.
 vapiAssistantId: 50e13a9e-22dd-4fe8-a03e-de627c5206c1
 voiceId: sarah
 voiceProvider: playht
 chatActive: false
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] Using fallback iframe communication
 [useStandaloneAttorney] Manager not ready, will retry...
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: Object
 [DashboardNew] Assistant ID in config: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: Object
 [DashboardNew] Assistant ID in config: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 🎯 [SimplePreviewPage] Received message from parent: Object
 🎯 [SimplePreviewPage] Processing config update from parent: Object
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: Object
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: Object
 [EnhancedPreview] Assistant ID in customizations: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 [EnhancedPreview] All assistant ID related fields: Object
 EnhancedPreview: Updated Vapi assistant ID to: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 Updated properties: firmName, titleText, attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, logoUrl, mascot, vapiContext, vapiAssistantId, theme, voiceId, voiceProvider, buttonColor
 🎯 [SimplePreviewPage] Received message from parent: Object
 🎯 [SimplePreviewPage] Processing config update from parent: Object
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: Object
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: Object
 [EnhancedPreview] Assistant ID in customizations: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 [EnhancedPreview] All assistant ID related fields: Object
 EnhancedPreview: Updated Vapi assistant ID to: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 Updated properties: firmName, titleText, attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, logoUrl, mascot, vapiContext, vapiAssistantId, theme, voiceId, voiceProvider, buttonColor
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 EnhancedPreview: Sent ready message to parent
 [EnhancedPreviewNew] State updated:
 firmName: LegalScout
 titleText: 
 logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 primaryColor: #2563eb
 secondaryColor: #1e40af
 vapiInstructions: You are Scout, a helpful legal assistant for LegalScout. Help clients understand their legal options and guide them through initial consultations.
 vapiAssistantId: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 voiceId: echo
 voiceProvider: 11labs
 chatActive: false
 [DashboardNew] Using fallback iframe communication
 [DashboardNew] 🔍 useAuth() results: Object
 [DashboardNew] 🔍 useAuth() results: Object
 ✅ [SimpleSubdomain] Vapi assistant loaded: LegalScout Legal Assistant
 ✅ [SimpleSubdomain] Config loaded for damon: Object
 SimplePreviewPage: Successfully loaded assistant config: Object
 SimplePreviewPage: Assistant config loaded successfully: Object
 SimplePreviewPage: Config merged with assistant data: Object
 SimplePreviewPage: Final config: Object
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [DashboardNew] Using fallback iframe communication
 🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
 [useStandaloneAttorney] Manager not ready, will retry...
 [DashboardNew] Using fallback iframe communication
 AuthContext: Auth state sync result: Object
 [useStandaloneAttorney] Manager not ready, will retry...
 AuthContext: Auth state sync result: Object
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: Object
 🔍 [App.jsx] ROOT ROUTE DECISION: Object
 🔍 [App.jsx] REDIRECT DECISION: Object
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: Object
 🔍 [App.jsx] ROOT ROUTE DECISION: Object
 🔍 [App.jsx] REDIRECT DECISION: Object
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 AuthContext: Auth state sync result: Object
 AuthContext: Auth state sync result: Object
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: Object
 🔍 [App.jsx] ROOT ROUTE DECISION: Object
 🔍 [App.jsx] REDIRECT DECISION: Object
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: Object
 🔍 [App.jsx] ROOT ROUTE DECISION: Object
 🔍 [App.jsx] REDIRECT DECISION: Object
 [DashboardNew] 🔍 useAuth() results: Object
 [DashboardNew] 🔍 useAuth() results: Object
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: Object
 🔍 [App.jsx] ROOT ROUTE DECISION: Object
 🔍 [App.jsx] REDIRECT DECISION: Object
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: Object
 🔍 [App.jsx] ROOT ROUTE DECISION: Object
 🔍 [App.jsx] REDIRECT DECISION: Object
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: Object
 🔍 [App.jsx] ROOT ROUTE DECISION: Object
 🔍 [App.jsx] REDIRECT DECISION: Object
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: Object
 🔍 [App.jsx] ROOT ROUTE DECISION: Object
 🔍 [App.jsx] REDIRECT DECISION: Object
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 🔐 [AuthContext] OAuth user data: Object
 🔐 [AuthContext] OAuth user data details: Object
 🔐 [AuthContext] Found OAuth email: <EMAIL>
 🔐 [AuthContext] Handling auth state for refresh...
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: Object
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: Object
 🔍 [App.jsx] ROOT ROUTE DECISION: Object
 🔍 [App.jsx] REDIRECT DECISION: Object
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: Object
 🔍 [App.jsx] ROOT ROUTE DECISION: Object
 🔍 [App.jsx] REDIRECT DECISION: Object
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:265
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:273
 Using client-side fallback for auth state management
 SyncContext: Auth state result: Object
 Development mode: Using mock consistency check result
 🔐 [AuthContext] OAuth user data: Object
 🔐 [AuthContext] OAuth user data details: Object
 🔐 [AuthContext] Found OAuth email: <EMAIL>
 🔐 [AuthContext] Handling auth state for refresh...
 SyncContext: Handling auth state for action: refresh
 SyncContext: Auth data: Object
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: Object
 🔍 [App.jsx] ROOT ROUTE DECISION: Object
 🔍 [App.jsx] REDIRECT DECISION: Object
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: Object
 🔍 [App.jsx] ROOT ROUTE DECISION: Object
 🔍 [App.jsx] REDIRECT DECISION: Object
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Auth state changed: INITIAL_SESSION
 OAuth user data (auth change): Object
 OAuth user data details (auth change): Object
 Found OAuth email (auth change): <EMAIL>
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: Object
 🔍 [App.jsx] ROOT ROUTE DECISION: Object
 🔍 [App.jsx] REDIRECT DECISION: Object
 🔥 [App.jsx] App component is starting!
 🔥 [App.jsx] Auth state: Object
 🔍 [App.jsx] ROOT ROUTE DECISION: Object
 🔍 [App.jsx] REDIRECT DECISION: Object
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 🏠 [SubdomainTester] Localhost detected, returning default subdomain
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
api/sync-tools/manage-auth-state:1  Failed to load resource: the server responded with a status of 500 (Internal Server Error)
 Auth state error details: Empty response from server
(anonymous) @ useSyncTools.js:265
 API error, falling back to client-side implementation: 
(anonymous) @ useSyncTools.js:273
 Using client-side fallback for auth state management
 SyncContext: Auth state result: Object
 Development mode: Using mock consistency check result
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: Object
 [DashboardNew] Assistant ID in config: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: Object
 [DashboardNew] Assistant ID in config: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 🎯 [SimplePreviewPage] Received message from parent: Object
 🎯 [SimplePreviewPage] Processing config update from parent: Object
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: Object
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: Object
 [EnhancedPreview] Assistant ID in customizations: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 [EnhancedPreview] All assistant ID related fields: Object
 EnhancedPreview: Updated Vapi assistant ID to: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 Updated properties: firmName, titleText, attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, logoUrl, mascot, vapiContext, vapiAssistantId, theme, voiceId, voiceProvider, buttonColor
 🎯 [SimplePreviewPage] Received message from parent: Object
 🎯 [SimplePreviewPage] Processing config update from parent: Object
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: Object
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: Object
 [EnhancedPreview] Assistant ID in customizations: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 [EnhancedPreview] All assistant ID related fields: Object
 EnhancedPreview: Updated Vapi assistant ID to: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 Updated properties: firmName, titleText, attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, logoUrl, mascot, vapiContext, vapiAssistantId, theme, voiceId, voiceProvider, buttonColor
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 [EnhancedPreviewNew] Component initializing with props: Object
 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
 Image is an absolute URL
 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
 [useStandaloneAttorney] Manager not ready, will retry...
 [DashboardNew] 🔍 useAuth() results: Object
 [DashboardNew] 🔍 useAuth() results: Object
 🔗 [AssistantAwareContext] Generating URLs: Object
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: Object
 🔗 [AssistantAwareContext] Generating URLs: Object
 ✅ [AssistantAwareContext] Generated assistant-specific URLs: Object
 [DashboardNew] Sending updated config to preview iframe
 [DashboardNew] Config being sent: Object
 [DashboardNew] Assistant ID in config: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 🎯 [SimplePreviewPage] Received message from parent: Object
 🎯 [SimplePreviewPage] Processing config update from parent: Object
 🎯 [SimplePreviewPage] Config updated successfully
 [EnhancedPreview] Received ANY message: Object
 [EnhancedPreview] Message source: parent
 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
 [EnhancedPreview] Received customization updates: Object
 [EnhancedPreview] Assistant ID in customizations: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
 [EnhancedPreview] All assistant ID related fields: Object
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText, attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, logoUrl, mascot, vapiContext, vapiAssistantId, theme, voiceId, voiceProvider, buttonColor
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:512 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:512 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
Button.jsx:209 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
imageStorage.js:121 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
imageStorage.js:139 Image is an absolute URL
Button.jsx:231 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
Button.jsx:209 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
imageStorage.js:121 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
imageStorage.js:139 Image is an absolute URL
Button.jsx:231 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
vapiConfig.js:71 [VapiConfig] Using SECRET key for server operations (server)
vapiConfig.js:72 [VapiConfig] Server key: 6734febc...
vapiMcpService.js:263 [VapiMcpService] Got API key from vapiConfig module
vapiMcpService.js:279 [VapiMcpService] Using SECRET key for server operations: 6734febc...
vapiMcpService.js:19 [VapiMcpService] INFO: Connection attempt 1/3 Object
vapiMcpService.js:406 [VapiMcpService] Attempting MCP connection to: /api/vapi-mcp-server
vapiMcpService.js:407 [VapiMcpService] Using Streamable HTTP transport (recommended)
vapiMcpService.js:413 [VapiMcpService] Using local MCP proxy at: /api/vapi-mcp-server
vapiMcpService.js:417 [VapiMcpService] Successfully configured to use local MCP proxy
vapiMcpService.js:505 [VapiMcpService] Getting assistant: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
vapiMcpService.js:588 [VapiMcpService] Error getting assistant: Error: Not connected
    at @modelcontextprotocol_sdk_client_index__js.js?v=7a56b667:6616:16
    at new Promise (<anonymous>)
    at Client.request (@modelcontextprotocol_sdk_client_index__js.js?v=7a56b667:6613:12)
    at Client.callTool (@modelcontextprotocol_sdk_client_index__js.js?v=7a56b667:6934:31)
    at VapiMcpService.getAssistant (vapiMcpService.js:568:42)
    at async AssistantDataService.syncAssistantNameFromVapi (assistantDataService.js:195:29)
    at async loadAssistantData (AssistantAwareContext.jsx:106:25)
getAssistant @ vapiMcpService.js:588
vapiMcpService.js:595 [VapiMcpService] Returning mock assistant due to error
getAssistant @ vapiMcpService.js:595
assistantDataService.js:208 [AssistantDataService] Received mock assistant data, skipping sync
syncAssistantNameFromVapi @ assistantDataService.js:208
AssistantAwareContext.jsx:112 ✅ [AssistantAwareContext] Loaded assistant data: Object
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: Object
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
SimplePreviewPage.jsx:180 🎯 [SimplePreviewPage] Received message from parent: Object
SimplePreviewPage.jsx:184 🎯 [SimplePreviewPage] Processing config update from parent: Object
SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: Object
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: Object
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText, attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, logoUrl, mascot, vapiContext, vapiAssistantId, theme, voiceId, voiceProvider, buttonColor
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:512 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:512 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
Button.jsx:209 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
imageStorage.js:121 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
imageStorage.js:139 Image is an absolute URL
Button.jsx:231 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
Button.jsx:209 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
imageStorage.js:121 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
imageStorage.js:139 Image is an absolute URL
Button.jsx:231 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
DashboardNew.jsx:942 [DashboardNew] Sending updated config to preview iframe
DashboardNew.jsx:943 [DashboardNew] Config being sent: Object
DashboardNew.jsx:944 [DashboardNew] Assistant ID in config: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
SimplePreviewPage.jsx:180 🎯 [SimplePreviewPage] Received message from parent: Object
SimplePreviewPage.jsx:184 🎯 [SimplePreviewPage] Processing config update from parent: Object
SimplePreviewPage.jsx:186 🎯 [SimplePreviewPage] Config updated successfully
EnhancedPreviewNew.jsx:146 [EnhancedPreview] Received ANY message: Object
EnhancedPreviewNew.jsx:147 [EnhancedPreview] Message source: parent
EnhancedPreviewNew.jsx:148 [EnhancedPreview] Message type: UPDATE_PREVIEW_CONFIG
EnhancedPreviewNew.jsx:158 [EnhancedPreview] Received customization updates: Object
EnhancedPreviewNew.jsx:159 [EnhancedPreview] Assistant ID in customizations: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
EnhancedPreviewNew.jsx:160 [EnhancedPreview] All assistant ID related fields: Object
EnhancedPreviewNew.jsx:287 EnhancedPreview: Updated Vapi assistant ID to: d1be86a2-49b3-4ff1-b39c-86f41c2b2f3d
EnhancedPreviewNew.jsx:335 Updated properties: firmName, titleText, attorneyName, practiceAreas, practiceDescription, primaryColor, secondaryColor, backgroundColor, backgroundOpacity, buttonText, buttonOpacity, practiceAreaBackgroundOpacity, textBackgroundColor, logoUrl, mascot, vapiContext, vapiAssistantId, theme, voiceId, voiceProvider, buttonColor
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:512 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:512 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
Button.jsx:209 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
imageStorage.js:121 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
imageStorage.js:139 Image is an absolute URL
Button.jsx:231 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
Button.jsx:209 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
imageStorage.js:121 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
imageStorage.js:139 Image is an absolute URL
Button.jsx:231 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
AuthContext.jsx:130 🔐 [AuthContext] Auth state sync result: Object
AuthContext.jsx:162 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
AuthContext.jsx:130 🔐 [AuthContext] Auth state sync result: Object
AuthContext.jsx:162 🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: Object
App.jsx:811 🔍 [App.jsx] ROOT ROUTE DECISION: Object
App.jsx:841 🔍 [App.jsx] REDIRECT DECISION: Object
App.jsx:387 🔥 [App.jsx] App component is starting!
App.jsx:393 🔥 [App.jsx] Auth state: Object
App.jsx:811 🔍 [App.jsx] ROOT ROUTE DECISION: Object
App.jsx:841 🔍 [App.jsx] REDIRECT DECISION: Object
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
subdomainTester.js:66 🏠 [SubdomainTester] Localhost detected, returning default subdomain
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:512 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
EnhancedPreviewNew.jsx:74 [EnhancedPreviewNew] Component initializing with props: Object
EnhancedPreviewNew.jsx:512 Using custom logoUrl: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
Button.jsx:209 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
imageStorage.js:121 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
imageStorage.js:139 Image is an absolute URL
Button.jsx:231 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
Button.jsx:209 Button component received mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
imageStorage.js:121 Processing image URL or ID: [145 chars]https://utopqxsvudgrtiwenlzl.supabase.co/storage/v...
imageStorage.js:139 Image is an absolute URL
Button.jsx:231 Processed mascot URL: https://utopqxsvudgrtiwenlzl.supabase.co/storage/v1/object/public/legalscout_bucket1/50e13a9e-22dd-4fe8-a03e-de627c5206c1/logo_1750094166924.webp
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
AttorneyProfileManager.js:417 [AttorneyProfileManager] Loading attorney by id: 87756a2c-a398-43f2-889a-b8815684df71
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
AttorneyProfileManager.js:64 [AttorneyProfileManager] Refreshed attorney data for auto-sync: Object
AttorneyProfileManager.js:1469 [AttorneyProfileManager] Saved attorney to localStorage: 87756a2c-a398-43f2-889a-b8815684df71
AttorneyProfileManager.js:78 [AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
useStandaloneAttorney.js:21 [useStandaloneAttorney] Manager not ready, will retry...
 [useStandaloneAttorney] Manager still not ready after 5 seconds
(anonymous) @ useStandaloneAttorney.js:64
 [useStandaloneAttorney] Manager not ready, will retry...
 [useStandaloneAttorney] Manager still not ready after 5 seconds
(anonymous) @ useStandaloneAttorney.js:64
