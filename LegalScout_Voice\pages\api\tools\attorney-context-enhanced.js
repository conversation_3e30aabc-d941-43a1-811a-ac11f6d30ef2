// API endpoint for attorney_context_enhanced tool
// Provides attorney-specific functionality with automatic context detection

import { supabase } from '../../../lib/supabase';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      results: [{ 
        toolCallId: 'unknown', 
        result: 'Method not allowed' 
      }] 
    });
  }

  try {
    const { message } = req.body;
    const { toolCallList, assistant, call } = message;
    
    if (!toolCallList || toolCallList.length === 0) {
      return res.status(400).json({ 
        results: [{ 
          toolCallId: 'unknown', 
          result: 'No tool calls found' 
        }] 
      });
    }

    const toolCall = toolCallList[0];
    const { 
      action, 
      client_name, 
      client_email, 
      client_phone, 
      legal_issue, 
      urgency, 
      additional_details 
    } = toolCall.arguments;
    
    // Get attorney context from assistant ID
    const assistantId = assistant?.id || call?.assistantId;
    
    if (!assistantId) {
      return res.status(400).json({
        results: [{
          toolCallId: toolCall.id,
          result: "Unable to identify attorney context."
        }]
      });
    }

    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .or(`vapi_assistant_id.eq.${assistantId},current_assistant_id.eq.${assistantId}`)
      .single();
    
    if (attorneyError || !attorney) {
      return res.status(200).json({
        results: [{
          toolCallId: toolCall.id,
          result: "I'm having trouble accessing attorney information right now. Please try again or contact us directly."
        }]
      });
    }

    let result;
    
    switch (action) {
      case 'get_attorney_info':
        result = `I'm an AI assistant for ${attorney.firm_name}. ${
          attorney.name ? `I work with ${attorney.name}. ` : ''
        }${
          attorney.office_address || attorney.address ? 
          `We're located at ${attorney.office_address || attorney.address}. ` : ''
        }${
          attorney.practice_areas?.length ? 
          `We specialize in ${attorney.practice_areas.join(', ')}.` : 
          'We handle various legal matters.'
        }`;
        break;
        
      case 'get_practice_areas':
        const areas = attorney.practice_areas?.length ? 
          attorney.practice_areas.join(', ') : 
          'General Legal Services';
        result = `${attorney.firm_name} specializes in: ${areas}. We're here to help with any questions you might have about these areas of law.`;
        break;
        
      case 'get_contact_info':
        result = `You can reach ${attorney.firm_name} at:${
          attorney.phone ? `\nPhone: ${attorney.phone}` : ''
        }\nEmail: ${attorney.email}${
          attorney.office_address || attorney.address ? 
          `\nAddress: ${attorney.office_address || attorney.address}` : ''
        }${
          attorney.scheduling_link ? 
          `\nSchedule online: ${attorney.scheduling_link}` : ''
        }`;
        break;
        
      case 'get_office_hours':
        result = `Our office hours are Monday through Friday, 9 AM to 5 PM. For urgent matters outside these hours, please leave a message and we'll get back to you as soon as possible.${
          attorney.phone ? ` You can reach us at ${attorney.phone}` : ''
        } or email us at ${attorney.email}.`;
        break;
        
      case 'schedule_consultation':
        if (!client_name || !client_email) {
          result = "To schedule a consultation, I'll need your name and email address. Could you please provide those?";
          break;
        }
        
        // Create consultation record
        const { data: consultation, error: consultationError } = await supabase
          .from('consultations')
          .insert({
            attorney_id: attorney.id,
            client_name: client_name,
            client_email: client_email,
            client_phone: client_phone,
            legal_issue: legal_issue,
            urgency: urgency || 'medium',
            status: 'pending',
            created_at: new Date().toISOString()
          })
          .select()
          .single();
          
        if (consultationError) {
          console.error('Error creating consultation:', consultationError);
          result = `I encountered an issue scheduling your consultation. Please call ${attorney.firm_name} directly${attorney.phone ? ` at ${attorney.phone}` : ''} or email ${attorney.email}.`;
        } else {
          result = `I've scheduled a consultation for ${client_name} with ${attorney.firm_name}. You'll receive a confirmation email at ${client_email} with the details. Our team will contact you within 24 hours to confirm the appointment time.`;
        }
        break;
        
      case 'create_case_file':
        if (!client_name) {
          result = "To create a case file, I'll need at least your name. Could you please provide that?";
          break;
        }
        
        // Create case file record
        const { data: caseFile, error: caseError } = await supabase
          .from('case_files')
          .insert({
            attorney_id: attorney.id,
            client_name: client_name,
            client_email: client_email,
            client_phone: client_phone,
            case_description: legal_issue,
            urgency_level: urgency || 'medium',
            additional_details: additional_details,
            status: 'new',
            created_at: new Date().toISOString()
          })
          .select()
          .single();
          
        if (caseError) {
          console.error('Error creating case file:', caseError);
          result = `I've noted your information for ${attorney.firm_name} and will make sure our team follows up with you.`;
        } else {
          const caseRef = `CF-${caseFile.id.toString().slice(-8)}`;
          result = `I've created a case file for ${client_name} with ${attorney.firm_name}. Your case reference number is ${caseRef}. Our legal team will review your case and contact you within 2 business days.`;
        }
        break;
        
      case 'transfer_to_attorney':
        // Log the transfer request
        console.log(`Transfer requested for ${attorney.firm_name}, urgency: ${urgency}`);
        
        if (urgency === 'emergency') {
          result = `This appears to be an emergency. Please call our office immediately${attorney.phone ? ` at ${attorney.phone}` : ''} or contact emergency services if this is a life-threatening situation.`;
        } else {
          result = `I'm connecting you with ${attorney.firm_name}. Please hold while I transfer your call.${attorney.phone ? ` If the transfer fails, you can reach us directly at ${attorney.phone}.` : ''}`;
        }
        break;
        
      default:
        result = `I can help you with attorney information, practice areas, contact details, scheduling consultations, and creating case files for ${attorney.firm_name}. What would you like to know?`;
    }

    return res.status(200).json({
      results: [{
        toolCallId: toolCall.id,
        result: result
      }]
    });

  } catch (error) {
    console.error('Attorney context enhanced error:', error);
    
    const toolCallId = req.body?.message?.toolCallList?.[0]?.id || 'unknown';
    
    return res.status(200).json({
      results: [{
        toolCallId: toolCallId,
        result: "I encountered an error processing your request. Please try again or contact us directly."
      }]
    });
  }
}
