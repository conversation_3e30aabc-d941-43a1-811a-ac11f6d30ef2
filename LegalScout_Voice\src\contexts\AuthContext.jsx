/**
 * Authentication Context
 *
 * This context provides authentication state and methods throughout the application.
 * It integrates with the synchronization tools to ensure authentication state is
 * properly synchronized across systems.
 */

import React, { useState, useEffect, useRef } from 'react';
import { getSupabaseClient, getRealSupabaseClient } from '../lib/supabase';
import {
  signInWithOAuth,
  handleOAuthCallback,
  signOut as authSignOut,
  getCurrentSession
} from '../services/authService';

// Create proper React context
const AuthContext = React.createContext(null);

// Custom hook to use the auth context
export const useAuth = () => {
  const context = React.useContext(AuthContext);

  if (!context) {
    console.warn('Auth context not found, returning default values');
    return {
      user: null,
      attorney: null,
      isLoading: false,
      signOut: () => Promise.resolve(),
      isAuthenticated: false
    };
  }

  return context;
};

/**
 * Authentication Provider
 *
 * @param {Object} props - The component props
 * @param {React.ReactNode} props.children - The child components
 * @returns {JSX.Element} The provider component
 */
export const AuthProvider = ({ children, syncTools }) => {
  // State for tracking authentication state
  const [user, setUser] = useState(null);
  const [attorney, setAttorney] = useState(null);
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Initialize authentication state
  useEffect(() => {
    const initAuth = async () => {
      console.log('🔐 [AuthContext] Starting auth initialization...');
      setLoading(true);
      setError(null);

      // CRITICAL FIX: For localhost, use normal auth flow but don't force attorney authentication
      if (typeof window !== 'undefined' &&
          (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1')) {
        console.log('🔐 [AuthContext] 🏠 Localhost detected - using normal auth flow without attorney forcing');
        // Continue with normal auth flow - don't bypass it
      }

      // 🛡️ CRITICAL: Force loading to false after maximum timeout (reduced for development)
      const forceLoadingTimeout = setTimeout(() => {
        console.log('🔐 [AuthContext] ⚠️ FORCING loading to false after timeout');
        setLoading(false);
      }, 2000); // 2 second maximum for faster development

      try {
        // Get current session from Supabase
        const supabase = await getSupabaseClient();
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('🔐 [AuthContext] Error getting session:', error);
          setUser(null);
          setSession(null);
          setAttorney(null);
        } else if (session?.user) {
          console.log('🔐 [AuthContext] OAuth user data:', session.user);

          // Ensure email is set from OAuth - check all possible locations with detailed logging
          console.log('🔐 [AuthContext] OAuth user data details:', {
            directEmail: session.user.email,
            metadataEmail: session.user.user_metadata?.email,
            identityEmail: session.user.identities?.[0]?.identity_data?.email,
            rawMetadata: session.user.user_metadata,
            rawAppMetadata: session.user.app_metadata,
            identities: session.user.identities
          });

          const userEmail = session.user.email ||
                           session.user.user_metadata?.email ||
                           session.user.identities?.[0]?.identity_data?.email ||
                           session.user.user_metadata?.email_verified && session.user.user_metadata?.sub ||
                           '';

          console.log('🔐 [AuthContext] Found OAuth email:', userEmail);

          const userWithEmail = {
            ...session.user,
            email: userEmail
          };

          setUser(userWithEmail);
          setSession(session);

          // If sync tools are available, use them to get attorney data
          if (syncTools?.handleAuthState) {
            try {
              console.log('🔐 [AuthContext] Handling auth state for refresh...');

              // 🛡️ CRITICAL: Set timeout for sync tools to prevent hanging
              const syncPromise = syncTools.handleAuthState({
                user: userWithEmail,
                session
              }, 'refresh');

              const syncTimeout = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Sync timeout')), 3000)
              );

              const syncResult = await Promise.race([syncPromise, syncTimeout]);

              console.log('🔐 [AuthContext] Auth state sync result:', {
                success: syncResult.success,
                hasAttorney: !!syncResult.attorney,
                fallback: !!syncResult.fallback,
                message: syncResult.message
              });

              if (syncResult.success && syncResult.attorney) {
                setAttorney(syncResult.attorney);
              } else if (syncResult.fallback) {
                // If we're using a fallback result, log it but don't update attorney state
                console.log('🔐 [AuthContext] Using fallback result, attorney state not updated');
              }
            } catch (syncError) {
              console.error('🔐 [AuthContext] Error syncing auth state:', syncError);
              // Continue despite the error - don't let auth sync failures break the auth flow
            }
          }
        } else {
          console.log('🔐 [AuthContext] No session found');
          setUser(null);
          setSession(null);
          setAttorney(null);
        }
      } catch (error) {
        console.error('🔐 [AuthContext] Unexpected error checking auth:', error);
        setUser(null);
        setSession(null);
        setAttorney(null);
        setError(error.message);
      } finally {
        clearTimeout(forceLoadingTimeout);
        console.log('🔐 [AuthContext] ✅ Auth initialization complete, setting loading to false');
        setLoading(false);
      }
    };

    initAuth();

    // Listen for auth changes - set up async
    const setupAuthListener = async () => {
      try {
        const supabase = await getSupabaseClient();
        const { data: authListener } = supabase.auth.onAuthStateChange(
          async (event, session) => {
            console.log('Auth state changed:', event);

        if (session?.user) {
          console.log('OAuth user data (auth change):', session.user);

          // Ensure email is set from OAuth - check all possible locations with detailed logging
          console.log('OAuth user data details (auth change):', {
            directEmail: session.user.email,
            metadataEmail: session.user.user_metadata?.email,
            identityEmail: session.user.identities?.[0]?.identity_data?.email,
            rawMetadata: session.user.user_metadata,
            rawAppMetadata: session.user.app_metadata,
            identities: session.user.identities
          });

          const userEmail = session.user.email ||
                           session.user.user_metadata?.email ||
                           session.user.identities?.[0]?.identity_data?.email ||
                           session.user.user_metadata?.email_verified && session.user.user_metadata?.sub ||
                           '';

          console.log('Found OAuth email (auth change):', userEmail);

          const userWithEmail = {
            ...session.user,
            email: userEmail
          };

          setUser(userWithEmail);
          setSession(session);

          // If sync tools are available and this is a sign-in event, use them to get attorney data
          if (syncTools?.handleAuthState && (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED')) {
            try {
              console.log('AuthContext: Handling auth state change for event:', event);

              const syncResult = await syncTools.handleAuthState({
                user: userWithEmail,
                session
              }, event === 'SIGNED_IN' ? 'login' : 'refresh');

              console.log('AuthContext: Auth state sync result:', {
                success: syncResult.success,
                hasAttorney: !!syncResult.attorney,
                fallback: !!syncResult.fallback,
                message: syncResult.message
              });

              if (syncResult.success && syncResult.attorney) {
                setAttorney(syncResult.attorney);
              } else if (syncResult.fallback) {
                // If we're using a fallback result, log it but don't update attorney state
                console.log('AuthContext: Using fallback result, attorney state not updated');
              }
            } catch (syncError) {
              console.error('Error syncing auth state on change:', syncError);
              // Continue despite the error - don't let auth sync failures break the auth flow
            }
          } else if (event === 'SIGNED_OUT') {
            setAttorney(null);
          }
        } else {
          setUser(null);
          setSession(null);
          setAttorney(null);
        }

        setLoading(false);
      }
    );

        // Store the listener for cleanup
        return authListener;
      } catch (error) {
        console.error('Failed to set up auth listener:', error);
        return null;
      }
    };

    // Set up the auth listener
    let authListenerPromise = setupAuthListener();

    // Cleanup subscription
    return () => {
      authListenerPromise.then(authListener => {
        if (authListener && authListener.subscription) {
          authListener.subscription.unsubscribe();
        }
      }).catch(error => {
        console.warn('Error cleaning up auth listener:', error);
      });
    };
  }, [syncTools]);

  /**
   * Sign in with OAuth provider
   *
   * @param {string} provider - The OAuth provider (e.g., 'google', 'github')
   * @returns {Promise<Object>} The sign in result
   */
  const login = async (provider) => {
    setError(null);

    try {
      return await signInWithOAuth(provider);
    } catch (error) {
      console.error('Login error:', error);
      setError(error.message);
      throw error;
    }
  };

  /**
   * Handle OAuth callback
   *
   * @returns {Promise<Object>} The callback result
   */
  const handleCallback = async () => {
    setLoading(true);
    setError(null);

    try {
      // If sync tools are available, use them to handle the callback
      if (syncTools?.handleAuthState) {
        try {
          console.log('AuthContext (handleCallback): Handling OAuth callback');

          const result = await handleOAuthCallback(syncTools.handleAuthState);

          console.log('AuthContext (handleCallback): OAuth callback result:', {
            success: result.success,
            hasSession: !!result.session,
            hasAttorney: !!result.attorney,
            fallback: !!result.fallback
          });

          if (result.success && result.session) {
            setUser(result.session.user);
            setSession(result.session);

            if (result.attorney) {
              setAttorney(result.attorney);
            } else if (result.fallback) {
              // If we're using a fallback result, log it but don't update attorney state
              console.log('AuthContext (handleCallback): Using fallback result, attorney state not updated');
            }
          }

          return result;
        } catch (callbackError) {
          console.error('Error in OAuth callback with sync:', callbackError);

          // Return a fallback result to allow the auth flow to continue
          const fallbackResult = {
            success: true,
            fallback: true,
            message: `OAuth callback handled with fallback due to error: ${callbackError.message}`,
            error: callbackError.message
          };

          console.log('AuthContext (handleCallback): Using fallback result:', fallbackResult);

          return fallbackResult;
        }
      } else {
        // Fall back to basic callback handling
        console.log('🔐 [AuthContext] Using real Supabase client for callback handling');
        const supabase = await getRealSupabaseClient();
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) throw error;

        if (session?.user) {
          setUser(session.user);
          setSession(session);
        }

        return { success: !!session, session };
      }
    } catch (error) {
      console.error('Callback error:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Sign out
   *
   * @returns {Promise<Object>} The sign out result
   */
  const signOut = async () => {
    setLoading(true);
    setError(null);

    try {
      // If sync tools are available, use them to handle sign out
      if (syncTools?.handleAuthState) {
        const result = await authSignOut(syncTools.handleAuthState);

        if (result.success) {
          setUser(null);
          setSession(null);
          setAttorney(null);
        }

        return result;
      } else {
        // Fall back to basic sign out
        const supabase = await getSupabaseClient();
        const { error } = await supabase.auth.signOut();

        if (error) throw error;

        setUser(null);
        setSession(null);
        setAttorney(null);

        return { success: true };
      }
    } catch (error) {
      console.error('Sign out error:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Refresh session
   *
   * @returns {Promise<Object>} The refresh result
   */
  const refreshSession = async () => {
    setLoading(true);
    setError(null);

    try {
      // If sync tools are available, use them to refresh the session
      if (syncTools?.handleAuthState) {
        const result = await getCurrentSession(syncTools.handleAuthState);

        if (result.success && result.session) {
          setUser(result.session.user);
          setSession(result.session);

          if (result.attorney) {
            setAttorney(result.attorney);
          }
        } else {
          setUser(null);
          setSession(null);
          setAttorney(null);
        }

        return result;
      } else {
        // Fall back to basic session refresh
        const supabase = await getSupabaseClient();
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) throw error;

        if (session?.user) {
          setUser(session.user);
          setSession(session);
        } else {
          setUser(null);
          setSession(null);
          setAttorney(null);
        }

        return { success: !!session, session };
      }
    } catch (error) {
      console.error('Refresh error:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Force real authentication in all environments
  const isDev = false; // Always use real authentication

  // Create a ref to track if we've already created a mock user
  const mockUserCreated = useRef(false);

  // Always use real authentication, even in development mode
  useEffect(() => {
    // Force real authentication by not creating mock users
    console.log('Using real authentication in all environments');

    // We're not creating mock users anymore, but we'll keep this for compatibility
    mockUserCreated.current = true;
  }, []);

  // Context value
  const value = {
    // Auth state
    user,
    attorney,
    session,
    isLoading: loading, // Use consistent naming with isLoading instead of loading
    error,
    isAuthenticated: !!session,

    // Auth methods
    login,
    handleCallback,
    signOut,
    refreshSession,

    // Helper methods
    clearError: () => setError(null)
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
