import React, { useState } from 'react';
import './VapiAssistantCleanup.css';

const VapiAssistantCleanup = () => {
  const [analysis, setAnalysis] = useState(null);
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState(null);

  const runAnalysis = async () => {
    setLoading(true);
    try {
      if (window.vapiAssistantCleanup) {
        const analysisResult = await window.vapiAssistantCleanup.analyzeAssistants();
        setAnalysis(analysisResult);
      } else {
        console.error('Vapi Assistant Cleanup utility not loaded');
      }
    } catch (error) {
      console.error('Error running analysis:', error);
    } finally {
      setLoading(false);
    }
  };

  const runCleanup = async (dryRun = true) => {
    setLoading(true);
    try {
      if (window.vapiAssistantCleanup) {
        const cleanupResults = await window.vapiAssistantCleanup.runCleanup({ 
          dryRun, 
          autoConfirm: dryRun 
        });
        setResults(cleanupResults);
        
        // Refresh analysis after cleanup
        if (!dryRun) {
          setTimeout(() => runAnalysis(), 1000);
        }
      }
    } catch (error) {
      console.error('Error running cleanup:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="vapi-assistant-cleanup">
      <div className="cleanup-header">
        <h3>🧹 Assistant Cleanup</h3>
        <p>Identify and remove unused assistants with default configurations</p>
      </div>

      <div className="cleanup-actions">
        <button 
          onClick={runAnalysis} 
          disabled={loading}
          className="btn btn-primary"
        >
          {loading ? '🔄 Analyzing...' : '📊 Analyze Assistants'}
        </button>

        {analysis && analysis.safeToDelete.length > 0 && (
          <>
            <button 
              onClick={() => runCleanup(true)} 
              disabled={loading}
              className="btn btn-secondary"
            >
              {loading ? '🔄 Running...' : '🔍 Dry Run Cleanup'}
            </button>

            <button 
              onClick={() => runCleanup(false)} 
              disabled={loading}
              className="btn btn-danger"
            >
              {loading ? '🔄 Deleting...' : '🗑️ Delete Unused Assistants'}
            </button>
          </>
        )}
      </div>

      {analysis && (
        <div className="cleanup-analysis">
          <h4>📋 Analysis Results</h4>
          <div className="analysis-stats">
            <div className="stat">
              <span className="stat-label">Total Assistants:</span>
              <span className="stat-value">{analysis.total}</span>
            </div>
            <div className="stat">
              <span className="stat-label">Protected:</span>
              <span className="stat-value">{analysis.protected}</span>
            </div>
            <div className="stat">
              <span className="stat-label">With Call Records:</span>
              <span className="stat-value">{analysis.hasCallRecords}</span>
            </div>
            <div className="stat safe-to-delete">
              <span className="stat-label">Safe to Delete:</span>
              <span className="stat-value">{analysis.safeToDelete.length}</span>
            </div>
          </div>

          {analysis.safeToDelete.length > 0 && (
            <div className="safe-to-delete-list">
              <h5>🗑️ Assistants Safe to Delete</h5>
              <div className="assistant-list">
                {analysis.safeToDelete.map((assistant, index) => (
                  <div key={assistant.id} className="assistant-item">
                    <div className="assistant-info">
                      <span className="assistant-id">{assistant.id}</span>
                      <span className="assistant-name">{assistant.name}</span>
                      <span className="assistant-date">
                        Created: {new Date(assistant.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    <span className="assistant-reason">{assistant.reason}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {analysis.protected.length > 0 && (
            <div className="protected-list">
              <h5>🛡️ Protected Assistants</h5>
              <div className="assistant-list">
                {analysis.protected.map((assistant, index) => (
                  <div key={assistant.id} className="assistant-item protected">
                    <div className="assistant-info">
                      <span className="assistant-id">{assistant.id}</span>
                      <span className="assistant-name">{assistant.name}</span>
                    </div>
                    <span className="assistant-reason">{assistant.reason}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {analysis.hasCallsButDefault.length > 0 && (
            <div className="has-calls-default-list">
              <h5>⚠️ Has Calls But Default Config</h5>
              <div className="assistant-list">
                {analysis.hasCallsButDefault.map((assistant, index) => (
                  <div key={assistant.id} className="assistant-item warning">
                    <div className="assistant-info">
                      <span className="assistant-id">{assistant.id}</span>
                      <span className="assistant-name">{assistant.name}</span>
                      <span className="call-count">{assistant.callCount} calls</span>
                    </div>
                    <span className="assistant-reason">{assistant.reason}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {results && (
        <div className="cleanup-results">
          <h4>📊 Cleanup Results</h4>
          {results.deleteResults && (
            <div className="results-stats">
              <div className="stat success">
                <span className="stat-label">Success:</span>
                <span className="stat-value">{results.deleteResults.success.length}</span>
              </div>
              <div className="stat error">
                <span className="stat-label">Failed:</span>
                <span className="stat-value">{results.deleteResults.failed.length}</span>
              </div>
              <div className="stat skipped">
                <span className="stat-label">Skipped:</span>
                <span className="stat-value">{results.deleteResults.skipped.length}</span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default VapiAssistantCleanup;
