import{R as f}from"./index-dd4c5999.js";const l={Provider:function(t){return typeof t.children<"u"?t.children:null},Consumer:function(t){return t.children&&typeof t.children=="function"?t.children({}):null},displayName:"LayoutGroupContext",_currentValue:{},_currentValue2:{},_threadCount:0,_defaultValue:{}};typeof window<"u"&&(window.LayoutGroupContext=l);const a={Provider:function(t){return typeof t.children<"u"?t.children:null},Consumer:function(t){return t.children&&typeof t.children=="function"?t.children({}):null},displayName:"MotionConfigContext",_currentValue:{},_currentValue2:{},_threadCount:0,_defaultValue:{}};typeof window<"u"&&(window.MotionConfigContext=a);const n=t=>({children:i,...r})=>{const{initial:o,animate:d,exit:c,transition:h,variants:s,whileHover:m,whileTap:C,whileFocus:g,whileDrag:y,whileInView:w,...u}=r;return f.createElement(t,u,i)},x=new Proxy({},{get:(t,e)=>n(e==="custom"||e==="div"?"div":e==="span"?"span":e==="button"?"button":e==="a"?"a":e==="ul"?"ul":e==="li"?"li":e==="p"?"p":e==="h1"?"h1":e==="h2"?"h2":e==="h3"?"h3":e==="h4"?"h4":e==="h5"?"h5":e==="h6"?"h6":e==="img"?"img":e==="svg"?"svg":e==="path"?"path":e==="circle"?"circle":e==="rect"?"rect":e==="line"?"line":e==="polyline"?"polyline":e==="polygon"?"polygon":e==="g"?"g":e==="defs"?"defs":e==="mask"?"mask":e==="clipPath"?"clipPath":e==="linearGradient"?"linearGradient":e==="radialGradient"?"radialGradient":e==="stop"?"stop":e==="filter"?"filter":e==="feGaussianBlur"?"feGaussianBlur":e==="feOffset"?"feOffset":e==="feComposite"?"feComposite":e==="feColorMatrix"?"feColorMatrix":e==="feMerge"?"feMerge":e==="feMergeNode"?"feMergeNode":e==="feBlend"?"feBlend":e==="feFlood"?"feFlood":"div")}),v=({children:t,...e})=>t||null;export{v as A,x as m};
