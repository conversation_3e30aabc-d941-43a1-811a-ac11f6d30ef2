import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { FaRobot, FaPhone, FaClock, FaChartLine, FaCalendarWeek, FaExternalLinkAlt, FaTrash, FaExclamationTriangle } from 'react-icons/fa';
import { useAuth } from '../../contexts/AuthContext';
import './VeryCoolAssistants.css';

/**
 * VeryCoolAssistants component displays all assistants with their subdomains, 
 * images, and call stats in an attractive card layout
 */
const VeryCoolAssistants = ({ attorney, onAssistantSelect, onNavigateToAgent }) => {
  const { user } = useAuth();
  const [assistants, setAssistants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [assistantStats, setAssistantStats] = useState({});
  const [deleteConfirmation, setDeleteConfirmation] = useState(null);
  const [deleting, setDeleting] = useState(false);

  // Load assistants and their data
  useEffect(() => {
    if (attorney?.id) {
      loadAssistants();
    }
  }, [attorney?.id]);

  // Subscribe to centralized assistant data changes
  useEffect(() => {
    const handleDataChange = (eventType, data) => {
      console.log('🔄 [VeryCoolAssistants] Assistant data changed:', eventType, data);

      if (eventType === 'assistant_deleted' || eventType === 'assistant_created' || eventType === 'cache_invalidated') {
        // Refresh when data changes
        if (attorney?.id) {
          loadAssistants();
        }
      }
    };

    // Subscribe to the centralized service
    let unsubscribe;

    const setupSubscription = async () => {
      try {
        const { AssistantDataService } = await import('../../services/assistantDataService');
        unsubscribe = AssistantDataService.subscribe(handleDataChange);
        console.log('✅ [VeryCoolAssistants] Subscribed to centralized assistant data service');
      } catch (error) {
        console.error('❌ [VeryCoolAssistants] Failed to subscribe to assistant data service:', error);
      }
    };

    setupSubscription();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [attorney?.id]);

  /**
   * Load all assistants using centralized service
   */
  const loadAssistants = async () => {
    try {
      setLoading(true);
      setError('');

      console.log('🔍 [VeryCoolAssistants] Loading assistants using centralized service for attorney:', attorney.id);

      // Use the same centralized service as the dropdown
      const { AssistantDataService } = await import('../../services/assistantDataService');
      const loadedAssistants = await AssistantDataService.getAssistantsForAttorney(attorney.id);

      console.log('📋 [VeryCoolAssistants] Loaded assistants from centralized service:', loadedAssistants);

      setAssistants(loadedAssistants);

      // Load stats for each assistant
      await loadAssistantStats(loadedAssistants);

    } catch (error) {
      console.error('❌ [VeryCoolAssistants] Error loading assistants:', error);
      setError('Failed to load assistants: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Load call statistics for each assistant
   */
  const loadAssistantStats = async (assistantList) => {
    const statsPromises = assistantList.map(async (assistant) => {
      try {
        // Get consultations for this assistant
        const { data: consultations, error } = await supabase
          .from('consultations')
          .select('duration, created_at, status')
          .eq('attorney_id', attorney.id)
          .eq('assistant_id', assistant.id);

        if (error) throw error;

        const now = new Date();
        const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

        const totalCalls = consultations.length;
        const totalDuration = consultations.reduce((sum, c) => sum + (c.duration || 0), 0);
        const averageDuration = totalCalls > 0 ? totalDuration / totalCalls : 0;
        const recentCalls = consultations.filter(c => 
          new Date(c.created_at) >= last7Days
        ).length;

        return {
          assistantId: assistant.id,
          totalCalls,
          totalDuration,
          averageDuration,
          recentCalls
        };
      } catch (error) {
        console.error(`Error loading stats for assistant ${assistant.id}:`, error);
        return {
          assistantId: assistant.id,
          totalCalls: 0,
          totalDuration: 0,
          averageDuration: 0,
          recentCalls: 0
        };
      }
    });

    const stats = await Promise.all(statsPromises);
    const statsLookup = {};
    stats.forEach(stat => {
      statsLookup[stat.assistantId] = stat;
    });
    setAssistantStats(statsLookup);
  };

  /**
   * Handle assistant card click
   */
  const handleAssistantClick = (assistant) => {
    // Set as current assistant and navigate to Agent tab
    if (onAssistantSelect) {
      onAssistantSelect(assistant.id);
    }
    if (onNavigateToAgent) {
      onNavigateToAgent();
    }
  };

  /**
   * Handle delete assistant confirmation
   */
  const handleDeleteClick = (e, assistant) => {
    e.stopPropagation(); // Prevent card click
    setDeleteConfirmation(assistant);
  };

  /**
   * Cancel delete confirmation
   */
  const cancelDelete = () => {
    setDeleteConfirmation(null);
  };

  /**
   * Confirm and execute assistant deletion
   */
  const confirmDelete = async () => {
    if (!deleteConfirmation) return;

    const assistantId = deleteConfirmation.id;
    const assistantName = deleteConfirmation.name;

    try {
      setDeleting(true);

      console.log('🗑️ [VeryCoolAssistants] Deleting assistant using centralized service:', assistantId);

      // Use the centralized deletion service
      const { AssistantDataService } = await import('../../services/assistantDataService');
      await AssistantDataService.deleteAssistant(attorney.id, assistantId, assistantName);

      console.log('✅ [VeryCoolAssistants] Successfully deleted assistant via centralized service');

      // Close confirmation dialog
      setDeleteConfirmation(null);

      // The centralized service will handle:
      // - Deleting from Vapi
      // - Deleting from Supabase
      // - Clearing cache
      // - Notifying all subscribers (including this component and the dropdown)
      // - Emitting global events

    } catch (error) {
      console.error('❌ [VeryCoolAssistants] Error deleting assistant:', error);
      setError('Failed to delete assistant: ' + error.message);
    } finally {
      setDeleting(false);
    }
  };

  /**
   * Generate default avatar image
   */
  const getDefaultAvatarImage = (initials) => {
    const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];
    const color = colors[initials.charCodeAt(0) % colors.length];
    
    return `data:image/svg+xml;base64,${btoa(`
      <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
        <circle cx="20" cy="20" r="20" fill="${color}"/>
        <text x="20" y="26" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">
          ${initials}
        </text>
      </svg>
    `)}`;
  };

  /**
   * Format duration in minutes
   */
  const formatDuration = (seconds) => {
    if (!seconds) return '0m';
    const minutes = Math.round(seconds / 60);
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  if (loading) {
    return (
      <div className="very-cool-assistants">
        <div className="assistants-header">
          <h2>My Assistants</h2>
        </div>
        <div className="loading-state">
          <div className="spinner"></div>
          <p>Loading your assistants...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="very-cool-assistants">
        <div className="assistants-header">
          <h2>My Assistants</h2>
        </div>
        <div className="error-state">
          <p>{error}</p>
          <button onClick={loadAssistants} className="retry-button">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="very-cool-assistants">
      <div className="assistants-header">
        <h2>My Assistants</h2>
        <p className="assistants-description">
          Click any assistant to configure and preview, or delete unused assistants
        </p>
      </div>

      {assistants.length === 0 ? (
        <div className="empty-state">
          <div className="empty-state-icon">
            <FaRobot />
          </div>
          <h3>No assistants yet</h3>
          <p>Create your first assistant in the Agent tab to get started.</p>
        </div>
      ) : (
        <>
          {/* Group assistants by deployment status */}
          {(() => {
            const deployedAssistants = assistants.filter(assistant => assistant.subdomain);
            const undeployedAssistants = assistants.filter(assistant => !assistant.subdomain);

            return (
              <>
                {/* Deployed Assistants Section */}
                {deployedAssistants.length > 0 && (
                  <div className="assistant-group">
                    <div className="group-header">
                      <h3 className="group-title">
                        <span className="status-indicator deployed"></span>
                        Deployed ({deployedAssistants.length})
                      </h3>
                      <p className="group-description">
                        Assistants with assigned subdomains that are live and accessible
                      </p>
                    </div>
                    <div className="assistants-grid">
                      {deployedAssistants.map((assistant) => {
                        const stats = assistantStats[assistant.id] || {};
                        const initials = assistant.name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2);
                        const imageUrl = assistant.image_url || getDefaultAvatarImage(initials);

                        return (
                          <div
                            key={assistant.id}
                            className="assistant-card deployed"
                            onClick={() => handleAssistantClick(assistant)}
                          >
                            <div className="assistant-card-header">
                              <div className="assistant-avatar">
                                <img
                                  src={imageUrl}
                                  alt={assistant.name}
                                  onError={(e) => {
                                    e.target.src = getDefaultAvatarImage(initials);
                                  }}
                                />
                                <div className="status-dot deployed" title="Deployed"></div>
                              </div>
                              <div className="assistant-info">
                                <h3 className="assistant-name">{assistant.name}</h3>
                                <div className="assistant-subdomain deployed">
                                  <FaExternalLinkAlt />
                                  <span className="subdomain-url">{assistant.subdomain}.legalscout.net</span>
                                </div>
                              </div>
                              <button
                                className="delete-assistant-btn"
                                onClick={(e) => handleDeleteClick(e, assistant)}
                                title="Delete Assistant"
                              >
                                <FaTrash />
                              </button>
                            </div>

                <div className="assistant-stats">
                  <div className="stat-item">
                    <FaPhone className="stat-icon" />
                    <div className="stat-content">
                      <div className="stat-value">{stats.totalCalls || 0}</div>
                      <div className="stat-label">Total Calls</div>
                    </div>
                  </div>

                  <div className="stat-item">
                    <FaClock className="stat-icon" />
                    <div className="stat-content">
                      <div className="stat-value">{formatDuration(stats.totalDuration)}</div>
                      <div className="stat-label">Total Duration</div>
                    </div>
                  </div>

                  <div className="stat-item">
                    <FaChartLine className="stat-icon" />
                    <div className="stat-content">
                      <div className="stat-value">{formatDuration(stats.averageDuration)}</div>
                      <div className="stat-label">Avg Duration</div>
                    </div>
                  </div>

                  <div className="stat-item">
                    <FaCalendarWeek className="stat-icon" />
                    <div className="stat-content">
                      <div className="stat-value">{stats.recentCalls || 0}</div>
                      <div className="stat-label">Last 7 Days</div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
                    </div>
                  </div>
                )}

                {/* No Subdomain Assigned Section */}
                {undeployedAssistants.length > 0 && (
                  <div className="assistant-group">
                    <div className="group-header">
                      <h3 className="group-title">
                        <span className="status-indicator undeployed"></span>
                        No Subdomain Assigned ({undeployedAssistants.length})
                      </h3>
                      <p className="group-description">
                        Assistants that need subdomain configuration to go live
                      </p>
                    </div>
                    <div className="assistants-grid">
                      {undeployedAssistants.map((assistant) => {
                        const stats = assistantStats[assistant.id] || {};
                        const initials = assistant.name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2);
                        const imageUrl = assistant.image_url || getDefaultAvatarImage(initials);

                        return (
                          <div
                            key={assistant.id}
                            className="assistant-card undeployed"
                            onClick={() => handleAssistantClick(assistant)}
                          >
                            <div className="assistant-card-header">
                              <div className="assistant-avatar">
                                <img
                                  src={imageUrl}
                                  alt={assistant.name}
                                  onError={(e) => {
                                    e.target.src = getDefaultAvatarImage(initials);
                                  }}
                                />
                                <div className="status-dot undeployed" title="No subdomain assigned"></div>
                              </div>
                              <div className="assistant-info">
                                <h3 className="assistant-name">{assistant.name}</h3>
                                <div className="assistant-subdomain">
                                  <span className="no-subdomain">No subdomain assigned</span>
                                </div>
                              </div>
                              <button
                                className="delete-assistant-btn"
                                onClick={(e) => handleDeleteClick(e, assistant)}
                                title="Delete Assistant"
                              >
                                <FaTrash />
                              </button>
                            </div>

                            <div className="assistant-stats">
                              <div className="stat-item">
                                <FaPhone className="stat-icon" />
                                <div className="stat-content">
                                  <div className="stat-value">{stats.totalCalls || 0}</div>
                                  <div className="stat-label">Total Calls</div>
                                </div>
                              </div>

                              <div className="stat-item">
                                <FaClock className="stat-icon" />
                                <div className="stat-content">
                                  <div className="stat-value">{formatDuration(stats.totalDuration)}</div>
                                  <div className="stat-label">Total Duration</div>
                                </div>
                              </div>

                              <div className="stat-item">
                                <FaChartLine className="stat-icon" />
                                <div className="stat-content">
                                  <div className="stat-value">{formatDuration(stats.averageDuration)}</div>
                                  <div className="stat-label">Avg Duration</div>
                                </div>
                              </div>

                              <div className="stat-item">
                                <FaCalendarWeek className="stat-icon" />
                                <div className="stat-content">
                                  <div className="stat-value">{stats.recentCalls || 0}</div>
                                  <div className="stat-label">Last 7 Days</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </>
            );
          })()}
        </>
      )}

      {/* Delete Confirmation Modal */}
      {deleteConfirmation && (
        <div className="delete-confirmation-overlay">
          <div className="delete-confirmation-modal">
            <div className="modal-header">
              <FaExclamationTriangle className="warning-icon" />
              <h3>Delete Assistant</h3>
            </div>

            <div className="modal-content">
              <p><strong>Are you sure you want to delete "{deleteConfirmation.name}"?</strong></p>

              <div className="warning-list">
                <p>⚠️ This action will permanently delete:</p>
                <ul>
                  <li>The assistant from Vapi</li>
                  <li>All UI configurations</li>
                  <li>Subdomain assignments</li>
                  <li>All consultation records</li>
                  <li>Call history and statistics</li>
                </ul>
              </div>

              <p className="final-warning">
                <strong>This action cannot be undone!</strong>
              </p>
            </div>

            <div className="modal-actions">
              <button
                className="btn btn-secondary"
                onClick={cancelDelete}
                disabled={deleting}
              >
                Cancel
              </button>
              <button
                className="btn btn-danger"
                onClick={confirmDelete}
                disabled={deleting}
              >
                {deleting ? 'Deleting...' : 'Delete Assistant'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VeryCoolAssistants;
