import { getSupabaseClient, isSupabaseConfigured } from '../lib/supabase';

/**
 * Service for handling Supabase authentication and attorney account management
 */
class SupabaseAuthService {
  /**
   * Sign in with OAuth provider (Google, etc.)
   * @param {string} provider - The OAuth provider to use (google, github, etc.)
   * @returns {Promise<Object>} The sign-in result
   */
  async signInWithOAuth(provider) {
    // Check if Supabase is configured
    if (!isSupabaseConfigured()) {
      return { data: null, error: new Error('Supabase not configured') };
    }

    try {
      // Always use current origin for redirect to fix localhost issue
      const redirectUrl = `${window.location.origin}/auth/callback`;

      console.log('[SupabaseAuthService] Using redirect URL:', redirectUrl);

      const supabase = await getSupabaseClient();
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: redirectUrl,
        },
      });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error(`${provider} sign-in error:`, error);
      return { data: null, error };
    }
  }

  /**
   * Sign up with email and password
   * @param {string} email - User's email
   * @param {string} password - User's password
   * @returns {Promise<Object>} The sign-up result
   */
  async signUpWithEmail(email, password) {
    // Check if Supabase is configured
    if (!isSupabaseConfigured()) {
      return { data: null, error: new Error('Supabase not configured') };
    }

    try {
      const supabase = await getSupabaseClient();
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Email sign-up error:', error);
      return { data: null, error };
    }
  }

  /**
   * Sign in with email and password
   * @param {string} email - User's email
   * @param {string} password - User's password
   * @returns {Promise<Object>} The sign-in result
   */
  async signInWithEmail(email, password) {
    // Check if Supabase is configured
    if (!isSupabaseConfigured()) {
      return { data: null, error: new Error('Supabase not configured') };
    }

    try {
      const supabase = await getSupabaseClient();
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Email sign-in error:', error);
      return { data: null, error };
    }
  }

  /**
   * Sign out the current user
   * @returns {Promise<Object>} The sign-out result
   */
  async signOut() {
    // Check if Supabase is configured
    if (!isSupabaseConfigured()) {
      return { error: null };
    }

    try {
      const supabase = await getSupabaseClient();
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      return { error: null };
    } catch (error) {
      console.error('Sign-out error:', error);
      return { error };
    }
  }

  /**
   * Get the current authenticated user
   * @returns {Promise<Object>} The current user
   */
  async getCurrentUser() {
    // Check if Supabase is configured
    if (!isSupabaseConfigured()) {
      return { user: null, error: null };
    }

    try {
      const supabase = await getSupabaseClient();
      const { data, error } = await supabase.auth.getUser();
      if (error) throw error;
      return { user: data.user, error: null };
    } catch (error) {
      console.error('Get current user error:', error);
      return { user: null, error };
    }
  }

  /**
   * Create a new attorney record in the database
   * @param {Object} attorneyData - The attorney data to store
   * @returns {Promise<Object>} The created attorney record
   */
  async createAttorney(attorneyData) {
    // Check if Supabase is configured
    if (!isSupabaseConfigured()) {
      return { data: null, error: new Error('Supabase not configured') };
    }

    try {
      // Check if subdomain is available
      const { data: existingAttorney, error: lookupError } = await supabase
        .from('attorneys')
        .select('id')
        .eq('subdomain', attorneyData.subdomain)
        .single();

      if (existingAttorney) {
        throw new Error('This subdomain is already taken. Please choose another one.');
      }

      // Create attorney record
      const { data, error } = await supabase
        .from('attorneys')
        .insert([attorneyData])
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Create attorney error:', error);
      return { data: null, error };
    }
  }

  /**
   * Get attorney data by user ID
   * @param {string} userId - The user ID to look up
   * @returns {Promise<Object>} The attorney data
   */
  async getAttorneyByUserId(userId) {
    // Check if Supabase is configured
    if (!isSupabaseConfigured()) {
      return { data: null, error: new Error('Supabase not configured') };
    }

    try {
      const { data, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Get attorney error:', error);
      return { data: null, error };
    }
  }

  /**
   * Check if a subdomain is available
   * @param {string} subdomain - The subdomain to check
   * @returns {Promise<boolean>} Whether the subdomain is available
   */
  async isSubdomainAvailable(subdomain) {
    // Check if Supabase is configured
    if (!isSupabaseConfigured()) {
      return { available: false, error: new Error('Supabase not configured') };
    }

    try {
      const { data, error } = await supabase
        .from('attorneys')
        .select('id')
        .eq('subdomain', subdomain)
        .single();

      if (error && error.code === 'PGRST116') {
        // No rows returned, subdomain is available
        return { available: true, error: null };
      }

      // Subdomain exists
      return { available: false, error: null };
    } catch (error) {
      console.error('Check subdomain error:', error);
      return { available: false, error };
    }
  }
}

// Export a singleton instance
export const supabaseAuthService = new SupabaseAuthService();
