#!/usr/bin/env node

/**
 * Production Build Script
 * Ensures proper build configuration for production deployment
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const __dirname = path.dirname(new URL(import.meta.url).pathname);
const rootDir = path.resolve(__dirname, '..');

console.log('🚀 Starting production build process...');

// Step 1: Clean previous build
console.log('🧹 Cleaning previous build...');
try {
  if (fs.existsSync(path.join(rootDir, 'dist'))) {
    fs.rmSync(path.join(rootDir, 'dist'), { recursive: true, force: true });
  }
  console.log('✅ Previous build cleaned');
} catch (error) {
  console.warn('⚠️ Could not clean previous build:', error.message);
}

// Step 2: Set production environment
console.log('🔧 Setting production environment...');
process.env.NODE_ENV = 'production';
process.env.VITE_NODE_ENV = 'production';

// Step 3: Build with Vite
console.log('📦 Building with Vite...');
try {
  const buildCommand = process.platform === 'win32' ? 'npm.cmd run build' : 'npm run build';
  execSync(buildCommand, {
    stdio: 'inherit',
    cwd: rootDir,
    env: {
      ...process.env,
      NODE_ENV: 'production',
      VITE_NODE_ENV: 'production'
    },
    shell: true
  });
  console.log('✅ Vite build completed');
} catch (error) {
  console.error('❌ Vite build failed:', error.message);
  process.exit(1);
}

// Step 4: Verify build output
console.log('🔍 Verifying build output...');
const distDir = path.join(rootDir, 'dist');
const indexHtml = path.join(distDir, 'index.html');

if (!fs.existsSync(distDir)) {
  console.error('❌ Dist directory not found');
  process.exit(1);
}

if (!fs.existsSync(indexHtml)) {
  console.error('❌ index.html not found in dist');
  process.exit(1);
}

// Check for main JS files
const assetsDir = path.join(distDir, 'assets');
if (fs.existsSync(assetsDir)) {
  const jsFiles = fs.readdirSync(assetsDir).filter(file => file.endsWith('.js'));
  console.log(`✅ Found ${jsFiles.length} JavaScript files in assets`);
} else {
  console.warn('⚠️ Assets directory not found');
}

// Step 5: Create deployment info
console.log('📝 Creating deployment info...');
const deploymentInfo = {
  buildTime: new Date().toISOString(),
  nodeEnv: 'production',
  version: process.env.npm_package_version || '1.0.0',
  commit: process.env.VERCEL_GIT_COMMIT_SHA || 'unknown'
};

fs.writeFileSync(
  path.join(distDir, 'deployment-info.json'),
  JSON.stringify(deploymentInfo, null, 2)
);

console.log('🎉 Production build completed successfully!');
console.log('📊 Build summary:');
console.log(`   - Build time: ${deploymentInfo.buildTime}`);
console.log(`   - Environment: ${deploymentInfo.nodeEnv}`);
console.log(`   - Version: ${deploymentInfo.version}`);
