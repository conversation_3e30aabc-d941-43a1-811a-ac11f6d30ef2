# LegalScout Voice - Testing Guide

This guide provides an overview of all the testing tools available to help you debug and validate your LegalScout Voice application.

## Quick Start

For immediate debugging, run these commands:

```bash
# Quick health check (fastest)
npm run test:system-quick

# Simple app test (comprehensive but fast)
npm run test:simple

# Full debug suite (most comprehensive)
npm run debug:app
```

## Available Test Commands

### 1. Health Checks

#### `npm run test:system-quick`
- **Purpose**: Basic health check without browser
- **Tests**: Environment variables, critical files, API connectivity
- **Duration**: ~5 seconds
- **Best for**: Quick validation that your environment is set up correctly

#### `npm run test:system-verbose`
- **Purpose**: Detailed health check with verbose output
- **Tests**: Same as quick but with detailed logging
- **Duration**: ~5 seconds
- **Best for**: Debugging environment issues

### 2. Simple Tests

#### `npm run test:simple`
- **Purpose**: Fast, comprehensive app testing
- **Tests**: Environment, APIs, app accessibility, critical files
- **Duration**: ~10 seconds
- **Best for**: Regular development testing

#### `npm run test:simple-local`
- **Purpose**: Test local development environment
- **Tests**: Same as simple but specifically for localhost:5174
- **Duration**: ~10 seconds
- **Best for**: Testing while developing locally

#### `npm run test:simple-prod`
- **Purpose**: Test production deployment
- **Tests**: Same as simple but for dashboard.legalscout.net
- **Duration**: ~10 seconds
- **Best for**: Validating production deployment

### 3. Debug Suites

#### `npm run debug:app`
- **Purpose**: Comprehensive diagnostic testing
- **Tests**: Environment, files, dependencies, APIs, build config
- **Duration**: ~15 seconds
- **Best for**: Deep debugging when something is broken

#### `npm run debug:quick`
- **Purpose**: Quick diagnostic without API calls
- **Tests**: Environment, files, dependencies, build config
- **Duration**: ~5 seconds
- **Best for**: Offline debugging

#### `npm run debug:full`
- **Purpose**: Full diagnostic including browser tests
- **Tests**: Everything plus browser-based tests
- **Duration**: ~30 seconds
- **Best for**: Complete system validation

### 4. Browser Tests

#### `npm run debug:browser`
- **Purpose**: Open browser-based debug interface
- **Tests**: Interactive testing in your browser
- **Duration**: Instant (opens browser)
- **Best for**: Visual debugging and real-time testing

### 5. Test Runner

#### `npm run test:runner`
- **Purpose**: Interactive test selection
- **Tests**: Choose which tests to run
- **Duration**: Varies
- **Best for**: Selective testing

#### Available test runner options:
```bash
npm run test:runner health      # Run health check
npm run test:runner debug       # Run debug suite
npm run test:runner all         # Run all tests
npm run test:runner --help      # Show all options
```

## Test Files and Scripts

### Core Test Scripts
- `scripts/quick-health-check.js` - Basic health validation
- `scripts/debug-app-suite.js` - Comprehensive diagnostic suite
- `scripts/simple-app-test.js` - Fast app validation
- `scripts/test-runner.js` - Interactive test selection

### Browser Test Pages
- `public/debug-test.html` - Interactive browser-based testing
- `public/system-test.html` - Existing system test page
- `public/comprehensive-system-test.js` - Browser test logic

## What Each Test Checks

### Environment Variables
- `VITE_SUPABASE_URL` - Supabase project URL
- `VITE_SUPABASE_KEY` - Supabase anonymous key
- `VITE_VAPI_PUBLIC_KEY` - Vapi public API key
- `VITE_VAPI_SECRET_KEY` - Vapi private API key

### Critical Files
- `src/App.jsx` - Main React application
- `src/lib/supabase.js` - Supabase client configuration
- `src/config/vapiConfig.js` - Vapi configuration
- `api/index.js` - API handler
- `package.json` - Project configuration
- `.env` - Environment variables

### API Connectivity
- Supabase REST API accessibility
- Vapi API accessibility
- App URL accessibility (local/production)

### Dependencies
- `@supabase/supabase-js` - Supabase client
- `react` - React framework
- `react-dom` - React DOM
- `vite` - Build tool

### Build Configuration
- `vite.config.js` - Vite configuration
- `vercel.json` - Vercel deployment configuration

## Troubleshooting Common Issues

### Environment Variables Missing
```bash
# Check your .env file
npm run test:system-verbose

# Fix: Ensure all required variables are in .env
```

### API Connectivity Issues
```bash
# Test API connectivity
npm run test:simple

# Check network and API keys
```

### App Not Accessible
```bash
# For local development
npm run dev:full
npm run test:simple-local

# For production
npm run test:simple-prod
```

### Missing Files
```bash
# Check critical files
npm run debug:app

# Restore missing files from git
```

## Best Practices

1. **Start with simple tests**: Use `npm run test:simple` for regular development
2. **Use health checks for CI/CD**: `npm run test:system-quick` is perfect for automated testing
3. **Debug with comprehensive suite**: Use `npm run debug:app` when something is broken
4. **Test both environments**: Use `test:simple-local` and `test:simple-prod` to validate both local and production
5. **Use browser tests for UI issues**: `npm run debug:browser` for visual debugging

## Integration with Development Workflow

### Before Starting Development
```bash
npm run test:simple-local
```

### During Development
```bash
npm run test:system-quick  # Quick validation
```

### Before Deployment
```bash
npm run debug:app          # Full validation
npm run test:simple-prod   # Test production
```

### When Debugging Issues
```bash
npm run debug:app          # Comprehensive diagnostics
npm run debug:browser      # Visual debugging
```

## Exit Codes

All tests return appropriate exit codes:
- `0` - All tests passed
- `1` - One or more tests failed

This makes them suitable for use in CI/CD pipelines and automated workflows.

## Adding New Tests

To add new tests, modify the appropriate script:
- Environment tests: `scripts/debug-app-suite.js`
- Simple tests: `scripts/simple-app-test.js`
- Browser tests: `public/debug-test.html`

Each test should follow the pattern:
1. Log what it's testing
2. Perform the test
3. Record the result with clear success/failure indication
4. Provide actionable fix suggestions for failures
