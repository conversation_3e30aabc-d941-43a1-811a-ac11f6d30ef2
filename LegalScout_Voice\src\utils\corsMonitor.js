/**
 * CORS Monitor - Helps debug and monitor CORS issues in LegalScout
 */

class CORSMonitor {
  constructor() {
    this.enabled = import.meta.env.DEV || localStorage.getItem('cors-debug') === 'true';
    this.requests = [];
    this.maxRequests = 50; // Keep last 50 requests
    
    if (this.enabled) {
      this.setupInterceptors();
      console.log('[CORS Monitor] Enabled - monitoring all requests');
    }
  }
  
  setupInterceptors() {
    // Intercept fetch requests
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const [url, options = {}] = args;
      const startTime = Date.now();
      
      this.logRequest('fetch', url, options);
      
      try {
        const response = await originalFetch(...args);
        this.logResponse('fetch', url, response, Date.now() - startTime);
        return response;
      } catch (error) {
        this.logError('fetch', url, error, Date.now() - startTime);
        throw error;
      }
    };
    
    // Intercept XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    const originalXHRSend = XMLHttpRequest.prototype.send;
    
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
      this._corsMonitor = { method, url, startTime: Date.now() };
      return originalXHROpen.call(this, method, url, ...args);
    };
    
    XMLHttpRequest.prototype.send = function(...args) {
      if (this._corsMonitor) {
        const monitor = window.corsMonitor;
        monitor.logRequest('xhr', this._corsMonitor.url, { method: this._corsMonitor.method });
        
        this.addEventListener('load', () => {
          monitor.logResponse('xhr', this._corsMonitor.url, this, Date.now() - this._corsMonitor.startTime);
        });
        
        this.addEventListener('error', () => {
          monitor.logError('xhr', this._corsMonitor.url, new Error('XHR Error'), Date.now() - this._corsMonitor.startTime);
        });
      }
      
      return originalXHRSend.call(this, ...args);
    };
  }
  
  logRequest(type, url, options) {
    if (!this.enabled) return;
    
    const request = {
      id: this.generateId(),
      type,
      url,
      method: options.method || 'GET',
      headers: options.headers || {},
      timestamp: new Date().toISOString(),
      isSupabase: this.isSupabaseRequest(url),
      isProxy: this.isProxyRequest(url)
    };
    
    this.requests.unshift(request);
    if (this.requests.length > this.maxRequests) {
      this.requests.pop();
    }
    
    console.log(`[CORS Monitor] 📤 ${type.toUpperCase()} ${request.method} ${url}`, {
      isSupabase: request.isSupabase,
      isProxy: request.isProxy,
      headers: request.headers
    });
  }
  
  logResponse(type, url, response, duration) {
    if (!this.enabled) return;
    
    const request = this.requests.find(r => r.url === url && r.type === type);
    if (request) {
      request.response = {
        status: response.status || response.statusCode,
        statusText: response.statusText || 'OK',
        headers: this.extractHeaders(response),
        duration
      };
      
      request.corsIssue = this.detectCORSIssue(request);
    }
    
    const statusEmoji = response.status >= 400 ? '❌' : '✅';
    console.log(`[CORS Monitor] ${statusEmoji} ${type.toUpperCase()} ${response.status} ${url} (${duration}ms)`, {
      corsHeaders: this.extractCORSHeaders(response),
      corsIssue: request?.corsIssue
    });
  }
  
  logError(type, url, error, duration) {
    if (!this.enabled) return;
    
    const request = this.requests.find(r => r.url === url && r.type === type);
    if (request) {
      request.error = {
        message: error.message,
        name: error.name,
        duration
      };
      
      request.corsIssue = this.detectCORSError(error);
    }
    
    console.error(`[CORS Monitor] 💥 ${type.toUpperCase()} ERROR ${url} (${duration}ms)`, {
      error: error.message,
      corsIssue: request?.corsIssue
    });
  }
  
  isSupabaseRequest(url) {
    return typeof url === 'string' && url.includes('.supabase.co');
  }
  
  isProxyRequest(url) {
    return typeof url === 'string' && url.includes('/api/supabase-proxy/');
  }
  
  detectCORSIssue(request) {
    if (!request.response) return null;
    
    const corsHeaders = this.extractCORSHeaders(request.response);
    
    if (request.isSupabase && !corsHeaders.allowOrigin) {
      return 'missing-cors-headers';
    }
    
    if (request.response.status === 0) {
      return 'cors-blocked';
    }
    
    return null;
  }
  
  detectCORSError(error) {
    const message = error.message.toLowerCase();
    
    if (message.includes('cors')) {
      return 'cors-error';
    }
    
    if (message.includes('access-control-allow-origin')) {
      return 'missing-allow-origin';
    }
    
    if (message.includes('preflight')) {
      return 'preflight-failed';
    }
    
    return null;
  }
  
  extractHeaders(response) {
    if (response.headers) {
      if (typeof response.headers.entries === 'function') {
        return Object.fromEntries(response.headers.entries());
      }
      if (typeof response.headers.forEach === 'function') {
        const headers = {};
        response.headers.forEach((value, key) => {
          headers[key] = value;
        });
        return headers;
      }
    }
    return {};
  }
  
  extractCORSHeaders(response) {
    const headers = this.extractHeaders(response);
    return {
      allowOrigin: headers['access-control-allow-origin'],
      allowMethods: headers['access-control-allow-methods'],
      allowHeaders: headers['access-control-allow-headers'],
      allowCredentials: headers['access-control-allow-credentials']
    };
  }
  
  generateId() {
    return Math.random().toString(36).substr(2, 9);
  }
  
  // Public methods for debugging
  getRequests() {
    return this.requests;
  }
  
  getCORSIssues() {
    return this.requests.filter(r => r.corsIssue);
  }
  
  getSupabaseRequests() {
    return this.requests.filter(r => r.isSupabase);
  }
  
  getProxyRequests() {
    return this.requests.filter(r => r.isProxy);
  }
  
  clear() {
    this.requests = [];
    console.log('[CORS Monitor] Request history cleared');
  }
  
  enable() {
    this.enabled = true;
    localStorage.setItem('cors-debug', 'true');
    console.log('[CORS Monitor] Enabled');
  }
  
  disable() {
    this.enabled = false;
    localStorage.removeItem('cors-debug');
    console.log('[CORS Monitor] Disabled');
  }
  
  report() {
    const total = this.requests.length;
    const corsIssues = this.getCORSIssues().length;
    const supabaseRequests = this.getSupabaseRequests().length;
    const proxyRequests = this.getProxyRequests().length;
    
    console.group('[CORS Monitor] Report');
    console.log(`Total requests: ${total}`);
    console.log(`CORS issues: ${corsIssues}`);
    console.log(`Supabase requests: ${supabaseRequests}`);
    console.log(`Proxy requests: ${proxyRequests}`);
    
    if (corsIssues > 0) {
      console.log('CORS Issues:', this.getCORSIssues());
    }
    
    console.groupEnd();
    
    return {
      total,
      corsIssues,
      supabaseRequests,
      proxyRequests,
      issues: this.getCORSIssues()
    };
  }
}

// Create global instance
const corsMonitor = new CORSMonitor();

// Expose to window for debugging
if (typeof window !== 'undefined') {
  window.corsMonitor = corsMonitor;
  
  // Add console commands
  console.log(`
🔧 CORS Monitor Commands:
  corsMonitor.report()     - Show summary report
  corsMonitor.getRequests() - Get all requests
  corsMonitor.getCORSIssues() - Get requests with CORS issues
  corsMonitor.clear()      - Clear request history
  corsMonitor.enable()     - Enable monitoring
  corsMonitor.disable()    - Disable monitoring
  `);
}

export default corsMonitor;
