import React from 'react';
import { FaTimes, FaEnvelope, FaPhone, FaCalendarAlt, FaMapMarkerAlt, FaClock, FaUser, FaFileAlt, FaCheckCircle, FaExclamationTriangle, FaUserCheck, FaClipboardCheck, FaInfoCircle, FaShareAlt, FaSearchPlus, FaGavel, FaUniversity, FaWpforms } from 'react-icons/fa';
import './BriefModal.css';

/**
 * BriefModal component for displaying detailed consultation information
 * Beautiful, clean document-style layout with client interaction buttons
 */
const BriefModal = ({ consultation, attorney, customFields, isOpen, onClose }) => {
  if (!isOpen || !consultation) return null;

  // Handle workflow actions
  const handleWorkflowAction = async (action) => {
    try {
      const updates = { workflow_stage: action };

      switch (action) {
        case 'qualify':
        case 'intake':
        case 'conflict-check':
        case 'collect-info':
        case 'draft':
        case 'review':
        case 'research':
        case 'forms':
          updates.status = 'in-progress';
          break;
        case 'refer':
        case 'file':
          updates.status = 'completed';
          break;
        default:
          break;
      }

      // Here you would typically make an API call to update the consultation
      // For now, we'll just close the modal
      console.log(`Workflow action: ${action}`, updates);
      onClose();
    } catch (error) {
      console.error('Error updating consultation workflow:', error);
    }
  };

  // Format date nicely
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get custom field value
  const getCustomFieldValue = (consultation, field) => {
    if (!consultation.metadata) return field.default_value || 'Not provided';
    
    // Try structured data first
    if (consultation.metadata.structured_data && consultation.metadata.structured_data[field.name]) {
      return consultation.metadata.structured_data[field.name];
    }
    
    // Try direct metadata
    if (consultation.metadata[field.name]) {
      return consultation.metadata[field.name];
    }
    
    return field.default_value || 'Not provided';
  };

  // Handle client actions
  const handleEmailClient = () => {
    if (consultation.client_email) {
      window.open(`mailto:${consultation.client_email}?subject=Follow-up: ${consultation.practice_area || 'Legal Consultation'}`);
    }
  };

  const handleCallClient = () => {
    if (consultation.client_phone) {
      window.open(`tel:${consultation.client_phone}`);
    }
  };

  const handleCallAttorney = () => {
    if (attorney?.phone) {
      window.open(`tel:${attorney.phone}`);
    } else {
      alert('Attorney phone number not available');
    }
  };

  const handleScheduleMeeting = () => {
    // This would integrate with calendar system
    alert('Calendar integration coming soon!');
  };

  return (
    <div className="brief-modal-overlay">
      <div className="brief-modal">
        {/* Header with client actions */}
        <div className="brief-header">
          <div className="brief-title-section">
            <h1 className="brief-title">Client Brief</h1>
            <p className="brief-subtitle">{formatDate(consultation.created_at)}</p>
          </div>
          
          <div className="brief-actions">
            {consultation.client_email && (
              <button className="action-btn primary" onClick={handleEmailClient}>
                <FaEnvelope /> Email Client
              </button>
            )}
            {consultation.client_phone && (
              <button className="action-btn secondary" onClick={handleCallClient}>
                <FaPhone /> Call Client
              </button>
            )}
            {attorney?.phone && (
              <button className="action-btn tertiary" onClick={handleCallAttorney}>
                <FaPhone /> Call Attorney
              </button>
            )}
            <button className="action-btn tertiary" onClick={handleScheduleMeeting}>
              <FaCalendarAlt /> Schedule Meeting
            </button>
            <button className="close-btn" onClick={onClose}>
              <FaTimes />
            </button>
          </div>
        </div>

        {/* Brief content */}
        <div className="brief-content">
          {/* Client Information Section */}
          <section className="brief-section">
            <h2 className="section-title">
              <FaUser className="section-icon" />
              Client Information
            </h2>
            <div className="section-content">
              <div className="info-grid">
                <div className="info-item">
                  <label>Name</label>
                  <value>{consultation.client_name || 'Anonymous Client'}</value>
                </div>
                {consultation.client_email && (
                  <div className="info-item">
                    <label>Email</label>
                    <value>
                      <a href={`mailto:${consultation.client_email}`} className="contact-link">
                        {consultation.client_email}
                      </a>
                    </value>
                  </div>
                )}
                {consultation.client_phone && (
                  <div className="info-item">
                    <label>Phone</label>
                    <value>
                      <a href={`tel:${consultation.client_phone}`} className="contact-link">
                        {consultation.client_phone}
                      </a>
                    </value>
                  </div>
                )}
                {consultation.practice_area && (
                  <div className="info-item">
                    <label>Practice Area</label>
                    <value>
                      <span className="practice-area-badge">{consultation.practice_area}</span>
                    </value>
                  </div>
                )}
                {consultation.location && (
                  <div className="info-item">
                    <label>Location</label>
                    <value>
                      <FaMapMarkerAlt className="location-icon" />
                      {consultation.location}
                    </value>
                  </div>
                )}
                {attorney?.phone && (
                  <div className="info-item">
                    <label>Attorney Contact</label>
                    <value>
                      <a href={`tel:${attorney.phone}`} className="contact-link">
                        <FaPhone /> {attorney.phone}
                      </a>
                    </value>
                  </div>
                )}
              </div>
            </div>
          </section>

          {/* Consultation Summary Section */}
          <section className="brief-section">
            <h2 className="section-title">
              <FaFileAlt className="section-icon" />
              Consultation Summary
            </h2>
            <div className="section-content">
              <div className="summary-content">
                {consultation.summary || 'No summary available for this consultation.'}
              </div>
            </div>
          </section>

          {/* Success Evaluation Section */}
          {consultation.metadata?.success_evaluation && (
            <section className="brief-section">
              <h2 className="section-title">
                <FaCheckCircle className="section-icon success" />
                Success Evaluation
              </h2>
              <div className="section-content">
                <div className="evaluation-content">
                  {consultation.metadata.success_evaluation}
                </div>
              </div>
            </section>
          )}

          {/* Custom Fields Section */}
          {Array.isArray(customFields) && customFields.length > 0 && (
            <section className="brief-section">
              <h2 className="section-title">
                <FaExclamationTriangle className="section-icon" />
                Case Details
              </h2>
              <div className="section-content">
                <div className="custom-fields-grid">
                  {customFields.map((field, index) => {
                    const value = getCustomFieldValue(consultation, field);
                    return (
                      <div key={index} className="custom-field-item">
                        <label>{field.label || field.name}</label>
                        <value className={`field-type-${field.type}`}>
                          {field.type === 'boolean' ? (value ? 'Yes' : 'No') : value}
                        </value>
                      </div>
                    );
                  })}
                </div>
              </div>
            </section>
          )}

          {/* Structured Data Section */}
          {consultation.metadata?.structured_data && (
            <section className="brief-section">
              <h2 className="section-title">
                <FaFileAlt className="section-icon" />
                Structured Data
              </h2>
              <div className="section-content">
                <div className="structured-data-grid">
                  {Object.entries(consultation.metadata.structured_data).map(([key, value]) => (
                    <div key={key} className="structured-data-item">
                      <label>{key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</label>
                      <value>{typeof value === 'object' ? JSON.stringify(value, null, 2) : value}</value>
                    </div>
                  ))}
                </div>
              </div>
            </section>
          )}

          {/* Call Metadata Section */}
          {consultation.metadata && (
            <section className="brief-section">
              <h2 className="section-title">
                <FaClock className="section-icon" />
                Call Information
              </h2>
              <div className="section-content">
                <div className="metadata-grid">
                  {consultation.metadata.call_duration && (
                    <div className="metadata-item">
                      <label>Duration</label>
                      <value>{Math.floor(consultation.metadata.call_duration / 60)}m {consultation.metadata.call_duration % 60}s</value>
                    </div>
                  )}
                  {consultation.metadata.end_reason && (
                    <div className="metadata-item">
                      <label>End Reason</label>
                      <value>{consultation.metadata.end_reason}</value>
                    </div>
                  )}
                  {consultation.status && (
                    <div className="metadata-item">
                      <label>Status</label>
                      <value>
                        <span className={`status-badge ${consultation.status}`}>
                          {consultation.status.charAt(0).toUpperCase() + consultation.status.slice(1)}
                        </span>
                      </value>
                    </div>
                  )}
                </div>
              </div>
            </section>
          )}
        </div>

        {/* Footer with workflow and additional actions */}
        <div className="brief-footer">
          {/* Workflow Actions */}
          <div className="workflow-actions">
            <div className="workflow-section">
              <h4 className="workflow-title">Pre-Engagement Actions</h4>
              <div className="workflow-buttons pre-engagement">
                <button
                  className="workflow-btn qualify pre-engagement"
                  onClick={() => handleWorkflowAction('qualify')}
                >
                  <FaUserCheck /> Qualify
                </button>
                <button
                  className="workflow-btn intake pre-engagement"
                  onClick={() => handleWorkflowAction('intake')}
                >
                  <FaClipboardCheck /> Intake
                </button>
                <button
                  className="workflow-btn conflict-check pre-engagement"
                  onClick={() => handleWorkflowAction('conflict-check')}
                >
                  <FaExclamationTriangle /> Conflict Check
                </button>
                <button
                  className="workflow-btn collect-info pre-engagement"
                  onClick={() => handleWorkflowAction('collect-info')}
                >
                  <FaInfoCircle /> Collect Info
                </button>
                <button
                  className="workflow-btn refer pre-engagement"
                  onClick={() => handleWorkflowAction('refer')}
                >
                  <FaShareAlt /> Refer
                </button>
              </div>
            </div>

            <div className="workflow-section">
              <h4 className="workflow-title">Client Engagement Actions</h4>
              <div className="workflow-buttons client-engagement">
                <button
                  className="workflow-btn draft client-engagement"
                  onClick={() => handleWorkflowAction('draft')}
                >
                  <FaFileAlt /> Draft
                </button>
                <button
                  className="workflow-btn review client-engagement"
                  onClick={() => handleWorkflowAction('review')}
                >
                  <FaSearchPlus /> Review
                </button>
                <button
                  className="workflow-btn research client-engagement"
                  onClick={() => handleWorkflowAction('research')}
                >
                  <FaGavel /> Research
                </button>
                <button
                  className="workflow-btn file client-engagement"
                  onClick={() => handleWorkflowAction('file')}
                >
                  <FaUniversity /> File
                </button>
                <button
                  className="workflow-btn forms client-engagement"
                  onClick={() => handleWorkflowAction('forms')}
                >
                  <FaWpforms /> Forms
                </button>
              </div>
            </div>
          </div>

          {/* Standard Actions */}
          <div className="footer-actions">
            <button className="footer-btn secondary">
              <FaFileAlt /> Export PDF
            </button>
            <button className="footer-btn secondary">
              <FaEnvelope /> Send to Client
            </button>
            <button className="footer-btn primary" onClick={onClose}>
              Close Brief
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BriefModal;
