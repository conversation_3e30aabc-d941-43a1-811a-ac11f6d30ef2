#!/usr/bin/env node

/**
 * LegalScout Voice - Simple Test Runner
 * 
 * This script provides an easy way to run various tests for debugging your app.
 * 
 * Usage:
 *   npm run test:runner
 *   node scripts/test-runner.js [test-name]
 * 
 * Available tests:
 *   health      - Quick health check
 *   debug       - Comprehensive debug suite
 *   env         - Environment variables only
 *   api         - API connectivity only
 *   files       - File system check only
 *   all         - Run all tests
 */

import { spawn } from 'child_process';
import { config } from 'dotenv';

// Load environment variables
config();

// Available tests
const TESTS = {
  health: {
    name: 'Quick Health Check',
    command: 'npm',
    args: ['run', 'test:system-quick'],
    description: 'Basic health check with environment and file validation'
  },
  debug: {
    name: 'Comprehensive Debug Suite',
    command: 'npm',
    args: ['run', 'debug:app'],
    description: 'Full diagnostic test suite with API connectivity'
  },
  'debug-quick': {
    name: 'Quick Debug Suite',
    command: 'npm',
    args: ['run', 'debug:quick'],
    description: 'Quick diagnostic tests without browser'
  },
  env: {
    name: 'Environment Variables Test',
    command: 'node',
    args: ['scripts/quick-health-check.js', '--verbose'],
    description: 'Test environment variable configuration'
  },
  browser: {
    name: 'Browser Debug Test',
    command: 'npm',
    args: ['run', 'debug:browser'],
    description: 'Open browser-based debug test page'
  },
  dev: {
    name: 'Start Development Server',
    command: 'npm',
    args: ['run', 'dev:full'],
    description: 'Start the full development environment'
  }
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [TestRunner]`;
  
  switch(type) {
    case 'success': console.log(`✅ ${prefix} ${message}`); break;
    case 'error': console.error(`❌ ${prefix} ${message}`); break;
    case 'warning': console.warn(`⚠️ ${prefix} ${message}`); break;
    default: console.log(`ℹ️ ${prefix} ${message}`);
  }
}

function showHelp() {
  console.log(`
🧪 LegalScout Voice - Test Runner

Usage: node scripts/test-runner.js [test-name]

Available Tests:
`);

  Object.entries(TESTS).forEach(([key, test]) => {
    console.log(`  ${key.padEnd(12)} - ${test.description}`);
  });

  console.log(`
Examples:
  node scripts/test-runner.js health
  node scripts/test-runner.js debug
  node scripts/test-runner.js all

Or use npm scripts:
  npm run test:system-quick
  npm run debug:app
  npm run debug:browser
`);
}

function runTest(testName) {
  return new Promise((resolve, reject) => {
    const test = TESTS[testName];
    
    if (!test) {
      reject(new Error(`Unknown test: ${testName}`));
      return;
    }

    log(`Starting ${test.name}...`, 'info');
    log(`Command: ${test.command} ${test.args.join(' ')}`, 'info');

    const child = spawn(test.command, test.args, {
      stdio: 'inherit',
      shell: true
    });

    child.on('close', (code) => {
      if (code === 0) {
        log(`${test.name} completed successfully`, 'success');
        resolve(code);
      } else {
        log(`${test.name} failed with exit code ${code}`, 'error');
        resolve(code); // Don't reject, just return the code
      }
    });

    child.on('error', (error) => {
      log(`Failed to start ${test.name}: ${error.message}`, 'error');
      reject(error);
    });
  });
}

async function runAllTests() {
  log('Running all tests...', 'info');
  
  const testOrder = ['health', 'debug-quick', 'env'];
  const results = {};
  
  for (const testName of testOrder) {
    try {
      log(`\n${'='.repeat(50)}`, 'info');
      const exitCode = await runTest(testName);
      results[testName] = exitCode;
    } catch (error) {
      log(`Test ${testName} failed: ${error.message}`, 'error');
      results[testName] = 1;
    }
  }
  
  // Summary
  log('\n📊 Test Summary:', 'info');
  let totalTests = 0;
  let passedTests = 0;
  
  Object.entries(results).forEach(([testName, exitCode]) => {
    totalTests++;
    if (exitCode === 0) {
      passedTests++;
      log(`  ${testName}: PASS`, 'success');
    } else {
      log(`  ${testName}: FAIL (exit code ${exitCode})`, 'error');
    }
  });
  
  const successRate = Math.round((passedTests / totalTests) * 100);
  log(`\nOverall: ${passedTests}/${totalTests} tests passed (${successRate}%)`, 
      successRate >= 80 ? 'success' : 'warning');
  
  return passedTests === totalTests;
}

// Main function
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
    showHelp();
    return;
  }
  
  const testName = args[0];
  
  try {
    if (testName === 'all') {
      const success = await runAllTests();
      process.exit(success ? 0 : 1);
    } else if (TESTS[testName]) {
      const exitCode = await runTest(testName);
      process.exit(exitCode);
    } else {
      log(`Unknown test: ${testName}`, 'error');
      log('Use --help to see available tests', 'info');
      process.exit(1);
    }
  } catch (error) {
    log(`Test runner failed: ${error.message}`, 'error');
    process.exit(1);
  }
}

// Handle errors
process.on('unhandledRejection', (error) => {
  log(`Unhandled rejection: ${error.message}`, 'error');
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  log(`Uncaught exception: ${error.message}`, 'error');
  process.exit(1);
});

// Run if called directly
if (process.argv[1] && process.argv[1].endsWith('test-runner.js')) {
  main();
}

export { runTest, runAllTests, TESTS };
