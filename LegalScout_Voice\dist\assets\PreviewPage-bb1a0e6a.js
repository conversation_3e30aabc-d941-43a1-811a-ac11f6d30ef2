import{p as Lt,b as Ft,r,j as c,M as zt,c as At,D as jt,d as Mt,f as Ut}from"./index-dd4c5999.js";import{m as X}from"./framer-motion-ef6d06c6.js";const W=["ariaDescribedBy","ariaLabel","ariaLabelledBy"],ke={ancestors:{tbody:["table"],td:["table"],th:["table"],thead:["table"],tfoot:["table"],tr:["table"]},attributes:{a:[...W,"dataFootnoteBackref","dataFootnoteRef",["className","data-footnote-backref"],"href"],blockquote:["cite"],code:[["className",/^language-./]],del:["cite"],div:["itemScope","itemType"],dl:[...W],h2:[["className","sr-only"]],img:[...W,"longDesc","src"],input:[["disabled",!0],["type","checkbox"]],ins:["cite"],li:[["className","task-list-item"]],ol:[...W,["className","contains-task-list"]],q:["cite"],section:["dataFootnotes",["className","footnotes"]],source:["srcSet"],summary:[...W],table:[...W],ul:[...W,["className","contains-task-list"]],"*":["abbr","accept","acceptCharset","accessKey","action","align","alt","axis","border","cellPadding","cellSpacing","char","charOff","charSet","checked","clear","colSpan","color","cols","compact","coords","dateTime","dir","encType","frame","hSpace","headers","height","hrefLang","htmlFor","id","isMap","itemProp","label","lang","maxLength","media","method","multiple","name","noHref","noShade","noWrap","open","prompt","readOnly","rev","rowSpan","rows","rules","scope","selected","shape","size","span","start","summary","tabIndex","title","useMap","vAlign","value","width"]},clobber:["ariaDescribedBy","ariaLabelledBy","id","name"],clobberPrefix:"user-content-",protocols:{cite:["http","https"],href:["http","https","irc","ircs","mailto","xmpp"],longDesc:["http","https"],src:["http","https"]},required:{input:{disabled:!0,type:"checkbox"}},strip:["script"],tagNames:["a","b","blockquote","br","code","dd","del","details","div","dl","dt","em","h1","h2","h3","h4","h5","h6","hr","i","img","input","ins","kbd","li","ol","p","picture","pre","q","rp","rt","ruby","s","samp","section","source","span","strike","strong","sub","summary","sup","table","tbody","td","tfoot","th","thead","tr","tt","ul","var"]},_={}.hasOwnProperty;function Bt(e,n){let s={type:"root",children:[]};const o={schema:n?{...ke,...n}:ke,stack:[]},l=Fe(o,e);return l&&(Array.isArray(l)?l.length===1?s=l[0]:s.children=l:s=l),s}function Fe(e,n){if(n&&typeof n=="object"){const s=n;switch(typeof s.type=="string"?s.type:""){case"comment":return Rt(e,s);case"doctype":return _t(e,s);case"element":return Vt(e,s);case"root":return Ot(e,s);case"text":return Ht(e,s)}}}function Rt(e,n){if(e.schema.allowComments){const s=typeof n.value=="string"?n.value:"",o=s.indexOf("-->"),u={type:"comment",value:o<0?s:s.slice(0,o)};return Q(u,n),u}}function _t(e,n){if(e.schema.allowDoctypes){const s={type:"doctype"};return Q(s,n),s}}function Vt(e,n){const s=typeof n.tagName=="string"?n.tagName:"";e.stack.push(s);const o=ze(e,n.children),l=Wt(e,n.properties);e.stack.pop();let u=!1;if(s&&s!=="*"&&(!e.schema.tagNames||e.schema.tagNames.includes(s))&&(u=!0,e.schema.ancestors&&_.call(e.schema.ancestors,s))){const k=e.schema.ancestors[s];let D=-1;for(u=!1;++D<k.length;)e.stack.includes(k[D])&&(u=!0)}if(!u)return e.schema.strip&&!e.schema.strip.includes(s)?o:void 0;const h={type:"element",tagName:s,properties:l,children:o};return Q(h,n),h}function Ot(e,n){const o={type:"root",children:ze(e,n.children)};return Q(o,n),o}function Ht(e,n){const o={type:"text",value:typeof n.value=="string"?n.value:""};return Q(o,n),o}function ze(e,n){const s=[];if(Array.isArray(n)){const o=n;let l=-1;for(;++l<o.length;){const u=Fe(e,o[l]);u&&(Array.isArray(u)?s.push(...u):s.push(u))}}return s}function Wt(e,n){const s=e.stack[e.stack.length-1],o=e.schema.attributes,l=e.schema.required,u=o&&_.call(o,s)?o[s]:void 0,h=o&&_.call(o,"*")?o["*"]:void 0,k=n&&typeof n=="object"?n:{},D={};let b;for(b in k)if(_.call(k,b)){const P=k[b];let B=De(e,Le(u,b),b,P);B==null&&(B=De(e,Le(h,b),b,P)),B!=null&&(D[b]=B)}if(l&&_.call(l,s)){const P=l[s];for(b in P)_.call(P,b)&&!_.call(D,b)&&(D[b]=P[b])}return D}function De(e,n,s,o){return n?Array.isArray(o)?$t(e,n,s,o):Ae(e,n,s,o):void 0}function $t(e,n,s,o){let l=-1;const u=[];for(;++l<o.length;){const h=Ae(e,n,s,o[l]);(typeof h=="number"||typeof h=="string")&&u.push(h)}return u}function Ae(e,n,s,o){if(!(typeof o!="boolean"&&typeof o!="number"&&typeof o!="string")&&qt(e,s,o)){if(typeof n=="object"&&n.length>1){let l=!1,u=0;for(;++u<n.length;){const h=n[u];if(h&&typeof h=="object"&&"flags"in h){if(h.test(String(o))){l=!0;break}}else if(h===o){l=!0;break}}if(!l)return}return e.schema.clobber&&e.schema.clobberPrefix&&e.schema.clobber.includes(s)?e.schema.clobberPrefix+o:o}}function qt(e,n,s){const o=e.schema.protocols&&_.call(e.schema.protocols,n)?e.schema.protocols[n]:void 0;if(!o||o.length===0)return!0;const l=String(s),u=l.indexOf(":"),h=l.indexOf("?"),k=l.indexOf("#"),D=l.indexOf("/");if(u<0||D>-1&&u>D||h>-1&&u>h||k>-1&&u>k)return!0;let b=-1;for(;++b<o.length;){const P=o[b];if(u===P.length&&l.slice(0,P.length)===P)return!0}return!1}function Q(e,n){const s=Lt(n);n.data&&(e.data=Ft(n.data)),s&&(e.position=s)}function Le(e,n){let s,o=-1;if(e)for(;++o<e.length;){const l=e[o],u=typeof l=="string"?l:l[0];if(u===n)return l;u==="data*"&&(s=l)}if(n.length>4&&n.slice(0,4).toLowerCase()==="data")return s}function Gt(e){return function(n){return Bt(n,e)}}const be=(e,n={})=>{if(console.log(`[iframeUtils] Sending message to parent: ${e}`),window.parent)try{window.parent.postMessage({type:e,...n},"*")}catch(s){console.error("[iframeUtils] Error sending message to parent:",s)}},Yt=(e,n)=>{console.log(`[iframeUtils] Subscribing to parent messages: ${e}`);const s=o=>{o.data&&o.data.type===e&&n(o.data)};return window.addEventListener("message",s),()=>{window.removeEventListener("message",s)}},Jt=(e=!1)=>{console.log("[iframeUtils] Setting up iframe ping/pong communication"),be("IFRAME_READY",{timestamp:Date.now(),isProduction:e});const n=setInterval(()=>{be("IFRAME_PING",{timestamp:Date.now(),visible:document.visibilityState==="visible",isProduction:e})},2e3),s=Yt("IFRAME_PONG",l=>{console.log("[iframeUtils] Received pong from parent",l),l.checkVisibility&&e&&(console.log("[iframeUtils] Parent requested visibility check, forcing visibility"),o())}),o=()=>{const l=document.createElement("style");l.textContent=`
      .preview-container, #preview-content, .content-container,
      .welcome-title, .button-container, .consultation-button-direct {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        z-index: 100 !important;
      }
    `,document.head.appendChild(l),setTimeout(()=>{try{document.head.removeChild(l)}catch{}},1e3)};return e&&o(),()=>{clearInterval(n),s()}},Xt=({firmName:e="Your Law Firm",attorneyName:n="Your Name",practiceAreas:s=[],primaryColor:o="#4B74AA",secondaryColor:l="#2C3E50",buttonColor:u="#3498db",logoUrl:h,welcomeMessage:k="Hello! I'm Scout, your legal assistant. How can I help you today?",informationGathering:D="Tell me about your situation, and I'll help find the right solution for you.",state:b="",backgroundOpacity:P=.9,backgroundColor:B="#1a1a1a",practiceDescription:se="Your AI legal assistant is ready to help",firmNameAnimation:ae,buttonText:le,buttonOpacity:ce=1,practiceAreaBackgroundOpacity:Z=.1,textBackgroundColor:de="#634C38",theme:E="dark"})=>{const me=new URLSearchParams(window.location.search),[Y,ee]=r.useState(!0),[ue,pe]=r.useState("chat"),[L,v]=r.useState([{sender:"bot",text:k}]),[z,V]=r.useState(""),[x,$]=r.useState(!0),[A,te]=r.useState(0),[J,q]=r.useState(()=>typeof window<"u"?window.matchMedia("(prefers-color-scheme: dark)").matches:!1),f=E==="light",[d,N]=r.useState(!0),[j,O]=r.useState(!1),[F,je]=r.useState(e),[Kt,Me]=r.useState(n),[Qt,Ue]=r.useState(s),[Zt,Be]=r.useState(b),[we,Re]=r.useState(se),[oe,_e]=r.useState(k),[Ve,Oe]=r.useState(D),[ne,He]=r.useState(h),[H,We]=r.useState(ae),[fe,$e]=r.useState(P),[ve,qe]=r.useState(o),[Ge,Ye]=r.useState(l),[ge,Je]=r.useState(B),[Xe,Ke]=r.useState(ce),[ye,Qe]=r.useState(le||"Start Consultation"),[Ze,et]=r.useState(Z),[tt,ot]=r.useState(de),xe={fadeIn:{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,ease:"easeOut",delay:.5}},slideIn:{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8,type:"spring",stiffness:50,damping:10,delay:.5}},scaleIn:{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.8,type:"spring",stiffness:100,damping:10,delay:.5}},bounceIn:{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},transition:{duration:.8,type:"spring",stiffness:100,damping:8,delay:.5}}},Ne="Welcome to the",Se="LegalScout",Ce="of:",[nt,it]=r.useState(""),[rt,st]=r.useState(""),[at,lt]=r.useState(""),[ct,Ee]=r.useState(!1),[Ie,dt]=r.useState(!1),[ie,mt]=r.useState(!1);r.useRef(null),r.useRef(null),r.useRef(null),r.useRef(null);const ut=r.useRef(null);r.useEffect(()=>{let t=0,a=1,i;return(()=>{i=setInterval(()=>{a===1?t<=Ne.length?(it(Ne.slice(0,t)),t++):(a=2,t=0):a===2&&(t<=Se.length?(st(Se.slice(0,t)),t++):(clearInterval(i),Ee(!0)))},150)})(),()=>clearInterval(i)},[]),r.useEffect(()=>{if(Ie){let t=0;const a=setInterval(()=>{t<=Ce.length?(lt(Ce.slice(0,t)),t++):clearInterval(a)},150);return()=>clearInterval(a)}},[Ie]),r.useEffect(()=>{ie&&setTimeout(()=>{Ee(!0)},500)},[ie]),r.useEffect(()=>{const t=document.documentElement.getAttribute("data-theme");if(t)q(t==="dark");else if(E)q(E!=="light");else{const a=window.matchMedia("(prefers-color-scheme: dark)");q(a.matches);const i=y=>{q(y.matches)};return a.addEventListener("change",i),()=>a.removeEventListener("change",i)}},[E]),r.useEffect(()=>{O(!0),console.log("[PreviewInterface] Component mounted"),N(!0),g("Ensuring start button is shown");let t;return x&&(t=setInterval(()=>{te(a=>{const i=a+(100-a)*.1;return i>=99?(console.log("[PreviewInterface] Loading complete, setting isLoading to false"),$(!1),N(!0),100):i})},100)),()=>{t&&(console.log("[PreviewInterface] Clearing loading interval on unmount"),clearInterval(t))}},[]),r.useEffect(()=>{if(!x&&A>=100){console.log("[PreviewInterface] Loading completed, initializing chat interface");const t=setTimeout(()=>{v([{sender:"bot",text:oe}]),N(!0),console.log("[PreviewInterface] Content visibility ensured"),g("Button visibility enforced after loading")},300);return()=>clearTimeout(t)}},[x,A,oe]),r.useEffect(()=>{const t=()=>{document.visibilityState==="visible"&&!x&&(console.log("[PreviewInterface] Document became visible, ensuring content is shown"),v(i=>[...i]))};document.addEventListener("visibilitychange",t);const a=setTimeout(()=>{!x&&A>=100&&(console.log("[PreviewInterface] Backup visibility check passed"),N(i=>i))},1e3);return()=>{document.removeEventListener("visibilitychange",t),clearTimeout(a)}},[x,A]),r.useEffect(()=>{const t=a=>{if(console.log("PreviewInterface received message:",a.data?.type),a.data&&a.data.type==="updateCustomizations"){const{customizations:i}=a.data;i.firmName!==void 0&&je(i.firmName),i.attorneyName!==void 0&&Me(i.attorneyName),i.practiceAreas!==void 0&&Ue(i.practiceAreas),i.state!==void 0&&Be(i.state),i.practiceDescription!==void 0&&Re(i.practiceDescription),i.welcomeMessage!==void 0&&_e(i.welcomeMessage),i.informationGathering!==void 0&&Oe(i.informationGathering),i.logoUrl!==void 0&&He(i.logoUrl),i.primaryColor!==void 0&&qe(i.primaryColor),i.secondaryColor!==void 0&&Ye(i.secondaryColor),i.backgroundColor!==void 0&&Je(i.backgroundColor),i.backgroundOpacity!==void 0&&(console.log("Setting background opacity:",i.backgroundOpacity),$e(Number(i.backgroundOpacity))),i.firmNameAnimation!==void 0&&We(i.firmNameAnimation),i.buttonOpacity!==void 0&&Ke(i.buttonOpacity),i.buttonText!==void 0&&Qe(i.buttonText),i.practiceAreaBackgroundOpacity!==void 0&&et(i.practiceAreaBackgroundOpacity),i.textBackgroundColor&&ot(i.textBackgroundColor)}};if(window.addEventListener("message",t),window.parent&&window!==window.parent)try{window.parent.postMessage({type:"previewReady"},"*")}catch(a){console.warn("Failed to send ready message to parent",a)}return()=>window.removeEventListener("message",t)},[]),r.useEffect(()=>{if(x&&A>90&&$(!1),!(new URLSearchParams(window.location.search).get("responsive")==="true")&&!j)return;const i=()=>{if(window.parent&&window!==window.parent)try{const T=document.documentElement.scrollHeight;window.parent.postMessage({type:"iframeHeight",height:T,source:"previewInterface"},"*")}catch(T){console.warn("Failed to send height to parent",T)}};i();const y=[100,500,1e3,2e3].map(T=>setTimeout(i,T));let S=null;const I=document.getElementById("preview-content");if(I&&window.ResizeObserver)S=new ResizeObserver(()=>i()),S.observe(I);else{const T=setInterval(i,2e3);return()=>clearInterval(T)}return()=>{y.forEach(clearTimeout),S&&I&&(S.unobserve(I),S.disconnect())}},[x,A,j]),r.useEffect(()=>{const a=new URLSearchParams(window.location.search).get("showStartButton");console.log("showStartButton param:",a),N(a!=="false"),setTimeout(()=>{console.log("Forcing button visibility"),N(!0),g("Button visibility enforced by timeout")},1e3)},[]);const Pe=()=>{z.trim()&&(v(t=>[...t,{sender:"user",text:z}]),V(""),setTimeout(()=>{v(t=>[...t,{sender:"bot",text:`Thanks for your message. This is a demo of how the chat interface works for ${F}.`}])},1e3))},pt=()=>H&&(H==="fadeIn"||H==="slideIn"||H==="scaleIn"||H==="bounceIn")?xe[H]:xe.fadeIn,ft=t=>{const a=t.sender==="user";return{backgroundColor:J?"#2a2a2a":"#f8f8f8",color:J?"#ffffff":"#333333",borderRadius:"8px",padding:"12px 16px",marginBottom:"12px",maxWidth:"85%",wordBreak:"break-word",boxShadow:E==="light"?"0 1px 3px rgba(0,0,0,0.1)":"none",alignSelf:a?"flex-end":"flex-start",position:"relative"}},gt=()=>{(L.length>1||!d)&&(x&&$(!1),A<100&&te(100)),g("Rendering with state",{showWelcomeSection:Y,activeTab:ue,messagesCount:L.length,showStartButton:d,isLoading:x});const t=L.length>1&&!d;return g("Should show chat interface:",t),c.jsxDEV("div",{id:"preview-content",className:`preview-interface ${H} relative w-full min-h-screen`,style:{visibility:"visible",display:"flex",flexDirection:"column",opacity:1,zIndex:10,overflowY:"auto",overflowX:"hidden",height:"auto",minHeight:"100vh",width:"100%",maxWidth:me.get("maxWidth")==="true"?"100%":"720px",margin:"0 auto",boxSizing:"border-box",left:"0",right:"0",backgroundColor:`rgba(${K(ge)}, ${fe})`,color:E==="dark"?"#ffffff":"#333333",paddingBottom:L.length>1?"400px":"0"},ref:ut,children:x&&A<100&&!L.length?c.jsxDEV("div",{className:"loading-screen",children:c.jsxDEV("div",{className:"loading-animation",children:[c.jsxDEV("div",{className:"spinner"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:544,columnNumber:15},globalThis),c.jsxDEV("div",{className:"loading-text",children:"Loading preview..."},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:545,columnNumber:15},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:543,columnNumber:13},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:542,columnNumber:11},globalThis):c.jsxDEV(c.Fragment,{children:[bt(),c.jsxDEV("div",{style:{flex:1},children:[L.length===1&&c.jsxDEV(c.Fragment,{children:[Y&&Te(),d&&wt()]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:553,columnNumber:17},globalThis),L.length>1&&c.jsxDEV("div",{style:{paddingBottom:"400px"},children:Y&&Te()},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:559,columnNumber:17},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:551,columnNumber:13},globalThis),t&&vt()]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:549,columnNumber:11},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:516,columnNumber:7},globalThis)},g=(t,a)=>{console.log(`%c [PreviewInterface DEBUG] ${t}`,"background: #ff5722; color: white; padding: 2px 5px; border-radius: 3px;",a||"")},ht=async()=>{try{if(g("Button clicked - ACTIVATING CHAT INTERFACE"),ee(!1),pe("chat"),v([{sender:"bot",text:oe},{sender:"bot",text:Ve},{sender:"bot",text:"This is a demo version of the consultation interface. Type a message to continue."}]),N(!1),g("Changing UI state to chat mode",{welcomeSection:!1,activeTab:"chat",messageCount:3,startButton:!1}),window.parent&&window!==window.parent)try{g("Attempting to notify parent window (optional)"),window.parent.postMessage({type:"REQUEST_START_CONSULTATION",firmName:F,practiceDescription:we,assistantId:jt},"*")}catch(t){g("Error sending to parent, but continuing anyway",t)}setTimeout(()=>{g("Forcing UI refresh"),v(t=>[...t])},100)}catch(t){g("Error in simplified consultation start",t),v([{sender:"bot",text:oe},{sender:"bot",text:"I'm ready to help with your legal questions. What would you like to know?"}]),N(!1),ee(!1)}};r.useEffect(()=>{g("Component mounted");const t=window!==window.parent;if(g(`Is in iframe: ${t}`),t){g("Parent referrer:",document.referrer);const a=i=>{g("Test message response received",{type:i.data?.type,origin:i.origin})};return window.addEventListener("message",a),g("Sending test message to parent"),window.parent.postMessage({type:"PREVIEW_READY_TEST"},"*"),g("Sending test message using utility"),be("PREVIEW_READY_TEST",{timestamp:Date.now()}),()=>{window.removeEventListener("message",a)}}},[]),r.useEffect(()=>{g("Setting up iframe ping/pong communication");const t=Jt();return()=>{g("Cleaning up iframe ping/pong"),t()}},[]);const bt=()=>{const t=f?ge:"#1a1a1a";return c.jsxDEV("header",{className:"px-4 py-3 flex items-center border-b w-full",style:{borderColor:J?"rgba(255,255,255,0.1)":"rgba(0,0,0,0.1)",backgroundColor:`rgba(${K(t)}, ${fe})`},children:L.length>1&&c.jsxDEV("div",{className:"flex-shrink-0 mr-3",children:ne?c.jsxDEV("div",{style:{display:"flex",alignItems:"center",backgroundColor:"rgba(255,255,255,0.2)",borderRadius:"6px",padding:"4px",justifyContent:"center"},children:c.jsxDEV("img",{src:ne,alt:`${F} logo`,style:{height:"32px",width:"auto",maxWidth:"100%",objectFit:"contain",display:"block",visibility:"visible",opacity:1,zIndex:100},className:"object-contain"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:708,columnNumber:15},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:700,columnNumber:13},globalThis):c.jsxDEV("div",{style:{display:"flex",alignItems:"center",backgroundColor:"rgba(255,255,255,0.2)",borderRadius:"6px",padding:"4px",justifyContent:"center"},children:c.jsxDEV("img",{src:"/PRIMARY CLEAR.png",alt:"LegalScout",style:{height:"32px",width:"auto"},className:"object-contain"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:733,columnNumber:15},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:725,columnNumber:13},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:698,columnNumber:9},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:690,columnNumber:7},globalThis)};`${K(f?"#f5f5f5":ge)}${fe}`;const wt=()=>{const t=!!ne;return c.jsxDEV("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",width:"auto",maxWidth:"210px",margin:"0 auto"},className:"button-container",children:c.jsxDEV("button",{onClick:ht,id:"start-consultation-button",name:"start-consultation","aria-label":ye||"Start Consultation",className:"consultation-button-direct",style:{width:"210px",height:"210px",borderRadius:"50%",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",backgroundColor:`rgba(${K(Ge)}, ${Number(Xe||1)})`,color:"white",fontSize:"18px",fontWeight:"500",cursor:"pointer",transition:"all 0.2s ease",boxShadow:"0 4px 12px rgba(0,0,0,0.1)",border:"none",marginBottom:"16px",position:"relative",overflow:"visible",margin:"0 auto"},children:c.jsxDEV("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:"100%",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},children:[t?c.jsxDEV("img",{src:ne,alt:`${F} logo`,style:{maxHeight:"120px",maxWidth:"140px",objectFit:"contain",display:"block",margin:"0 auto 12px"},className:"logo-image"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:809,columnNumber:15},globalThis):c.jsxDEV("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",margin:"0 auto 12px"},children:c.jsxDEV("svg",{width:"100",height:"100",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:c.jsxDEV("path",{d:"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:829,columnNumber:19},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:828,columnNumber:17},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:822,columnNumber:15},globalThis),c.jsxDEV("span",{style:{textAlign:"center",width:"100%",padding:"0 10px"},children:ye||"Start Consultation"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:838,columnNumber:13},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:797,columnNumber:11},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:769,columnNumber:9},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:758,columnNumber:7},globalThis)},vt=()=>(g("Rendering chat interface"),c.jsxDEV("div",{id:"chat-interface-container",style:{display:"flex",flexDirection:"column",position:"fixed",bottom:0,left:"50%",transform:"translateX(-50%)",width:"100%",maxWidth:"800px",padding:"16px",backgroundColor:f?"rgba(255,255,255,0.9)":"rgba(0,0,0,0.2)",borderTopLeftRadius:"12px",borderTopRightRadius:"12px",boxShadow:f?"0 -4px 6px rgba(0,0,0,0.1)":"0 -4px 6px rgba(0,0,0,0.3)",zIndex:100},children:[c.jsxDEV("div",{id:"messages-container",style:{maxHeight:"300px",overflowY:"auto",padding:"8px",display:"flex",flexDirection:"column",marginBottom:"16px"},children:Nt()},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:875,columnNumber:9},globalThis),c.jsxDEV("div",{id:"input-container",style:{display:"flex",position:"relative",padding:"8px"},children:[c.jsxDEV("label",{htmlFor:"chat-message-input",className:"sr-only",children:"Type your message"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:897,columnNumber:11},globalThis),c.jsxDEV("input",{id:"chat-message-input",name:"message",type:"text",value:z,onChange:t=>V(t.target.value),onKeyPress:t=>{t.key==="Enter"&&Pe()},placeholder:"Type your message here...","aria-label":"Message input",style:{flex:1,padding:"12px 16px",borderRadius:"24px",border:`1px solid ${It}`,backgroundColor:Ct,color:Et,outline:"none",fontSize:"16px",boxShadow:f?"0 1px 3px rgba(0,0,0,0.1)":"none"}},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:898,columnNumber:11},globalThis),c.jsxDEV("button",{type:"button",id:"send-message-button",name:"send-button",onClick:Pe,disabled:!z.trim(),className:"send-button p-2 rounded-full flex items-center justify-center","aria-label":"Send message",style:{marginLeft:"8px",backgroundColor:ve,color:"white",border:"none",borderRadius:"50%",width:"40px",height:"40px",display:"flex",alignItems:"center",justifyContent:"center",cursor:z.trim()?"pointer":"not-allowed",opacity:z.trim()?1:.5},children:c.jsxDEV("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:c.jsxDEV("path",{d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:947,columnNumber:15},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:946,columnNumber:13},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:923,columnNumber:5},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:889,columnNumber:9},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:856,columnNumber:7},globalThis));r.useEffect(()=>{if(new URLSearchParams(window.location.search).get("fullWidth")==="true"){const i=document.getElementById("preview-content");if(i){i.style.maxWidth="100%",i.style.width="100%",i.style.margin="0 auto";const y=document.querySelector(".welcome-title");y&&(y.style.maxWidth="100%",y.style.width="100%",y.style.margin="0 auto",y.style.padding="0 16px")}}},[j]),r.useEffect(()=>{if(typeof window<"u"){const t=JSON.parse;return window.JSON.parse=a=>{try{return t(a)}catch(i){return typeof a=="string"&&a.trim().startsWith("<!DOCTYPE")?(console.warn("Received HTML instead of JSON, returning empty object"),{}):(console.warn("JSON parse error:",i),{})}},()=>{window.JSON.parse=t}}},[]),r.useEffect(()=>{console.log("[PreviewInterface] Initializing component with props:",{firmName:e,attorneyName:n,practiceAreas:s,primaryColor:o,secondaryColor:l,logoUrl:h,theme:E,backgroundOpacity:P});const t=window.location.hostname!=="localhost"&&window.location.hostname!=="127.0.0.1";console.log("[PreviewInterface] Environment:",t?"Production":"Development");const a=()=>{const p=document.getElementById("preview-content");console.log("[PreviewInterface] Content element exists:",!!p),p&&console.log("[PreviewInterface] Content element styles:",{display:window.getComputedStyle(p).display,visibility:window.getComputedStyle(p).visibility,opacity:window.getComputedStyle(p).opacity,backgroundColor:window.getComputedStyle(p).backgroundColor}),["welcome-title","button-container","consultation-button-direct"].forEach(m=>{const C=document.querySelector(`.${m}`);console.log(`[PreviewInterface] ${m} exists:`,!!C),C&&console.log(`[PreviewInterface] ${m} styles:`,{display:window.getComputedStyle(C).display,visibility:window.getComputedStyle(C).visibility,opacity:window.getComputedStyle(C).opacity})})},i=new MutationObserver(p=>{let m=!1;p.forEach(C=>{if(C.type==="attributes"&&C.attributeName==="style"){const U=C.target,Tt=U.style.display,kt=U.style.visibility,Dt=U.style.opacity;(U.style.display!==Tt||U.style.visibility!==kt||parseFloat(U.style.opacity)<.5&&Dt!==U.style.opacity)&&(console.log("[PreviewInterface] Significant style mutation detected:",{element:C.target,display:U.style.display,visibility:U.style.visibility,opacity:U.style.opacity}),m=!0)}}),m&&a()}),y=new MutationObserver(p=>{const m=p.filter(C=>C.type==="childList"&&C.removedNodes.length>0&&Array.from(C.removedNodes).some(U=>U.nodeType===Node.ELEMENT_NODE));m.length>0&&console.log("[PreviewInterface] DOM elements removed:",{count:m.length,mutations:m.map(C=>({target:C.target,removedNodes:C.removedNodes.length}))})}),S=document.getElementById("preview-content");S&&(i.observe(S,{attributes:!0,attributeFilter:["style"],subtree:!0}),y.observe(S,{childList:!0,subtree:!0})),a();const I=p=>(console.error("[PreviewInterface] Caught error:",{message:p.message,filename:p.filename,lineno:p.lineno,colno:p.colno,error:p.error}),p.preventDefault(),!0),T=p=>{console.error("[PreviewInterface] Unhandled rejection:",{reason:p.reason,promise:p.promise}),p.preventDefault()},w=p=>{const m=p.target;(m instanceof HTMLImageElement||m instanceof HTMLScriptElement||m instanceof HTMLLinkElement)&&console.log("[PreviewInterface] Resource loaded:",{type:m.tagName,src:m instanceof HTMLImageElement||m instanceof HTMLScriptElement?m.src:m instanceof HTMLLinkElement?m.href:null,success:!m.hasAttribute("data-error")})},R=p=>{const m=p.target;(m instanceof HTMLImageElement||m instanceof HTMLScriptElement||m instanceof HTMLLinkElement)&&(console.error("[PreviewInterface] Resource failed to load:",{type:m.tagName,src:m instanceof HTMLImageElement||m instanceof HTMLScriptElement?m.src:m instanceof HTMLLinkElement?m.href:null,error:p}),m.setAttribute("data-error","true"))};window.addEventListener("error",I),window.addEventListener("unhandledrejection",T),window.addEventListener("load",w,!0),window.addEventListener("error",R,!0);const M=setInterval(()=>{console.log("[PreviewInterface] Current state:",{isLoading:x,loadingProgress:A,isMounted:j,showStartButton:d,messages:L.length}),a()},5e3),G=p=>{console.log("[PreviewInterface] Received message:",{type:p.data?.type,origin:p.origin,source:p.source===window.parent?"parent":"other"})};return window.addEventListener("message",G),()=>{window.removeEventListener("error",I),window.removeEventListener("unhandledrejection",T),window.removeEventListener("load",w,!0),window.removeEventListener("error",R,!0),window.removeEventListener("message",G),i.disconnect(),y.disconnect(),clearInterval(M),console.log("[PreviewInterface] Component cleanup completed")}},[x,A,j,d,L.length]);const[to,yt]=r.useState(!0);r.useEffect(()=>{const t=setTimeout(()=>{yt(x)},300);return()=>clearTimeout(t)},[x]),r.useEffect(()=>{const t=y=>{y.forEach(S=>{if(S.type==="childList"&&S.removedNodes.length>0){const I=S.target;(I.id==="preview-content"||I.classList.contains("preview-interface")||I.classList.contains("content-container"))&&(console.warn("[PreviewInterface] Prevented removal of critical element:",I),S.removedNodes.forEach(T=>{document.body.contains(T)||I.appendChild(T)}))}})},a=new MutationObserver(t),i=document.getElementById("preview-content");return i&&a.observe(i,{childList:!0,subtree:!0}),()=>a.disconnect()},[]);const[re,xt]=r.useState(0),he=r.useCallback(t=>{const a=Date.now();a-re<300||t.data?.type==="updateCustomizations"&&xt(a)},[re]);r.useEffect(()=>(window.addEventListener("message",he),()=>window.removeEventListener("message",he)),[he]),r.useEffect(()=>{if(window.location.hostname!=="localhost"&&window.location.hostname!=="127.0.0.1"){console.log("[PreviewInterface] Applying production-specific protections");const a=()=>{const w=document.getElementById("preview-content");w&&(w.style.display="block",w.style.visibility="visible",w.style.opacity="1",["welcome-title","button-container","consultation-button-direct"].forEach(R=>{const M=w.querySelector(`.${R}`);M instanceof HTMLElement&&(M.style.display="block",M.style.visibility="visible",M.style.opacity="1")}))},i=[100,500,1e3,2e3,5e3].map(w=>setTimeout(a,w)),y=new MutationObserver(w=>{w.forEach(R=>{if(R.type==="childList"&&R.removedNodes.length>0){const M=R.target;(M.id==="preview-content"||M.classList.contains("preview-interface")||M.classList.contains("content-container"))&&(console.warn("[PreviewInterface] Preventing removal of essential element in production"),R.removedNodes.forEach(G=>{if(!document.body.contains(G))try{M.appendChild(G),console.log("[PreviewInterface] Restored removed node:",G)}catch(p){console.error("[PreviewInterface] Failed to restore node:",p)}}))}})}),S=document.getElementById("preview-content");S&&y.observe(S,{childList:!0,subtree:!0});const I=w=>{if(![window.location.origin,"https://legalscout.net","https://www.legalscout.net"].includes(w.origin)){console.warn("[PreviewInterface] Blocked message from unauthorized origin:",w.origin);return}if(w.data?.type==="updateCustomizations"){if(Date.now()-re<300)return;try{requestAnimationFrame(()=>{a()})}catch(M){console.error("[PreviewInterface] Error handling production message:",M)}}};window.addEventListener("message",I);const T=setInterval(()=>{const w=document.getElementById("preview-content");(!w||!document.body.contains(w))&&(console.warn("[PreviewInterface] Content missing, attempting recovery"),a())},1e3);return()=>{i.forEach(clearTimeout),y.disconnect(),window.removeEventListener("message",I),clearInterval(T)}}},[re]),r.useEffect(()=>{const t=document.createElement("style");return t.textContent=`
      .logo-image {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        pointer-events: auto !important;
      }

      /* Accessibility helper class */
      .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border-width: 0;
      }
    `,document.head.appendChild(t),()=>{document.head.removeChild(t)}},[]);const Nt=()=>(g(`Rendering ${L.length} messages`),L.map((t,a)=>{const i=ft(t);return c.jsxDEV("div",{style:{display:"flex",justifyContent:t.sender==="user"?"flex-end":"flex-start",width:"100%",marginBottom:"16px"},children:c.jsxDEV("div",{style:i,children:t.text},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:1456,columnNumber:11},globalThis)},a,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:1447,columnNumber:9},globalThis)})),St={fontFamily:"'Courier New', Courier, monospace",fontSize:"24px",color:E==="light"?"#333333":"rgba(255,255,255,0.9)",whiteSpace:"pre-wrap",minHeight:"36px",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px",position:"relative"},Ct=E==="light"?"#ffffff":"rgba(255,255,255,0.1)",Et=E==="light"?"#333333":"rgba(255,255,255,0.9)",It=E==="light"?"#e0e0e0":"rgba(255,255,255,0.2)",Pt={fontSize:"16px",opacity:.9,marginBottom:"24px",maxWidth:"600px",margin:"0 auto",backgroundColor:E==="light"?"rgba(255,255,255,0.9)":`rgba(${K(tt)}, ${Ze})`,padding:"20px",borderRadius:"8px",color:E==="light"?"#333333":"rgba(255,255,255,0.95)",boxShadow:E==="light"?"0 4px 6px rgba(0, 0, 0, 0.1)":"0 4px 12px rgba(0, 0, 0, 0.3)"},Te=()=>{const t=pt(),a={fontSize:"64px",fontFamily:'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',fontWeight:"700",marginBottom:"20px",color:ve,lineHeight:"1.1",letterSpacing:"-0.02em",textAlign:"center",maxWidth:"100%",width:"100%",margin:"0 auto 20px",overflow:"hidden",wordWrap:"break-word"};return c.jsxDEV("div",{style:{textAlign:"center",marginBottom:"20px",width:"100%",maxWidth:"100%",padding:"20px",position:"relative",top:0,left:"50%",transform:"translateX(-50%)",overflow:"hidden"},children:[c.jsxDEV("div",{style:{marginBottom:"16px",position:"relative"},children:c.jsxDEV(X.div,{initial:{opacity:0},animate:{opacity:1},style:St,children:[c.jsxDEV("span",{children:nt},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:1539,columnNumber:13},globalThis)," ",c.jsxDEV("span",{style:{display:"inline-flex",alignItems:"center",position:"relative",marginRight:"48px"},children:[rt,ct&&c.jsxDEV(X.img,{src:"/PRIMARY CLEAR.png",alt:"LegalScout Mascot",initial:{scale:.2,opacity:0,x:150,y:-50},animate:{scale:1,opacity:1,x:0,y:"-50%"},transition:{type:"spring",stiffness:120,damping:20,duration:1.5,mass:1.2},onAnimationComplete:()=>dt(!0),style:{width:"48px",height:"48px",position:"absolute",left:"100%",top:"50%",marginLeft:"8px",transform:"translateY(-50%)",display:"block",zIndex:2}},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:1548,columnNumber:17},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:1540,columnNumber:13},globalThis),c.jsxDEV("span",{children:at},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:1585,columnNumber:13},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:1534,columnNumber:9},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:1533,columnNumber:9},globalThis),c.jsxDEV(X.div,{style:{display:"flex",alignItems:"center",justifyContent:"center"},children:c.jsxDEV(X.h2,{className:"welcome-title",initial:t?.initial,animate:t?.animate,transition:t?.transition,onAnimationComplete:()=>mt(!0),style:a,children:F||"Your AI Legal Assistant"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:1592,columnNumber:11},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:1589,columnNumber:9},globalThis),c.jsxDEV(X.div,{style:Pt,initial:{opacity:0,y:20},animate:{opacity:ie?.9:0,y:ie?0:20},transition:{delay:.3,duration:.8,ease:"easeOut"},children:c.jsxDEV(zt,{remarkPlugins:[At],rehypePlugins:[Gt],children:we},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:1617,columnNumber:11},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:1604,columnNumber:17},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/preview/PreviewInterface.tsx",lineNumber:1521,columnNumber:7},globalThis)};return r.useEffect(()=>{g(`showStartButton state changed to: ${d}`),setTimeout(()=>{const t=document.querySelector(".button-container"),a=document.querySelector(".consultation-button-direct");g("Button elements after state change:",{buttonContainerExists:!!t,consultationButtonExists:!!a})},100)},[d]),gt()};function K(e){try{if(!e||(e=e.replace(/^#/,""),!/^[0-9A-Fa-f]{6}$/.test(e)))return"0, 0, 0";const n=parseInt(e,16),s=n>>16&255,o=n>>8&255,l=n&255;return`${s}, ${o}, ${l}`}catch(n){return console.error("Error converting hex to RGB:",n),"0, 0, 0"}}const io=()=>{const e={firmName:"Smith & Associates",logoUrl:"",backgroundColor:"#f5f5f5",backgroundOpacity:.8,primaryColor:"#4B74AA",secondaryColor:"#607D8B",buttonColor:"#3498db",welcomeMessage:"Welcome to our virtual legal assistant.",informationGathering:"To better assist you, I'll need a few details about your situation.",practiceDescription:"We specialize in providing expert legal services across various practice areas.",practiceAreas:["Personal Injury","Family Law","Criminal Defense"],state:"California",attorneyName:"John Smith",firmNameAnimation:"fadeIn",practiceAreaBackgroundOpacity:.1,textBackgroundColor:"#634C38",buttonText:"Start Consultation",buttonOpacity:1},n=r.useRef(null),s=Mt(),o=new URLSearchParams(s.search),l=o.get("theme")||"light",u=o.get("firmName")||e.firmName,h=o.get("logoUrl")||e.logoUrl,k=Ut(h),D=o.get("backgroundColor")||e.backgroundColor,b=parseFloat(o.get("backgroundOpacity")||"0.8"),P=o.get("primaryColor")||e.primaryColor,B=o.get("secondaryColor")||e.secondaryColor,se=o.get("buttonColor")||e.buttonColor,ae=o.get("welcomeMessage")||e.welcomeMessage,le=o.get("informationGathering")||e.informationGathering,ce=o.get("practiceDescription")||e.practiceDescription,Z=o.get("practiceAreas"),de=Z?Z.split(","):e.practiceAreas,E=o.get("state")||e.state,me=o.get("attorneyName")||e.attorneyName,Y=o.get("firmNameAnimation")||e.firmNameAnimation,ee=parseFloat(o.get("practiceAreaBackgroundOpacity")||"0.1"),ue=o.get("textBackgroundColor")||e.textBackgroundColor,pe=o.get("buttonText")||e.buttonText,L=parseFloat(o.get("buttonOpacity")||"1"),v=o.get("production")==="true"||window.location.hostname!=="localhost"&&window.location.hostname!=="127.0.0.1",z=o.get("centered")==="true",V=o.get("fullWidth")==="true",[x,$]=r.useState(!1),[A,te]=r.useState(null),[J,q]=r.useState(!1);return r.useEffect(()=>{const f=window.fetch;return window.fetch=function(d,N){return d&&typeof d=="string"&&(d.includes("subdomain")||d.includes("config"))?(console.log("[PreviewPage] Intercepting config request to prevent errors:",d),Promise.resolve({ok:!0,status:200,json:()=>Promise.resolve({}),text:()=>Promise.resolve("{}")})):f(d,N)},()=>{window.fetch=f}},[]),r.useEffect(()=>{console.log("[PreviewPage] Component mounted, production mode:",v),$(!0);const f=console.error;if(console.error=(...d)=>{te(d.join(" ")),f.apply(console,d)},v){const d=setTimeout(()=>{console.log("[PreviewPage] Forcing re-render in production"),q(!0)},1e3);return()=>{clearTimeout(d),console.log("[PreviewPage] Component unmounting"),console.error=f}}return()=>{console.log("[PreviewPage] Component unmounting"),console.error=f}},[v]),r.useEffect(()=>{$(!0);const f=()=>{const j=document.documentElement.scrollHeight;if(window.parent&&window!==window.parent)try{console.log("[PreviewPage] Reporting height:",j),window.parent.postMessage({type:"iframeHeight",height:j,source:"previewPage"},"*")}catch(O){console.warn("[PreviewPage] Error posting message to parent:",O)}};window.addEventListener("load",f),f();const d=[50,100,500,1e3,2e3].map(j=>setTimeout(f,j)),N=setInterval(f,1e3);return()=>{window.removeEventListener("load",f),d.forEach(j=>clearTimeout(j)),clearInterval(N)}},[]),r.useEffect(()=>{if(n.current){console.log("[PreviewPage] Setting up MutationObserver");const f=new MutationObserver(d=>{d.forEach(N=>{N.removedNodes.length>0&&console.log("[PreviewPage] Nodes were removed from the DOM",N.removedNodes)})});return f.observe(n.current,{childList:!0,subtree:!0}),()=>f.disconnect()}},[n.current]),r.useEffect(()=>{document.documentElement.setAttribute("data-theme",l)},[l]),r.useEffect(()=>{const f=document.createElement("style");if(f.textContent=`
      body, html {
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
        overflow-x: hidden !important;
        ${z?`
          display: flex !important;
          justify-content: center !important;
          align-items: flex-start !important;
        `:""}
        box-sizing: border-box !important;
      }

      .preview-container {
        min-height: 500px !important;
        visibility: visible !important;
        display: block !important;
        ${z||V?`
          max-width: 100% !important;
          width: 100% !important;
          margin: 0 auto !important;
          left: 0 !important;
          right: 0 !important;
          box-sizing: border-box !important;
        `:""}
      }

      #preview-content {
        min-height: 500px !important;
        visibility: visible !important;
        display: block !important;
        ${z||V?`
          max-width: 100% !important;
          width: 100% !important;
          margin: 0 auto !important;
          overflow-x: hidden !important;
          left: 0 !important;
          right: 0 !important;
          box-sizing: border-box !important;
        `:""}
      }

      /* Production-specific styles */
      ${v?`
        body {
          overflow: visible !important;
        }

        .welcome-title, .button-container, .consultation-button-direct, .content-container {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
          z-index: 100 !important;
          position: relative !important;
          ${z||V?`
            max-width: 100% !important;
            width: 100% !important;
            margin-left: auto !important;
            margin-right: auto !important;
            box-sizing: border-box !important;
            text-align: center !important;
          `:""}
        }

        .welcome-title {
          max-width: 100% !important;
          width: 100% !important;
          word-wrap: break-word !important;
          overflow-wrap: break-word !important;
          text-align: center !important;
          margin-left: auto !important;
          margin-right: auto !important;
        }

        img, svg {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
        }

        /* Hide error messages */
        [style*="background-color: red"], [style*="background-color: rgba(255, 0, 0"], [style*="background-color:#ff0000"] {
          display: none !important;
        }
      `:""}
    `,document.head.appendChild(f),v){const d=setInterval(()=>{[".preview-container","#preview-content",".content-container",".welcome-title",".button-container",".consultation-button-direct"].forEach(O=>{const F=document.querySelector(O);F&&(F.style.display="block",F.style.visibility="visible",F.style.opacity="1",z&&(F.style.maxWidth="100%",F.style.width="100%",F.style.margin="0 auto",F.style.overflowX="hidden"))}),document.querySelectorAll('[style*="background-color: red"], [style*="background-color: rgba(255, 0, 0"]').forEach(O=>{O.style.display="none"})},500);return()=>{clearInterval(d),document.head.removeChild(f)}}return()=>{document.head.removeChild(f)}},[v,z,V]),r.useEffect(()=>{if(!v)return;const f=setInterval(()=>{console.log("[PreviewPage] Running production visibility fix");const d=document.createElement("div");d.id="force-repaint",d.style.position="fixed",d.style.top="0",d.style.left="0",d.style.width="1px",d.style.height="1px",d.style.zIndex="0",document.body.appendChild(d),d.offsetHeight,document.body.removeChild(d)},2e3);return()=>clearInterval(f)},[v]),c.jsxDEV("div",{ref:n,className:"min-h-screen preview-container",style:{minHeight:"800px",visibility:"visible",display:"block",position:"relative",zIndex:1},"data-production":v?"true":"false","data-force-render":J?"true":"false",children:[A&&c.jsxDEV("div",{style:{position:"fixed",top:0,left:0,right:0,background:"red",color:"white",padding:"5px",zIndex:9999,fontSize:"12px"},children:["Error: ",A]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/PreviewPage.jsx",lineNumber:384,columnNumber:9},globalThis),(x||v)&&c.jsxDEV(Xt,{firmName:u,logoUrl:k,backgroundColor:D,backgroundOpacity:b,primaryColor:P,secondaryColor:B,buttonColor:se,welcomeMessage:ae,informationGathering:le,practiceDescription:ce,practiceAreas:de,state:E,attorneyName:me,firmNameAnimation:Y,practiceAreaBackgroundOpacity:ee,textBackgroundColor:ue,buttonText:pe,buttonOpacity:L,theme:l},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/PreviewPage.jsx",lineNumber:401,columnNumber:9},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/PreviewPage.jsx",lineNumber:369,columnNumber:5},globalThis)};export{io as default};
