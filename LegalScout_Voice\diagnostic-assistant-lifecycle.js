/**
 * Comprehensive Assistant ID Lifecycle Diagnostic
 * Run this in browser console to diagnose assistant data flow issues
 */

console.log('🔍 ASSISTANT ID LIFECYCLE DIAGNOSTIC');
console.log('=====================================');

async function runDiagnostic() {
  const results = {
    timestamp: new Date().toISOString(),
    issues: [],
    recommendations: []
  };

  // 1. Check Local Storage for Attorney Data
  console.log('\n📊 1. CHECKING LOCAL STORAGE');
  const attorneyData = localStorage.getItem('attorney');
  if (attorneyData) {
    try {
      const attorney = JSON.parse(attorneyData);
      console.log('✅ Attorney data found:', {
        id: attorney.id,
        firm_name: attorney.firm_name,
        vapi_assistant_id: attorney.vapi_assistant_id,
        current_assistant_id: attorney.current_assistant_id
      });
      
      // Check for UUID vs Vapi ID confusion
      if (attorney.vapi_assistant_id && attorney.vapi_assistant_id.length > 40) {
        results.issues.push('❌ vapi_assistant_id looks like a UUID, not a Vapi ID');
        results.recommendations.push('Check database for correct Vapi assistant ID mapping');
      }
    } catch (e) {
      results.issues.push('❌ Invalid attorney data in localStorage');
    }
  } else {
    results.issues.push('❌ No attorney data in localStorage');
  }

  // 2. Check Assistant Context
  console.log('\n🎯 2. CHECKING ASSISTANT CONTEXT');
  if (window.React && window.AssistantAwareContext) {
    console.log('✅ AssistantAwareContext available');
  } else {
    results.issues.push('❌ AssistantAwareContext not available');
  }

  // 3. Check for Mock Data
  console.log('\n🎭 3. CHECKING FOR MOCK DATA');
  const mockElements = document.querySelectorAll('*');
  let mockCount = 0;
  Array.from(mockElements).forEach(el => {
    if (el.textContent && el.textContent.includes('Mock Assistant')) {
      mockCount++;
    }
  });
  
  if (mockCount > 0) {
    results.issues.push(`❌ Found ${mockCount} elements with "Mock Assistant" text`);
    results.recommendations.push('Check Vapi MCP connection and API keys');
  }

  // 4. Check Network Requests
  console.log('\n🌐 4. MONITORING NETWORK REQUESTS');
  const originalFetch = window.fetch;
  let apiCalls = [];
  
  window.fetch = function(...args) {
    const url = args[0];
    if (typeof url === 'string' && (url.includes('vapi') || url.includes('assistant'))) {
      apiCalls.push({
        url: url,
        timestamp: new Date().toISOString()
      });
      console.log('📡 API Call:', url);
    }
    return originalFetch.apply(this, args);
  };

  // Restore fetch after 10 seconds
  setTimeout(() => {
    window.fetch = originalFetch;
    console.log('📊 API Calls Summary:', apiCalls);
  }, 10000);

  // 5. Check Assistant Dropdown State
  console.log('\n📋 5. CHECKING ASSISTANT DROPDOWN');
  const dropdowns = document.querySelectorAll('.enhanced-assistant-dropdown, .custom-dropdown');
  if (dropdowns.length > 0) {
    console.log(`✅ Found ${dropdowns.length} assistant dropdowns`);
    
    dropdowns.forEach((dropdown, index) => {
      const selectedText = dropdown.querySelector('.assistant-name, .selected-assistant');
      if (selectedText) {
        console.log(`Dropdown ${index} selected:`, selectedText.textContent);
        if (selectedText.textContent.includes('Mock') || selectedText.textContent.includes('Error')) {
          results.issues.push(`❌ Dropdown ${index} showing mock/error data`);
        }
      }
    });
  } else {
    results.issues.push('❌ No assistant dropdowns found');
  }

  // 6. Check Console Errors
  console.log('\n🚨 6. MONITORING CONSOLE ERRORS');
  const originalError = console.error;
  let errorCount = 0;
  
  console.error = function(...args) {
    const errorMsg = args.join(' ');
    if (errorMsg.includes('404') || errorMsg.includes('assistant') || errorMsg.includes('vapi')) {
      errorCount++;
      results.issues.push(`❌ Console Error: ${errorMsg.substring(0, 100)}`);
    }
    originalError.apply(console, args);
  };

  // Restore console.error after 10 seconds
  setTimeout(() => {
    console.error = originalError;
    console.log(`🚨 Found ${errorCount} relevant console errors`);
  }, 10000);

  // 7. Test Assistant ID Validation
  console.log('\n🔍 7. TESTING ASSISTANT ID VALIDATION');
  const testIds = [
    '50e13a9e-22dd-4fe8-a03e-de627c5206c1', // Valid Vapi ID
    '87756a2c-a398-43f2-889a-b8815684df71', // Looks like UUID
    'mock-12345',                            // Mock ID
    null,                                    // Null ID
    undefined                                // Undefined ID
  ];

  testIds.forEach(id => {
    const isValid = id && typeof id === 'string' && id.length <= 40 && !id.includes('mock');
    console.log(`ID: ${id} -> ${isValid ? '✅ Valid' : '❌ Invalid'}`);
    if (!isValid && id) {
      results.issues.push(`❌ Invalid assistant ID detected: ${id}`);
    }
  });

  // 8. Final Report
  setTimeout(() => {
    console.log('\n📋 DIAGNOSTIC REPORT');
    console.log('====================');
    console.log('Issues Found:', results.issues.length);
    results.issues.forEach(issue => console.log(issue));
    
    console.log('\nRecommendations:');
    results.recommendations.forEach(rec => console.log('💡', rec));
    
    if (results.issues.length === 0) {
      console.log('✅ No major issues detected!');
    } else {
      console.log('❌ Issues detected - see recommendations above');
    }
    
    // Store results globally for further inspection
    window.assistantDiagnosticResults = results;
    console.log('\n💾 Results saved to window.assistantDiagnosticResults');
  }, 12000);

  return results;
}

// Auto-run diagnostic
runDiagnostic();

// Make available globally
window.runAssistantDiagnostic = runDiagnostic;
