/**
 * Vapi SDK Loading Test
 * 
 * Tests and fixes Vapi SDK loading issues identified in the logs
 */

class VapiSDKLoadingTest {
  constructor() {
    this.attempts = [];
    this.errors = [];
    this.loadingStrategies = [
      'npm-package',
      'cdn-primary',
      'cdn-fallback',
      'local-fallback'
    ];
  }

  async runTest() {
    console.log('🚀 Vapi SDK Loading Test');
    console.log('========================');

    try {
      await this.checkCurrentVapiStatus();
      await this.testLoadingStrategies();
      await this.createVapiFallback();
      await this.validateVapiIntegration();
      
      this.generateReport();
    } catch (error) {
      console.error('❌ Vapi SDK loading test failed:', error);
      this.errors.push(`Test failure: ${error.message}`);
    }
  }

  async checkCurrentVapiStatus() {
    console.log('\n🔍 Checking current Vapi status...');
    
    try {
      if (typeof window.Vapi !== 'undefined') {
        console.log('✅ Vapi SDK already loaded');
        console.log('📦 Vapi object:', Object.keys(window.Vapi));
        this.attempts.push('Vapi already available');
        return true;
      } else {
        console.log('⚠️ Vapi SDK not currently loaded');
        return false;
      }
    } catch (error) {
      console.error('❌ Vapi status check failed:', error);
      this.errors.push(`Vapi status check: ${error.message}`);
      return false;
    }
  }

  async testLoadingStrategies() {
    console.log('\n🧪 Testing Vapi loading strategies...');
    
    for (const strategy of this.loadingStrategies) {
      try {
        console.log(`\n🔄 Trying strategy: ${strategy}`);
        const success = await this.tryLoadingStrategy(strategy);
        
        if (success) {
          console.log(`✅ Strategy ${strategy} succeeded`);
          this.attempts.push(`${strategy}: SUCCESS`);
          break;
        } else {
          console.log(`❌ Strategy ${strategy} failed`);
          this.attempts.push(`${strategy}: FAILED`);
        }
      } catch (error) {
        console.error(`❌ Strategy ${strategy} error:`, error);
        this.attempts.push(`${strategy}: ERROR - ${error.message}`);
      }
    }
  }

  async tryLoadingStrategy(strategy) {
    switch (strategy) {
      case 'npm-package':
        return await this.loadFromNPM();
      case 'cdn-primary':
        return await this.loadFromCDN('https://cdn.vapi.ai/web-sdk@2.2.2/dist/web-sdk.js');
      case 'cdn-fallback':
        return await this.loadFromCDN('https://unpkg.com/@vapi-ai/web@latest/dist/index.js');
      case 'local-fallback':
        return await this.createLocalFallback();
      default:
        return false;
    }
  }

  async loadFromNPM() {
    try {
      const vapiModule = await import('@vapi-ai/web');
      if (vapiModule && vapiModule.default) {
        window.Vapi = vapiModule.default;
        return true;
      }
      return false;
    } catch (error) {
      console.log('NPM import failed:', error.message);
      return false;
    }
  }

  async loadFromCDN(url) {
    return new Promise((resolve) => {
      const script = document.createElement('script');
      script.src = url;
      script.onload = () => {
        if (typeof window.Vapi !== 'undefined') {
          console.log(`✅ Vapi loaded from CDN: ${url}`);
          resolve(true);
        } else {
          console.log(`❌ Vapi not available after loading from: ${url}`);
          resolve(false);
        }
      };
      script.onerror = () => {
        console.log(`❌ Failed to load from CDN: ${url}`);
        resolve(false);
      };
      
      // Set timeout for loading
      setTimeout(() => {
        if (typeof window.Vapi === 'undefined') {
          console.log(`⏰ Timeout loading from: ${url}`);
          resolve(false);
        }
      }, 10000);
      
      document.head.appendChild(script);
    });
  }

  async createLocalFallback() {
    console.log('🔧 Creating local Vapi fallback...');
    
    try {
      // Create a minimal Vapi fallback
      window.Vapi = class VapiFallback {
        constructor(config) {
          this.config = config;
          this.isConnected = false;
          console.log('🔄 VapiFallback initialized with config:', config);
        }

        start() {
          console.log('🎤 VapiFallback: start() called');
          this.isConnected = true;
          return Promise.resolve();
        }

        stop() {
          console.log('🛑 VapiFallback: stop() called');
          this.isConnected = false;
          return Promise.resolve();
        }

        send(message) {
          console.log('📤 VapiFallback: send() called with:', message);
          return Promise.resolve();
        }

        on(event, callback) {
          console.log(`👂 VapiFallback: listening for event '${event}'`);
          // Store callbacks for potential future use
          if (!this.eventCallbacks) this.eventCallbacks = {};
          if (!this.eventCallbacks[event]) this.eventCallbacks[event] = [];
          this.eventCallbacks[event].push(callback);
        }

        off(event, callback) {
          console.log(`🔇 VapiFallback: removing listener for event '${event}'`);
          if (this.eventCallbacks && this.eventCallbacks[event]) {
            const index = this.eventCallbacks[event].indexOf(callback);
            if (index > -1) {
              this.eventCallbacks[event].splice(index, 1);
            }
          }
        }
      };

      console.log('✅ VapiFallback created successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to create VapiFallback:', error);
      return false;
    }
  }

  async createVapiFallback() {
    console.log('\n🛠️ Creating enhanced Vapi fallback utilities...');
    
    try {
      // Create Vapi configuration helper
      window.VapiConfigHelper = {
        getDefaultConfig: () => ({
          publicKey: window.VITE_VAPI_PUBLIC_KEY || '310f0d43-27c2-47a5-a76d-e55171d024f7',
          assistantId: null,
          transcriber: {
            provider: 'deepgram',
            model: 'nova-2',
            language: 'en-US'
          },
          voice: {
            provider: 'playht',
            voiceId: 'cho'
          }
        }),

        validateConfig: (config) => {
          const required = ['publicKey'];
          const missing = required.filter(key => !config[key]);
          
          if (missing.length > 0) {
            console.warn('⚠️ Missing required Vapi config:', missing);
            return false;
          }
          
          return true;
        }
      };

      // Create Vapi state manager
      window.VapiStateManager = {
        state: {
          isLoaded: typeof window.Vapi !== 'undefined',
          isConnected: false,
          currentCall: null,
          error: null
        },

        updateState: (updates) => {
          Object.assign(window.VapiStateManager.state, updates);
          console.log('📊 Vapi state updated:', window.VapiStateManager.state);
        },

        getState: () => window.VapiStateManager.state
      };

      console.log('✅ Vapi fallback utilities created');
      this.attempts.push('Vapi fallback utilities created');
    } catch (error) {
      console.error('❌ Failed to create Vapi fallback utilities:', error);
      this.errors.push(`Vapi fallback utilities: ${error.message}`);
    }
  }

  async validateVapiIntegration() {
    console.log('\n🔍 Validating Vapi integration...');
    
    try {
      if (typeof window.Vapi !== 'undefined') {
        // Test basic Vapi functionality
        const testConfig = window.VapiConfigHelper.getDefaultConfig();
        
        if (window.VapiConfigHelper.validateConfig(testConfig)) {
          console.log('✅ Vapi configuration validation passed');
          
          // Try to create a Vapi instance
          try {
            const vapiInstance = new window.Vapi(testConfig);
            console.log('✅ Vapi instance created successfully');
            this.attempts.push('Vapi instance creation: SUCCESS');
          } catch (error) {
            console.log('⚠️ Vapi instance creation failed:', error.message);
            this.attempts.push('Vapi instance creation: FAILED');
          }
        } else {
          this.errors.push('Vapi configuration validation failed');
        }
      } else {
        this.errors.push('Vapi not available for validation');
      }
    } catch (error) {
      console.error('❌ Vapi integration validation failed:', error);
      this.errors.push(`Vapi integration validation: ${error.message}`);
    }
  }

  generateReport() {
    console.log('\n📊 Vapi SDK Loading Report');
    console.log('==========================');
    
    if (this.attempts.length > 0) {
      console.log('\n🔄 Loading Attempts:');
      this.attempts.forEach(attempt => console.log(`  - ${attempt}`));
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ Errors:');
      this.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    const vapiAvailable = typeof window.Vapi !== 'undefined';
    console.log(`\n📈 Summary: Vapi SDK ${vapiAvailable ? 'LOADED' : 'NOT LOADED'}`);
    
    if (vapiAvailable) {
      console.log('🎉 Vapi SDK is now available!');
      console.log('📖 Available utilities:');
      console.log('  - window.Vapi (main SDK)');
      console.log('  - window.VapiConfigHelper (configuration helper)');
      console.log('  - window.VapiStateManager (state management)');
    } else {
      console.log('🚨 Vapi SDK could not be loaded');
      console.log('💡 Fallback utilities are available for basic functionality');
    }
  }
}

// Auto-run if in browser environment
if (typeof window !== 'undefined') {
  window.VapiSDKLoadingTest = VapiSDKLoadingTest;
  
  // Auto-run after page load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        const test = new VapiSDKLoadingTest();
        test.runTest();
      }, 1500);
    });
  } else {
    setTimeout(() => {
      const test = new VapiSDKLoadingTest();
      test.runTest();
    }, 1500);
  }
}

// Export for Node.js environment
if (typeof module !== 'undefined' && module.exports) {
  module.exports = VapiSDKLoadingTest;
}
