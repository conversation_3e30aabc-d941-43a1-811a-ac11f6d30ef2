
// File: pages/api/tools/live-dossier-enhanced.js
// API endpoint for live_dossier_enhanced tool

import { supabase } from '../../lib/supabase';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    const { assistantId, callId, dossier, userMessage, scoutMessage, userLocation } = req.body;
    
    // Get attorney context from assistant ID
    const { data: attorney, error: attorneyError } = await supabase
      .from('attorneys')
      .select('*')
      .or(`vapi_assistant_id.eq.${assistantId},current_assistant_id.eq.${assistantId}`)
      .single();
    
    if (attorneyError || !attorney) {
      console.error('Attorney not found for assistant:', assistantId);
      return res.status(404).json({ 
        success: false, 
        error: 'Attorney context not found' 
      });
    }

    // Save dossier update to database
    const { data: dossierRecord, error: dossierError } = await supabase
      .from('dossier_updates')
      .insert({
        attorney_id: attorney.id,
        assistant_id: assistantId,
        call_id: callId,
        status: dossier.status,
        jurisdiction: dossier.jurisdiction,
        client_background: dossier.clientBackground,
        legal_issues: dossier.legalIssues,
        statement_of_facts: dossier.statementOfFacts,
        objectives: dossier.objectives,
        user_message: userMessage,
        scout_message: scoutMessage,
        user_location: userLocation,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (dossierError) {
      console.error('Error saving dossier:', dossierError);
      return res.status(500).json({ 
        success: false, 
        error: 'Failed to save dossier update' 
      });
    }

    // Trigger real-time updates (if using websockets/SSE)
    // await broadcastDossierUpdate(attorney.id, dossierRecord);

    return res.status(200).json({
      success: true,
      message: 'Dossier updated successfully',
      data: {
        dossierUpdateId: dossierRecord.id,
        attorneyFirm: attorney.firm_name,
        timestamp: dossierRecord.created_at
      }
    });

  } catch (error) {
    console.error('Dossier update error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}
