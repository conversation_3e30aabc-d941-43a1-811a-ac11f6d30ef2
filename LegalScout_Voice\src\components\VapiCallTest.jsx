import React, { useState, useEffect } from 'react';
import Vapi from '@vapi-ai/web';

/**
 * Simple test component to verify Vapi Web SDK integration
 * Uses the latest Vapi Web SDK patterns from docs.vapi.ai
 */
const VapiCallTest = ({ assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a' }) => {
  const [vapi, setVapi] = useState(null);
  const [isCallActive, setIsCallActive] = useState(false);
  const [callStatus, setCallStatus] = useState('idle');
  const [error, setError] = useState(null);

  // Initialize Vapi instance
  useEffect(() => {
    try {
      console.log('[VapiCallTest] Initializing Vapi with public key');
      const publicKey = import.meta.env.VITE_VAPI_PUBLIC_KEY || '310f0d43-27c2-47a5-a76d-e55171d024f7';
      const vapiInstance = new Vapi(publicKey);
      
      // Set up event listeners
      vapiInstance.on('call-start', () => {
        console.log('[VapiCallTest] Call started');
        setIsCallActive(true);
        setCallStatus('connected');
        setError(null);
      });

      vapiInstance.on('call-end', () => {
        console.log('[VapiCallTest] Call ended');
        setIsCallActive(false);
        setCallStatus('idle');
      });

      vapiInstance.on('error', (err) => {
        console.error('[VapiCallTest] Call error:', err);
        setError(err.message || 'Call error occurred');
        setIsCallActive(false);
        setCallStatus('error');
      });

      vapiInstance.on('speech-start', () => {
        console.log('[VapiCallTest] Assistant started speaking');
      });

      vapiInstance.on('speech-end', () => {
        console.log('[VapiCallTest] Assistant stopped speaking');
      });

      vapiInstance.on('message', (message) => {
        console.log('[VapiCallTest] Message received:', message);
      });

      setVapi(vapiInstance);
      console.log('[VapiCallTest] Vapi instance initialized successfully');
    } catch (err) {
      console.error('[VapiCallTest] Error initializing Vapi:', err);
      setError('Failed to initialize Vapi: ' + err.message);
    }
  }, []);

  // Start call function
  const startCall = async () => {
    if (!vapi) {
      setError('Vapi not initialized');
      return;
    }

    if (isCallActive) {
      console.log('[VapiCallTest] Call already active');
      return;
    }

    try {
      console.log('[VapiCallTest] Starting call with assistant ID:', assistantId);
      setCallStatus('connecting');
      setError(null);

      // Use the latest Vapi Web SDK pattern - just pass the assistant ID
      // This should work for web calls (not phone calls)
      await vapi.start(assistantId);

      console.log('[VapiCallTest] Call start request sent successfully');
    } catch (err) {
      console.error('[VapiCallTest] Error starting call:', err);
      setError('Failed to start call: ' + err.message);
      setCallStatus('error');

      // Try alternative approach using MCP server for outbound calls
      console.log('[VapiCallTest] Attempting alternative call creation via MCP...');
      try {
        const { vapiMcpService } = await import('../services/vapiMcpService');
        await vapiMcpService.ensureConnection();

        // Create an outbound call (requires phone number)
        // This is just for testing - in production you'd need a real phone number
        console.log('[VapiCallTest] MCP service available for outbound calls');
        setError('Web call failed. MCP service is available for phone calls.');
      } catch (mcpErr) {
        console.error('[VapiCallTest] MCP service also failed:', mcpErr);
        setError('Both web call and MCP service failed: ' + err.message);
      }
    }
  };

  // Stop call function
  const stopCall = () => {
    if (!vapi || !isCallActive) {
      return;
    }

    try {
      console.log('[VapiCallTest] Stopping call');
      vapi.stop();
    } catch (err) {
      console.error('[VapiCallTest] Error stopping call:', err);
      setError('Failed to stop call: ' + err.message);
    }
  };

  return (
    <div style={{ 
      padding: '20px', 
      border: '2px solid #4B74AA', 
      borderRadius: '8px', 
      margin: '20px',
      backgroundColor: '#f8f9fa'
    }}>
      <h3>Vapi Call Test</h3>
      <div style={{ marginBottom: '10px' }}>
        <strong>Assistant ID:</strong> {assistantId}
      </div>
      <div style={{ marginBottom: '10px' }}>
        <strong>Status:</strong> {callStatus}
      </div>
      {error && (
        <div style={{ 
          color: 'red', 
          marginBottom: '10px',
          padding: '10px',
          backgroundColor: '#ffe6e6',
          borderRadius: '4px'
        }}>
          <strong>Error:</strong> {error}
        </div>
      )}
      <div style={{ display: 'flex', gap: '10px' }}>
        <button 
          onClick={startCall}
          disabled={!vapi || isCallActive || callStatus === 'connecting'}
          style={{
            padding: '10px 20px',
            backgroundColor: isCallActive ? '#ccc' : '#4B74AA',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isCallActive ? 'not-allowed' : 'pointer'
          }}
        >
          {callStatus === 'connecting' ? 'Connecting...' : isCallActive ? 'Call Active' : 'Start Call'}
        </button>
        <button 
          onClick={stopCall}
          disabled={!isCallActive}
          style={{
            padding: '10px 20px',
            backgroundColor: isCallActive ? '#dc3545' : '#ccc',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isCallActive ? 'pointer' : 'not-allowed'
          }}
        >
          Stop Call
        </button>
      </div>
      <div style={{ marginTop: '15px', fontSize: '12px', color: '#666' }}>
        <p>This test component uses the latest Vapi Web SDK patterns to verify call functionality.</p>
        <p>Check the browser console for detailed logs.</p>
        <p><strong>Expected behavior:</strong> Click "Start Call" → Browser requests microphone permission → Call connects → You can speak with the assistant</p>
        <p><strong>If it fails:</strong> Check console for specific error messages</p>
      </div>
    </div>
  );
};

export default VapiCallTest;
