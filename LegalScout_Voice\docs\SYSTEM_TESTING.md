# LegalScout Voice - System Testing Guide

## Overview

This comprehensive testing system ensures all components of LegalScout Voice are working correctly across different environments. It tests environment variables, API connectivity, UI components, and more.

## Quick Start

### 1. Visual Testing (Recommended)

Open the test page in your browser:
- **Local Development**: http://localhost:5174/system-test.html
- **Production**: https://dashboard.legalscout.net/system-test.html

### 2. Command Line Testing

```bash
# Install dependencies (if not already installed)
npm install

# Run comprehensive system test
npm run test:system

# Run quick health check only
npm run test:system-quick

# Test production environment
npm run test:system-production

# Verbose output
npm run test:system-verbose

# Save results to file
node scripts/run-system-test.js --output test-results.json
```

### 3. Integrated Testing

The system test integration is automatically loaded in your main application and provides:
- **Floating Test Button**: Click the 🧪 button in the bottom-right corner
- **Automatic Health Monitoring**: Runs health checks every 5 minutes
- **Status Indicators**: Button color changes based on system health

## Test Categories

### 1. Environment Variables
Tests that all required environment variables are properly set:
- `VITE_SUPABASE_URL`
- `VITE_SUPABASE_KEY` / `VITE_SUPABASE_ANON_KEY`
- `VITE_VAPI_PUBLIC_KEY`
- `VITE_VAPI_SECRET_KEY`

### 2. Supabase Connectivity
- Library loading
- Database connectivity
- Authentication state
- API key validation

### 3. Vapi Integration
- API key availability
- API connectivity
- Assistant management

### 4. UI Components
- React library loading
- DOM container availability
- Stylesheet loading

### 5. Network & CORS
- Cross-origin request handling
- API endpoint accessibility

### 6. Browser Compatibility
- Required API availability
- WebRTC support (for voice calls)
- Local storage functionality

### 7. Performance
- Page load times
- Memory usage monitoring
- DOM query performance

## Understanding Test Results

### Status Indicators

- **✅ PASSED**: Test completed successfully
- **❌ FAILED**: Critical issue that needs immediate attention
- **⚠️ WARNING**: Non-critical issue or missing optional feature

### Health Check Status

- **HEALTHY**: All systems operational
- **WARNING**: Minor issues detected (1-2 problems)
- **CRITICAL**: Major issues detected (3+ problems)
- **ERROR**: Health check itself failed

## Troubleshooting Common Issues

### Environment Variable Issues

**Problem**: Missing environment variables
**Solution**: Update Vercel environment variable scoping to "All Environments"

1. Go to Vercel Dashboard → Project → Settings → Environment Variables
2. Edit each variable and change scope from "Preview" or "Production" to "All Environments"
3. Redeploy the application

### Supabase Connectivity Issues

**Problem**: Invalid API key or connection failed
**Solutions**:
- Verify `VITE_SUPABASE_URL` and `VITE_SUPABASE_KEY` are correct
- Check if Supabase project is active
- Verify CORS settings in Supabase dashboard

### Vapi Integration Issues

**Problem**: Vapi API authentication failed
**Solutions**:
- Verify `VITE_VAPI_PUBLIC_KEY` and `VITE_VAPI_SECRET_KEY` are correct
- Check Vapi account status and API limits
- Ensure keys have proper permissions

### UI Loading Issues

**Problem**: React or other libraries not loading
**Solutions**:
- Check Content Security Policy (CSP) settings
- Verify CDN accessibility
- Check browser console for specific errors

## Automated Testing in CI/CD

Add to your GitHub Actions or other CI/CD pipeline:

```yaml
- name: Run System Tests
  run: |
    npm install
    npm run test:system-production --output ci-test-results.json
```

## Configuration

### System Test Integration Settings

Edit `public/system-test-integration.js` to customize:

```javascript
const CONFIG = {
  enableFloatingButton: true,        // Show floating test button
  enableAutoHealthCheck: true,       // Automatic health monitoring
  enablePerformanceMonitoring: true, // Performance tracking
  healthCheckInterval: 5 * 60 * 1000, // 5 minutes
  showInProduction: false            // Show test features in production
};
```

### Command Line Options

```bash
node scripts/run-system-test.js [options]

Options:
  --url <url>        URL to test (default: http://localhost:5175)
  --production       Test production environment
  --quick            Run quick health check only
  --timeout <ms>     Test timeout in milliseconds (default: 30000)
  --output <file>    Save results to JSON file
  --verbose          Verbose output
  --headless <bool>  Run in headless mode (default: true)
  --help             Show help
```

## Manual Testing Checklist

When running manual tests, verify:

- [ ] Environment variables are properly loaded
- [ ] Supabase connection works
- [ ] Vapi API calls succeed
- [ ] Authentication flow works
- [ ] Voice calls can be initiated
- [ ] Dashboard loads without errors
- [ ] All UI components render correctly
- [ ] No console errors
- [ ] Performance is acceptable

## Getting Help

If tests are failing:

1. **Check the browser console** for detailed error messages
2. **Run verbose tests** with `--verbose` flag
3. **Save test results** with `--output` flag for analysis
4. **Compare results** between local and production environments
5. **Check environment variable scoping** in Vercel dashboard

## Test Results Storage

Test results are automatically saved to:
- **Browser**: `localStorage` as `legalscout_test_report`
- **Command Line**: Specified output file or console
- **Integration**: Available via `window.legalScoutTestResults`

Access saved results:
```javascript
// In browser console
JSON.parse(localStorage.getItem('legalscout_test_report'))

// Via integration
window.systemTestIntegration.getLastHealthStatus()
```
