# 🔧 CORS Solution Guide for LegalScout

## Overview

This document describes the comprehensive CORS (Cross-Origin Resource Sharing) solution implemented for LegalScout to resolve the persistent CORS errors with Supabase requests.

## Problem

LegalScout was experiencing CORS errors when making requests to Supa<PERSON> from the browser:

```
Access to fetch at 'https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/...' 
from origin 'https://legalscout.net' has been blocked by CORS policy: 
No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## Solution Architecture

### 1. CORS Proxy Pattern

We implemented a **CORS Proxy** that routes Supabase requests through our own API endpoints, which have proper CORS headers configured.

**Flow:**
```
Frontend → API Proxy → Supabase → API Proxy → Frontend
```

### 2. Automatic Request Interception

The Supabase client automatically detects Supabase API requests and routes them through the proxy:

```javascript
// Before: Direct request (CORS blocked)
fetch('https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys')

// After: Automatic proxy routing
fetch('/api/supabase-proxy/attorneys')
```

### 3. Fallback Mechanism

If the proxy is unavailable, the system falls back to direct requests with enhanced CORS handling.

## Implementation Details

### Files Modified

1. **`src/lib/supabase.js`** - Enhanced Supabase client with CORS-aware fetch
2. **`api/index.js`** - Added Supabase proxy endpoint
3. **`vercel.json`** - Updated CORS headers configuration
4. **`src/utils/corsMonitor.js`** - CORS monitoring and debugging utility

### Key Components

#### 1. CORS-Aware Fetch Function

```javascript
const corsAwareFetch = async (url, options = {}) => {
  if (url.includes('.supabase.co/rest/v1/')) {
    // Route through proxy
    return fetch(`/api/supabase-proxy/${endpoint}`, {
      ...options,
      headers: {
        ...options.headers,
        'X-Original-URL': url,
        'X-Supabase-Key': key
      }
    });
  }
  return fetch(url, options);
};
```

#### 2. Supabase Proxy Endpoint

```javascript
// /api/supabase-proxy/{endpoint}
async function handleSupabaseProxy(req, res, path) {
  const endpoint = path.replace('/api/supabase-proxy/', '');
  const supabaseUrl = `${SUPABASE_URL}/rest/v1/${endpoint}`;
  
  const response = await fetch(supabaseUrl, {
    method: req.method,
    headers: {
      'apikey': SUPABASE_KEY,
      'Authorization': `Bearer ${SUPABASE_KEY}`,
      'Content-Type': 'application/json'
    },
    body: req.body
  });
  
  return res.status(response.status).json(await response.json());
}
```

#### 3. Enhanced CORS Headers

```json
{
  "source": "/api/supabase-proxy/(.*)",
  "headers": [
    {
      "key": "Access-Control-Allow-Origin",
      "value": "*"
    },
    {
      "key": "Access-Control-Allow-Methods", 
      "value": "GET, POST, PUT, DELETE, OPTIONS, PATCH"
    },
    {
      "key": "Access-Control-Allow-Headers",
      "value": "Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept, X-Original-URL, X-Supabase-Key, Range"
    }
  ]
}
```

## Testing

### 1. Automated Test Page

Visit `/test-cors-solution.html` to run automated tests:

- ✅ Environment check
- ✅ CORS proxy functionality
- ⚠️ Direct Supabase requests (expected to fail)
- ✅ Enhanced Supabase client

### 2. CORS Monitor

The CORS monitor automatically tracks all requests in development:

```javascript
// Console commands
corsMonitor.report()        // Show summary
corsMonitor.getCORSIssues() // Get CORS problems
corsMonitor.clear()         // Clear history
```

### 3. Manual Testing

```javascript
// Test proxy directly
fetch('/api/supabase-proxy/attorneys?select=id&limit=1')
  .then(r => r.json())
  .then(console.log);

// Test through Supabase client (should use proxy automatically)
supabase.from('attorneys').select('id').limit(1);
```

## Deployment

### 1. Environment Variables

Ensure these are set in Vercel:

```bash
SUPABASE_URL=https://utopqxsvudgrtiwenlzl.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 2. Vercel Configuration

The `vercel.json` includes:
- CORS headers for all API routes
- Specific headers for the Supabase proxy
- Proper CSP configuration

### 3. Function Limits

The solution uses the existing `/api/index.js` function to stay within Vercel's 12 function limit.

## Monitoring & Debugging

### 1. Console Logs

Look for these log messages:

```
[CORS Proxy] Routing Supabase request through API proxy
[CORS Proxy] Successfully routed through proxy
[Supabase Proxy] GET attorneys
```

### 2. Network Tab

- Proxy requests: `/api/supabase-proxy/*`
- Direct requests: `*.supabase.co/rest/v1/*`

### 3. Error Patterns

Common issues and solutions:

| Error | Cause | Solution |
|-------|-------|----------|
| `CORS policy: No 'Access-Control-Allow-Origin'` | Direct Supabase request | Proxy should handle automatically |
| `Proxy failed, using fallback` | API endpoint down | Check Vercel function logs |
| `Invalid API key` | Wrong environment variables | Verify Vercel env vars |

## Benefits

1. **Non-destructive** - Existing code continues to work
2. **Automatic** - No code changes needed for existing Supabase calls
3. **Resilient** - Falls back to direct requests if proxy fails
4. **Debuggable** - Comprehensive monitoring and logging
5. **Scalable** - Uses existing API infrastructure

## Troubleshooting

### If CORS errors persist:

1. **Check proxy endpoint:**
   ```bash
   curl https://legalscout.net/api/supabase-proxy/attorneys?select=id&limit=1
   ```

2. **Verify environment variables:**
   ```javascript
   console.log('Supabase URL:', process.env.SUPABASE_URL);
   ```

3. **Check CORS monitor:**
   ```javascript
   corsMonitor.report();
   ```

4. **Review Vercel logs:**
   - Go to Vercel dashboard
   - Check function logs for `/api/index.js`

### Emergency Fallback

If the proxy completely fails, you can temporarily disable it:

```javascript
// In src/lib/supabase.js, comment out the corsAwareFetch
// and use standard fetch:
// fetch: corsAwareFetch  // Comment this line
```

## Future Enhancements

1. **Caching** - Add response caching to reduce API calls
2. **Rate Limiting** - Implement rate limiting for the proxy
3. **Analytics** - Track proxy usage and performance
4. **Health Checks** - Automated proxy health monitoring

## Support

For issues with the CORS solution:

1. Check the console for CORS Monitor logs
2. Test the proxy endpoint directly
3. Review Vercel function logs
4. Use the automated test page at `/test-cors-solution.html`
