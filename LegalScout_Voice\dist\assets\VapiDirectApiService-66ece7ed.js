class r{constructor(){this.apiUrl="https://api.vapi.ai",this.apiKey="6734febc-fc65-4669-93b0-929b31ff6564"}async getAssistant(i){try{const e=await fetch(`${this.apiUrl}/assistant/${i}`,{headers:{Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json"}});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const t=await e.json();return console.log("🔍 [VapiDirectApiService] Retrieved complete assistant data:",{id:t.id,name:t.name,hasFirstMessage:!!t.firstMessage,hasInstructions:!!t.model?.messages?.[0]?.content,voice:t.voice,model:t.model?.model}),t}catch(e){throw console.error("❌ [VapiDirectApiService] Error getting assistant:",e),e}}async updateAssistant(i,e){try{console.log("🔧 [VapiDirectApiService] Updating assistant:",{assistantId:i.substring(0,8)+"...",updateData:e});const t=await fetch(`${this.apiUrl}/assistant/${i}`,{method:"PATCH",headers:{Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){const a=await t.text();throw new Error(`HTTP ${t.status}: ${a}`)}const s=await t.json();return console.log("✅ [VapiDirectApiService] Assistant updated successfully"),s}catch(t){throw console.error("❌ [VapiDirectApiService] Error updating assistant:",t),t}}async getAssistantWithFallback(i,e=null){try{return await this.getAssistant(i)}catch(t){if(console.warn("🔄 [VapiDirectApiService] Direct API failed, trying MCP fallback:",t.message),e)try{return await e.getAssistant(i)}catch(s){throw console.error("❌ [VapiDirectApiService] Both direct API and MCP failed:",s),new Error(`Both direct API and MCP failed: ${t.message}, ${s.message}`)}else throw t}}async getCompleteAssistantData(i,e=null){try{const[t,s]=await Promise.allSettled([this.getAssistant(i),e?e.getAssistant(i):Promise.resolve(null)]);if(t.status==="fulfilled")return console.log("✅ [VapiDirectApiService] Using direct API data (complete)"),t.value;if(s.status==="fulfilled"&&s.value)return console.log("⚠️ [VapiDirectApiService] Using MCP data (may be incomplete)"),s.value;throw new Error("Both direct API and MCP failed to retrieve assistant data")}catch(t){throw console.error("❌ [VapiDirectApiService] Error getting complete assistant data:",t),t}}}const n=new r;export{n as default,n as vapiDirectApiService};
