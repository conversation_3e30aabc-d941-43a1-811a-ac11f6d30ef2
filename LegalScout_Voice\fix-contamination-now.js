#!/usr/bin/env node

/**
 * Immediate Contamination Fix
 * Run this to fix the "Joe's Pizza" issue right now
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

// Environment variables loaded above

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixContaminationNow() {
  console.log('🚀 IMMEDIATE CONTAMINATION FIX');
  console.log('==============================\n');

  try {
    // Step 1: Check current contamination
    console.log('🔍 Checking for contaminated data...');
    
    const { data: attorneys, error: fetchError } = await supabase
      .from('attorneys')
      .select('*')
      .or('<EMAIL>,<EMAIL>,<EMAIL>');

    if (fetchError) {
      console.error('❌ Error fetching attorneys:', fetchError);
      return;
    }

    if (!attorneys || attorneys.length === 0) {
      console.log('📋 No attorney records found');
      return;
    }

    console.log(`📋 Found ${attorneys.length} attorney records:`);
    attorneys.forEach(attorney => {
      console.log(`  - ${attorney.firm_name} (${attorney.email})`);
      if (attorney.firm_name && attorney.firm_name.toLowerCase().includes('pizza')) {
        console.log(`    🚨 CONTAMINATION DETECTED: ${attorney.firm_name}`);
      }
    });

    // Step 2: Apply fix
    console.log('\n🧹 Applying contamination fix...');
    
    const correctFirmName = 'LegalScout';
    const correctWelcomeMessage = `Hello! I'm Scout from ${correctFirmName}. How can I help you with your legal needs today?`;
    const correctInstructions = `You are Scout, a legal assistant for ${correctFirmName}. Help potential clients understand their legal needs and collect relevant information for consultation. Be professional, helpful, and empathetic.`;

    const { data: updatedAttorneys, error: updateError } = await supabase
      .from('attorneys')
      .update({
        firm_name: correctFirmName,
        welcome_message: correctWelcomeMessage,
        vapi_instructions: correctInstructions,
        updated_at: new Date().toISOString()
      })
      .or('<EMAIL>,<EMAIL>,<EMAIL>')
      .select();

    if (updateError) {
      console.error('❌ Error updating attorneys:', updateError);
      return;
    }

    console.log(`✅ Updated ${updatedAttorneys?.length || 0} attorney records`);

    // Step 3: Verify fix
    console.log('\n✅ Verifying fix...');
    
    const { data: verifyAttorneys, error: verifyError } = await supabase
      .from('attorneys')
      .select('firm_name, email, vapi_assistant_id')
      .or('<EMAIL>,<EMAIL>,<EMAIL>');

    if (verifyError) {
      console.error('❌ Error verifying fix:', verifyError);
      return;
    }

    console.log('📋 Verification results:');
    verifyAttorneys.forEach(attorney => {
      console.log(`  ✅ ${attorney.firm_name} (${attorney.email})`);
      
      if (attorney.firm_name && attorney.firm_name.toLowerCase().includes('pizza')) {
        console.log(`    🚨 WARNING: Contamination still present!`);
      } else {
        console.log(`    ✅ Clean: ${attorney.firm_name}`);
      }
    });

    console.log('\n🎉 CONTAMINATION FIX COMPLETE!');
    console.log('Your assistant should now properly show "LegalScout" instead of "Joe\'s Pizza"');
    console.log('\nNext steps:');
    console.log('1. Refresh your browser');
    console.log('2. The notification should now show correct firm name');
    console.log('3. Assistant will be configured as "Scout from LegalScout"');

  } catch (error) {
    console.error('❌ Fix failed:', error);
  }
}

// Run the fix
fixContaminationNow();
