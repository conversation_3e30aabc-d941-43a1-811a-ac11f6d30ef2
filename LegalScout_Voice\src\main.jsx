import React from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import App from './App.jsx'
import './index.css'
import 'leaflet/dist/leaflet.css'
import './fixes/interactionFix.css'
import ErrorBoundary from './utils/ErrorBoundary.jsx'
import ProductionErrorBoundary from './components/ProductionErrorBoundary.jsx'
import SyncAuthProvider from './components/SyncAuthProvider.jsx'
import { ThemeProvider } from './contexts/ThemeContext.jsx'
import { AttorneyStateProvider } from './contexts/AttorneyStateContext.jsx'

// Import essential utilities
import './utils/schemaGenerator.js'

// Simple environment detection
const isDevelopment = () => {
  try {
    return import.meta.env.DEV || window.location.hostname === 'localhost';
  } catch (e) {
    return window.location.hostname === 'localhost';
  }
};

console.log('🚀 [LegalScout] Starting React app...');

// CRITICAL: Initialize production environment variables FIRST
if (import.meta.env.PROD && typeof window !== 'undefined') {
  console.log('🔧 [main.jsx] Production mode detected - initializing environment...');

  // Set production environment variables directly in window for immediate availability
  window.VITE_SUPABASE_URL = window.VITE_SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
  window.VITE_SUPABASE_KEY = window.VITE_SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
  window.VITE_VAPI_PUBLIC_KEY = window.VITE_VAPI_PUBLIC_KEY || '310f0d43-27c2-47a5-a76d-e55171d024f7';
  window.VITE_VAPI_SECRET_KEY = window.VITE_VAPI_SECRET_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';

  console.log('✅ [main.jsx] Production environment variables set in window object');
}

// DEBUG: Check environment variables in production
console.log('🔥 [main.jsx] Environment variables check:', {
  NODE_ENV: import.meta.env.NODE_ENV,
  DEV: import.meta.env.DEV,
  PROD: import.meta.env.PROD,
  VITE_SUPABASE_URL: !!import.meta.env.VITE_SUPABASE_URL,
  VITE_SUPABASE_KEY: !!import.meta.env.VITE_SUPABASE_KEY,
  VITE_VAPI_PUBLIC_KEY: !!import.meta.env.VITE_VAPI_PUBLIC_KEY,
  VITE_VAPI_SECRET_KEY: !!import.meta.env.VITE_VAPI_SECRET_KEY,
  window_VITE_SUPABASE_URL: typeof window !== 'undefined' ? !!window.VITE_SUPABASE_URL : 'N/A',
  window_VITE_SUPABASE_KEY: typeof window !== 'undefined' ? !!window.VITE_SUPABASE_KEY : 'N/A',
  hostname: window.location.hostname,
  isDev: isDevelopment()
});

// DEBUG: Check if critical imports loaded
console.log('🔥 [main.jsx] Critical imports check:', {
  React: !!React,
  ReactDOM: !!ReactDOM,
  App: !!App,
  BrowserRouter: !!BrowserRouter,
  ProductionErrorBoundary: !!ProductionErrorBoundary,
  SyncAuthProvider: !!SyncAuthProvider
});

// Render React app
try {
  const root = ReactDOM.createRoot(document.getElementById('root'));

  root.render(
    <React.StrictMode>
      <ProductionErrorBoundary>
        <ErrorBoundary showDetails={isDevelopment()} onReset={() => window.location.reload()}>
          <BrowserRouter>
            <ThemeProvider>
              <AttorneyStateProvider>
                <SyncAuthProvider>
                  <App />
                </SyncAuthProvider>
              </AttorneyStateProvider>
            </ThemeProvider>
          </BrowserRouter>
        </ErrorBoundary>
      </ProductionErrorBoundary>
    </React.StrictMode>
  );

  console.log('✅ [LegalScout] React app rendered successfully');
} catch (error) {
  console.error('❌ [LegalScout] Failed to render React app:', error);

  // Fallback error display
  const root = document.getElementById('root');
  if (root) {
    root.innerHTML = `
      <div style="padding: 20px; background: #ffebee; color: #c62828; font-family: monospace;">
        <h1>❌ Application Error</h1>
        <p><strong>Error:</strong> ${error.message}</p>
        <button onclick="location.reload()" style="
          padding: 10px 20px;
          background: #c62828;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          margin-top: 10px;
        ">Reload Page</button>
      </div>
    `;
  }
}