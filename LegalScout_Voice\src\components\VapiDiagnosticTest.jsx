import React, { useState, useEffect, useRef } from 'react';

/**
 * Comprehensive diagnostic test for Vapi call termination issues
 * This component captures detailed information about what's actually happening
 */
const VapiDiagnosticTest = () => {
  const [logs, setLogs] = useState([]);
  const [vapiInstance, setVapiInstance] = useState(null);
  const [callState, setCallState] = useState('idle');
  const [diagnosticData, setDiagnosticData] = useState({});
  const [isRecording, setIsRecording] = useState(false);
  const callStartTime = useRef(null);
  const eventCounts = useRef({});

  const addLog = (message, type = 'info', data = null) => {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      message,
      type,
      data: data ? JSON.stringify(data, null, 2) : null,
      id: Date.now() + Math.random()
    };
    
    setLogs(prev => [...prev, logEntry]);
    console.log(`[VapiDiagnostic] ${message}`, data || '');
  };

  const updateDiagnosticData = (key, value) => {
    setDiagnosticData(prev => ({
      ...prev,
      [key]: value,
      lastUpdated: new Date().toISOString()
    }));
  };

  // Test microphone access
  const testMicrophone = async () => {
    try {
      addLog('🎤 Testing microphone access and device enumeration...');

      // First, enumerate all available devices
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioInputs = devices.filter(device => device.kind === 'audioinput');

      addLog(`📱 Found ${audioInputs.length} audio input devices`, 'info', {
        devices: audioInputs.map(d => ({
          deviceId: d.deviceId,
          label: d.label || 'Unknown Device',
          groupId: d.groupId
        }))
      });

      if (audioInputs.length === 0) {
        addLog('❌ No audio input devices found!', 'error');
        updateDiagnosticData('microphoneTest', {
          hasDevices: false,
          error: 'No audio input devices available'
        });
        return;
      }

      // Test each device individually to identify which ones work
      for (let i = 0; i < Math.min(audioInputs.length, 3); i++) {
        const device = audioInputs[i];
        try {
          addLog(`🔍 Testing device ${i + 1}: ${device.label || 'Unknown'}`);

          const stream = await navigator.mediaDevices.getUserMedia({
            audio: {
              deviceId: { exact: device.deviceId },
              echoCancellation: true,
              noiseSuppression: true,
              autoGainControl: true
            }
          });

          addLog(`✅ Device ${i + 1} works: ${stream.getAudioTracks()[0]?.label}`, 'success');
          stream.getTracks().forEach(track => track.stop());

        } catch (deviceError) {
          addLog(`❌ Device ${i + 1} failed: ${deviceError.message}`, 'error');
        }
      }

      // Test default device (no deviceId specified)
      addLog('🎤 Testing default device selection...');
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });
      
      addLog('✅ Microphone access granted', 'success');
      
      // Test audio levels
      const audioContext = new AudioContext();
      const analyser = audioContext.createAnalyser();
      const microphone = audioContext.createMediaStreamSource(stream);
      microphone.connect(analyser);
      
      const dataArray = new Uint8Array(analyser.frequencyBinCount);
      
      // Monitor audio for 3 seconds
      let maxLevel = 0;
      const checkAudio = () => {
        analyser.getByteFrequencyData(dataArray);
        const level = Math.max(...dataArray);
        maxLevel = Math.max(maxLevel, level);
      };
      
      const interval = setInterval(checkAudio, 100);
      
      setTimeout(() => {
        clearInterval(interval);
        stream.getTracks().forEach(track => track.stop());
        audioContext.close();
        
        addLog(`🔊 Audio level test complete. Max level: ${maxLevel}`, 
               maxLevel > 10 ? 'success' : 'warning');
        updateDiagnosticData('microphoneTest', {
          hasAccess: true,
          maxAudioLevel: maxLevel,
          isWorking: maxLevel > 10
        });
      }, 3000);
      
    } catch (error) {
      addLog(`❌ Microphone test failed: ${error.message}`, 'error');
      updateDiagnosticData('microphoneTest', {
        hasAccess: false,
        error: error.message
      });
    }
  };

  // Test Vapi assistant configuration
  const testAssistantConfig = async () => {
    try {
      addLog('🔍 Testing assistant configuration...');
      
      const assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
      
      // Use MCP to get assistant details
      const response = await fetch('/api/vapi-mcp-server', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          method: 'get_assistant_vapi-mcp-server',
          params: {
            assistantId: assistantId
          }
        })
      });
      
      if (response.ok) {
        const assistantData = await response.json();
        addLog('✅ Assistant configuration retrieved', 'success', assistantData);
        updateDiagnosticData('assistantConfig', assistantData);
        
        // Check for potential issues
        const issues = [];
        if (!assistantData.voice) issues.push('No voice configured');
        if (!assistantData.model) issues.push('No model configured');
        if (!assistantData.firstMessage) issues.push('No first message');
        
        if (issues.length > 0) {
          addLog(`⚠️ Potential assistant issues: ${issues.join(', ')}`, 'warning');
        }
      } else {
        addLog('❌ Failed to retrieve assistant configuration', 'error');
      }
    } catch (error) {
      addLog(`❌ Assistant config test failed: ${error.message}`, 'error');
    }
  };

  // Create Vapi instance with comprehensive event logging
  const createVapiInstance = async () => {
    try {
      addLog('🔧 Creating Vapi instance...');
      
      const apiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
      if (!apiKey) {
        throw new Error('No Vapi API key found');
      }
      
      addLog(`🔑 Using API key: ${apiKey.substring(0, 8)}...`);
      
      let Vapi;
      if (window.Vapi) {
        addLog('📦 Using global Vapi from CDN');
        Vapi = window.Vapi;
      } else {
        addLog('📦 Importing Vapi module...');
        const VapiModule = await import('@vapi-ai/web');
        Vapi = VapiModule.default || VapiModule.Vapi || VapiModule;
      }
      
      const vapi = new Vapi(apiKey);
      addLog('✅ Vapi instance created');
      
      // Set up comprehensive event logging
      const events = [
        'call-start', 'call-end', 'speech-start', 'speech-end',
        'volume-level', 'message', 'error', 'transcript',
        'model-output', 'function-call', 'hang', 'tool-calls'
      ];
      
      events.forEach(eventName => {
        vapi.on(eventName, (data) => {
          eventCounts.current[eventName] = (eventCounts.current[eventName] || 0) + 1;
          
          addLog(`📡 Event: ${eventName}`, 'info', {
            eventCount: eventCounts.current[eventName],
            data: data,
            timeSinceStart: callStartTime.current ? 
              Date.now() - callStartTime.current : null
          });
          
          // Track specific events
          if (eventName === 'call-start') {
            setCallState('connected');
            callStartTime.current = Date.now();
          } else if (eventName === 'call-end') {
            setCallState('ended');
            const duration = callStartTime.current ? 
              Date.now() - callStartTime.current : 0;
            updateDiagnosticData('callDuration', duration);
          } else if (eventName === 'error') {
            setCallState('error');
            updateDiagnosticData('lastError', data);
          }
        });
      });
      
      setVapiInstance(vapi);
      updateDiagnosticData('vapiInstanceCreated', true);
      
    } catch (error) {
      addLog(`❌ Failed to create Vapi instance: ${error.message}`, 'error');
      updateDiagnosticData('vapiInstanceError', error.message);
    }
  };

  // Start diagnostic call
  const startDiagnosticCall = async () => {
    if (!vapiInstance) {
      addLog('❌ No Vapi instance available', 'error');
      return;
    }
    
    try {
      setCallState('connecting');
      setIsRecording(true);
      callStartTime.current = Date.now();
      eventCounts.current = {};
      
      addLog('🚀 Starting diagnostic call...');
      
      const assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
      addLog(`🎯 Using assistant ID: ${assistantId}`);
      
      // Log browser and environment info
      updateDiagnosticData('environment', {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
        hardwareConcurrency: navigator.hardwareConcurrency
      });
      
      // Start the call
      const result = await vapiInstance.start(assistantId);
      addLog('✅ Call start command sent', 'success', result);
      
    } catch (error) {
      addLog(`❌ Failed to start call: ${error.message}`, 'error');
      setCallState('error');
      updateDiagnosticData('callStartError', error.message);
    }
  };

  // Stop call
  const stopCall = async () => {
    if (vapiInstance) {
      try {
        await vapiInstance.stop();
        addLog('🛑 Call stopped manually');
        setCallState('idle');
        setIsRecording(false);
      } catch (error) {
        addLog(`❌ Error stopping call: ${error.message}`, 'error');
      }
    }
  };

  // Clear logs
  const clearLogs = () => {
    setLogs([]);
    setDiagnosticData({});
    eventCounts.current = {};
  };

  // Export diagnostic data
  const exportDiagnostics = () => {
    const diagnosticReport = {
      timestamp: new Date().toISOString(),
      logs: logs,
      diagnosticData: diagnosticData,
      eventCounts: eventCounts.current,
      callState: callState
    };
    
    const blob = new Blob([JSON.stringify(diagnosticReport, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `vapi-diagnostic-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="p-6 border rounded-lg bg-gray-50">
      <h3 className="text-xl font-bold mb-4">🔬 Vapi Call Diagnostic Test</h3>
      
      {/* Status */}
      <div className="mb-4">
        <strong>Call State:</strong>
        <span className={`ml-2 px-3 py-1 rounded text-sm ${
          callState === 'connected' ? 'bg-green-100 text-green-800' :
          callState === 'error' ? 'bg-red-100 text-red-800' :
          callState === 'connecting' ? 'bg-yellow-100 text-yellow-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {callState}
        </span>
        {isRecording && <span className="ml-2 text-red-600">🔴 Recording Events</span>}
      </div>

      {/* Controls */}
      <div className="mb-4 space-x-2">
        <button onClick={testMicrophone} className="px-3 py-1 bg-blue-500 text-white rounded text-sm">
          Test Microphone
        </button>
        <button onClick={testAssistantConfig} className="px-3 py-1 bg-purple-500 text-white rounded text-sm">
          Test Assistant Config
        </button>
        <button onClick={createVapiInstance} className="px-3 py-1 bg-green-500 text-white rounded text-sm">
          Create Vapi Instance
        </button>
        <button 
          onClick={startDiagnosticCall} 
          disabled={!vapiInstance || callState === 'connecting' || callState === 'connected'}
          className="px-3 py-1 bg-orange-500 text-white rounded text-sm disabled:opacity-50"
        >
          Start Diagnostic Call
        </button>
        <button 
          onClick={stopCall} 
          disabled={callState !== 'connected'}
          className="px-3 py-1 bg-red-500 text-white rounded text-sm disabled:opacity-50"
        >
          Stop Call
        </button>
        <button onClick={clearLogs} className="px-3 py-1 bg-gray-500 text-white rounded text-sm">
          Clear Logs
        </button>
        <button onClick={exportDiagnostics} className="px-3 py-1 bg-indigo-500 text-white rounded text-sm">
          Export Diagnostics
        </button>
      </div>

      {/* Event Counts */}
      {Object.keys(eventCounts.current).length > 0 && (
        <div className="mb-4 p-3 bg-blue-50 rounded">
          <strong>Event Counts:</strong>
          <div className="grid grid-cols-4 gap-2 mt-2 text-sm">
            {Object.entries(eventCounts.current).map(([event, count]) => (
              <div key={event} className="flex justify-between">
                <span>{event}:</span>
                <span className="font-mono">{count}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Logs */}
      <div className="h-96 overflow-y-auto border border-gray-300 p-3 bg-white text-xs font-mono">
        {logs.map((log) => (
          <div key={log.id} className={`mb-2 ${
            log.type === 'error' ? 'text-red-600' :
            log.type === 'success' ? 'text-green-600' :
            log.type === 'warning' ? 'text-orange-600' :
            'text-gray-700'
          }`}>
            <div className="flex items-start gap-2">
              <span className="text-gray-500 text-xs">
                [{new Date(log.timestamp).toLocaleTimeString()}]
              </span>
              <span>{log.message}</span>
            </div>
            {log.data && (
              <pre className="ml-20 mt-1 text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                {log.data}
              </pre>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default VapiDiagnosticTest;
