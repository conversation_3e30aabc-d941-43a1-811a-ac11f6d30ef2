// Clean Auth Solution
(function() {
  console.log('🔐 [AUTH] Applying clean authentication solution...');
  
  // Remove conflicting fetch interceptors
  if (window.originalFetch && typeof window.originalFetch === 'function') {
    window.fetch = window.originalFetch;
    console.log('✅ [AUTH] Restored original fetch');
  }
  
  // Clean auth state
  window.authManager = {
    clearState: function() {
      // Clear any conflicting auth state
      delete window.supabaseAuthOverride;
      delete window.authInterceptor;
      console.log('✅ [AUTH] Auth state cleared');
    }
  };
  
  console.log('✅ [AUTH] Clean auth solution applied');
})();