/**
 * Vapi Assistant Configuration Component
 *
 * This component handles the configuration of Vapi assistants for attorneys.
 * It ensures that attorneys have a valid Vapi assistant ID and provides
 * UI for editing Vapi-related fields.
 *
 * It implements a single source of truth pattern:
 * 1. Data is stored in Supabase
 * 2. Changes flow from UI → Supabase → Vapi (never in reverse)
 * 3. Each field has its own save/abandon buttons
 */

import React, { useState, useEffect, useCallback } from 'react';
import { vapiAssistantService } from '../../services/vapiAssistantService';
import { createLogger } from '../../utils/loggerUtils';
import { supabase } from '../../lib/supabase';
import * as vapiDirectApi from '../../utils/vapiDirectApi';
import { vapiMcpService } from '../../services/vapiMcpService';
import { attorneyToVapi, vapiToAttorney, createVapiUpdateData } from '../../utils/vapiFieldMapping';
import './VapiAssistantConfig.css';

const logger = createLogger('VapiAssistantConfig');

const VapiAssistantConfig = ({ attorney, onUpdate }) => {
  // State for Vapi assistant data
  const [assistantData, setAssistantData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // State for edited fields
  const [editedFields, setEditedFields] = useState({});
  const [hasChanges, setHasChanges] = useState(false);

  // State for diagnostics
  const [showDiagnostics, setShowDiagnostics] = useState(false);
  const [diagnosticsData, setDiagnosticsData] = useState({
    publicKey: import.meta.env.VITE_VAPI_PUBLIC_KEY || 'Not set',
    secretKey: import.meta.env.VITE_VAPI_SECRET_KEY ? 'Set' : 'Not set',
    forceMcpMode: import.meta.env.VITE_FORCE_MCP_MODE === 'true' ? true : false,
    localStorageKey: localStorage.getItem('vapi_api_key') ? 'Set' : 'Not set'
  });

  // Load assistant data when attorney changes
  useEffect(() => {
    const loadAssistantData = async () => {
      // Clear assistant data if attorney is null
      if (!attorney) {
        setAssistantData(null);
        return;
      }

      // Clear assistant data if attorney has no assistant ID
      if (!attorney.vapi_assistant_id) {
        setAssistantData(null);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        logger.info(`Loading assistant data for ${attorney.vapi_assistant_id}`);

        // Try to get assistant data using MCP service first
        let assistant = null;

        try {
          // Ensure connection to Vapi MCP server
          await vapiMcpService.ensureConnection();

          // Get assistant data
          assistant = await vapiMcpService.getAssistant(attorney.vapi_assistant_id);

          if (assistant) {
            logger.info(`Loaded assistant data using MCP service for ${attorney.vapi_assistant_id}`);
          }
        } catch (mcpError) {
          logger.warn(`MCP service failed: ${mcpError.message}, trying direct API`);
        }

        // If MCP failed, try direct API
        if (!assistant) {
          try {
            assistant = await vapiDirectApi.getAssistant(attorney.vapi_assistant_id);

            if (assistant) {
              logger.info(`Loaded assistant data using direct API for ${attorney.vapi_assistant_id}`);
            }
          } catch (directApiError) {
            logger.warn(`Direct API failed: ${directApiError.message}, trying vapiAssistantService`);

            // Last resort: try vapiAssistantService
            try {
              assistant = await vapiAssistantService.getAssistant(attorney.vapi_assistant_id);

              if (assistant) {
                logger.info(`Loaded assistant data using vapiAssistantService for ${attorney.vapi_assistant_id}`);
              }
            } catch (serviceError) {
              logger.error(`All methods failed to load assistant data: ${serviceError.message}`);
              throw new Error(`Failed to load assistant data: ${serviceError.message}`);
            }
          }
        }

        // If we still don't have assistant data, show error
        if (!assistant) {
          logger.warn(`No assistant found with ID ${attorney.vapi_assistant_id}`);
          setError(`No assistant found with ID ${attorney.vapi_assistant_id}`);
          setLoading(false);
          return;
        }

        // Set assistant data
        setAssistantData(assistant);

        // Extract fields from Vapi assistant to update attorney record if needed
        const extractedFields = vapiToAttorney(assistant);

        // Check if any fields need to be updated in Supabase
        const fieldsToUpdate = {};
        let needsUpdate = false;

        // Only update fields that are different
        Object.keys(extractedFields).forEach(field => {
          // Skip the assistant ID field
          if (field === 'vapi_assistant_id') return;

          // For objects, compare stringified versions
          if (typeof extractedFields[field] === 'object' && extractedFields[field] !== null) {
            const currentValue = attorney[field] ? JSON.stringify(attorney[field]) : '';
            const newValue = JSON.stringify(extractedFields[field]);

            if (currentValue !== newValue) {
              fieldsToUpdate[field] = extractedFields[field];
              needsUpdate = true;
            }
          }
          // For primitive values, compare directly
          else if (attorney[field] !== extractedFields[field]) {
            fieldsToUpdate[field] = extractedFields[field];
            needsUpdate = true;
          }
        });

        // If fields need to be updated, update Supabase
        if (needsUpdate) {
          logger.info(`Updating attorney record with fields from Vapi: ${Object.keys(fieldsToUpdate).join(', ')}`);

          try {
            const { error: supabaseError } = await supabase
              .from('attorneys')
              .update(fieldsToUpdate)
              .eq('id', attorney.id);

            if (supabaseError) {
              logger.error(`Error updating attorney record: ${supabaseError.message}`);
            } else {
              logger.info('Attorney record updated successfully');

              // Notify parent component of updates
              if (onUpdate) {
                onUpdate({
                  ...attorney,
                  ...fieldsToUpdate
                });
              }
            }
          } catch (updateError) {
            logger.error(`Error updating attorney record: ${updateError.message}`);
          }
        }
      } catch (err) {
        logger.error(`Error loading assistant data: ${err.message}`);

        // Provide more helpful error messages based on the error type
        if (err.message.includes('Not connected to Vapi MCP server') || err.message.includes('connection')) {
          setError(`Error connecting to Vapi API. Please check your network connection and try again. If the issue persists, contact support.`);
        } else if (err.message.includes('404') || err.message.includes('not found')) {
          // If the assistant wasn't found, suggest creating a new one
          setError(`Assistant not found with ID: ${attorney.vapi_assistant_id}. You may need to create a new assistant.`);
        } else if (err.message.includes('Failed to load assistant data')) {
          // If all methods failed, provide a more detailed error message
          setError(`Unable to load assistant data. Please check your API key and network connection. If the issue persists, try creating a new assistant.`);
        } else {
          setError(`Error loading assistant data: ${err.message}`);
        }
      } finally {
        setLoading(false);
      }
    };

    loadAssistantData();
  }, [attorney, onUpdate]);

  // Handle field changes
  const handleFieldChange = (field, value) => {
    setEditedFields(prev => ({
      ...prev,
      [field]: value
    }));

    setHasChanges(true);
  };

  // Save a single field to both Supabase and Vapi
  const handleSaveField = async (field, value) => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Check if attorney data is available
      if (!attorney) {
        throw new Error('Attorney data is not available. Please refresh the page and try again.');
      }

      // Check if assistant ID is available
      if (!attorney.vapi_assistant_id) {
        throw new Error('No assistant ID found. Please create an assistant first.');
      }

      logger.info(`Saving field ${field} with value: ${typeof value === 'object' ? JSON.stringify(value) : value}`);

      // Step 1: Update Supabase first (single source of truth)
      logger.info(`Updating attorney data in Supabase for ${attorney.id}`);
      const { error: supabaseError } = await supabase
        .from('attorneys')
        .update({ [field]: value })
        .eq('id', attorney.id);

      if (supabaseError) {
        throw new Error(`Error updating Supabase: ${supabaseError.message}`);
      }

      // Step 2: Update Vapi assistant
      logger.info(`Updating Vapi assistant ${attorney.vapi_assistant_id}`);

      // Create update data for Vapi using our utility
      const vapiUpdateData = createVapiUpdateData({ [field]: value }, assistantData);

      // Try to update assistant using MCP service first
      let updatedAssistant = null;

      try {
        // Ensure connection to Vapi MCP server
        await vapiMcpService.ensureConnection();

        // Update assistant
        updatedAssistant = await vapiMcpService.updateAssistant(
          attorney.vapi_assistant_id,
          vapiUpdateData
        );

        if (updatedAssistant) {
          logger.info(`Updated assistant using MCP service`);
        }
      } catch (mcpError) {
        logger.warn(`MCP service failed to update assistant: ${mcpError.message}, trying direct API`);
      }

      // If MCP failed, try direct API
      if (!updatedAssistant) {
        try {
          updatedAssistant = await vapiDirectApi.updateAssistant(
            attorney.vapi_assistant_id,
            vapiUpdateData
          );

          if (updatedAssistant) {
            logger.info(`Updated assistant using direct API`);
          }
        } catch (directApiError) {
          logger.warn(`Direct API failed: ${directApiError.message}, trying vapiAssistantService`);

          // Last resort: try vapiAssistantService
          try {
            // Create updated attorney data with just the edited field
            const updatedAttorneyData = {
              ...attorney,
              [field]: value
            };

            await vapiAssistantService.updateAssistantConfiguration(
              attorney.vapi_assistant_id,
              updatedAttorneyData
            );

            // Reload assistant data
            updatedAssistant = await vapiAssistantService.getAssistant(attorney.vapi_assistant_id);

            if (updatedAssistant) {
              logger.info(`Updated assistant using vapiAssistantService`);
            }
          } catch (serviceError) {
            logger.error(`All methods failed to update assistant: ${serviceError.message}`);
            throw new Error(`Failed to update assistant: ${serviceError.message}`);
          }
        }
      }

      // If we still don't have an updated assistant, show warning
      if (!updatedAssistant) {
        logger.warn(`No updated assistant data returned`);
        setError(`Warning: Supabase was updated but Vapi update may have failed`);
      } else {
        // Set assistant data
        setAssistantData(updatedAssistant);
      }

      // Notify parent component of updates
      if (onUpdate) {
        onUpdate({
          ...attorney,
          [field]: value
        });
      }

      // Remove the field from edited fields
      setEditedFields(prev => {
        const newFields = { ...prev };
        delete newFields[field];
        return newFields;
      });

      // Update hasChanges based on remaining edited fields
      setHasChanges(Object.keys(editedFields).length > 1); // > 1 because we haven't removed the current field yet

      // Show success message
      setSuccess(`Field "${field}" updated successfully`);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      logger.error(`Error saving field ${field}: ${err.message}`);
      setError(`Error saving field ${field}: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Save all changes to both Supabase and Vapi
  const handleSaveChanges = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Check if attorney data is available
      if (!attorney) {
        throw new Error('Attorney data is not available. Please refresh the page and try again.');
      }

      // Check if assistant ID is available
      if (!attorney.vapi_assistant_id) {
        throw new Error('No assistant ID found. Please create an assistant first.');
      }

      // Create updated attorney data with edited fields
      const updatedAttorneyData = {
        ...attorney,
        ...editedFields
      };

      // Step 1: Update Supabase first (single source of truth)
      logger.info(`Updating attorney data in Supabase for ${attorney.id}`);
      const { error: supabaseError } = await supabase
        .from('attorneys')
        .update(editedFields)
        .eq('id', attorney.id);

      if (supabaseError) {
        throw new Error(`Error updating Supabase: ${supabaseError.message}`);
      }

      // Step 2: Update Vapi assistant
      logger.info(`Updating Vapi assistant ${attorney.vapi_assistant_id}`);

      // Create update data for Vapi using our utility
      const vapiUpdateData = createVapiUpdateData(editedFields, assistantData);

      // Try to update assistant using MCP service first
      let updatedAssistant = null;

      try {
        // Ensure connection to Vapi MCP server
        await vapiMcpService.ensureConnection();

        // Update assistant
        updatedAssistant = await vapiMcpService.updateAssistant(
          attorney.vapi_assistant_id,
          vapiUpdateData
        );

        if (updatedAssistant) {
          logger.info(`Updated assistant using MCP service`);
        }
      } catch (mcpError) {
        logger.warn(`MCP service failed to update assistant: ${mcpError.message}, trying direct API`);
      }

      // If MCP failed, try direct API
      if (!updatedAssistant) {
        try {
          updatedAssistant = await vapiDirectApi.updateAssistant(
            attorney.vapi_assistant_id,
            vapiUpdateData
          );

          if (updatedAssistant) {
            logger.info(`Updated assistant using direct API`);
          }
        } catch (directApiError) {
          logger.warn(`Direct API failed: ${directApiError.message}, trying vapiAssistantService`);

          // Last resort: try vapiAssistantService
          try {
            await vapiAssistantService.updateAssistantConfiguration(
              attorney.vapi_assistant_id,
              updatedAttorneyData
            );

            // Reload assistant data
            updatedAssistant = await vapiAssistantService.getAssistant(attorney.vapi_assistant_id);

            if (updatedAssistant) {
              logger.info(`Updated assistant using vapiAssistantService`);
            }
          } catch (serviceError) {
            logger.error(`All methods failed to update assistant: ${serviceError.message}`);
            throw new Error(`Failed to update assistant: ${serviceError.message}`);
          }
        }
      }

      // If we still don't have an updated assistant, show warning
      if (!updatedAssistant) {
        logger.warn(`No updated assistant data returned`);
        setError(`Warning: Supabase was updated but Vapi update may have failed`);
      } else {
        // Set assistant data
        setAssistantData(updatedAssistant);
      }

      // Notify parent component of updates
      if (onUpdate) {
        onUpdate(updatedAttorneyData);
      }

      // Reset edited fields
      setEditedFields({});
      setHasChanges(false);

      // Show success message
      setSuccess('Assistant configuration updated successfully');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      logger.error(`Error saving assistant configuration: ${err.message}`);
      setError(`Error saving assistant configuration: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Create assistant if one doesn't exist
  const handleCreateAssistant = async () => {
    setLoading(true);
    setError(null);

    try {
      // Check if attorney data is available
      if (!attorney) {
        throw new Error('Attorney data is not available. Please refresh the page and try again.');
      }

      logger.info('Creating new assistant for attorney');

      // Convert attorney data to Vapi format using our utility
      const assistantConfig = attorneyToVapi(attorney);

      // Try to create assistant using MCP service first
      let assistant = null;

      try {
        // Ensure connection to Vapi MCP server
        await vapiMcpService.ensureConnection();

        // Create assistant
        assistant = await vapiMcpService.createAssistant(assistantConfig);

        if (assistant) {
          logger.info(`Created assistant using MCP service with ID ${assistant.id}`);
        }
      } catch (mcpError) {
        logger.warn(`MCP service failed to create assistant: ${mcpError.message}, trying direct API`);
      }

      // If MCP failed, try direct API
      if (!assistant) {
        try {
          assistant = await vapiDirectApi.createAssistant(assistantConfig);

          if (assistant) {
            logger.info(`Created assistant using direct API with ID ${assistant.id}`);
          }
        } catch (directApiError) {
          logger.warn(`Direct API failed: ${directApiError.message}, trying vapiAssistantService`);

          // Last resort: try vapiAssistantService
          try {
            assistant = await vapiAssistantService.createAssistantForAttorney(attorney);

            if (assistant) {
              logger.info(`Created assistant using vapiAssistantService with ID ${assistant.id}`);
            }
          } catch (serviceError) {
            logger.error(`All methods failed to create assistant: ${serviceError.message}`);
            throw new Error(`Failed to create assistant: ${serviceError.message}`);
          }
        }
      }

      // If we still don't have an assistant, show error
      if (!assistant || !assistant.id) {
        throw new Error('Failed to create assistant - no valid assistant ID returned');
      }

      logger.info(`Successfully created new assistant with ID ${assistant.id}`);

      // Update Supabase with the new assistant ID
      const { error: supabaseError } = await supabase
        .from('attorneys')
        .update({ vapi_assistant_id: assistant.id })
        .eq('id', attorney.id);

      if (supabaseError) {
        logger.error(`Error updating Supabase with new assistant ID: ${supabaseError.message}`);
        setError(`Assistant created but failed to update database: ${supabaseError.message}`);
      }

      // Update attorney with new assistant ID
      const updatedAttorney = {
        ...attorney,
        vapi_assistant_id: assistant.id
      };

      // Notify parent component of updates
      if (onUpdate) {
        onUpdate(updatedAttorney);
      }

      // Set assistant data
      setAssistantData(assistant);

      // Show success message
      setSuccess('New assistant created successfully');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      logger.error(`Error creating assistant: ${err.message}`);
      setError(`Error creating assistant: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Cancel changes
  const handleCancelChanges = () => {
    setEditedFields({});
    setHasChanges(false);
  };

  // Render component
  return (
    <div className="vapi-assistant-config">
      <h3>Voice Assistant Configuration</h3>

      {loading && <div className="loading">Loading...</div>}

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {success && (
        <div className="success-message">
          {success}
        </div>
      )}

      {/* Show loading message if attorney data is not available yet */}
      {!attorney ? (
        <div className="loading">Loading attorney data...</div>
      ) : !attorney.vapi_assistant_id || attorney.vapi_assistant_id.startsWith('mock-') ? (
        <div className="no-assistant">
          <p>
            {attorney.vapi_assistant_id && attorney.vapi_assistant_id.startsWith('mock-')
              ? 'Voice assistant needs to be properly configured. Mock assistant detected.'
              : 'No voice assistant configured for this attorney.'}
          </p>
          <button
            className="create-assistant-button"
            onClick={handleCreateAssistant}
            disabled={loading}
          >
            {attorney.vapi_assistant_id && attorney.vapi_assistant_id.startsWith('mock-')
              ? 'Fix Assistant'
              : 'Create Assistant'}
          </button>
        </div>
      ) : (
        <div className="assistant-details">
          <div className="assistant-field">
            <label>Assistant ID:</label>
            <div className="field-value">{attorney.vapi_assistant_id}</div>
            <button
              className="toggle-diagnostics-button"
              onClick={() => setShowDiagnostics(!showDiagnostics)}
            >
              {showDiagnostics ? 'Hide Diagnostics' : 'Show Diagnostics'}
            </button>
          </div>

          {showDiagnostics && (
            <div className="diagnostics-section">
              <h4>Voice Assistant Diagnostics</h4>

              <div className="diagnostics-group">
                <h5>Environment Configuration</h5>
                <pre>{JSON.stringify(diagnosticsData, null, 2)}</pre>
              </div>

              <div className="diagnostics-group">
                <h5>API Connection Test</h5>
                <button
                  className="test-connection-button"
                  onClick={async () => {
                    try {
                      setLoading(true);
                      await vapiMcpService.ensureConnection();
                      setSuccess('API connection successful');
                      setTimeout(() => setSuccess(null), 3000);
                    } catch (err) {
                      setError(`API error: ${err.status || err.statusCode || ''}\n${err.message || JSON.stringify(err)}`);
                    } finally {
                      setLoading(false);
                    }
                  }}
                  disabled={loading}
                >
                  Test Connection
                </button>
              </div>

              <div className="diagnostics-group">
                <h5>Get Assistant Test</h5>
                <div className="test-assistant-form">
                  <input
                    type="text"
                    value={attorney.vapi_assistant_id}
                    readOnly
                    placeholder="Assistant ID"
                  />
                  <button
                    className="test-assistant-button"
                    onClick={async () => {
                      try {
                        setLoading(true);
                        const assistant = await vapiMcpService.getAssistant(attorney.vapi_assistant_id);
                        if (assistant) {
                          setSuccess('Assistant found successfully');
                          setTimeout(() => setSuccess(null), 3000);
                        } else {
                          setError(`Assistant not found with ID: ${attorney.vapi_assistant_id}`);
                        }
                      } catch (err) {
                        setError(`API error: ${err.status || err.statusCode || ''}\n${err.message || JSON.stringify(err)}`);
                      } finally {
                        setLoading(false);
                      }
                    }}
                    disabled={loading}
                  >
                    Test Get Assistant
                  </button>
                </div>
              </div>
            </div>
          )}

          {assistantData && (
            <>
              <div className="assistant-field">
                <label>Name:</label>
                <div className="field-value">{assistantData.name}</div>
              </div>

              <div className="assistant-field">
                <label htmlFor="vapi-first-message">First Message:</label>
                <textarea
                  id="vapi-first-message"
                  value={editedFields.welcome_message !== undefined ? editedFields.welcome_message : (attorney.welcome_message || assistantData.firstMessage || '')}
                  onChange={(e) => handleFieldChange('welcome_message', e.target.value)}
                  placeholder="Enter first message"
                />
                {editedFields.welcome_message !== undefined && (
                  <div className="field-actions">
                    <button
                      className="cancel-button"
                      onClick={() => {
                        const newEditedFields = {...editedFields};
                        delete newEditedFields.welcome_message;
                        setEditedFields(newEditedFields);
                        setHasChanges(Object.keys(newEditedFields).length > 0);
                      }}
                      disabled={loading}
                    >
                      Discard
                    </button>
                    <button
                      className="save-button"
                      onClick={() => handleSaveField('welcome_message', editedFields.welcome_message)}
                      disabled={loading}
                    >
                      Save
                    </button>
                  </div>
                )}
              </div>

              <div className="assistant-field">
                <label htmlFor="vapi-instructions">Instructions:</label>
                <textarea
                  id="vapi-instructions"
                  value={editedFields.vapi_instructions !== undefined ? editedFields.vapi_instructions : (attorney.vapi_instructions || assistantData.instructions || '')}
                  onChange={(e) => handleFieldChange('vapi_instructions', e.target.value)}
                  placeholder="Enter instructions"
                />
                {editedFields.vapi_instructions !== undefined && (
                  <div className="field-actions">
                    <button
                      className="cancel-button"
                      onClick={() => {
                        const newEditedFields = {...editedFields};
                        delete newEditedFields.vapi_instructions;
                        setEditedFields(newEditedFields);
                        setHasChanges(Object.keys(newEditedFields).length > 0);
                      }}
                      disabled={loading}
                    >
                      Discard
                    </button>
                    <button
                      className="save-button"
                      onClick={() => handleSaveField('vapi_instructions', editedFields.vapi_instructions)}
                      disabled={loading}
                    >
                      Save
                    </button>
                  </div>
                )}
              </div>

              <div className="assistant-field">
                <label>Voice:</label>
                <div className="field-value">
                  {assistantData.voice?.provider || 'playht'} / {assistantData.voice?.voiceId || 'sarah'}
                </div>
                {/* Add voice selection dropdown here in the future */}
              </div>

              <div className="assistant-field">
                <label>AI Model:</label>
                <div className="field-value">
                  {assistantData.llm?.provider || 'openai'} / {assistantData.llm?.model || 'gpt-4o'}
                </div>
                {/* Add model selection dropdown here in the future */}
              </div>
            </>
          )}

          {hasChanges && (
            <div className="form-actions">
              <button
                className="cancel-button"
                onClick={handleCancelChanges}
                disabled={loading}
              >
                Cancel
              </button>
              <button
                className="save-button"
                onClick={handleSaveChanges}
                disabled={loading}
              >
                Save Changes
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default VapiAssistantConfig;
