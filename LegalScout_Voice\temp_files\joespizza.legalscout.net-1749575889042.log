emergency-api-key-fix.js:11 🚨 [EmergencyApiKeyFix] Starting emergency API key fix...
critical-production-fix.js:15 🚨 [CriticalProductionFix] Starting critical production fixes...
(index):22 ✅ Vapi keys set globally
(index):43 ✅ Supabase keys set globally - should load correct assistant by domain
(index):54 🚀 [EMERGENCY] Starting emergency critical fixes...
(index):58 🔧 [EMERGENCY] Adding process polyfill
(index):65 ✅ [EMERGENCY] Process polyfill added
(index):76 🔧 [EMERGENCY] Development mode: false (forced production)
(index):106 ✅ [EMERGENCY] Fetch patched with Supabase API key preservation
(index):109 🎉 [EMERGENCY] Emergency fixes complete!
robust-state-handler.js:19 🛡️ [<PERSON>ustStateHandler] Initializing comprehensive state management...
robust-state-handler.js:38 🛡️ [RobustStateHandler] Initialization attempt 1/3
disable-automatic-assistant-creation.js:9 [DisableAutomaticAssistantCreation] Starting fix...
disable-automatic-assistant-creation.js:14 [DisableAutomaticAssistantCreation] DISABLED - Orphaned assistant problem resolved
disable-automatic-assistant-creation.js:15 [DisableAutomaticAssistantCreation] Using VapiDirectApiService for complete data loading
disable-automatic-assistant-creation.js:20 [DisableAutomaticAssistantCreation] Fix script loaded - automatic assistant creation management ready
controlled-assistant-creation.js:9 [ControlledAssistantCreation] Starting controlled assistant creation system...
controlled-assistant-creation.js:178 [ControlledAssistantCreation] Controlled assistant creation system ready
standalone-attorney-manager-fixed.js:11 [StandaloneAttorneyManager] Initializing...
standalone-attorney-manager-fixed.js:308 [StandaloneAttorneyManager] Using cached Vapi configuration
standalone-attorney-manager-fixed.js:1097 [StandaloneAttorneyManager] Global instance created
standalone-attorney-manager-fixed.js:69 [StandaloneAttorneyManager] Current subdomain: joespizza
standalone-attorney-manager-fixed.js:73 [StandaloneAttorneyManager] Loading attorney for subdomain: joespizza
standalone-attorney-manager-fixed.js:83 [StandaloneAttorneyManager] localStorage doesn't have attorney for subdomain, trying Supabase
standalone-attorney-manager-fixed.js:120 [StandaloneAttorneyManager] Loading attorney from Supabase for subdomain: joespizza
fix-validate-attorney-data.js:9 [FixValidateAttorneyData] Starting fix...
fix-validate-attorney-data.js:21 [FixValidateAttorneyData] validateAttorneyData method already exists
fix-validate-attorney-data.js:135 [FixValidateAttorneyData] Fix applied immediately
fix-vapi-assistant-switch.js:11 [FixVapiAssistantSwitch] Starting fix...
fix-vapi-assistant-switch.js:103 [FixVapiAssistantSwitch] Applying fix to StandaloneAttorneyManager...
fix-vapi-assistant-switch.js:209 [FixVapiAssistantSwitch] Fix applied successfully
enhance-attorney-manager.js:11 [EnhanceAttorneyManager] Starting enhancement...
enhance-attorney-manager.js:25 [EnhanceAttorneyManager] Enhancing StandaloneAttorneyManager...
enhance-attorney-manager.js:476 [EnhanceAttorneyManager] StandaloneAttorneyManager enhanced successfully
fix-enhance-attorney-manager.js:11 [FixEnhanceAttorneyManager] Starting fix...
fix-enhance-attorney-manager.js:25 [FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...
fix-enhance-attorney-manager.js:123 [FixEnhanceAttorneyManager] Fix applied successfully
fix-vapi-assistant-config.js:5 [FixVapiAssistantConfig] Starting fix...
fix-vapi-assistant-config.js:10 [FixVapiAssistantConfig] Updating assistant configuration for web calls...
fix-vapi-assistant-config.js:43 [FixVapiAssistantConfig] Sending update request to Vapi API...
fix-vapi-assistant-config.js:81 [FixVapiAssistantConfig] Fix script loaded
unified-banner-fix.js:8 [UnifiedBannerFix] Starting unified banner fix...
unified-banner-fix.js:281 [UnifiedBannerFix] Simple banner system created
unified-banner-fix.js:166 [UnifiedBannerFix] Event listeners set up
unified-banner-fix.js:219 [UnifiedBannerFix] Interface state monitoring started
unified-banner-fix.js:226 [UnifiedBannerFix] Banner was recently removed, ensuring upload interface is visible
unified-banner-fix.js:293 [UnifiedBannerFix] Unified banner fix initialized successfully
unified-banner-fix.js:312 [UnifiedBannerFix] Unified banner fix script loaded
final-label-fix.js:9 🔧 [FinalLabelFix] Starting final label accessibility fix...
final-label-fix.js:109 🎉 [FinalLabelFix] Fixed 0 label accessibility issues
final-label-fix.js:183 ✅ [FinalLabelFix] Final label fix initialized and monitoring
test-assistant-id-propagation.js:5 🧪 [TestAssistantIDPropagation] Starting test...
test-assistant-id-propagation.js:161 ✅ [TestAssistantIDPropagation] Test script loaded
vapi-assistant-cleanup.js:5 🧹 [VapiAssistantCleanup] Starting cleanup utility...
vapi-assistant-cleanup.js:280 ✅ [VapiAssistantCleanup] Cleanup utility loaded
vapi-assistant-cleanup.js:281 💡 Usage:
vapi-assistant-cleanup.js:282   - window.vapiAssistantCleanup.runCleanup() // Dry run analysis
vapi-assistant-cleanup.js:283   - window.vapiAssistantCleanup.runCleanup({ dryRun: false }) // Actually delete
vapi-assistant-cleanup.js:284   - window.vapiAssistantCleanup.analyzeAssistants() // Just analyze
purge-assistants.js:5 🗑️ [PURGE] Starting immediate assistant purge...
purge-assistants.js:113 🚀 STARTING PURGE NOW!
purge-assistants.js:153 ✅ [PURGE] Purge script loaded and will execute in 3 seconds...
purge-assistants.js:154 🛑 To cancel, run: clearTimeout() or refresh the page
dashboard-iframe-manager.js:29 [DashboardIframeManager] Initializing Dashboard Iframe Manager
dashboard-iframe-manager.js:29 [DashboardIframeManager] Document body not ready, retrying in 100ms...
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 potential preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Found 0 accessible preview iframes
dashboard-iframe-manager.js:29 [DashboardIframeManager] Dashboard Iframe Manager initialized successfully
production-cors-fix.js:11 [ProductionCorsFix] 🔧 Starting comprehensive CORS and API fixes...
clean-auth-solution.js:9 🧹 [CleanAuthSolution] Starting clean authentication solution...
clean-auth-solution.js:202 🧹 [CleanAuthSolution] Executing clean authentication solution...
clean-auth-solution.js:164 🧹 [CleanAuthSolution] Disabling conflicting scripts...
clean-auth-solution.js:172 ✅ [CleanAuthSolution] Conflicting scripts disabled
clean-auth-solution.js:19 🧹 [CleanAuthSolution] Restoring original fetch function...
clean-auth-solution.js:32 ✅ [CleanAuthSolution] Original fetch restored
clean-auth-solution.js:85 🧹 [CleanAuthSolution] Checking for corrupted auth state...
clean-auth-solution.js:37 🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...
clean-auth-solution.js:122 🧹 [CleanAuthSolution] Setting up auth state monitoring...
clean-auth-solution.js:177 🧹 [CleanAuthSolution] Testing the solution...
clean-auth-solution.js:215 ✅ [CleanAuthSolution] Clean authentication solution complete
dashboard-iframe-manager.js:29 [DashboardIframeManager] Document body not ready, retrying in 100ms...
robust-state-handler.js:50 ⏳ [RobustStateHandler] Waiting for dependencies... {domReady: false, supabaseExists: false, supabaseHasFrom: undefined}
production-debug-vapi.js:82 Uncaught SyntaxError: Cannot use import statement outside a module (at production-debug-vapi.js:82:16)
clean-auth-solution.js:139 ✅ [CleanAuthSolution] Auth state monitoring set up
dashboard-iframe-manager.js:29 [DashboardIframeManager] Iframe observer set up successfully
clean-auth-solution.js:128 🧹 [CleanAuthSolution] Auth state changed: INITIAL_SESSION
standalone-attorney-manager-fixed.js:185 [StandaloneAttorneyManager] Supabase client is now available
standalone-attorney-manager-fixed.js:130 [StandaloneAttorneyManager] Executing Supabase query for subdomain: joespizza
fix-vapi-assistant-config.js:62 [FixVapiAssistantConfig] ✅ Assistant updated successfully: {id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', orgId: 'badbec9d-0ee3-43fb-8b18-7f26cdce3e4a', name: 'LegalScout Assistant', voice: {…}, createdAt: '2025-06-01T16:19:35.343Z', …}
fix-vapi-assistant-config.js:66 [FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls
fix-vapi-assistant-config.js:67 [FixVapiAssistantConfig] - firstMessage: Hello! How can I help you today?
fix-vapi-assistant-config.js:68 [FixVapiAssistantConfig] - model.messages: [{…}]
vapiLoader.js:30 [VapiLoader] Starting Vapi SDK loading process
vapiLoader.js:34 [VapiLoader] Attempting to import @vapi-ai/web package
supabase.js:29 Environment variables: {VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', REACT_APP_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co', VITE_SUPABASE_KEY: 'exists', VITE_SUPABASE_ANON_KEY: 'exists', REACT_APP_SUPABASE_KEY: 'exists', …}REACT_APP_SUPABASE_ANON_KEY: "exists"REACT_APP_SUPABASE_KEY: "exists"REACT_APP_SUPABASE_URL: "https://utopqxsvudgrtiwenlzl.supabase.co"VITE_SUPABASE_ANON_KEY: "exists"VITE_SUPABASE_KEY: "exists"VITE_SUPABASE_URL: "https://utopqxsvudgrtiwenlzl.supabase.co"[[Prototype]]: Object
supabase.js:40 Supabase URL configured: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:46 Supabase Key configured: eyJhb...K4cRU
supabase.js:82 Using Supabase URL: https://utopqxsvudgrtiwenlzl.supabase.co
supabase.js:99 [Supabase] Created clean fetch from iframe to bypass interceptors
supabase.js:130 Supabase client initialized successfully with proper headers
supabase.js:133 Testing Supabase connection...
supabase.js:168 Supabase client ready for use
supabase.js:176 Attaching Supabase client to window.supabase
vapiMcpService.js:94 [VapiMcpService] Created clean fetch from iframe
vapiMcpService.js:19 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
environmentVerifier.js:58 Environment Variable Verification
(index):104 
            
            
           GET https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=*&subdomain=eq.joespizza 406 (Not Acceptable)
window.fetch @ (index):104
e.720.t.resolveFetch @ supabase-js@2:7
(anonymous) @ supabase-js@2:7
o @ supabase-js@2:7
Promise.then
l @ supabase-js@2:7
(anonymous) @ supabase-js@2:7
e.720.o @ supabase-js@2:7
e.720.t.fetchWithAuth @ supabase-js@2:7
then @ supabase-js@2:7
setTimeout
(anonymous) @ standalone-attorney-manager-fixed.js:190
waitForSupabase @ standalone-attorney-manager-fixed.js:190
await in waitForSupabase
loadAttorneyBySubdomain @ standalone-attorney-manager-fixed.js:123
loadAttorneyBySubdomainIfNeeded @ standalone-attorney-manager-fixed.js:84
initializeVapiConfig @ standalone-attorney-manager-fixed.js:59
await in initializeVapiConfig
StandaloneAttorneyManager @ standalone-attorney-manager-fixed.js:43
(anonymous) @ standalone-attorney-manager-fixed.js:1095
(anonymous) @ standalone-attorney-manager-fixed.js:1098
standalone-attorney-manager-fixed.js:140 [StandaloneAttorneyManager] Error loading attorney by subdomain: {code: 'PGRST116', details: 'The result contains 0 rows', hint: null, message: 'JSON object requested, multiple (or no) rows returned'}
overrideMethod @ hook.js:608
loadAttorneyBySubdomain @ standalone-attorney-manager-fixed.js:140
await in loadAttorneyBySubdomain
loadAttorneyBySubdomainIfNeeded @ standalone-attorney-manager-fixed.js:84
initializeVapiConfig @ standalone-attorney-manager-fixed.js:59
await in initializeVapiConfig
StandaloneAttorneyManager @ standalone-attorney-manager-fixed.js:43
(anonymous) @ standalone-attorney-manager-fixed.js:1095
(anonymous) @ standalone-attorney-manager-fixed.js:1098
standalone-attorney-manager-fixed.js:141 [StandaloneAttorneyManager] Error details: {code: 'PGRST116', message: 'JSON object requested, multiple (or no) rows returned', details: 'The result contains 0 rows', hint: null}
overrideMethod @ hook.js:608
loadAttorneyBySubdomain @ standalone-attorney-manager-fixed.js:141
await in loadAttorneyBySubdomain
loadAttorneyBySubdomainIfNeeded @ standalone-attorney-manager-fixed.js:84
initializeVapiConfig @ standalone-attorney-manager-fixed.js:59
await in initializeVapiConfig
StandaloneAttorneyManager @ standalone-attorney-manager-fixed.js:43
(anonymous) @ standalone-attorney-manager-fixed.js:1095
(anonymous) @ standalone-attorney-manager-fixed.js:1098
index.ts:5 Loaded contentScript
joespizza.legalscout.net/:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
test-assistant-id-propagation.js:14 ⚠️ Assistant dropdown not found
overrideMethod @ hook.js:608
testAssistantIDPropagation @ test-assistant-id-propagation.js:14
runAllTests @ test-assistant-id-propagation.js:125
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
test-assistant-id-propagation.js:98 ⚠️ Vapi Direct API service not found
overrideMethod @ hook.js:608
testVapiAPIEndpoints @ test-assistant-id-propagation.js:98
runAllTests @ test-assistant-id-propagation.js:127
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
test-assistant-id-propagation.js:141 ⚠️ Some tests failed. Check the logs above for details.
overrideMethod @ hook.js:608
runAllTests @ test-assistant-id-propagation.js:141
(anonymous) @ test-assistant-id-propagation.js:158
setTimeout
(anonymous) @ test-assistant-id-propagation.js:156
(anonymous) @ test-assistant-id-propagation.js:162
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/840bfa3f-0b96-44d6-bb65-68c8836acce5 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 840bfa3f-0b96-44d6-bb65-68c8836acce5: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/b29f29c9-d3ff-409d-9385-55dd16a1f744 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete b29f29c9-d3ff-409d-9385-55dd16a1f744: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/246eca2b-f523-4bbe-ac77-066f4eb3616b 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 246eca2b-f523-4bbe-ac77-066f4eb3616b: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/459c8339-2b2b-4d9f-aff9-73b147ebcafa 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 459c8339-2b2b-4d9f-aff9-73b147ebcafa: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/80a0b06c-394b-40aa-987b-35ab17123efc 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 80a0b06c-394b-40aa-987b-35ab17123efc: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/a4e45c56-5f29-4171-8917-1a5fbb36dc8b 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete a4e45c56-5f29-4171-8917-1a5fbb36dc8b: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/f3298201-08e9-4d96-83e3-0b78d7460ea8 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete f3298201-08e9-4d96-83e3-0b78d7460ea8: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
clean-auth-solution.js:79 🧹 [CleanAuthSolution] Timeout waiting for Supabase
overrideMethod @ hook.js:608
(anonymous) @ clean-auth-solution.js:79
setTimeout
ensureSupabaseClient @ clean-auth-solution.js:77
executeSolution @ clean-auth-solution.js:208
(anonymous) @ clean-auth-solution.js:219
(anonymous) @ clean-auth-solution.js:221
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/39515477-15a9-4b75-81d5-095bf3bb5691 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 39515477-15a9-4b75-81d5-095bf3bb5691: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/38719ef3-28af-49ab-bcaa-2fbc436c78d8 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 38719ef3-28af-49ab-bcaa-2fbc436c78d8: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
(index):104 
            
            
           DELETE https://api.vapi.ai/assistant/6e311928-9842-438f-b933-3817a0e63b5d 404 (Not Found)
window.fetch @ (index):104
window.fetch @ emergency-api-key-fix.js:111
window.fetch @ critical-production-fix.js:89
window.fetch @ production-cors-fix.js:105
deleteAssistant @ purge-assistants.js:37
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
purge-assistants.js:50 ❌ Failed to delete 6e311928-9842-438f-b933-3817a0e63b5d: 404 - {"message":"Not Found","statusCode":404}
overrideMethod @ hook.js:608
deleteAssistant @ purge-assistants.js:50
await in deleteAssistant
purgeAllUnusedAssistants @ purge-assistants.js:80
await in purgeAllUnusedAssistants
(anonymous) @ purge-assistants.js:116
setTimeout
(anonymous) @ purge-assistants.js:114
(anonymous) @ purge-assistants.js:156
