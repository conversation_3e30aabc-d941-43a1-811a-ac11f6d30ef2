// Central service for all assistant data operations
// This ensures ALL components use the same data source and logic

export class AssistantDataService {

  // Static cache for assistant data to ensure consistency across components
  static _assistantCache = new Map();
  static _cacheTimestamp = null;
  static _cacheExpiry = 30000; // 30 seconds
  static _subscribers = new Set();

  /**
   * Subscribe to assistant data changes
   */
  static subscribe(callback) {
    this._subscribers.add(callback);
    return () => this._subscribers.delete(callback);
  }

  /**
   * Notify all subscribers of data changes
   */
  static notifySubscribers(eventType, data) {
    console.log(`📡 [AssistantDataService] Notifying ${this._subscribers.size} subscribers:`, eventType, data);
    this._subscribers.forEach(callback => {
      try {
        callback(eventType, data);
      } catch (error) {
        console.error('Error in subscriber callback:', error);
      }
    });
  }

  /**
   * Clear cache and notify subscribers
   */
  static invalidateCache() {
    this._assistantCache.clear();
    this._cacheTimestamp = null;
    this.notifySubscribers('cache_invalidated', {});
  }

  /**
   * Get all assistants for an attorney (SINGLE SOURCE OF TRUTH)
   * Used by: Dropdown, VeryCoolAssistants, Profile Page, Navigation, etc.
   * Returns enriched data with Vapi info and subdomains
   */
  static async getAssistantsForAttorney(attorneyId, forceRefresh = false) {
    const cacheKey = `attorney_${attorneyId}`;
    const now = Date.now();

    // Check cache first (unless force refresh)
    if (!forceRefresh &&
        this._assistantCache.has(cacheKey) &&
        this._cacheTimestamp &&
        (now - this._cacheTimestamp) < this._cacheExpiry) {
      console.log('📋 [AssistantDataService] Returning cached data for attorney:', attorneyId);
      return this._assistantCache.get(cacheKey);
    }

    console.log('🔄 [AssistantDataService] Loading fresh data for attorney:', attorneyId);

    try {
      const { supabase } = await import('../lib/supabase');

      // Get all assistant UI configs for this attorney
      const { data: configs, error: configError } = await supabase
        .from('assistant_ui_configs')
        .select('*')
        .eq('attorney_id', attorneyId)
        .order('created_at', { ascending: false });

      if (configError) throw configError;

      // Get subdomain mappings
      const { data: subdomains, error: subdomainError } = await supabase
        .from('assistant_subdomains')
        .select('*')
        .eq('attorney_id', attorneyId);

      if (subdomainError) throw subdomainError;

      // Create subdomain lookup
      const subdomainLookup = {};
      subdomains?.forEach(sub => {
        subdomainLookup[sub.assistant_id] = sub.subdomain;
      });

      // Enrich with Vapi data using DIRECT API (more reliable than MCP)
      const enrichedAssistants = await Promise.all(
        configs.map(async (config) => {
          try {
            // Try direct Vapi API first (more reliable)
            const VAPI_API_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
            const response = await fetch(`https://api.vapi.ai/assistant/${config.assistant_id}`, {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${VAPI_API_KEY}`,
                'Content-Type': 'application/json'
              }
            });

            if (response.ok) {
              const vapiAssistant = await response.json();
              console.log(`✅ [AssistantDataService] Loaded Vapi data for ${config.assistant_id}:`, vapiAssistant.name);

              return {
                id: config.assistant_id,
                name: vapiAssistant?.name || config.assistant_name || `Assistant ${config.assistant_id.slice(0, 8)}`,
                image_url: config.assistant_image_url,
                subdomain: subdomainLookup[config.assistant_id],
                hasConfig: true,
                config: config,
                vapiData: vapiAssistant,
                source: 'database_with_vapi_direct'
              };
            } else {
              throw new Error(`Vapi API returned ${response.status}`);
            }
          } catch (error) {
            console.warn(`⚠️ [AssistantDataService] Could not load Vapi data for ${config.assistant_id}:`, error.message);
            return {
              id: config.assistant_id,
              name: config.assistant_name || `Assistant ${config.assistant_id.slice(0, 8)}`,
              image_url: config.assistant_image_url,
              subdomain: subdomainLookup[config.assistant_id],
              hasConfig: true,
              config: config,
              vapiData: null,
              source: 'database_only'
            };
          }
        })
      );

      // Cache the results
      this._assistantCache.set(cacheKey, enrichedAssistants);
      this._cacheTimestamp = now;

      console.log(`✅ [AssistantDataService] Loaded ${enrichedAssistants.length} assistants for attorney:`, attorneyId);

      return enrichedAssistants;

    } catch (error) {
      console.error('❌ [AssistantDataService] Error loading assistants:', error);
      throw error;
    }
  }

  /**
   * Delete an assistant completely (CENTRALIZED DELETION)
   * Used by: VeryCoolAssistants, EnhancedAgentPreview, etc.
   */
  static async deleteAssistant(attorneyId, assistantId, assistantName = 'Assistant') {
    console.log(`🗑️ [AssistantDataService] Deleting assistant: ${assistantId} for attorney: ${attorneyId}`);

    try {
      const { supabase } = await import('../lib/supabase');

      // 1. Delete from Vapi first
      try {
        const VAPI_API_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
        const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${VAPI_API_KEY}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          console.warn(`⚠️ [AssistantDataService] Failed to delete from Vapi: ${response.status}`);
          // Continue with local cleanup even if Vapi deletion fails
        } else {
          console.log('✅ [AssistantDataService] Successfully deleted from Vapi');
        }
      } catch (vapiError) {
        console.warn('⚠️ [AssistantDataService] Vapi deletion failed:', vapiError);
        // Continue with local cleanup
      }

      // 2. Delete from Supabase (all related data)

      // Delete assistant UI config
      const { error: configError } = await supabase
        .from('assistant_ui_configs')
        .delete()
        .eq('assistant_id', assistantId)
        .eq('attorney_id', attorneyId);

      if (configError) {
        console.error('Error deleting UI config:', configError);
      }

      // Delete assistant subdomain
      const { error: subdomainError } = await supabase
        .from('assistant_subdomains')
        .delete()
        .eq('assistant_id', assistantId)
        .eq('attorney_id', attorneyId);

      if (subdomainError) {
        console.error('Error deleting subdomain:', subdomainError);
      }

      // Delete consultations
      const { error: consultationsError } = await supabase
        .from('consultations')
        .delete()
        .eq('assistant_id', assistantId)
        .eq('attorney_id', attorneyId);

      if (consultationsError) {
        console.error('Error deleting consultations:', consultationsError);
      }

      // Update attorney if this was their current assistant
      const { data: attorney } = await supabase
        .from('attorneys')
        .select('current_assistant_id, vapi_assistant_id')
        .eq('id', attorneyId)
        .single();

      if (attorney && (attorney.current_assistant_id === assistantId || attorney.vapi_assistant_id === assistantId)) {
        const { error: attorneyError } = await supabase
          .from('attorneys')
          .update({
            current_assistant_id: null,
            vapi_assistant_id: null,
            updated_at: new Date().toISOString()
          })
          .eq('id', attorneyId);

        if (attorneyError) {
          console.error('Error updating attorney:', attorneyError);
        }
      }

      // 3. Clear cache and notify all subscribers
      this.invalidateCache();
      this.notifySubscribers('assistant_deleted', {
        assistantId,
        assistantName,
        attorneyId
      });

      // 4. Emit global event for backward compatibility
      const deleteEvent = new CustomEvent('assistantDeleted', {
        detail: { assistantId, assistantName, attorneyId }
      });
      window.dispatchEvent(deleteEvent);

      console.log('✅ [AssistantDataService] Successfully deleted assistant and notified all components');

      return true;

    } catch (error) {
      console.error('❌ [AssistantDataService] Error deleting assistant:', error);
      throw error;
    }
  }

  /**
   * Get assistant subdomain mapping
   * Used by: Navigation, URL generation, etc.
   */
  static async getAssistantSubdomain(assistantId, attorneyId) {
    const { supabase } = await import('../lib/supabase');
    
    const { data: subdomainData, error } = await supabase
      .from('assistant_subdomains')
      .select('subdomain')
      .eq('assistant_id', assistantId)
      .eq('attorney_id', attorneyId)
      .eq('is_active', true)
      .single();

    if (error || !subdomainData) return null;
    return subdomainData.subdomain;
  }

  /**
   * Create complete assistant with ALL required records
   * Used by: Assistant creation in any component
   */
  static async createCompleteAssistant(attorneyId, assistantData) {
    console.log('🚀 [AssistantDataService] Creating complete assistant...');
    
    try {
      // 1. Create Vapi assistant using the correct service and method
      const { vapiAssistantService } = await import('./vapiAssistantService');

      // Convert assistantData to attorney format expected by the service
      const attorneyData = {
        id: attorneyId,
        firm_name: assistantData.firm_name,
        vapi_instructions: assistantData.instructions,
        first_message: assistantData.firstMessage,
        voice_provider: assistantData.voice?.provider || 'openai',
        voice_id: assistantData.voice?.voiceId || 'echo',
        ai_model: assistantData.model || 'gpt-4o',
        subdomain: assistantData.baseSubdomain
      };

      const newAssistant = await vapiAssistantService.createAssistantForAttorney(attorneyData);

      if (!newAssistant?.id) {
        throw new Error('Failed to create Vapi assistant');
      }

      console.log('✅ [AssistantDataService] Vapi assistant created:', newAssistant.id);

      // 2. Create assistant UI config (CRITICAL)
      await this.createAssistantUIConfig(attorneyId, newAssistant.id, assistantData);

      // 3. Create subdomain mapping (CRITICAL)
      await this.createAssistantSubdomain(attorneyId, newAssistant.id, assistantData);

      // 4. Create attorney-assistant mapping (compatibility)
      await this.createAttorneyAssistantMapping(attorneyId, newAssistant.id);

      console.log('✅ [AssistantDataService] Complete assistant created successfully');
      return newAssistant;

    } catch (error) {
      console.error('❌ [AssistantDataService] Failed to create complete assistant:', error);
      throw error;
    }
  }

  /**
   * Create assistant UI config record
   */
  static async createAssistantUIConfig(attorneyId, assistantId, assistantData) {
    const { assistantUIConfigService } = await import('./assistantUIConfigService');

    await assistantUIConfigService.createDefaultConfig(
      attorneyId,
      assistantId,
      {
        assistant_name: assistantData.name,
        firm_name: assistantData.firm_name,
        vapi_instructions: assistantData.instructions,
        voice_provider: assistantData.voice?.provider || 'openai',
        voice_id: assistantData.voice?.voiceId || 'echo',
        ai_model: assistantData.model || 'gpt-4o'
      }
    );

    console.log('✅ [AssistantDataService] Assistant UI config created');
  }

  /**
   * Create assistant subdomain mapping
   */
  static async createAssistantSubdomain(attorneyId, assistantId, assistantData) {
    const { assistantSubdomainService } = await import('./assistantSubdomainService');
    
    // Generate unique subdomain
    const baseSubdomain = assistantData.baseSubdomain || 'assistant';
    const uniqueSubdomain = await this.generateUniqueSubdomain(baseSubdomain, assistantId);
    
    await assistantSubdomainService.createSubdomainForAssistant(
      assistantId,
      attorneyId,
      uniqueSubdomain,
      false // Not primary
    );
    
    console.log('✅ [AssistantDataService] Assistant subdomain created:', uniqueSubdomain);
  }

  /**
   * Create attorney-assistant mapping (for compatibility)
   */
  static async createAttorneyAssistantMapping(attorneyId, assistantId) {
    const { supabase } = await import('../lib/supabase');
    
    const { error } = await supabase
      .from('attorney_assistants')
      .insert({
        attorney_id: attorneyId,
        assistant_id: assistantId,
        is_active: true,
        created_at: new Date().toISOString()
      });

    if (error) {
      console.warn('[AssistantDataService] Could not create attorney-assistant mapping:', error);
    } else {
      console.log('✅ [AssistantDataService] Attorney-assistant mapping created');
    }
  }

  /**
   * Generate unique subdomain for assistant
   */
  static async generateUniqueSubdomain(baseSubdomain, assistantId) {
    const { supabase } = await import('../lib/supabase');
    
    // Try base subdomain first
    let subdomain = baseSubdomain;
    let counter = 1;
    
    while (true) {
      const { data, error } = await supabase
        .from('assistant_subdomains')
        .select('subdomain')
        .eq('subdomain', subdomain)
        .single();

      // If no record found, subdomain is available
      if (error && error.code === 'PGRST116') {
        return subdomain;
      }

      // If subdomain exists, try next variation
      counter++;
      subdomain = `${baseSubdomain}-${counter}`;
      
      // Safety limit
      if (counter > 100) {
        // Fallback to assistant ID suffix
        return `${baseSubdomain}-${assistantId.slice(0, 8)}`;
      }
    }
  }

  /**
   * Sync assistant name from Vapi to database
   */
  static async syncAssistantNameFromVapi(attorneyId, assistantId) {
    try {
      const { vapiAssistantService } = await import('./vapiAssistantService');

      // Add timeout and better error handling
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Vapi request timeout')), 10000)
      );

      const assistantPromise = vapiAssistantService.getAssistant(assistantId);
      const assistantData = await Promise.race([assistantPromise, timeoutPromise]);

      if (assistantData?.name && !assistantData.mock) {
        const { assistantUIConfigService } = await import('./assistantUIConfigService');
        await assistantUIConfigService.saveAssistantConfig(
          attorneyId,
          assistantId,
          { assistant_name: assistantData.name }
        );

        console.log('✅ [AssistantDataService] Synced assistant name from Vapi:', assistantData.name);
        return assistantData.name;
      } else if (assistantData?.mock) {
        console.warn('[AssistantDataService] Received mock assistant data, skipping sync');
        return null;
      }
    } catch (error) {
      console.warn('[AssistantDataService] Could not sync assistant name from Vapi:', error.message);

      // Don't throw error, just return null to allow graceful degradation
      if (error.message.includes('timeout')) {
        console.warn('[AssistantDataService] Vapi request timed out, continuing without sync');
      }
    }
    return null;
  }

  /**
   * Generate unique assistant name
   */
  static generateUniqueAssistantName(firmName, existingAssistants = []) {
    const timestamp = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
    const assistantNumber = existingAssistants.length + 1;
    const baseName = firmName || 'LegalScout';
    
    return `${baseName} Agent ${assistantNumber} (${timestamp})`;
  }
}

export default AssistantDataService;
