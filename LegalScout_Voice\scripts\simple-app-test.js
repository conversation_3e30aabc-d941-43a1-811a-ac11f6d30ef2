#!/usr/bin/env node

/**
 * LegalScout Voice - Simple App Test
 * 
 * This script performs basic tests that can help you debug your app quickly.
 * It's designed to be simple, fast, and provide clear feedback.
 * 
 * Usage:
 *   npm run test:simple
 *   node scripts/simple-app-test.js [options]
 * 
 * Options:
 *   --local        Test local development server
 *   --production   Test production deployment
 *   --verbose      Verbose output
 *   --help         Show help
 */

import { config } from 'dotenv';

// Load environment variables
config();

// Configuration
const DEFAULT_CONFIG = {
  local: false,
  production: false,
  verbose: false
};

// Test URLs
const URLS = {
  local: 'http://localhost:5174',
  production: 'https://dashboard.legalscout.net'
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [SimpleTest]`;
  
  switch(type) {
    case 'success': console.log(`✅ ${prefix} ${message}`); break;
    case 'error': console.error(`❌ ${prefix} ${message}`); break;
    case 'warning': console.warn(`⚠️ ${prefix} ${message}`); break;
    default: console.log(`ℹ️ ${prefix} ${message}`);
  }
}

// Test 1: Environment Check
function testEnvironment() {
  log('🔍 Testing environment setup...', 'info');
  
  const requiredVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_KEY', 
    'VITE_VAPI_PUBLIC_KEY',
    'VITE_VAPI_SECRET_KEY'
  ];
  
  let allPresent = true;
  
  for (const varName of requiredVars) {
    if (process.env[varName]) {
      log(`✓ ${varName} is set`, 'success');
    } else {
      log(`✗ ${varName} is missing`, 'error');
      allPresent = false;
    }
  }
  
  return allPresent;
}

// Test 2: Basic API Test
async function testAPIs() {
  log('🔍 Testing API connectivity...', 'info');
  
  let allWorking = true;
  
  // Test Supabase
  try {
    const { default: fetch } = await import('node-fetch');
    const supabaseUrl = process.env.VITE_SUPABASE_URL;
    
    if (supabaseUrl) {
      const response = await fetch(`${supabaseUrl}/rest/v1/`, {
        method: 'HEAD',
        timeout: 5000
      });
      
      if (response.ok || response.status === 401) {
        log('✓ Supabase API is accessible', 'success');
      } else {
        log(`✗ Supabase API returned ${response.status}`, 'error');
        allWorking = false;
      }
    }
  } catch (error) {
    if (error.code !== 'MODULE_NOT_FOUND') {
      log(`✗ Supabase API test failed: ${error.message}`, 'error');
      allWorking = false;
    }
  }
  
  // Test Vapi
  try {
    const { default: fetch } = await import('node-fetch');
    const vapiKey = process.env.VITE_VAPI_PUBLIC_KEY;
    
    if (vapiKey) {
      const response = await fetch('https://api.vapi.ai/assistant', {
        method: 'HEAD',
        headers: {
          'Authorization': `Bearer ${vapiKey}`
        },
        timeout: 5000
      });
      
      if (response.ok || response.status === 401 || response.status === 403) {
        log('✓ Vapi API is accessible', 'success');
      } else {
        log(`✗ Vapi API returned ${response.status}`, 'error');
        allWorking = false;
      }
    }
  } catch (error) {
    if (error.code !== 'MODULE_NOT_FOUND') {
      log(`✗ Vapi API test failed: ${error.message}`, 'error');
      allWorking = false;
    }
  }
  
  return allWorking;
}

// Test 3: App URL Test
async function testAppURL(config) {
  log('🔍 Testing app URL accessibility...', 'info');
  
  const url = config.production ? URLS.production : URLS.local;
  
  try {
    const { default: fetch } = await import('node-fetch');
    
    const response = await fetch(url, {
      method: 'HEAD',
      timeout: 10000
    });
    
    if (response.ok) {
      log(`✓ App is accessible at ${url}`, 'success');
      return true;
    } else {
      log(`✗ App returned ${response.status} at ${url}`, 'error');
      return false;
    }
  } catch (error) {
    if (error.code !== 'MODULE_NOT_FOUND') {
      log(`✗ App URL test failed: ${error.message}`, 'error');
      if (config.local) {
        log('💡 Tip: Make sure your development server is running (npm run dev:full)', 'warning');
      }
      return false;
    }
    return true; // Skip test if fetch not available
  }
}

// Test 4: Critical Files Check
async function testCriticalFiles() {
  log('🔍 Testing critical files...', 'info');

  const fs = await import('fs');
  const path = await import('path');
  
  const criticalFiles = [
    'src/App.jsx',
    'src/lib/supabase.js',
    'src/config/vapiConfig.js',
    'api/index.js',
    'package.json',
    '.env'
  ];
  
  let allPresent = true;
  
  for (const file of criticalFiles) {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      log(`✓ ${file} exists`, 'success');
    } else {
      log(`✗ ${file} is missing`, 'error');
      allPresent = false;
    }
  }
  
  return allPresent;
}

// Main test function
async function runSimpleTests(config) {
  log('🚀 Starting Simple App Tests', 'info');
  
  if (config.verbose) {
    log('Configuration:', 'info');
    console.log(JSON.stringify(config, null, 2));
  }
  
  const results = {
    environment: false,
    apis: false,
    appUrl: false,
    files: false
  };
  
  try {
    // Run tests
    results.environment = testEnvironment();
    results.apis = await testAPIs();
    results.appUrl = await testAppURL(config);
    results.files = await testCriticalFiles();
    
    // Summary
    const passed = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;
    const successRate = Math.round((passed / total) * 100);
    
    log('\n📊 Test Results:', 'info');
    log(`Environment Setup: ${results.environment ? 'PASS' : 'FAIL'}`, 
        results.environment ? 'success' : 'error');
    log(`API Connectivity: ${results.apis ? 'PASS' : 'FAIL'}`, 
        results.apis ? 'success' : 'error');
    log(`App Accessibility: ${results.appUrl ? 'PASS' : 'FAIL'}`, 
        results.appUrl ? 'success' : 'error');
    log(`Critical Files: ${results.files ? 'PASS' : 'FAIL'}`, 
        results.files ? 'success' : 'error');
    
    log(`\nOverall: ${passed}/${total} tests passed (${successRate}%)`, 
        successRate >= 75 ? 'success' : 'warning');
    
    // Recommendations
    if (!results.environment) {
      log('💡 Fix: Check your .env file and ensure all required variables are set', 'warning');
    }
    if (!results.apis) {
      log('💡 Fix: Check your API keys and network connectivity', 'warning');
    }
    if (!results.appUrl && config.local) {
      log('💡 Fix: Start your development server with: npm run dev:full', 'warning');
    }
    if (!results.files) {
      log('💡 Fix: Restore missing files from your repository', 'warning');
    }
    
    return { success: passed === total, results };
    
  } catch (error) {
    log(`💥 Test suite failed: ${error.message}`, 'error');
    return { success: false, error: error.message };
  }
}

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const config = { ...DEFAULT_CONFIG };

  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--local':
        config.local = true;
        break;
      case '--production':
        config.production = true;
        break;
      case '--verbose':
        config.verbose = true;
        break;
      case '--help':
        showHelp();
        process.exit(0);
        break;
      default:
        console.error(`Unknown option: ${args[i]}`);
        process.exit(1);
    }
  }

  // Default to local if neither specified
  if (!config.local && !config.production) {
    config.local = true;
  }

  return config;
}

// Show help
function showHelp() {
  console.log(`
LegalScout Voice - Simple App Test

Usage: node scripts/simple-app-test.js [options]

Options:
  --local        Test local development server (default)
  --production   Test production deployment
  --verbose      Verbose output
  --help         Show this help

Examples:
  node scripts/simple-app-test.js
  node scripts/simple-app-test.js --local --verbose
  node scripts/simple-app-test.js --production
`);
}

// Main function
async function main() {
  const config = parseArgs();
  const result = await runSimpleTests(config);
  
  process.exit(result.success ? 0 : 1);
}

// Handle errors
process.on('unhandledRejection', (error) => {
  log(`Unhandled rejection: ${error.message}`, 'error');
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  log(`Uncaught exception: ${error.message}`, 'error');
  process.exit(1);
});

// Run if called directly
if (process.argv[1] && process.argv[1].endsWith('simple-app-test.js')) {
  main();
}

export { runSimpleTests, parseArgs };
