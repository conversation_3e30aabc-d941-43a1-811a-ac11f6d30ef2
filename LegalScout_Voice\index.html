<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LegalScout</title>

    <!-- Essential CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />

    <!-- MINIMAL PRODUCTION ENVIRONMENT SETUP -->
    <script>
      // Essential environment variables - inline for reliability
      (function() {
        console.log('🚀 [LegalScout] Initializing environment...');

        // Set essential environment variables
        const envVars = {
          'VITE_SUPABASE_URL': 'https://utopqxsvudgrtiwenlzl.supabase.co',
          'VITE_SUPABASE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU',
          'VITE_VAPI_PUBLIC_KEY': '310f0d43-27c2-47a5-a76d-e55171d024f7',
          'VITE_VAPI_SECRET_KEY': '6734febc-fc65-4669-93b0-929b31ff6564'
        };

        // Set on window for immediate access
        Object.keys(envVars).forEach(key => {
          window[key] = envVars[key];
        });

        // Ensure process.env exists for compatibility
        if (!window.process) {
          window.process = { env: envVars, browser: true };
        } else {
          Object.assign(window.process.env, envVars);
        }

        console.log('✅ [LegalScout] Environment initialized');
      })();
    </script>

    <!-- Inline Authentication Test Function -->
    <script>
      // Simple inline test function that works immediately
      window.fixMyAuth = async function() {
        console.log('🔧 [FixMyAuth] Starting authentication fix...');

        try {
          // Get Supabase client
          const { getSupabaseClient } = await import('/src/lib/supabase.js');
          const supabase = await getSupabaseClient();

          // Get current user
          const { data: { user }, error: authError } = await supabase.auth.getUser();
          if (authError || !user) {
            console.error('❌ [FixMyAuth] Not authenticated:', authError);
            return { success: false, error: 'Not authenticated' };
          }

          console.log('✅ [FixMyAuth] User authenticated:', user.email);

          // Get all attorney profiles for this email
          const { data: attorneys, error: attorneyError } = await supabase
            .from('attorneys')
            .select('*')
            .eq('email', user.email)
            .order('created_at', { ascending: false });

          if (attorneyError) {
            console.error('❌ [FixMyAuth] Attorney lookup failed:', attorneyError);
            return { success: false, error: 'Attorney lookup failed' };
          }

          console.log('📋 [FixMyAuth] Found', attorneys?.length || 0, 'attorney profiles');

          if (attorneys && attorneys.length > 0) {
            // Smart selection: prefer profile with assistant ID
            let selectedProfile = attorneys[0]; // Default to newest

            const profileWithAssistant = attorneys.find(profile =>
              profile.vapi_assistant_id &&
              profile.vapi_assistant_id !== 'null' &&
              profile.vapi_assistant_id.trim() !== ''
            );

            if (profileWithAssistant) {
              selectedProfile = profileWithAssistant;
              console.log('🎯 [FixMyAuth] Selected profile with assistant ID:', selectedProfile.vapi_assistant_id);
            } else {
              console.log('📝 [FixMyAuth] Selected newest profile (no assistant ID)');
            }

            // Store in localStorage
            localStorage.setItem('attorney', JSON.stringify(selectedProfile));
            console.log('💾 [FixMyAuth] Attorney profile stored in localStorage');

            console.log('✅ [FixMyAuth] Success! Profile details:', {
              id: selectedProfile.id,
              email: selectedProfile.email,
              firm_name: selectedProfile.firm_name,
              subdomain: selectedProfile.subdomain,
              vapi_assistant_id: selectedProfile.vapi_assistant_id
            });

            // Refresh the page to load with correct profile
            console.log('🔄 [FixMyAuth] Refreshing page to load correct profile...');
            setTimeout(() => {
              window.location.reload();
            }, 2000);

            return {
              success: true,
              attorney: selectedProfile,
              message: 'Authentication fixed! Page will refresh...'
            };
          } else {
            console.log('❌ [FixMyAuth] No attorney profiles found for:', user.email);
            return { success: false, error: 'No attorney profiles found' };
          }

        } catch (error) {
          console.error('❌ [FixMyAuth] Fix failed:', error);
          return { success: false, error: error.message };
        }
      };

      console.log('🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.');
    </script>

    <!-- Inline Authentication Test (since external scripts are failing) -->
    <script>
      window.testAuthFlowInline = async function() {
        console.log('🧪 [TestAuthFlow] Starting inline authentication test...');

        try {
          // Import Supabase client
          const { getSupabaseClient } = await import('/src/lib/supabase.js');
          const supabase = await getSupabaseClient();

          // Get current user
          const { data: { user }, error: authError } = await supabase.auth.getUser();

          if (authError || !user) {
            console.error('❌ [TestAuthFlow] Not authenticated:', authError);
            return { success: false, error: 'Not authenticated' };
          }

          console.log('✅ [TestAuthFlow] User authenticated:', user.email);
          console.log('🔍 [TestAuthFlow] User ID:', user.id);

          // Get all attorney profiles for this email
          const { data: attorneys, error: attorneyError } = await supabase
            .from('attorneys')
            .select('*')
            .eq('email', user.email)
            .order('created_at', { ascending: false });

          if (attorneyError) {
            console.error('❌ [TestAuthFlow] Attorney lookup error:', attorneyError);
            return { success: false, error: 'Attorney lookup failed' };
          }

          console.log('📋 [TestAuthFlow] Found', attorneys?.length || 0, 'attorney profiles:');
          attorneys?.forEach((attorney, index) => {
            console.log(`  ${index + 1}. ID: ${attorney.id}`);
            console.log(`     Subdomain: ${attorney.subdomain}`);
            console.log(`     Assistant: ${attorney.vapi_assistant_id || 'NONE'}`);
            console.log(`     Created: ${attorney.created_at}`);
            console.log(`     User ID: ${attorney.user_id}`);
          });

          // Apply your smart selection logic
          let selectedProfile = attorneys?.[0]; // Default to newest

          if (attorneys?.length > 0) {
            const profileWithAssistant = attorneys.find(profile =>
              profile.vapi_assistant_id &&
              profile.vapi_assistant_id !== 'null' &&
              profile.vapi_assistant_id.trim() !== ''
            );

            if (profileWithAssistant) {
              selectedProfile = profileWithAssistant;
              console.log('🎯 [TestAuthFlow] Selected profile WITH assistant:', selectedProfile.vapi_assistant_id);
            } else {
              console.log('📝 [TestAuthFlow] Selected newest profile (no assistant found)');
            }

            // Store in localStorage
            localStorage.setItem('attorney', JSON.stringify(selectedProfile));
            console.log('💾 [TestAuthFlow] Attorney stored in localStorage');

            // Force page refresh to load the correct profile
            console.log('🔄 [TestAuthFlow] Refreshing page to load correct profile...');
            setTimeout(() => {
              window.location.reload();
            }, 1000);

            return {
              success: true,
              attorney: selectedProfile,
              message: 'Attorney profile selected and stored'
            };
          }

          return { success: false, error: 'No attorney profiles found' };

        } catch (error) {
          console.error('❌ [TestAuthFlow] Test failed:', error);
          return { success: false, error: error.message };
        }
      };

      console.log('🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.');
    </script>

  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>