import React, { useEffect, useRef, useState } from 'react';
import './SpeechParticles.css';
import './SpeechMistVisualization.css';

const SpeechParticles = ({ className, primaryColor = '#4B74AA', secondaryColor = '#2C3E50' }) => {
  const canvasRef = useRef(null);
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const microphoneStreamRef = useRef(null);
  const animationFrameRef = useRef(null);
  const [microphoneActive, setMicrophoneActive] = useState(false);
  const [userSpeaking, setUserSpeaking] = useState(false);
  const userSpeakingTimeoutRef = useRef(null);

  // Initialize the visualization
  useEffect(() => {
    console.log('SpeechParticles: Initializing component');

    // Wait for canvas to be available before loading script
    const waitForCanvas = () => {
      const canvas = canvasRef.current;
      if (!canvas) {
        console.log('SpeechParticles: Canvas not ready, waiting...');
        setTimeout(waitForCanvas, 100);
        return;
      }

      console.log('SpeechParticles: Canvas found, loading script');
      loadSpeechParticlesScript();
    };

    const loadSpeechParticlesScript = () => {
      // Check if script is already loaded
      if (window.updateAudioSource) {
        console.log('SpeechParticles: Script already loaded, configuring...');
        configureSpeechParticles();
        return;
      }

      // Load the speech particles script
      const script = document.createElement('script');
      script.src = '/speech-particles.js';
      script.async = true;

      // Add event listeners to track script loading
      script.onload = () => {
        console.log('SpeechParticles: Script loaded successfully');
        configureSpeechParticles();
      };

      script.onerror = (error) => {
        console.error('SpeechParticles: Error loading script:', error);
      };

      document.body.appendChild(script);
    };

    const configureSpeechParticles = () => {
      if (window.updateAudioSource) {
        console.log('SpeechParticles: updateAudioSource function is available');

        // Configure colors for the speech particles
        if (window.setSpeechParticleColors) {
          console.log('🎨 SpeechParticles: About to set colors - User:', primaryColor, 'Assistant:', secondaryColor);
          window.setSpeechParticleColors({
            userColor: primaryColor,      // Use primary color for user
            assistantColor: secondaryColor // Use secondary color for assistant
          });
          console.log('🎨 SpeechParticles: Colors configured successfully');
        } else {
          console.warn('🎨 SpeechParticles: setSpeechParticleColors function not available yet');
        }

        // Force a user interaction to ensure audio context can start
        // This helps with browsers that require user interaction to start audio
        const resumeAudioContext = () => {
          if (audioContextRef.current && audioContextRef.current.state === 'suspended') {
            audioContextRef.current.resume().then(() => {
              console.log('SpeechParticles: Audio context resumed by user interaction');
            });
          }
        };

        // Add listeners to common user interactions
        document.addEventListener('click', resumeAudioContext, { once: true });
        document.addEventListener('touchstart', resumeAudioContext, { once: true });
        document.addEventListener('keydown', resumeAudioContext, { once: true });

        console.log('SpeechParticles: Added user interaction listeners for audio context');

        // If microphone is already active but processing hasn't started, start it now
        if (microphoneActive && analyserRef.current && !animationFrameRef.current) {
          console.log('SpeechParticles: Script loaded and microphone is ready, starting processing');
          processMicrophoneInput();
        }
      } else {
        console.error('SpeechParticles: updateAudioSource function is not available');
      }
    };

    // Start the process
    waitForCanvas();

    // Clean up
    return () => {
      console.log('SpeechParticles: Cleaning up component');

      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      if (microphoneStreamRef.current) {
        microphoneStreamRef.current.getTracks().forEach(track => track.stop());
      }

      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close();
      }
    };
  }, [primaryColor, secondaryColor]); // Re-run when colors change

  // Set up microphone input processing
  useEffect(() => {
    const setupMicrophone = async () => {
      try {
        // Skip microphone setup if Vapi call is active to prevent audio context conflicts
        // But still allow the particles to work with Vapi audio events
        if (window.vapiCallActive && window.vapiCallActive !== false) {
          console.log('SpeechParticles: Skipping microphone setup - Vapi call is active, will use Vapi audio events instead');
          setMicrophoneActive(false); // Don't use our own microphone
          return;
        }

        console.log('SpeechParticles: Setting up microphone...');

        // Check if audio context already exists and is not closed
        if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
          console.log('SpeechParticles: Audio context already exists, reusing...');
        } else {
          // Create audio context with explicit options for better compatibility
          const AudioContext = window.AudioContext || window.webkitAudioContext;
          audioContextRef.current = new AudioContext({
            latencyHint: 'interactive',
            sampleRate: 44100
          });
          console.log('SpeechParticles: New audio context created');
        }

        // Resume audio context (needed for some browsers)
        if (audioContextRef.current.state === 'suspended') {
          await audioContextRef.current.resume();
          console.log('SpeechParticles: Audio context resumed');
        }

        // Only create analyser if it doesn't exist or if audio context was recreated
        if (!analyserRef.current || audioContextRef.current.state === 'closed') {
          // Create and configure analyser with more sensitive settings
          analyserRef.current = audioContextRef.current.createAnalyser();
          analyserRef.current.fftSize = 1024; // Smaller FFT size for faster response
          analyserRef.current.smoothingTimeConstant = 0.5; // Less smoothing for more responsive visualization
          analyserRef.current.minDecibels = -85; // Lower threshold to detect quieter sounds
          analyserRef.current.maxDecibels = -30; // Upper threshold for louder sounds
          console.log('SpeechParticles: Analyser created and configured');
        }

        console.log('SpeechParticles: Requesting microphone access...');

        // Get microphone stream with explicit constraints
        const constraints = {
          audio: {
            echoCancellation: false, // Disable to get raw audio
            noiseSuppression: false, // Disable to get raw audio
            autoGainControl: false   // Disable to get raw audio
          }
        };

        const stream = await navigator.mediaDevices.getUserMedia(constraints);
        microphoneStreamRef.current = stream;

        console.log('SpeechParticles: Microphone access granted, connecting to analyser...');

        // Only create audio nodes if context is not closed
        if (audioContextRef.current.state !== 'closed') {
          // Create a gain node to boost the microphone signal
          const gainNode = audioContextRef.current.createGain();
          gainNode.gain.value = 3.0; // Significantly boost the signal (increased from 1.5)

          // Connect microphone to gain node to analyser (proper audio graph)
          const source = audioContextRef.current.createMediaStreamSource(stream);
          source.connect(gainNode);
          gainNode.connect(analyserRef.current);

          console.log('SpeechParticles: Microphone connected with boosted gain:', gainNode.gain.value);
        } else {
          console.warn('SpeechParticles: Audio context is closed, cannot create audio nodes');
          return;
        }

        console.log('SpeechParticles: Microphone setup complete, starting processing...');
        setMicrophoneActive(true);

        // Start processing microphone input only if speech particles script is loaded
        if (window.updateAudioSource) {
          console.log('SpeechParticles: Speech particles script is ready, starting microphone processing');
          processMicrophoneInput();
        } else {
          console.log('SpeechParticles: Speech particles script not ready yet, will wait for it to load');
          // Don't start processing yet - it will be started when the script loads
        }
      } catch (error) {
        console.error('SpeechParticles: Error accessing microphone:', error);

        // Try to provide more helpful error messages
        if (error.name === 'NotAllowedError') {
          console.error('SpeechParticles: Microphone access denied by user or system');
        } else if (error.name === 'NotFoundError') {
          console.error('SpeechParticles: No microphone found on this device');
        }
      }
    };

    // Only set up microphone if the canvas is ready
    if (canvasRef.current) {
      setupMicrophone();
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      // Clear user speaking timeout
      if (userSpeakingTimeoutRef.current) {
        clearTimeout(userSpeakingTimeoutRef.current);
      }

      // Properly clean up microphone resources
      if (microphoneStreamRef.current) {
        console.log('SpeechParticles: Stopping microphone tracks');
        microphoneStreamRef.current.getTracks().forEach(track => track.stop());
        microphoneStreamRef.current = null;
      }

      // Don't close audio context immediately - let it be reused
      // Only close if component is truly unmounting
    };
  }, [canvasRef.current]);

  // Process microphone input and update visualization
  const processMicrophoneInput = () => {
    // Check if we have all required references
    if (!analyserRef.current || !microphoneActive || !window.updateAudioSource) {
      // Log the specific missing references for debugging
      const missing = [];
      if (!analyserRef.current) missing.push('analyserRef');
      if (!microphoneActive) missing.push('microphoneActive');
      if (!window.updateAudioSource) missing.push('window.updateAudioSource');

      console.log('SpeechParticles: Missing required references:', missing.join(', '));

      // Don't retry indefinitely - stop after a reasonable number of attempts
      // This prevents the infinite loop that's causing the console spam
      return;
    }

    try {
      // Get both frequency and time domain data for better analysis
      const bufferLength = analyserRef.current.frequencyBinCount;
      const freqDataArray = new Uint8Array(bufferLength);
      const timeDataArray = new Uint8Array(bufferLength);

      analyserRef.current.getByteFrequencyData(freqDataArray);
      analyserRef.current.getByteTimeDomainData(timeDataArray);

      // Calculate RMS amplitude from time domain data (more accurate for voice)
      let sumSquares = 0;
      for (let i = 0; i < bufferLength; i++) {
        // Convert from 0-255 to -1 to 1 range
        const amplitude = (timeDataArray[i] / 128) - 1;
        sumSquares += amplitude * amplitude;
      }
      const rms = Math.sqrt(sumSquares / bufferLength);

      // Calculate average from frequency data (good for detecting speech frequencies)
      let freqSum = 0;
      for (let i = 0; i < bufferLength; i++) {
        freqSum += freqDataArray[i];
      }
      const freqAverage = freqSum / bufferLength;

      // Use the higher of the two methods for better sensitivity
      // Apply a higher weight to the RMS value as it's better for voice detection
      const rmsWeighted = rms * 1.5; // Boost RMS value
      const freqWeighted = freqAverage / 100; // Increased from 128 for more sensitivity
      const rawAmplitude = Math.max(rmsWeighted, freqWeighted);
      const normalizedAmplitude = Math.min(rawAmplitude, 1); // Normalize to 0-1 range

      // Calculate dominant frequency - focus on speech frequencies
      let maxValue = 0;
      let dominantIndex = 0;

      // Focus on the frequency range most relevant to human speech (roughly 80-600 Hz)
      const minFreq = 80;
      const maxFreq = 600;
      const nyquist = audioContextRef.current.sampleRate / 2;
      const minIndex = Math.floor(bufferLength * minFreq / nyquist);
      const maxIndex = Math.floor(bufferLength * maxFreq / nyquist);

      for (let i = minIndex; i <= maxIndex; i++) {
        if (freqDataArray[i] > maxValue) {
          maxValue = freqDataArray[i];
          dominantIndex = i;
        }
      }

      // Convert index to frequency in Hz
      const dominantFrequency = dominantIndex * audioContextRef.current.sampleRate / (bufferLength * 2);

      // Extremely low threshold for maximum sensitivity
      if (normalizedAmplitude > 0.005) { // Even lower threshold to catch very quiet speech
        // Apply very aggressive scaling to make it much more visible
        const scaledAmplitude = Math.min(normalizedAmplitude * 10, 1); // 10x scaling for maximum visibility

        // Force a minimum amplitude to ensure visibility even with quiet sounds
        const finalAmplitude = Math.max(0.5, scaledAmplitude); // Increased minimum for better visibility

        // Log EVERY detection for debugging
        console.log('🎤 SpeechParticles: User microphone input detected:',
          finalAmplitude.toFixed(2),
          'Raw:', normalizedAmplitude.toFixed(4),
          'Scaled:', scaledAmplitude.toFixed(2),
          'Freq:', Math.round(dominantFrequency) + 'Hz',
          'Colors:', { primaryColor, secondaryColor });

        // Set user speaking state
        setUserSpeaking(true);

        // Clear any existing timeout
        if (userSpeakingTimeoutRef.current) {
          clearTimeout(userSpeakingTimeoutRef.current);
        }

        // Update visualization with user audio - using the boosted amplitude
        window.updateAudioSource(finalAmplitude, dominantFrequency, 'user');

        // Also log to console to verify it's being called with detailed info
        console.log('🔵 USER PARTICLES: Calling updateAudioSource with amplitude', finalAmplitude.toFixed(2), 'speaker: user', 'primaryColor:', primaryColor);

        // Set a timeout to keep user speaking active for a bit longer
        userSpeakingTimeoutRef.current = setTimeout(() => {
          console.log('Speech particles: user speaking timeout - stopping user particles');
          setUserSpeaking(false);
        }, 300); // Keep user particles for 300ms after they stop speaking
      } else {
        // Don't immediately set user not speaking - let the timeout handle it
        // This prevents Vapi events from immediately overriding user microphone

        // Only clear user particles if we're not in the timeout period
        if (!userSpeaking) {
          // When user is silent, set amplitude to null to allow fallback to ambient mode
          window.updateAudioSource(null, null, null);
        }

        // Log occasional silence for debugging
        if (Math.random() < 0.005) { // Only log occasionally to avoid console spam
          console.log('SpeechParticles: User microphone silent, raw level:', normalizedAmplitude.toFixed(4));
        }
      }
    } catch (error) {
      console.error('SpeechParticles: Error processing microphone input:', error);
    }

    // Continue processing in the next frame only if we have all required references
    if (analyserRef.current && microphoneActive && window.updateAudioSource) {
      animationFrameRef.current = requestAnimationFrame(processMicrophoneInput);
    }
  };

  // Set up Vapi audio level event listener
  useEffect(() => {
    const handleVapiAudioLevel = (event) => {
      if (!event.data || !window.updateAudioSource) return;

      console.log('SpeechParticles: Received window message event:', event.data);

      // Check if this is a Vapi iframe message with audio level data
      if (event.data.what === 'iframe-call-message' &&
          event.data.action === 'remote-participants-audio-level' &&
          event.data.participantsAudioLevel) {

        console.log('SpeechParticles: Processing audio level event:', event.data.participantsAudioLevel);

        // Extract audio levels
        const audioLevels = Object.values(event.data.participantsAudioLevel);

        if (audioLevels.length > 0) {
          // Convert to numbers and filter out NaN values
          const numericLevels = audioLevels
            .map(level => typeof level === 'number' ? level : parseFloat(level))
            .filter(level => !isNaN(level));

          if (numericLevels.length > 0) {
            // Find the maximum level
            const level = Math.max(...numericLevels);

            // Scale the level to be more visible (raw levels are often very small)
            // Apply a more aggressive scaling to make it more dramatic
            const scaledLevel = Math.min(level * 8, 1);

            console.log('SpeechParticles: Calculated assistant audio level:', scaledLevel);

            // Use a default frequency range for assistant speech
            // This could be improved with actual frequency analysis if available
            const baseFrequency = 200; // Base frequency for assistant voice
            const frequencyVariation = 150; // More variation for more dynamic visualization
            const assistantFrequency = baseFrequency + (frequencyVariation * scaledLevel);

            // Only update if we have significant audio AND user is not speaking
            if (scaledLevel > 0.05 && !userSpeaking) {
              // Update the visualization for assistant audio
              window.updateAudioSource(scaledLevel, assistantFrequency, 'assistant');
              console.log('🔴 ASSISTANT PARTICLES: Calling updateAudioSource with amplitude', scaledLevel.toFixed(2), 'speaker: assistant', 'secondaryColor:', secondaryColor);
            } else if (!userSpeaking) {
              // When assistant is silent and user is not speaking, allow fallback to ambient mode
              window.updateAudioSource(null, null, null);
            }
            // If user is speaking, don't override with assistant audio
          }
        }
      }

      // Also check for direct volume level events
      if (event.data.type === 'volume-level' ||
          event.data.type === 'volume' ||
          event.data.action === 'volume-level') {

        console.log('SpeechParticles: Processing direct volume event:', event.data);

        let level = 0;

        // Handle regular volume events
        if (event.data.level !== undefined || event.data.volume !== undefined) {
          level = event.data.level || event.data.volume || 0;
        }

        // Normalize the level to be between 0 and 1
        const normalizedLevel = typeof level === 'number' ? Math.min(Math.max(level, 0), 1) : 0;

        // Apply a more aggressive scaling to make it more dramatic
        const scaledLevel = Math.min(normalizedLevel * 5, 1);

        if (scaledLevel > 0.05 && !userSpeaking) {
          console.log('SpeechParticles: Using direct volume level:', scaledLevel);

          // Use a default frequency for assistant speech with more variation
          const assistantFrequency = 200 + (150 * scaledLevel);

          // Update the visualization for assistant audio
          window.updateAudioSource(scaledLevel, assistantFrequency, 'assistant');
        } else if (!userSpeaking) {
          // When assistant is silent and user is not speaking, allow fallback to ambient mode
          window.updateAudioSource(null, null, null);
        }
      }

      // Check for speech status events
      if (event.data.action === 'app-message' && event.data.data) {
        try {
          // Skip parsing if data is not a valid JSON string
          if (typeof event.data.data !== 'string' || event.data.data === 'listening') {
            return;
          }
          const messageData = JSON.parse(event.data.data);

          if (messageData.type === 'speech-update') {
            console.log('SpeechParticles: Speech update event:', messageData);

            // If assistant starts speaking, make sure we're in assistant mode
            if (messageData.status === 'started' && messageData.role === 'assistant') {
              console.log('SpeechParticles: Assistant started speaking');
              // We don't set amplitude here, we'll wait for volume events
              window.externalSpeaker = 'assistant';
            }

            // If assistant stops speaking, reset to silence
            if (messageData.status === 'stopped' && messageData.role === 'assistant') {
              console.log('SpeechParticles: Assistant stopped speaking');
              window.updateAudioSource(0, 200, 'assistant');
            }

            // If user starts speaking, make sure we're in user mode
            if (messageData.status === 'started' && messageData.role === 'user') {
              console.log('SpeechParticles: User started speaking');
              window.externalSpeaker = 'user';
            }
          }
        } catch (error) {
          console.error('SpeechParticles: Error parsing app-message data:', error);
        }
      }
    };

    // Add event listener for Vapi audio level events
    window.addEventListener('message', handleVapiAudioLevel);

    // Also listen for direct volume level events from the VapiCall component
    const handleVolumeChange = (event) => {
      if (event.detail && typeof event.detail.level === 'number' && window.updateAudioSource) {
        const level = event.detail.level;
        console.log('SpeechParticles: Custom volume event received:', level);

        // Apply a more aggressive scaling to make it more dramatic
        const scaledLevel = Math.min(level * 5, 1);

        if (scaledLevel > 0.05 && !userSpeaking) {
          console.log('SpeechParticles: Custom volume event with level:', scaledLevel);

          // Use a default frequency for assistant speech with more variation
          const assistantFrequency = 200 + (150 * scaledLevel);

          // Update the visualization for assistant audio
          window.updateAudioSource(scaledLevel, assistantFrequency, 'assistant');
        } else if (!userSpeaking) {
          // When assistant is silent and user is not speaking, allow fallback to ambient mode
          window.updateAudioSource(null, null, null);
        }
      }
    };

    // Add custom event listener for volume changes
    window.addEventListener('vapi-volume-change', handleVolumeChange);

    return () => {
      window.removeEventListener('message', handleVapiAudioLevel);
      window.removeEventListener('vapi-volume-change', handleVolumeChange);
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      id="mistCanvas"
      className={`speech-particles-canvas speech-mist-canvas ${className || ''}`}
    />
  );
};

export default SpeechParticles;
