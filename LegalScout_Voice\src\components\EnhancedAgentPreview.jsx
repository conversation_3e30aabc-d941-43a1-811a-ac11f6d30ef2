import React, { useState, useEffect, useMemo } from 'react';
import { FaTrash, FaExclamationTriangle } from 'react-icons/fa';
import EnhancedVapiCall from './EnhancedVapiCall';
import EnhancedCallController from './call/EnhancedCallController';
import { createAttorneyPreviewConfig } from '../utils/previewConfigHandler';
import { checkPreviewConsistency } from '../services/EnhancedSyncTools';
import { getCallDebugger } from '../utils/callDebugger';
import './EnhancedAgentPreview.css';

/**
 * Enhanced Agent Preview Component
 *
 * This component provides a preview of the attorney's AI assistant using
 * the enhanced Vapi components. It can be used as a drop-in replacement
 * for the existing agent preview in the dashboard.
 *
 * @param {Object} props
 * @param {Object} props.attorney - The attorney object from Supabase
 * @param {boolean} props.isDarkTheme - Whether to use dark theme
 * @param {Function} props.onError - Callback when an error occurs
 */
const EnhancedAgentPreview = ({
  attorney,
  isDarkTheme = false,
  onError,
  showDebugPanel = false, // Disable debug panel by default
  onAssistantDeleted
}) => {
  // Initialize call debugger
  const callDebugger = useMemo(() => getCallDebugger('EnhancedAgentPreview'), []);
  callDebugger.log('Initializing EnhancedAgentPreview component', {
    attorneyId: attorney?.id,
    assistantId: attorney?.vapi_assistant_id
  });

  // State for call active status
  const [isCallActive, setIsCallActive] = useState(false);
  // State for preview mode (call controller or full call)
  const [previewMode, setPreviewMode] = useState('controller');
  // State for error message
  const [errorMessage, setErrorMessage] = useState(null);
  // State for processed config
  const [processedConfig, setProcessedConfig] = useState(null);
  // State for delete confirmation
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [deleting, setDeleting] = useState(false);

  // Process attorney data to create preview config and check consistency
  useEffect(() => {
    if (!attorney) {
      setErrorMessage('No attorney data available');
      return;
    }

    const processAttorneyData = async () => {
      try {
        // Create attorney preview config
        const config = createAttorneyPreviewConfig(attorney);
        setProcessedConfig(config);

        // Check preview consistency
        if (attorney.id && attorney.vapi_assistant_id) {
          console.log('[EnhancedAgentPreview] Checking preview consistency');
          const consistencyResult = await checkPreviewConsistency({
            attorneyId: attorney.id
          });

          console.log('[EnhancedAgentPreview] Consistency check result:', consistencyResult);

          if (!consistencyResult.consistent) {
            console.warn('[EnhancedAgentPreview] Preview inconsistency detected:', consistencyResult.discrepancies || consistencyResult.errors);

            if (consistencyResult.action === 'fixed') {
              console.log('[EnhancedAgentPreview] Inconsistency fixed automatically');
            }
          }
        }

        setErrorMessage(null);
      } catch (error) {
        console.error('Error processing attorney data:', error);
        setErrorMessage(`Error processing attorney data: ${error.message}`);

        // Call onError callback if provided
        if (onError) {
          onError(error);
        }
      }
    };

    processAttorneyData();
  }, [attorney, onError]);

  // Handle call start
  const handleCallStart = () => {
    callDebugger.log('Call started in preview', { assistantId: attorney?.vapi_assistant_id });
    setIsCallActive(true);
  };

  // Handle call end
  const handleCallEnd = () => {
    callDebugger.log('Call ended in preview');
    setIsCallActive(false);
  };

  // Handle preview mode toggle
  const handlePreviewModeToggle = () => {
    setPreviewMode(prevMode => prevMode === 'controller' ? 'full' : 'controller');
  };

  // Handle error
  const handleError = (error) => {
    console.error('Preview error:', error);
    setErrorMessage(typeof error === 'string' ? error : error.message);

    // Call onError callback if provided
    if (onError) {
      onError(error);
    }
  };

  // Handle delete assistant
  const handleDeleteAssistant = () => {
    setShowDeleteConfirmation(true);
  };

  // Cancel delete
  const cancelDelete = () => {
    setShowDeleteConfirmation(false);
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!attorney?.vapi_assistant_id) return;

    try {
      setDeleting(true);
      const assistantId = attorney.vapi_assistant_id;

      console.log(`🗑️ Deleting assistant from preview: ${assistantId}`);

      // Delete from Vapi
      const VAPI_API_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
      const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${VAPI_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.warn(`Failed to delete from Vapi: ${response.status}`);
      }

      // Delete from Supabase
      const { supabase } = await import('../lib/supabase');

      // Delete UI config
      await supabase
        .from('assistant_ui_configs')
        .delete()
        .eq('assistant_id', assistantId)
        .eq('attorney_id', attorney.id);

      // Delete subdomain
      await supabase
        .from('assistant_subdomains')
        .delete()
        .eq('assistant_id', assistantId)
        .eq('attorney_id', attorney.id);

      // Delete consultations
      await supabase
        .from('consultations')
        .delete()
        .eq('assistant_id', assistantId)
        .eq('attorney_id', attorney.id);

      // Update attorney
      await supabase
        .from('attorneys')
        .update({
          current_assistant_id: null,
          vapi_assistant_id: null,
          updated_at: new Date().toISOString()
        })
        .eq('id', attorney.id);

      console.log('✅ Successfully deleted assistant');

      // Emit event to notify dropdown and other components
      const deleteEvent = new CustomEvent('assistantDeleted', {
        detail: { assistantId, assistantName: attorney?.firm_name || 'Assistant' }
      });
      window.dispatchEvent(deleteEvent);
      console.log('📡 [EnhancedAgentPreview] Emitted assistantDeleted event:', assistantId);

      // Close confirmation
      setShowDeleteConfirmation(false);

      // Notify parent component
      if (onAssistantDeleted) {
        onAssistantDeleted(assistantId);
      }

    } catch (error) {
      console.error('Error deleting assistant:', error);
      setErrorMessage('Failed to delete assistant: ' + error.message);
    } finally {
      setDeleting(false);
    }
  };

  // Render error message
  const renderErrorMessage = () => {
    if (!errorMessage) return null;

    return (
      <div className="preview-error">
        <p>{errorMessage}</p>
      </div>
    );
  };

  // Render preview content based on mode
  const renderPreviewContent = () => {
    // If no attorney data or processed config, show error
    if (!attorney || !processedConfig) {
      return (
        <div className="preview-placeholder">
          <p>No attorney data available for preview</p>
        </div>
      );
    }

    // If no valid assistant ID, show error
    if (!attorney.vapi_assistant_id || attorney.vapi_assistant_id.startsWith('mock-')) {
      const isMockId = attorney.vapi_assistant_id && attorney.vapi_assistant_id.startsWith('mock-');

      return (
        <div className="preview-placeholder">
          <p>
            {isMockId
              ? 'Voice assistant needs to be properly configured. Mock assistant detected.'
              : 'No Vapi assistant ID available for this attorney'}
          </p>
          <p>Please configure an assistant in the Voice Assistant section</p>
        </div>
      );
    }

    // Render based on preview mode
    if (previewMode === 'controller') {
      return (
        <div className="controller-preview">
          <EnhancedCallController
            assistantId={attorney.vapi_assistant_id}
            showTranscript={true}
            showVisualization={true}
          />
        </div>
      );
    } else {
      return (
        <div className="full-call-preview">
          <EnhancedVapiCall
            assistantId={attorney.vapi_assistant_id}
            customInstructions={{
              firmName: attorney.firm_name,
              welcomeMessage: attorney.welcome_message,
              voiceId: attorney.voice_id,
              voiceProvider: attorney.voice_provider || '11labs'
            }}
            onEndCall={handleCallEnd}
            showTranscript={true}
            showDossier={true}
            showDebugPanel={showDebugPanel}
          />
        </div>
      );
    }
  };

  return (
    <div className={`enhanced-agent-preview ${isDarkTheme ? 'dark' : 'light'}`}>
      <div className="preview-header">
        <h2 className="preview-title">Agent Preview</h2>
        <div className="preview-controls">
          <button
            className="preview-mode-toggle"
            onClick={handlePreviewModeToggle}
          >
            {previewMode === 'controller' ? 'Show Full Call' : 'Show Controller'}
          </button>
        </div>
      </div>

      {renderErrorMessage()}

      <div className="preview-content">
        {renderPreviewContent()}
      </div>

      <div className="preview-footer">
        <div className="preview-note">
          <p>This preview uses your actual Vapi assistant configuration.</p>
          {attorney?.vapi_assistant_id && (
            <div className="assistant-info-row">
              <span className="assistant-id">Assistant ID: {attorney.vapi_assistant_id}</span>
              <button
                className="delete-assistant-btn-preview"
                onClick={handleDeleteAssistant}
                title="Delete this assistant and all associated data"
              >
                <FaTrash />
                Delete Assistant
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirmation && (
        <div className="delete-confirmation-overlay">
          <div className="delete-confirmation-modal">
            <div className="modal-header">
              <FaExclamationTriangle className="warning-icon" />
              <h3>Delete Assistant</h3>
            </div>

            <div className="modal-content">
              <p><strong>Are you sure you want to delete this assistant?</strong></p>
              <p>Assistant ID: {attorney?.vapi_assistant_id}</p>

              <div className="warning-list">
                <p>⚠️ This action will permanently delete:</p>
                <ul>
                  <li>The assistant from Vapi</li>
                  <li>All UI configurations</li>
                  <li>Subdomain assignments</li>
                  <li>All consultation records</li>
                  <li>Call history and statistics</li>
                </ul>
              </div>

              <p className="final-warning">
                <strong>This action cannot be undone!</strong>
              </p>
            </div>

            <div className="modal-actions">
              <button
                className="btn btn-secondary"
                onClick={cancelDelete}
                disabled={deleting}
              >
                Cancel
              </button>
              <button
                className="btn btn-danger"
                onClick={confirmDelete}
                disabled={deleting}
              >
                {deleting ? 'Deleting...' : 'Delete Assistant'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedAgentPreview;
