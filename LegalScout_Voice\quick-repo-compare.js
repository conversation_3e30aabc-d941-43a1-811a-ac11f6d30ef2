#!/usr/bin/env node

/**
 * Quick Repository Comparison
 * Compares your current repo with your other LegalScout repo
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🔍 QUICK REPOSITORY COMPARISON');
console.log('==============================\n');

async function quickAnalysis() {
  try {
    // Step 1: Clone the other repo to a temp directory
    console.log('📥 Cloning other LegalScout repository...');
    const tempDir = '.temp-quick-analysis';
    
    // Clean up if exists
    if (fs.existsSync(tempDir)) {
      execSync(`rmdir /s /q "${tempDir}"`, { stdio: 'pipe' });
    }
    
    // Clone the other repo
    execSync(`git clone --depth 1 https://github.com/damonkost/LegalScout.git "${tempDir}"`, { 
      stdio: 'pipe' 
    });
    
    console.log('✅ Clone completed\n');
    
    // Step 2: Compare key directories
    console.log('📊 COMPARISON RESULTS:');
    console.log('======================\n');
    
    // Compare components
    const otherComponents = getJSFiles(path.join(tempDir, 'src/components'));
    const currentComponents = getJSFiles('src/components');
    
    console.log('📦 COMPONENTS COMPARISON:');
    console.log(`  Current repo: ${currentComponents.length} components`);
    console.log(`  Other repo: ${otherComponents.length} components\n`);
    
    // Find new components
    const newComponents = otherComponents.filter(comp => 
      !currentComponents.some(curr => path.basename(curr) === path.basename(comp))
    );
    
    if (newComponents.length > 0) {
      console.log('🆕 NEW COMPONENTS IN OTHER REPO:');
      newComponents.forEach(comp => {
        const name = path.basename(comp);
        const analysis = analyzeComponent(comp);
        console.log(`  ✨ ${name} - Score: ${analysis.score}/10`);
        if (analysis.features.length > 0) {
          console.log(`     Features: ${analysis.features.join(', ')}`);
        }
      });
      console.log('');
    }
    
    // Compare services
    const otherServices = getJSFiles(path.join(tempDir, 'src/services'));
    const currentServices = getJSFiles('src/services');
    
    console.log('🛠️  SERVICES COMPARISON:');
    console.log(`  Current repo: ${currentServices.length} services`);
    console.log(`  Other repo: ${otherServices.length} services\n`);
    
    // Find new services
    const newServices = otherServices.filter(service => 
      !currentServices.some(curr => path.basename(curr) === path.basename(service))
    );
    
    if (newServices.length > 0) {
      console.log('🆕 NEW SERVICES IN OTHER REPO:');
      newServices.forEach(service => {
        const name = path.basename(service);
        const analysis = analyzeComponent(service);
        console.log(`  ⚙️  ${name} - Score: ${analysis.score}/10`);
        if (analysis.features.length > 0) {
          console.log(`     Features: ${analysis.features.join(', ')}`);
        }
      });
      console.log('');
    }
    
    // Compare package.json
    console.log('📦 PACKAGE.JSON COMPARISON:');
    const otherPkg = JSON.parse(fs.readFileSync(path.join(tempDir, 'package.json'), 'utf8'));
    const currentPkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    const otherDeps = { ...otherPkg.dependencies, ...otherPkg.devDependencies };
    const currentDeps = { ...currentPkg.dependencies, ...currentPkg.devDependencies };
    
    const newDeps = Object.keys(otherDeps).filter(dep => !currentDeps[dep]);
    
    if (newDeps.length > 0) {
      console.log('  🆕 New dependencies in other repo:');
      newDeps.forEach(dep => {
        console.log(`     📦 ${dep}@${otherDeps[dep]}`);
      });
    } else {
      console.log('  ✅ No new dependencies found');
    }
    console.log('');
    
    // Generate recommendations
    console.log('🎯 RECOMMENDATIONS:');
    console.log('==================\n');
    
    const highValueComponents = newComponents.filter(comp => {
      const analysis = analyzeComponent(comp);
      return analysis.score >= 6;
    });
    
    const highValueServices = newServices.filter(service => {
      const analysis = analyzeComponent(service);
      return analysis.score >= 6;
    });
    
    if (highValueComponents.length > 0 || highValueServices.length > 0) {
      console.log('🔥 HIGH-VALUE ITEMS TO CONSIDER:');
      
      highValueComponents.forEach(comp => {
        console.log(`  📦 ${path.basename(comp)} - Modern component with valuable features`);
      });
      
      highValueServices.forEach(service => {
        console.log(`  🛠️  ${path.basename(service)} - Advanced service with modern patterns`);
      });
      
      console.log('\n💡 NEXT STEPS:');
      console.log('  1. Review the high-value items above');
      console.log('  2. Selectively copy components that enhance your current architecture');
      console.log('  3. Test integration without breaking existing functionality');
      console.log('  4. Consider new dependencies for additional capabilities');
    } else {
      console.log('✨ Your current repository appears to have all the valuable components!');
      console.log('   The other repo doesn\'t contain significant improvements to merge.');
    }
    
    // Cleanup
    console.log('\n🧹 Cleaning up...');
    execSync(`rmdir /s /q "${tempDir}"`, { stdio: 'pipe' });
    console.log('✅ Analysis complete!\n');
    
  } catch (error) {
    console.error('❌ Error during analysis:', error.message);
  }
}

function getJSFiles(dirPath) {
  const files = [];
  
  if (!fs.existsSync(dirPath)) {
    return files;
  }
  
  function scanDir(currentDir) {
    try {
      const items = fs.readdirSync(currentDir, { withFileTypes: true });
      
      items.forEach(item => {
        const fullPath = path.join(currentDir, item.name);
        
        if (item.isFile() && (item.name.endsWith('.js') || item.name.endsWith('.jsx'))) {
          files.push(fullPath);
        } else if (item.isDirectory() && !item.name.startsWith('.') && !item.name.includes('node_modules')) {
          scanDir(fullPath);
        }
      });
    } catch (error) {
      // Skip directories we can't read
    }
  }
  
  scanDir(dirPath);
  return files;
}

function analyzeComponent(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const features = [];
    let score = 0;
    
    // Check for modern React patterns
    if (/use[A-Z]\w+|useState|useEffect|useCallback|useMemo/.test(content)) {
      features.push('Modern React Hooks');
      score += 2;
    }
    
    // Check for Vapi integration
    if (/vapi|Vapi/.test(content)) {
      features.push('Vapi Integration');
      score += 3;
    }
    
    // Check for Assistant features
    if (/assistant|Assistant/.test(content)) {
      features.push('Assistant Management');
      score += 2;
    }
    
    // Check for MCP integration
    if (/mcp|MCP/.test(content)) {
      features.push('MCP Integration');
      score += 3;
    }
    
    // Check for service patterns
    if (/class \w+Service|export.*Service/.test(content)) {
      features.push('Service Architecture');
      score += 1;
    }
    
    // Check for async patterns
    if (/async|await|Promise/.test(content)) {
      features.push('Async Patterns');
      score += 1;
    }
    
    // Check for error handling
    if (/try\s*{|catch\s*\(|\.catch\(/.test(content)) {
      features.push('Error Handling');
      score += 1;
    }
    
    // Check for OAuth integration
    if (/oauth|OAuth|auth\./.test(content)) {
      features.push('OAuth Integration');
      score += 2;
    }
    
    return { score, features };
    
  } catch (error) {
    return { score: 0, features: [] };
  }
}

// Run the analysis
quickAnalysis();
