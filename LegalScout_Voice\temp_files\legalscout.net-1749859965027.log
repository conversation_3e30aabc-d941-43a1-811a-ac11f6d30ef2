dashboard:16 🚀 [LegalScout] Initializing environment...
dashboard:38 ✅ [LegalScout] Environment initialized
dashboard:127 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
dashboard:214 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
index-0247fdc2.js:60 [VapiLoader] Starting Vapi SDK loading process
index-0247fdc2.js:60 [VapiLoader] Attempting to import @vapi-ai/web package
index-0247fdc2.js:147 [VapiMcpService] Created clean fetch from iframe
index-0247fdc2.js:147 [VapiMcpService] INFO: Vapi MCP Service initialized Object
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:16 🚀 [LegalScout] Initializing environment...
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:38 ✅ [LegalScout] Environment initialized
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:127 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:214 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:16 🚀 [LegalScout] Initializing environment...
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:38 ✅ [LegalScout] Environment initialized
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:127 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:214 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
index-0247fdc2.js:60 [VapiLoader] Starting Vapi SDK loading process
index-0247fdc2.js:60 [VapiLoader] Attempting to import @vapi-ai/web package
index-0247fdc2.js:147 [VapiMcpService] Created clean fetch from iframe
index-0247fdc2.js:147 [VapiMcpService] INFO: Vapi MCP Service initialized [object Object]
index-0247fdc2.js:60 [VapiLoader] Starting Vapi SDK loading process
index-0247fdc2.js:60 [VapiLoader] Attempting to import @vapi-ai/web package
index-0247fdc2.js:147 [VapiMcpService] Created clean fetch from iframe
index-0247fdc2.js:147 [VapiMcpService] INFO: Vapi MCP Service initialized [object Object]
index-0247fdc2.js:974 SimplePreviewPage: No attorney found with subdomain: default
s @ index-0247fdc2.js:974
index-0247fdc2.js:974 SimplePreviewPage: No attorney data found for subdomain: default
(anonymous) @ index-0247fdc2.js:974
index-0247fdc2.js:974 SimplePreviewPage: No attorney found with subdomain: default
s @ index-0247fdc2.js:974
index-0247fdc2.js:974 SimplePreviewPage: No attorney data found for subdomain: default
(anonymous) @ index-0247fdc2.js:974
index.ts:5 Loaded contentScript
dashboard:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
hook.js:608 No suitable preview iframe found or contentWindow not accessible
overrideMethod @ hook.js:608
hook.js:608 No suitable preview iframe found or contentWindow not accessible
overrideMethod @ hook.js:608
hook.js:608 No suitable preview iframe found or contentWindow not accessible
overrideMethod @ hook.js:608
hook.js:608 No suitable preview iframe found or contentWindow not accessible
overrideMethod @ hook.js:608
hook.js:608 No suitable preview iframe found or contentWindow not accessible
overrideMethod @ hook.js:608
hook.js:608 No suitable preview iframe found or contentWindow not accessible
overrideMethod @ hook.js:608
hook.js:608 [DashboardNew] Loading timeout reached, forcing loading state to false
overrideMethod @ hook.js:608
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:16 🚀 [LegalScout] Initializing environment...
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:38 ✅ [LegalScout] Environment initialized
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:127 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:214 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
index-0247fdc2.js:60 [VapiLoader] Starting Vapi SDK loading process
index-0247fdc2.js:60 [VapiLoader] Attempting to import @vapi-ai/web package
index-0247fdc2.js:147 [VapiMcpService] Created clean fetch from iframe
index-0247fdc2.js:147 [VapiMcpService] INFO: Vapi MCP Service initialized Object
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:16 🚀 [LegalScout] Initializing environment...
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:38 ✅ [LegalScout] Environment initialized
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:127 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true:214 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
index-0247fdc2.js:60 [VapiLoader] Starting Vapi SDK loading process
index-0247fdc2.js:60 [VapiLoader] Attempting to import @vapi-ai/web package
index-0247fdc2.js:147 [VapiMcpService] Created clean fetch from iframe
index-0247fdc2.js:147 [VapiMcpService] INFO: Vapi MCP Service initialized Object
index-0247fdc2.js:974 SimplePreviewPage: No attorney found with subdomain: default
s @ index-0247fdc2.js:974
index-0247fdc2.js:974 SimplePreviewPage: No attorney data found for subdomain: default
(anonymous) @ index-0247fdc2.js:974
index-0247fdc2.js:974 SimplePreviewPage: No attorney found with subdomain: default
s @ index-0247fdc2.js:974
index-0247fdc2.js:974 SimplePreviewPage: No attorney data found for subdomain: default
(anonymous) @ index-0247fdc2.js:974
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
