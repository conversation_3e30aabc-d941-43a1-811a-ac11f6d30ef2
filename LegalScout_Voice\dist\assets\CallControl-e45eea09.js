import{r as a,e as k,v as M,s as P,m as A,a as R,j as e,R as $,u as q,g as O}from"./index-dd4c5999.js";import{A as B,m as I}from"./framer-motion-ef6d06c6.js";const H=(E={})=>{const{token:p,onError:u}=E,[C,t]=a.useState(!0),[m,b]=a.useState(null),[l,y]=a.useState(null),[h,v]=a.useState(null),[n,d]=a.useState(null),[f,N]=a.useState([]),[j,g]=a.useState(!1),[F,c]=a.useState(0),[z,x]=a.useState("unknown"),[w,U]=a.useState(!1),r=a.useCallback(o=>{console.error("Call control error:",o),b(o.message||"An error occurred"),u&&u(o)},[u]);a.useEffect(()=>{p&&(async()=>{try{if(t(!0),!p)throw new Error("No token provided");const s=await M(p);if(!s)throw new Error("Invalid or expired token");if(y(s),!await k.initialize())throw new Error("Failed to initialize Vapi call service");const i=await k.getCall(s.callId);if(!i)throw new Error("Call not found");v(i),x(i.status);const{data:T,error:L}=await P.from("attorneys").select("*").eq("id",s.attorneyId).single();if(L)throw new Error("Attorney not found");d(T),i.transcripts&&Array.isArray(i.transcripts)&&N(i.transcripts),t(!1),U(!0)}catch(s){r(s),t(!1)}})()},[p,r]),a.useEffect(()=>{if(!w||!l||!l.callId)return;const o=setInterval(async()=>{try{const s=await k.getCall(l.callId);if(!s){clearInterval(o),b("Call no longer available");return}v(s),x(s.status),s.transcripts&&Array.isArray(s.transcripts)&&N(s.transcripts),(s.status==="completed"||s.status==="ended")&&(clearInterval(o),U(!1))}catch(s){console.error("Error polling call updates:",s)}},5e3);return()=>clearInterval(o)},[w,l]);const V=a.useCallback(async o=>{if(!(!o.trim()||!l||!l.callId))try{return console.log(`[useCallControl] Sending message to call ${l.callId}: ${o}`),!0}catch(s){return r(s),!1}},[l,r]),D=a.useCallback(async()=>{if(!(!l||!l.callId))try{return console.log(`[useCallControl] Taking over call ${l.callId} for attorney ${l.attorneyId}`),!1}catch(o){return r(o),!1}},[l,r]),_=a.useCallback(async()=>{if(!(!l||!l.callId))try{return console.log(`[useCallControl] Ending call ${l.callId}`),x("ended"),!0}catch(o){return r(o),!1}},[l,r]);return{loading:C,error:m,tokenData:l,call:h,attorney:n,transcripts:f,assistantIsSpeaking:j,volumeLevel:F,callStatus:z,sendMessage:V,takeOverCall:D,endCall:_,clearError:()=>b(null)}},Y=(E={})=>{const{assistantId:p=A.voice.vapi.defaultAssistantId,customInstructions:u={},onCallEnd:C}=E,[t,m]=a.useState(!1),[b,l]=a.useState([]),[y,h]=a.useState(null),[v,n]=a.useState(0),[d,f]=a.useState(null),[N,j]=a.useState("disconnected"),g=a.useRef(null),F=a.useRef(null),c=a.useCallback(async()=>{try{if(j("connecting"),!await R.connect(A.voice.vapi.publicKey))throw new Error("Failed to connect to Vapi");return j("connected"),!0}catch(r){return console.error("[use-vapi] Error initializing Vapi:",r),f(r.message),j("error"),!1}},[]),z=a.useCallback(async r=>{try{if(N!=="connected"&&!await c())throw new Error("Failed to initialize Vapi");const V=r||p;if(!V)throw new Error("No assistant ID provided");const D=document.createElement("iframe");D.style.display="none",D.src=`https://embed.vapi.ai/${V}`,document.body.appendChild(D),g.current=D;const _=s=>{if(s.data&&s.data.what==="iframe-call-message"){if(s.data.action==="remote-participants-audio-level"){const S=s.data.participantsAudioLevel||{},i=S.assistant||0,T=S.user||0;i>T?(n(i),h("assistant")):T>0?(n(T),h("user")):(n(0),h(null))}if(s.data.action==="transcript"){const{role:S,text:i}=s.data;S&&i&&l(T=>[...T,{role:S,text:i}])}if(s.data.action==="call-status"){const{status:S,callId:i}=s.data;S==="connected"?(m(!0),i&&(F.current=i)):S==="disconnected"&&(m(!1),C&&C())}}};window.addEventListener("message",_);const o=()=>{window.removeEventListener("message",_),g.current&&(document.body.removeChild(g.current),g.current=null)};return window.vapiCleanup=o,m(!0),!0}catch(V){return console.error("[use-vapi] Error starting call:",V),f(V.message),!1}},[p,N,c,C]),x=a.useCallback(async()=>{try{return g.current&&g.current.contentWindow.postMessage({action:"end-call"},"*"),window.vapiCleanup&&window.vapiCleanup(),m(!1),h(null),n(0),!0}catch(r){return console.error("[use-vapi] Error stopping call:",r),f(r.message),!1}},[]),w=a.useCallback(async()=>t?x():z(),[t,z,x]),U=a.useCallback(()=>{l([])},[]);return a.useEffect(()=>()=>{t&&x()},[t,x]),{isSessionActive:t,conversation:b,currentSpeaker:y,volumeLevel:v,error:d,connectionStatus:N,toggleCall:w,startCall:z,stopCall:x,clearConversation:U}},K=({className:E,style:p})=>{const{volumeLevel:u,isSessionActive:C,currentSpeaker:t}=Y(),[m,b]=a.useState(Array(50).fill(5)),l=a.useRef(null);a.useEffect(()=>{C&&u>0?y(u):h()},[u,C]);const y=n=>{const d=Math.min(n*10,1);if(b(m.map(()=>{const f=d*100,N=Math.random()*.5+.75;return f*N})),window.updateAudioSource){const f=t||"user",g=(f==="assistant"?200:300)+150*d;window.updateAudioSource(d,g,f)}},h=()=>{b(m.map(()=>5+Math.random()*10)),window.updateAudioSource&&window.updateAudioSource(null,null,null)},v=()=>C?t==="assistant"?"rgba(0, 191, 255, 0.8)":t==="user"?"rgba(0, 200, 100, 0.8)":"rgba(200, 200, 200, 0.5)":"rgba(200, 200, 200, 0.5)";return e.jsxDEV("div",{className:`enhanced-speech-particles ${E||""}`,style:p,children:[e.jsxDEV("canvas",{ref:l,id:"mistCanvas",className:"speech-particles-canvas speech-mist-canvas"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/EnhancedSpeechParticles.jsx",lineNumber:87,columnNumber:7},globalThis),e.jsxDEV(B,{children:e.jsxDEV(I.div,{className:"bars-container",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.5},children:e.jsxDEV("svg",{width:"100%",height:"100%",viewBox:"0 0 1000 200",preserveAspectRatio:"xMidYMid meet",className:"bars-svg",children:m.map((n,d)=>e.jsxDEV($.Fragment,{children:[e.jsxDEV(I.rect,{x:500+d*10-245,y:100-n/2,width:"5",height:n,fill:v(),initial:{height:5,y:97.5},animate:{height:n,y:100-n/2,transition:{duration:.1,ease:"easeOut"}}},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/EnhancedSpeechParticles.jsx",lineNumber:111,columnNumber:17},globalThis),e.jsxDEV(I.rect,{x:500-d*10-5-245,y:100-n/2,width:"5",height:n,fill:v(),initial:{height:5,y:97.5},animate:{height:n,y:100-n/2,transition:{duration:.1,ease:"easeOut"}}},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/EnhancedSpeechParticles.jsx",lineNumber:127,columnNumber:17},globalThis)]},d,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/EnhancedSpeechParticles.jsx",lineNumber:110,columnNumber:15},globalThis))},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/EnhancedSpeechParticles.jsx",lineNumber:102,columnNumber:11},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/EnhancedSpeechParticles.jsx",lineNumber:95,columnNumber:9},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/EnhancedSpeechParticles.jsx",lineNumber:94,columnNumber:7},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/components/EnhancedSpeechParticles.jsx",lineNumber:85,columnNumber:5},globalThis)};const J=()=>{const E=q(),p=O(),[u,C]=a.useState(""),{loading:t,error:m,call:b,attorney:l,transcripts:y,assistantIsSpeaking:h,volumeLevel:v,callStatus:n,sendMessage:d,takeOverCall:f,endCall:N}=H({token:p,onError:c=>console.error("Call control error:",c)}),j=async()=>{if(!u.trim())return;await d(u)&&C("")},g=async()=>{await f()||alert("Call takeover not implemented yet")},F=async()=>{await N()};return t?e.jsxDEV("div",{className:"call-control-container loading",children:[e.jsxDEV("div",{className:"loading-spinner"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:71,columnNumber:9},globalThis),e.jsxDEV("p",{children:"Loading call control..."},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:72,columnNumber:9},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:70,columnNumber:7},globalThis):m?e.jsxDEV("div",{className:"call-control-container error",children:[e.jsxDEV("h2",{children:"Error"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:81,columnNumber:9},globalThis),e.jsxDEV("p",{children:m},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:82,columnNumber:9},globalThis),e.jsxDEV("button",{onClick:()=>E("/"),children:"Return to Home"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:83,columnNumber:9},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:80,columnNumber:7},globalThis):e.jsxDEV("div",{className:"call-control-container",children:[e.jsxDEV("header",{className:"call-control-header",children:[e.jsxDEV("h1",{children:"Call Control"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:91,columnNumber:9},globalThis),e.jsxDEV("div",{className:"call-status",children:[e.jsxDEV("span",{className:`status-indicator ${n}`},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:93,columnNumber:11},globalThis),e.jsxDEV("span",{className:"status-text",children:n},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:94,columnNumber:11},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:92,columnNumber:9},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:90,columnNumber:7},globalThis),e.jsxDEV("div",{className:"call-info",children:[e.jsxDEV("div",{className:"attorney-info",children:[e.jsxDEV("h2",{children:l?.firm_name||"Your Law Firm"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:100,columnNumber:11},globalThis),e.jsxDEV("p",{children:l?.name||"Attorney"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:101,columnNumber:11},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:99,columnNumber:9},globalThis),e.jsxDEV("div",{className:"call-details",children:[e.jsxDEV("p",{children:["Call ID: ",b?.id]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:105,columnNumber:11},globalThis),e.jsxDEV("p",{children:["Customer: ",b?.customer?.phoneNumber||"Unknown"]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:106,columnNumber:11},globalThis),e.jsxDEV("p",{children:["Duration: ",Math.round((b?.duration||0)/60)," minutes"]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:107,columnNumber:11},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:104,columnNumber:9},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:98,columnNumber:7},globalThis),e.jsxDEV("div",{className:"call-visualization",children:e.jsxDEV(K,{isSpeaking:h,volumeLevel:v,speaker:"assistant"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:112,columnNumber:9},globalThis)},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:111,columnNumber:7},globalThis),e.jsxDEV("div",{className:"transcript-container",children:[e.jsxDEV("h3",{children:"Live Transcript"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:120,columnNumber:9},globalThis),e.jsxDEV("div",{className:"transcript-messages",children:y.map((c,z)=>e.jsxDEV("div",{className:`transcript-message ${c.role}`,children:[e.jsxDEV("div",{className:"message-header",children:[e.jsxDEV("span",{className:"message-role",children:c.role==="assistant"?"Assistant":"Client"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:128,columnNumber:17},globalThis),e.jsxDEV("span",{className:"message-time",children:new Date(c.timestamp).toLocaleTimeString()},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:131,columnNumber:17},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:127,columnNumber:15},globalThis),e.jsxDEV("div",{className:"message-content",children:c.text},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:135,columnNumber:15},globalThis)]},z,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:123,columnNumber:13},globalThis))},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:121,columnNumber:9},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:119,columnNumber:7},globalThis),e.jsxDEV("div",{className:"control-panel",children:[e.jsxDEV("div",{className:"message-input",children:[e.jsxDEV("input",{type:"text",value:u,onChange:c=>C(c.target.value),placeholder:"Type a message to send to the assistant...",disabled:n!=="in-progress"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:143,columnNumber:11},globalThis),e.jsxDEV("button",{onClick:j,disabled:!u.trim()||n!=="in-progress",children:"Send"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:150,columnNumber:11},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:142,columnNumber:9},globalThis),e.jsxDEV("div",{className:"control-buttons",children:[e.jsxDEV("button",{className:"take-over-button",onClick:g,disabled:n!=="in-progress",children:"Take Over Call"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:159,columnNumber:11},globalThis),e.jsxDEV("button",{className:"end-call-button",onClick:F,disabled:n!=="in-progress",children:"End Call"},void 0,!1,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:167,columnNumber:11},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:158,columnNumber:9},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:141,columnNumber:7},globalThis)]},void 0,!0,{fileName:"C:/Users/<USER>/Scout_Finalize/src/pages/CallControl.jsx",lineNumber:89,columnNumber:5},globalThis)};export{J as default};
