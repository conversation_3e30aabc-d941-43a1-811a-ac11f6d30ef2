/**
 * Assistant-Aware Context
 *
 * Provides assistant-specific global values that adapt when users switch assistants.
 * All URLs, links, embed codes, and configurations are dynamically generated
 * based on the currently selected assistant.
 */

import React, { createContext, useContext, useMemo, useCallback, useState, useEffect } from 'react';
import { useStandaloneAttorney } from '../hooks/useStandaloneAttorney';

const AssistantAwareContext = createContext();

export const useAssistantAware = () => {
  const context = useContext(AssistantAwareContext);
  if (!context) {
    throw new Error('useAssistantAware must be used within an AssistantAwareProvider');
  }
  return context;
};

export const AssistantAwareProvider = ({ children }) => {
  const { attorney, loading } = useStandaloneAttorney();
  const [assistantSubdomain, setAssistantSubdomain] = useState(null);
  const [assistantName, setAssistantName] = useState(null);
  const [loadingAssistantData, setLoadingAssistantData] = useState(false);
  const [forceAssistantId, setForceAssistantId] = useState(null); // DIRECT OVERRIDE

  // Get current assistant ID - DIRECT OVERRIDE TAKES PRIORITY
  const currentAssistantId = useMemo(() => {
    // Priority order: forceAssistantId > current_assistant_id > vapi_assistant_id
    const assistantId = forceAssistantId || attorney?.current_assistant_id || attorney?.vapi_assistant_id;

    // Validate assistant ID format
    const isValidAssistantId = assistantId &&
      typeof assistantId === 'string' &&
      assistantId.length > 10 &&
      !assistantId.includes('mock') &&
      !assistantId.includes('undefined') &&
      // CRITICAL FIX: Reject Supabase UUIDs that are mistakenly stored as assistant IDs
      !assistantId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i);

    const resolvedId = isValidAssistantId ? assistantId : null;

    console.log('🔍 [AssistantAwareContext] Current assistant ID:', {
      forceAssistantId,
      current_assistant_id: attorney?.current_assistant_id,
      vapi_assistant_id: attorney?.vapi_assistant_id,
      resolved: resolvedId,
      isValid: isValidAssistantId,
      attorneyId: attorney?.id
    });

    return resolvedId;
  }, [forceAssistantId, attorney?.current_assistant_id, attorney?.vapi_assistant_id]);

  // Load assistant-specific data when assistant ID changes
  useEffect(() => {
    console.log('🔄 [AssistantAwareContext] useEffect triggered:', {
      currentAssistantId,
      attorneyId: attorney?.id,
      hasCurrentAssistant: Boolean(currentAssistantId),
      hasAttorney: Boolean(attorney?.id)
    });

    if (currentAssistantId && attorney?.id) {
      console.log('✅ [AssistantAwareContext] Loading assistant data for:', currentAssistantId);
      loadAssistantData(currentAssistantId, attorney.id);
    } else {
      console.log('⚠️ [AssistantAwareContext] Clearing assistant data - no assistant or attorney');
      // Clear assistant data if no assistant selected
      setAssistantSubdomain(null);
      setAssistantName(null);
    }
  }, [currentAssistantId, attorney?.id]);

  // Load assistant subdomain and name using systematic service
  const loadAssistantData = async (assistantId, attorneyId) => {
    if (!assistantId || !attorneyId) {
      console.log('⚠️ [AssistantAwareContext] Missing assistantId or attorneyId, clearing data');
      setAssistantSubdomain(null);
      setAssistantName(null);
      return;
    }

    try {
      setLoadingAssistantData(true);

      // SYSTEMATIC: Use centralized assistant data service
      const { AssistantDataService } = await import('../services/assistantDataService');

      // Load assistant subdomain with timeout
      let subdomain = null;
      try {
        const subdomainPromise = AssistantDataService.getAssistantSubdomain(assistantId, attorneyId);
        const subdomainTimeout = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Subdomain lookup timeout')), 5000)
        );
        subdomain = await Promise.race([subdomainPromise, subdomainTimeout]);
      } catch (subdomainError) {
        console.warn('⚠️ [AssistantAwareContext] Subdomain lookup failed:', subdomainError.message);
      }
      setAssistantSubdomain(subdomain);

      // Load and sync assistant name from Vapi (with improved error handling)
      let assistantName = null;
      try {
        assistantName = await AssistantDataService.syncAssistantNameFromVapi(attorneyId, assistantId);
      } catch (nameError) {
        console.warn('⚠️ [AssistantAwareContext] Assistant name sync failed:', nameError.message);
      }
      setAssistantName(assistantName);

      console.log('✅ [AssistantAwareContext] Loaded assistant data:', {
        assistantId,
        subdomain,
        assistantName,
        hasSubdomain: !!subdomain,
        hasName: !!assistantName
      });

    } catch (error) {
      console.error('❌ [AssistantAwareContext] Error loading assistant data:', error);
      setAssistantSubdomain(null);
      setAssistantName(null);
    } finally {
      setLoadingAssistantData(false);
    }
  };

  // Get current assistant data
  const currentAssistant = useMemo(() => {
    if (!attorney || !currentAssistantId) return null;

    return {
      id: currentAssistantId,
      subdomain: assistantSubdomain || attorney.subdomain, // Fallback to attorney subdomain
      firmName: attorney.firm_name,
      attorneyName: attorney.name,
      attorneyId: attorney.id,
      assistantName: assistantName,
      ...attorney
    };
  }, [attorney, currentAssistantId, assistantSubdomain, assistantName]);

  // Generate assistant-aware URLs
  const urls = useMemo(() => {
    console.log('🔗 [AssistantAwareContext] Generating URLs:', {
      currentAssistantId,
      assistantSubdomain,
      attorneySubdomain: attorney?.subdomain,
      hasAssistantSelected: Boolean(currentAssistantId)
    });

    // CRITICAL: Only use assistant subdomain if we have a selected assistant
    // Don't fall back to attorney subdomain for assistant-specific URLs
    if (!currentAssistantId || !assistantSubdomain) {
      console.log('⚠️ [AssistantAwareContext] No assistant selected or no assistant subdomain - using placeholder');
      return {
        shareUrl: 'https://select-assistant.legalscout.net',
        embedUrl: 'https://select-assistant.legalscout.net/embed',
        previewUrl: '/simple-preview?subdomain=default',
        webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call',
        baseUrl: 'https://select-assistant.legalscout.net'
      };
    }

    // Use ONLY assistant-specific subdomain for assistant-aware URLs
    const baseUrl = `https://${assistantSubdomain}.legalscout.net`;

    const generatedUrls = {
      shareUrl: baseUrl,
      embedUrl: `${baseUrl}/embed`,
      previewUrl: `/simple-preview?subdomain=${assistantSubdomain}&loadFromSupabase=true&useEnhancedPreview=true&assistantId=${currentAssistantId}`,
      webhookUrl: 'https://dashboard.legalscout.net/api/webhook/vapi-call',
      baseUrl: baseUrl
    };

    console.log('✅ [AssistantAwareContext] Generated assistant-specific URLs:', generatedUrls);
    return generatedUrls;
  }, [assistantSubdomain, currentAssistantId]);

  // Generate embed codes
  const embedCodes = useMemo(() => {
    const { embedUrl, shareUrl } = urls;
    // CRITICAL: Only use assistant subdomain for embed codes, no fallback to attorney
    const effectiveSubdomain = assistantSubdomain || 'select-assistant';

    return {
      iframe: `<iframe src="${embedUrl}" width="100%" height="600" frameborder="0" allow="microphone"></iframe>`,
      widget: `<!-- LegalScout Widget -->
<script src="https://widget.legalscout.net/loader.js" async></script>
<div data-legalscout-widget data-subdomain="${effectiveSubdomain}"></div>`,
      button: `<a href="${shareUrl}" target="_blank" style="display: inline-block; padding: 12px 24px; background-color: #4B74AA; color: white; text-decoration: none; border-radius: 6px; font-family: Arial, sans-serif;">Chat with ${currentAssistant?.firmName || 'Our'} AI Assistant</a>`
    };
  }, [urls, currentAssistant, assistantSubdomain, attorney?.subdomain]);

  // Copy to clipboard function
  const copyToClipboard = useCallback(async (text, type = 'Text') => {
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      return false;
    }
  }, []);

  // Generate call control URL
  const generateCallControlUrl = useCallback((callId, options = {}) => {
    if (!currentAssistant?.id || !callId) return null;

    const token = btoa(JSON.stringify({
      callId,
      assistantId: currentAssistant.id,
      attorneyId: currentAssistant.attorneyId,
      subdomain: currentAssistant.subdomain,
      timestamp: Date.now(),
      expires: Date.now() + (options.expiresInHours || 1) * 60 * 60 * 1000
    }));

    const baseUrl = options.baseUrl || window.location.origin;
    return `${baseUrl}/call-control?token=${token}`;
  }, [currentAssistant]);

  // Generate preview URL with theme and options
  const generatePreviewUrl = useCallback((options = {}) => {
    const {
      theme = 'light',
      mobile = false,
      fullscreen = false,
      ...otherOptions
    } = options;

    if (!currentAssistant?.subdomain) {
      return '/simple-preview?subdomain=default';
    }

    const params = new URLSearchParams({
      subdomain: currentAssistant.subdomain,
      theme,
      loadFromSupabase: 'true',
      useEnhancedPreview: 'true',
      assistantId: currentAssistant.id || '',
      ...otherOptions
    });

    if (mobile) params.set('mobile', 'true');
    if (fullscreen) params.set('fullscreen', 'true');

    return `/simple-preview?${params.toString()}`;
  }, [currentAssistant]);

  // Get assistant-specific environment variables
  const environmentVars = useMemo(() => {
    return {
      supabaseUrl: import.meta.env.VITE_SUPABASE_URL || window.VITE_SUPABASE_URL,
      supabaseKey: import.meta.env.VITE_SUPABASE_KEY || window.VITE_SUPABASE_KEY,
      vapiPublicKey: import.meta.env.VITE_VAPI_PUBLIC_KEY || window.VITE_VAPI_PUBLIC_KEY,
      vapiPrivateKey: import.meta.env.VITE_VAPI_SECRET_KEY || window.VITE_VAPI_SECRET_KEY,
      // Assistant-specific overrides could go here in the future
      assistantId: currentAssistant?.id,
      subdomain: currentAssistant?.subdomain
    };
  }, [currentAssistant]);

  // Context value
  const value = {
    // Current assistant data
    currentAssistant,
    loading: loading || loadingAssistantData,

    // URLs
    urls,

    // Embed codes
    embedCodes,

    // Utility functions
    copyToClipboard,
    generateCallControlUrl,
    generatePreviewUrl,

    // Environment variables
    environmentVars,

    // Helper functions
    isAssistantSelected: Boolean(currentAssistant?.id),
    getAssistantDisplayName: () => {
      // Prefer assistant name, then firm name, then attorney name
      return currentAssistant?.assistantName ||
             currentAssistant?.firmName ||
             currentAssistant?.attorneyName ||
             'LegalScout Assistant';
    },
    getSubdomain: () => assistantSubdomain || attorney?.subdomain || 'your-firm',
    hasAssistantSubdomain: Boolean(assistantSubdomain),

    // DIRECT CONTROL FUNCTIONS
    setCurrentAssistant: (assistantId) => {
      console.log('🎯 [AssistantAwareContext] DIRECT assistant change:', assistantId);

      // Validate the assistant ID before setting it
      if (assistantId && typeof assistantId === 'string') {
        // Check if it's a Supabase UUID (invalid for Vapi)
        if (assistantId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
          console.error('🚨 [AssistantAwareContext] Rejecting Supabase UUID as assistant ID:', assistantId);
          return;
        }

        // Check for mock IDs
        if (assistantId.includes('mock') || assistantId.includes('undefined')) {
          console.error('🚨 [AssistantAwareContext] Rejecting invalid assistant ID:', assistantId);
          return;
        }
      }

      setForceAssistantId(assistantId);

      // Also update the attorney manager if available
      try {
        const manager = window.standaloneAttorneyManager;
        if (manager && manager.attorney && assistantId) {
          const updatedAttorney = {
            ...manager.attorney,
            current_assistant_id: assistantId
          };
          manager.attorney = updatedAttorney;
          manager.notifySubscribers();
          console.log('✅ [AssistantAwareContext] Updated attorney manager with new assistant ID');
        }
      } catch (error) {
        console.warn('⚠️ [AssistantAwareContext] Could not update attorney manager:', error);
      }
    }
  };

  return (
    <AssistantAwareContext.Provider value={value}>
      {children}
    </AssistantAwareContext.Provider>
  );
};

export default AssistantAwareProvider;
