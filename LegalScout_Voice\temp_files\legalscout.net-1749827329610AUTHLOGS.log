home:16 🚀 [LegalScout] Initializing environment...
home:38 ✅ [LegalScout] Environment initialized
index-c6dceca3.js:48 Attaching Supabase client to window.supabase
index-c6dceca3.js:105 [VapiLoader] Starting Vapi SDK loading process
index-c6dceca3.js:105 [VapiLoader] Attempting to import @vapi-ai/web package
index-c6dceca3.js:192 [VapiMcpService] Created clean fetch from iframe
index-c6dceca3.js:192 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
index-c6dceca3.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at hfe.setupRealtimeSubscription (index-c6dceca3.js:531:35095)
    at hfe.autoInitializeFromLocalStorage (index-c6dceca3.js:531:27820)
    at new hfe (index-c6dceca3.js:531:27600)
    at index-c6dceca3.js:531:56692
overrideMethod @ hook.js:608
Sn @ index-c6dceca3.js:48
setupRealtimeSubscription @ index-c6dceca3.js:531
autoInitializeFromLocalStorage @ index-c6dceca3.js:531
hfe @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
index-c6dceca3.js:531 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at hfe.setupRealtimeSubscription (index-c6dceca3.js:531:35095)
    at hfe.autoInitializeFromLocalStorage (index-c6dceca3.js:531:27820)
    at new hfe (index-c6dceca3.js:531:27600)
    at index-c6dceca3.js:531:56692
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-c6dceca3.js:531
hfe @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
index-c6dceca3.js:48 🔐 [AuthContext] Unexpected error checking auth: Error: Supabase client not initialized. Use getSupabaseClient() first.
    at Object.get (index-c6dceca3.js:48:75549)
    at index-c6dceca3.js:48:79344
    at index-c6dceca3.js:48:80965
    at m_ (index-c6dceca3.js:40:24270)
    at Id (index-c6dceca3.js:40:42393)
    at index-c6dceca3.js:40:40710
    at D (index-c6dceca3.js:25:1585)
    at MessagePort.j (index-c6dceca3.js:25:1948)
overrideMethod @ hook.js:608
(anonymous) @ index-c6dceca3.js:48
(anonymous) @ index-c6dceca3.js:48
m_ @ index-c6dceca3.js:40
Id @ index-c6dceca3.js:40
(anonymous) @ index-c6dceca3.js:40
D @ index-c6dceca3.js:25
j @ index-c6dceca3.js:25
index-c6dceca3.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at index-c6dceca3.js:48:81003
    at index-c6dceca3.js:48:82389
    at m_ (index-c6dceca3.js:40:24270)
    at Id (index-c6dceca3.js:40:42393)
    at index-c6dceca3.js:40:40710
    at D (index-c6dceca3.js:25:1585)
    at MessagePort.j (index-c6dceca3.js:25:1948)
overrideMethod @ hook.js:608
Sn @ index-c6dceca3.js:48
(anonymous) @ index-c6dceca3.js:48
(anonymous) @ index-c6dceca3.js:48
m_ @ index-c6dceca3.js:40
Id @ index-c6dceca3.js:40
(anonymous) @ index-c6dceca3.js:40
D @ index-c6dceca3.js:25
j @ index-c6dceca3.js:25
index-c6dceca3.js:48 Failed to set up auth listener: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at index-c6dceca3.js:48:81003
    at index-c6dceca3.js:48:82389
    at m_ (index-c6dceca3.js:40:24270)
    at Id (index-c6dceca3.js:40:42393)
    at index-c6dceca3.js:40:40710
    at D (index-c6dceca3.js:25:1585)
    at MessagePort.j (index-c6dceca3.js:25:1948)
overrideMethod @ hook.js:608
(anonymous) @ index-c6dceca3.js:48
await in (anonymous)
(anonymous) @ index-c6dceca3.js:48
m_ @ index-c6dceca3.js:40
Id @ index-c6dceca3.js:40
(anonymous) @ index-c6dceca3.js:40
D @ index-c6dceca3.js:25
j @ index-c6dceca3.js:25
index.ts:5 Loaded contentScript
home:1 Error handling response: TypeError: Cannot set properties of undefined (setting 'current')
    at chrome-extension://kljjoeapehcmaphfcjkmbhkinoaopdnd/app.bundle.js:410:1620
Vivaldi: Prohibitted blocking overflowing/scrolling of the document.
index-c6dceca3.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at hfe.loadAttorneyById (index-c6dceca3.js:531:34723)
    at index-c6dceca3.js:531:28060
overrideMethod @ hook.js:608
Sn @ index-c6dceca3.js:48
loadAttorneyById @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
setTimeout
autoInitializeFromLocalStorage @ index-c6dceca3.js:531
hfe @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
index-c6dceca3.js:531 [AttorneyProfileManager] Error loading attorney by id: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at hfe.loadAttorneyById (index-c6dceca3.js:531:34723)
    at index-c6dceca3.js:531:28060
overrideMethod @ hook.js:608
loadAttorneyById @ index-c6dceca3.js:531
await in loadAttorneyById
(anonymous) @ index-c6dceca3.js:531
setTimeout
autoInitializeFromLocalStorage @ index-c6dceca3.js:531
hfe @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
index-c6dceca3.js:531 [AttorneyProfileManager] Could not refresh attorney data for auto-sync: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at hfe.loadAttorneyById (index-c6dceca3.js:531:34723)
    at index-c6dceca3.js:531:28060
overrideMethod @ hook.js:608
(anonymous) @ index-c6dceca3.js:531
setTimeout
autoInitializeFromLocalStorage @ index-c6dceca3.js:531
hfe @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
index-c6dceca3.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at gL (index-c6dceca3.js:48:76048)
    at b (index-c6dceca3.js:477:41536)
    at Object.ZV (index-c6dceca3.js:37:9864)
    at QV (index-c6dceca3.js:37:10018)
    at e7 (index-c6dceca3.js:37:10075)
    at XC (index-c6dceca3.js:37:31482)
    at y6 (index-c6dceca3.js:37:31899)
overrideMethod @ hook.js:608
Sn @ index-c6dceca3.js:48
gL @ index-c6dceca3.js:48
b @ index-c6dceca3.js:477
ZV @ index-c6dceca3.js:37
QV @ index-c6dceca3.js:37
e7 @ index-c6dceca3.js:37
XC @ index-c6dceca3.js:37
y6 @ index-c6dceca3.js:37
(anonymous) @ index-c6dceca3.js:37
A2 @ index-c6dceca3.js:40
zP @ index-c6dceca3.js:37
dy @ index-c6dceca3.js:37
t2 @ index-c6dceca3.js:37
g7 @ index-c6dceca3.js:37
index-c6dceca3.js:48 💥 [Auth] Google sign-in error: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at gL (index-c6dceca3.js:48:76048)
    at b (index-c6dceca3.js:477:41536)
    at Object.ZV (index-c6dceca3.js:37:9864)
    at QV (index-c6dceca3.js:37:10018)
    at e7 (index-c6dceca3.js:37:10075)
    at XC (index-c6dceca3.js:37:31482)
    at y6 (index-c6dceca3.js:37:31899)
overrideMethod @ hook.js:608
gL @ index-c6dceca3.js:48
await in gL
b @ index-c6dceca3.js:477
ZV @ index-c6dceca3.js:37
QV @ index-c6dceca3.js:37
e7 @ index-c6dceca3.js:37
XC @ index-c6dceca3.js:37
y6 @ index-c6dceca3.js:37
(anonymous) @ index-c6dceca3.js:37
A2 @ index-c6dceca3.js:40
zP @ index-c6dceca3.js:37
dy @ index-c6dceca3.js:37
t2 @ index-c6dceca3.js:37
g7 @ index-c6dceca3.js:37
index-c6dceca3.js:477 Google sign-in error: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at gL (index-c6dceca3.js:48:76048)
    at b (index-c6dceca3.js:477:41536)
    at Object.ZV (index-c6dceca3.js:37:9864)
    at QV (index-c6dceca3.js:37:10018)
    at e7 (index-c6dceca3.js:37:10075)
    at XC (index-c6dceca3.js:37:31482)
    at y6 (index-c6dceca3.js:37:31899)
overrideMethod @ hook.js:608
b @ index-c6dceca3.js:477
await in b
ZV @ index-c6dceca3.js:37
QV @ index-c6dceca3.js:37
e7 @ index-c6dceca3.js:37
XC @ index-c6dceca3.js:37
y6 @ index-c6dceca3.js:37
(anonymous) @ index-c6dceca3.js:37
A2 @ index-c6dceca3.js:40
zP @ index-c6dceca3.js:37
dy @ index-c6dceca3.js:37
t2 @ index-c6dceca3.js:37
g7 @ index-c6dceca3.js:37
feedback.js:1 [Violation] Added non-passive event listener to a scroll-blocking 'touchstart' event. Consider marking event handler as 'passive' to make the page more responsive. See https://www.chromestatus.com/feature/5745543795965952
Ve.k.toolbar @ feedback.js:1
Ve @ feedback.js:1
(anonymous) @ feedback.js:1
u @ feedback.js:1
(anonymous) @ feedback.js:1
(anonymous) @ feedback.js:1
r @ feedback.js:1
s @ feedback.js:1
index-c6dceca3.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at hfe.setupRealtimeSubscription (index-c6dceca3.js:531:35095)
    at index-c6dceca3.js:531:36862
overrideMethod @ hook.js:608
Sn @ index-c6dceca3.js:48
setupRealtimeSubscription @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-c6dceca3.js:531
hfe @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
index-c6dceca3.js:531 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at hfe.setupRealtimeSubscription (index-c6dceca3.js:531:35095)
    at index-c6dceca3.js:531:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-c6dceca3.js:531
hfe @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
index-c6dceca3.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at hfe.setupRealtimeSubscription (index-c6dceca3.js:531:35095)
    at index-c6dceca3.js:531:36862
overrideMethod @ hook.js:608
Sn @ index-c6dceca3.js:48
setupRealtimeSubscription @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-c6dceca3.js:531
hfe @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
index-c6dceca3.js:531 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at hfe.setupRealtimeSubscription (index-c6dceca3.js:531:35095)
    at index-c6dceca3.js:531:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-c6dceca3.js:531
hfe @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
ActiveCheckHelper.ts:21 received intentional event
ActiveCheckHelper.ts:8 updating page active status
index-c6dceca3.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at hfe.setupRealtimeSubscription (index-c6dceca3.js:531:35095)
    at index-c6dceca3.js:531:36862
overrideMethod @ hook.js:608
Sn @ index-c6dceca3.js:48
setupRealtimeSubscription @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-c6dceca3.js:531
hfe @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
index-c6dceca3.js:531 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at hfe.setupRealtimeSubscription (index-c6dceca3.js:531:35095)
    at index-c6dceca3.js:531:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-c6dceca3.js:531
hfe @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
ActiveCheckHelper.ts:21 received intentional event
index-c6dceca3.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at gL (index-c6dceca3.js:48:76048)
    at b (index-c6dceca3.js:477:41536)
    at Object.ZV (index-c6dceca3.js:37:9864)
    at QV (index-c6dceca3.js:37:10018)
    at e7 (index-c6dceca3.js:37:10075)
    at XC (index-c6dceca3.js:37:31482)
    at y6 (index-c6dceca3.js:37:31899)
overrideMethod @ hook.js:608
Sn @ index-c6dceca3.js:48
gL @ index-c6dceca3.js:48
b @ index-c6dceca3.js:477
ZV @ index-c6dceca3.js:37
QV @ index-c6dceca3.js:37
e7 @ index-c6dceca3.js:37
XC @ index-c6dceca3.js:37
y6 @ index-c6dceca3.js:37
(anonymous) @ index-c6dceca3.js:37
A2 @ index-c6dceca3.js:40
zP @ index-c6dceca3.js:37
dy @ index-c6dceca3.js:37
t2 @ index-c6dceca3.js:37
g7 @ index-c6dceca3.js:37
index-c6dceca3.js:48 💥 [Auth] Google sign-in error: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at gL (index-c6dceca3.js:48:76048)
    at b (index-c6dceca3.js:477:41536)
    at Object.ZV (index-c6dceca3.js:37:9864)
    at QV (index-c6dceca3.js:37:10018)
    at e7 (index-c6dceca3.js:37:10075)
    at XC (index-c6dceca3.js:37:31482)
    at y6 (index-c6dceca3.js:37:31899)
overrideMethod @ hook.js:608
gL @ index-c6dceca3.js:48
await in gL
b @ index-c6dceca3.js:477
ZV @ index-c6dceca3.js:37
QV @ index-c6dceca3.js:37
e7 @ index-c6dceca3.js:37
XC @ index-c6dceca3.js:37
y6 @ index-c6dceca3.js:37
(anonymous) @ index-c6dceca3.js:37
A2 @ index-c6dceca3.js:40
zP @ index-c6dceca3.js:37
dy @ index-c6dceca3.js:37
t2 @ index-c6dceca3.js:37
g7 @ index-c6dceca3.js:37
index-c6dceca3.js:477 Google sign-in error: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at gL (index-c6dceca3.js:48:76048)
    at b (index-c6dceca3.js:477:41536)
    at Object.ZV (index-c6dceca3.js:37:9864)
    at QV (index-c6dceca3.js:37:10018)
    at e7 (index-c6dceca3.js:37:10075)
    at XC (index-c6dceca3.js:37:31482)
    at y6 (index-c6dceca3.js:37:31899)
overrideMethod @ hook.js:608
b @ index-c6dceca3.js:477
await in b
ZV @ index-c6dceca3.js:37
QV @ index-c6dceca3.js:37
e7 @ index-c6dceca3.js:37
XC @ index-c6dceca3.js:37
y6 @ index-c6dceca3.js:37
(anonymous) @ index-c6dceca3.js:37
A2 @ index-c6dceca3.js:40
zP @ index-c6dceca3.js:37
dy @ index-c6dceca3.js:37
t2 @ index-c6dceca3.js:37
g7 @ index-c6dceca3.js:37
ActiveCheckHelper.ts:8 updating page active status
index-c6dceca3.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at hfe.setupRealtimeSubscription (index-c6dceca3.js:531:35095)
    at index-c6dceca3.js:531:36862
overrideMethod @ hook.js:608
Sn @ index-c6dceca3.js:48
setupRealtimeSubscription @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-c6dceca3.js:531
hfe @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
index-c6dceca3.js:531 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at hfe.setupRealtimeSubscription (index-c6dceca3.js:531:35095)
    at index-c6dceca3.js:531:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-c6dceca3.js:531
hfe @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
index-c6dceca3.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at hfe.setupRealtimeSubscription (index-c6dceca3.js:531:35095)
    at index-c6dceca3.js:531:36862
overrideMethod @ hook.js:608
Sn @ index-c6dceca3.js:48
setupRealtimeSubscription @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-c6dceca3.js:531
hfe @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
index-c6dceca3.js:531 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at hfe.setupRealtimeSubscription (index-c6dceca3.js:531:35095)
    at index-c6dceca3.js:531:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-c6dceca3.js:531
hfe @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
index-c6dceca3.js:48 ❌ [Supabase] Failed to create client: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at hfe.setupRealtimeSubscription (index-c6dceca3.js:531:35095)
    at index-c6dceca3.js:531:36862
overrideMethod @ hook.js:608
Sn @ index-c6dceca3.js:48
setupRealtimeSubscription @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-c6dceca3.js:531
hfe @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
index-c6dceca3.js:531 [AttorneyProfileManager] Error setting up Realtime subscription: TypeError: Cannot read properties of undefined (reading 'headers')
    at new uG (index-c6dceca3.js:48:69816)
    at dG (index-c6dceca3.js:48:72276)
    at Sn (index-c6dceca3.js:48:74981)
    at hfe.setupRealtimeSubscription (index-c6dceca3.js:531:35095)
    at index-c6dceca3.js:531:36862
overrideMethod @ hook.js:608
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
(anonymous) @ index-c6dceca3.js:531
setTimeout
setupRealtimeSubscription @ index-c6dceca3.js:531
await in setupRealtimeSubscription
autoInitializeFromLocalStorage @ index-c6dceca3.js:531
hfe @ index-c6dceca3.js:531
(anonymous) @ index-c6dceca3.js:531
