// System Test Integration
(function() {
  console.log('🧪 [TEST] Initializing system test integration...');
  
  // Basic system health check
  window.systemTest = {
    checkHealth: function() {
      const checks = {
        react: typeof React !== 'undefined',
        supabase: typeof window.supabase !== 'undefined',
        vapi: typeof window.vapi !== 'undefined' || typeof window.VITE_VAPI_PUBLIC_KEY !== 'undefined'
      };
      
      console.log('🧪 [TEST] System health:', checks);
      return checks;
    }
  };
  
  console.log('✅ [TEST] System test integration ready');
})();