home:16 🚀 [LegalScout] Initializing environment...
home:38 ✅ [LegalScout] Environment initialized
home:127 🔧 [FixMyAuth] Function loaded. Run fixMyAuth() in console to fix authentication.
home:214 🧪 [TestAuthFlow] Inline test loaded. Run testAuthFlowInline() in console.
index-e3b9f71c.js:105 [VapiLoader] Starting Vapi SDK loading process
index-e3b9f71c.js:105 [VapiLoader] Attempting to import @vapi-ai/web package
index-e3b9f71c.js:192 [VapiMcpService] Created clean fetch from iframe
index-e3b9f71c.js:192 [VapiMcpService] INFO: Vapi MCP Service initialized {maxAttempts: 3, timeout: 30000, reconnectDelay: 5000, hasCleanFetch: true}
index-e3b9f71c.js:48 ❌ [Supabase] Production client failed, using stub: Cannot read properties of undefined (reading 'headers')
wR @ index-e3b9f71c.js:48
Ja @ index-e3b9f71c.js:48
(anonymous) @ index-e3b9f71c.js:48
(anonymous) @ index-e3b9f71c.js:48
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:1067 ❌ [App] Assistant routing service failed: TypeError: xe is not a function
    at index-e3b9f71c.js:1067:4013
(anonymous) @ index-e3b9f71c.js:1067
await in (anonymous)
(anonymous) @ index-e3b9f71c.js:1067
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:192 
            
            
           POST https://api.vapi.ai/call/web 400 (Bad Request)
(anonymous) @ index-e3b9f71c.js:192
(anonymous) @ index-e3b9f71c.js:192
await in (anonymous)
callControllerCreateWebCall @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
(anonymous) @ index-e3b9f71c.js:155
Promise.then
(anonymous) @ index-e3b9f71c.js:155
await in (anonymous)
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:155
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:192 Response {data: null, error: {…}, type: 'cors', url: 'https://api.vapi.ai/call/web', redirected: false, …}
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
Promise.then
(anonymous) @ index-e3b9f71c.js:155
await in (anonymous)
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:155
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Vapi error: Response {data: null, error: {…}, type: 'cors', url: 'https://api.vapi.ai/call/web', redirected: false, …}
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
Promise.then
(anonymous) @ index-e3b9f71c.js:155
await in (anonymous)
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:155
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Continuing despite Vapi error
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
Promise.then
(anonymous) @ index-e3b9f71c.js:155
await in (anonymous)
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:155
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Vapi error: Response {data: null, error: {…}, type: 'cors', url: 'https://api.vapi.ai/call/web', redirected: false, …}
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
Promise.then
(anonymous) @ index-e3b9f71c.js:155
await in (anonymous)
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:155
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Continuing despite Vapi error
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
Promise.then
(anonymous) @ index-e3b9f71c.js:155
await in (anonymous)
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:155
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
VM2354 speech-particles.js:1 Uncaught SyntaxError: Unexpected token '<' (at VM2354 speech-particles.js:1:1)
index-e3b9f71c.js:192 SpeechParticles: updateAudioSource function is not available
x @ index-e3b9f71c.js:192
E.onload @ index-e3b9f71c.js:192
script
_ @ index-e3b9f71c.js:192
v @ index-e3b9f71c.js:192
(anonymous) @ index-e3b9f71c.js:192
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:192 
            
            
           POST https://api.vapi.ai/call/web 400 (Bad Request)
(anonymous) @ index-e3b9f71c.js:192
(anonymous) @ index-e3b9f71c.js:192
await in (anonymous)
callControllerCreateWebCall @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:192 Response {data: null, error: {…}, type: 'cors', url: 'https://api.vapi.ai/call/web', redirected: false, …}
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Vapi error: Response {data: null, error: {…}, type: 'cors', url: 'https://api.vapi.ai/call/web', redirected: false, …}
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Continuing despite Vapi error
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Vapi error: Response {data: null, error: {…}, type: 'cors', url: 'https://api.vapi.ai/call/web', redirected: false, …}
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Continuing despite Vapi error
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Vapi error: Response {data: null, error: {…}, type: 'cors', url: 'https://api.vapi.ai/call/web', redirected: false, …}
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Continuing despite Vapi error
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Vapi error: Response {data: null, error: {…}, type: 'cors', url: 'https://api.vapi.ai/call/web', redirected: false, …}
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Continuing despite Vapi error
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:192 
            
            
           POST https://api.vapi.ai/call/web 400 (Bad Request)
(anonymous) @ index-e3b9f71c.js:192
(anonymous) @ index-e3b9f71c.js:192
await in (anonymous)
callControllerCreateWebCall @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
(anonymous) @ index-e3b9f71c.js:155
Promise.then
(anonymous) @ index-e3b9f71c.js:155
await in (anonymous)
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:155
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:192 Response {data: null, error: {…}, type: 'cors', url: 'https://api.vapi.ai/call/web', redirected: false, …}
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
Promise.then
(anonymous) @ index-e3b9f71c.js:155
await in (anonymous)
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:155
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Vapi error: Response {data: null, error: {…}, type: 'cors', url: 'https://api.vapi.ai/call/web', redirected: false, …}
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
Promise.then
(anonymous) @ index-e3b9f71c.js:155
await in (anonymous)
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:155
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Continuing despite Vapi error
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
Promise.then
(anonymous) @ index-e3b9f71c.js:155
await in (anonymous)
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:155
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Vapi error: Response {data: null, error: {…}, type: 'cors', url: 'https://api.vapi.ai/call/web', redirected: false, …}
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
Promise.then
(anonymous) @ index-e3b9f71c.js:155
await in (anonymous)
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:155
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Continuing despite Vapi error
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
Promise.then
(anonymous) @ index-e3b9f71c.js:155
await in (anonymous)
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:155
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
speech-particles.js:1 Uncaught SyntaxError: Unexpected token '<' (at speech-particles.js:1:1)
index-e3b9f71c.js:192 SpeechParticles: updateAudioSource function is not available
x @ index-e3b9f71c.js:192
E.onload @ index-e3b9f71c.js:192
script
_ @ index-e3b9f71c.js:192
v @ index-e3b9f71c.js:192
(anonymous) @ index-e3b9f71c.js:192
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:192 
            
            
           POST https://api.vapi.ai/call/web 400 (Bad Request)
(anonymous) @ index-e3b9f71c.js:192
(anonymous) @ index-e3b9f71c.js:192
await in (anonymous)
callControllerCreateWebCall @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:192 Response {data: null, error: {…}, type: 'cors', url: 'https://api.vapi.ai/call/web', redirected: false, …}
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Vapi error: Response {data: null, error: {…}, type: 'cors', url: 'https://api.vapi.ai/call/web', redirected: false, …}
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Continuing despite Vapi error
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Vapi error: Response {data: null, error: {…}, type: 'cors', url: 'https://api.vapi.ai/call/web', redirected: false, …}
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Continuing despite Vapi error
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Vapi error: Response {data: null, error: {…}, type: 'cors', url: 'https://api.vapi.ai/call/web', redirected: false, …}
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Continuing despite Vapi error
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Vapi error: Response {data: null, error: {…}, type: 'cors', url: 'https://api.vapi.ai/call/web', redirected: false, …}
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
index-e3b9f71c.js:113 Continuing despite Vapi error
onError @ index-e3b9f71c.js:113
Ar.emit @ index-e3b9f71c.js:192
emit @ index-e3b9f71c.js:192
start @ index-e3b9f71c.js:192
await in start
(anonymous) @ index-e3b9f71c.js:155
(anonymous) @ index-e3b9f71c.js:197
(anonymous) @ index-e3b9f71c.js:197
setTimeout
(anonymous) @ index-e3b9f71c.js:197
__ @ index-e3b9f71c.js:40
Id @ index-e3b9f71c.js:40
(anonymous) @ index-e3b9f71c.js:40
D @ index-e3b9f71c.js:25
j @ index-e3b9f71c.js:25
