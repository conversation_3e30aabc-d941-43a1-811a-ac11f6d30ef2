/**
 * Simple test runner for assistant state management tests
 * Can be executed from browser console or as a standalone script
 */

import { assistantStateTest } from '../tests/assistantStateManagement.test.js';

/**
 * Run assistant state management tests
 * @param {boolean} cleanup - Whether to clean up test data after running
 * @returns {Promise<Object>} Test results
 */
export async function runAssistantStateTests(cleanup = true) {
  console.log('🚀 [TestRunner] Starting assistant state management tests...');
  
  try {
    // Run all tests
    const results = await assistantStateTest.runAllTests();
    
    // Clean up test data if requested
    if (cleanup) {
      await assistantStateTest.cleanup();
    }
    
    console.log('🏁 [TestRunner] Tests completed!');
    return results;
  } catch (error) {
    console.error('💥 [TestRunner] Test execution failed:', error);
    
    // Try to clean up even if tests failed
    if (cleanup) {
      try {
        await assistantStateTest.cleanup();
      } catch (cleanupError) {
        console.warn('⚠️ [TestRunner] Cleanup failed:', cleanupError.message);
      }
    }
    
    throw error;
  }
}

/**
 * Run tests and display results in a formatted way
 */
export async function runAndDisplayTests() {
  try {
    const results = await runAssistantStateTests();
    
    console.log('\n📊 [TestRunner] Test Results Summary:');
    console.log('=====================================');
    
    results.results.forEach(result => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${result.test}: ${result.message}`);
    });
    
    console.log('=====================================');
    console.log(`Total: ${results.passedCount}/${results.totalCount} tests passed`);
    
    if (results.allPassed) {
      console.log('🎉 All tests passed! Assistant state management is working correctly.');
    } else {
      console.log('⚠️ Some tests failed. Please review the issues above.');
    }
    
    return results;
  } catch (error) {
    console.error('💥 [TestRunner] Failed to run tests:', error);
    return null;
  }
}

// Make functions available globally for browser console testing
if (typeof window !== 'undefined') {
  window.runAssistantStateTests = runAssistantStateTests;
  window.runAndDisplayTests = runAndDisplayTests;
  
  console.log('🔧 [TestRunner] Test functions available globally:');
  console.log('   - runAssistantStateTests()');
  console.log('   - runAndDisplayTests()');
}
