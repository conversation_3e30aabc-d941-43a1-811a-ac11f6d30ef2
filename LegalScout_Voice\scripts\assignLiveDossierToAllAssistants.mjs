/**
 * Assign Live Dossier Tool to All Assistants
 * 
 * This script ensures every assistant has the live_dossier_api tool assigned
 * and removes the old Make.com webhook-based tool.
 */

import fs from 'fs';

// Configuration
const VAPI_API_URL = 'https://api.vapi.ai';
const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';

// Tool IDs
const OLD_LIVE_DOSSIER_TOOL_ID = '4a0d63cf-0b84-4eec-bddf-9c5869439d7e'; // Make.com webhook
const NEW_LIVE_DOSSIER_TOOL_ID = '023c4ae4-9516-413d-a6cf-8469da66bbe2'; // API Request tool

/**
 * Make a request to the Vapi API
 */
async function makeVapiRequest(endpoint, options = {}) {
  const url = `${VAPI_API_URL}${endpoint}`;
  
  const response = await fetch(url, {
    ...options,
    headers: {
      'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
      'Content-Type': 'application/json',
      ...options.headers
    }
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Vapi API Error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  return await response.json();
}

/**
 * Get all assistants
 */
async function getAllAssistants() {
  console.log('🔍 Fetching all assistants...');
  
  try {
    const assistants = await makeVapiRequest('/assistant?limit=1000');
    console.log(`✅ Found ${assistants.length} assistants`);
    return assistants;
  } catch (error) {
    console.error('❌ Error fetching assistants:', error.message);
    throw error;
  }
}

/**
 * Update assistant tools
 */
async function updateAssistantTools(assistantId, currentToolIds, assistantName) {
  try {
    // Remove old live_dossier tool if present
    const updatedToolIds = currentToolIds.filter(id => id !== OLD_LIVE_DOSSIER_TOOL_ID);
    
    // Add new live_dossier_api tool if not present
    if (!updatedToolIds.includes(NEW_LIVE_DOSSIER_TOOL_ID)) {
      updatedToolIds.push(NEW_LIVE_DOSSIER_TOOL_ID);
    }
    
    // Only update if there are changes
    if (JSON.stringify(currentToolIds.sort()) === JSON.stringify(updatedToolIds.sort())) {
      console.log(`⏭️  ${assistantName} (${assistantId}) - No changes needed`);
      return { updated: false, reason: 'no_changes' };
    }
    
    // Update the assistant
    const updateData = {
      toolIds: updatedToolIds
    };
    
    await makeVapiRequest(`/assistant/${assistantId}`, {
      method: 'PATCH',
      body: JSON.stringify(updateData)
    });
    
    const hadOldTool = currentToolIds.includes(OLD_LIVE_DOSSIER_TOOL_ID);
    const hadNewTool = currentToolIds.includes(NEW_LIVE_DOSSIER_TOOL_ID);
    
    let action = '';
    if (hadOldTool && !hadNewTool) {
      action = 'Replaced old tool with new API tool';
    } else if (!hadOldTool && !hadNewTool) {
      action = 'Added new API tool';
    } else if (hadOldTool && hadNewTool) {
      action = 'Removed old tool (kept new API tool)';
    }
    
    console.log(`✅ ${assistantName} (${assistantId}) - ${action}`);
    return { 
      updated: true, 
      action,
      oldToolCount: currentToolIds.length,
      newToolCount: updatedToolIds.length
    };
    
  } catch (error) {
    console.error(`❌ Error updating ${assistantName} (${assistantId}):`, error.message);
    return { updated: false, error: error.message };
  }
}

/**
 * Process all assistants
 */
async function processAllAssistants() {
  const assistants = await getAllAssistants();
  
  const results = {
    total: assistants.length,
    updated: 0,
    noChanges: 0,
    errors: 0,
    details: []
  };
  
  console.log('\n🔧 Processing assistants...\n');
  
  for (let i = 0; i < assistants.length; i++) {
    const assistant = assistants[i];
    const progress = `[${i + 1}/${assistants.length}]`;
    
    console.log(`${progress} Processing: ${assistant.name} (${assistant.id})`);
    
    const result = await updateAssistantTools(
      assistant.id,
      assistant.toolIds || [],
      assistant.name
    );
    
    results.details.push({
      id: assistant.id,
      name: assistant.name,
      ...result
    });
    
    if (result.updated) {
      results.updated++;
    } else if (result.error) {
      results.errors++;
    } else {
      results.noChanges++;
    }
    
    // Small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  return results;
}

/**
 * Generate summary report
 */
function generateReport(results) {
  console.log('\n📊 ASSIGNMENT SUMMARY');
  console.log('='.repeat(50));
  console.log(`📈 Total Assistants: ${results.total}`);
  console.log(`✅ Updated: ${results.updated}`);
  console.log(`⏭️  No Changes Needed: ${results.noChanges}`);
  console.log(`❌ Errors: ${results.errors}`);
  
  if (results.updated > 0) {
    console.log('\n✅ SUCCESSFULLY UPDATED:');
    results.details
      .filter(d => d.updated)
      .forEach(detail => {
        console.log(`  • ${detail.name} - ${detail.action}`);
      });
  }
  
  if (results.errors > 0) {
    console.log('\n❌ ERRORS:');
    results.details
      .filter(d => d.error)
      .forEach(detail => {
        console.log(`  • ${detail.name} - ${detail.error}`);
      });
  }
  
  console.log('\n🎯 TOOL ASSIGNMENT STATUS:');
  console.log(`• Old live_dossier tool (${OLD_LIVE_DOSSIER_TOOL_ID}): REMOVED`);
  console.log(`• New live_dossier_api tool (${NEW_LIVE_DOSSIER_TOOL_ID}): ASSIGNED`);
  
  console.log('\n💡 NEXT STEPS:');
  console.log('1. Test the new API Request tool with a few assistants');
  console.log('2. Monitor API endpoint logs for proper data flow');
  console.log('3. Verify dossier updates appear in your UI');
  console.log('4. Remove Make.com webhook dependency');
}

/**
 * Main execution function
 */
async function main() {
  try {
    console.log('🚀 Starting Live Dossier Tool Assignment...\n');
    
    console.log('🔧 TOOL MIGRATION PLAN:');
    console.log(`• Remove: live_dossier (${OLD_LIVE_DOSSIER_TOOL_ID}) - Make.com webhook`);
    console.log(`• Add: live_dossier_api (${NEW_LIVE_DOSSIER_TOOL_ID}) - Direct API`);
    console.log('');
    
    // Process all assistants
    const results = await processAllAssistants();
    
    // Generate and display report
    generateReport(results);
    
    // Save detailed results
    const reportData = {
      timestamp: new Date().toISOString(),
      migration: {
        from: `live_dossier (${OLD_LIVE_DOSSIER_TOOL_ID})`,
        to: `live_dossier_api (${NEW_LIVE_DOSSIER_TOOL_ID})`,
        type: 'Make.com webhook → Direct API Request'
      },
      results: results
    };
    
    fs.writeFileSync('live-dossier-assignment-report.json', JSON.stringify(reportData, null, 2));
    console.log('\n💾 Detailed report saved to: live-dossier-assignment-report.json');
    
    console.log('\n✅ Live Dossier Tool Assignment Complete!');
    
  } catch (error) {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  }
}

// Run the script
main();
