/**
 * Attorney Profile Manager
 *
 * A comprehensive service for managing attorney profiles across systems:
 * - Supa<PERSON> (database)
 * - <PERSON><PERSON><PERSON> (voice assistant)
 * - localStorage (client-side cache)
 */

import { getSupabaseClient } from '../lib/supabase';
import { vapiServiceManager } from './vapiServiceManager';

class AttorneyProfileManager {
  constructor() {
    this.currentAttorney = null;
    this.subscription = null;
    this.listeners = new Set();
    this.isInitialized = false;
    this.lastSyncTime = null;
    this.syncStatus = { consistent: false, message: 'Not synchronized yet' };
    this.isRecoveryMode = false;
    this.recoveryAttempts = 0;
    this.MAX_RECOVERY_ATTEMPTS = 3;
    this.pendingUpdates = new Map();
    this.initPromise = null;

    // Bind methods to ensure consistent 'this'
    this.initialize = this.initialize.bind(this);
    this.handleAttorneyUpdate = this.handleAttorneyUpdate.bind(this);
    this.checkVapiSynchronization = this.checkVapiSynchronization.bind(this);
    this.forceSynchronization = this.forceSynchronization.bind(this);

    // Auto-initialize from localStorage if available
    this.autoInitializeFromLocalStorage();
  }

  // Auto-initialize from localStorage if available
  autoInitializeFromLocalStorage() {
    try {
      const storedAttorney = this.loadFromLocalStorage();
      if (storedAttorney && storedAttorney.id) {
        console.log('[AttorneyProfileManager] Auto-initializing from localStorage');
        this.currentAttorney = storedAttorney;

        // Only set up Supabase Realtime subscription for authenticated users
        // Skip for preview/demo mode to prevent authentication errors
        if (this.isAuthenticatedUser(storedAttorney)) {
          this.setupRealtimeSubscription(storedAttorney.id).catch(error => {
            console.warn('[AttorneyProfileManager] Failed to set up realtime subscription:', error);
          });
        } else {
          console.log('[AttorneyProfileManager] Preview mode detected, skipping Realtime subscription');
        }

        // Schedule a sync check with refreshed data
        setTimeout(async () => {
          try {
            // Refresh attorney data from Supabase before sync check
            let attorneyToSync = storedAttorney;
            if (storedAttorney.id && typeof storedAttorney.id === 'string' && !storedAttorney.id.startsWith('dev-')) {
              try {
                const refreshedAttorney = await this.loadAttorneyById(storedAttorney.id);
                if (refreshedAttorney) {
                  console.log('[AttorneyProfileManager] Refreshed attorney data for auto-sync:', {
                    id: refreshedAttorney.id,
                    vapi_assistant_id: refreshedAttorney.vapi_assistant_id
                  });
                  attorneyToSync = refreshedAttorney;
                  this.currentAttorney = refreshedAttorney;
                  this.saveToLocalStorage(refreshedAttorney);
                }
              } catch (refreshError) {
                console.warn('[AttorneyProfileManager] Could not refresh attorney data for auto-sync:', refreshError);
              }
            }

            // Skip automatic Vapi sync - following one-way sync pattern
            console.log('[AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)');
          } catch (error) {
            console.error('[AttorneyProfileManager] Auto-sync check error:', error);
          }
        }, 2000);
      }
    } catch (error) {
      console.error('[AttorneyProfileManager] Auto-initialize error:', error);
    }
  }

  // Check if this is an authenticated user vs preview mode
  isAuthenticatedUser(attorney) {
    if (!attorney || !attorney.id) {
      return false;
    }

    // Development/mock attorneys are not authenticated users
    if (attorney.id.startsWith('dev-') || attorney.id.startsWith('mock-')) {
      return false;
    }

    // Check if we have a real user session (not just localStorage data)
    if (typeof window !== 'undefined') {
      // If there's no auth context or user session, this is preview mode
      const authContext = window.__AUTH_CONTEXT_VALUE__;
      if (!authContext || !authContext.user || !authContext.session) {
        return false;
      }
    }

    return true;
  }

  // Initialize the manager with the current user
  async initialize(userId, email = null) {
    // If already initializing, return the existing promise
    if (this.initPromise) {
      return this.initPromise;
    }

    // Create a new initialization promise
    this.initPromise = this._initialize(userId, email);

    try {
      const result = await this.initPromise;
      return result;
    } finally {
      // Clear the promise when done
      this.initPromise = null;
    }
  }

  // Internal initialization method
  async _initialize(userId, email = null) {
    console.log('[AttorneyProfileManager] Initializing with userId:', userId, 'email:', email);

    if (this.isInitialized && this.currentAttorney) {
      console.log('[AttorneyProfileManager] Already initialized with attorney:', this.currentAttorney.id);
      return this.currentAttorney;
    }

    try {
      // Try multiple methods to find the attorney profile
      let attorney = null;

      // Method 1: Try to load by user ID
      if (userId) {
        try {
          attorney = await this.loadAttorneyByUserId(userId);
          if (attorney) {
            console.log('[AttorneyProfileManager] Found attorney by userId:', attorney.id);
          }
        } catch (error) {
          console.warn('[AttorneyProfileManager] Error loading by userId:', error);
        }
      }

      // Method 2: Try to load by email if available
      if (!attorney && email) {
        try {
          attorney = await this.loadAttorneyByEmail(email);
          if (attorney) {
            console.log('[AttorneyProfileManager] Found attorney by email:', attorney.id);

            // CRITICAL: Prevent assistant creation if attorney already has one
            if (attorney.vapi_assistant_id && !attorney.vapi_assistant_id.includes('mock')) {
              console.log('[AttorneyProfileManager] ✅ Attorney already has valid assistant:', attorney.vapi_assistant_id);
            }

            // If found by email but not linked to user ID, update the link
            if (userId && (!attorney.user_id || attorney.user_id !== userId)) {
              console.log('[AttorneyProfileManager] Linking attorney to userId:', userId);
              attorney = await this.updateAttorneyInSupabase({
                id: attorney.id,
                user_id: userId
              });
            }
          }
        } catch (error) {
          console.warn('[AttorneyProfileManager] Error loading by email:', error);
        }
      }

      // Method 3: Try to load from localStorage
      if (!attorney) {
        const storedAttorney = this.loadFromLocalStorage();
        if (storedAttorney && storedAttorney.id) {
          console.log('[AttorneyProfileManager] Found attorney in localStorage:', storedAttorney.id);

          // Verify the attorney exists in Supabase
          try {
            attorney = await this.loadAttorneyById(storedAttorney.id);
            if (attorney) {
              console.log('[AttorneyProfileManager] Verified attorney from localStorage exists in Supabase');

              // If not linked to user ID, update the link
              if (userId && (!attorney.user_id || attorney.user_id !== userId)) {
                console.log('[AttorneyProfileManager] Linking attorney to userId:', userId);
                attorney = await this.updateAttorneyInSupabase({
                  id: attorney.id,
                  user_id: userId
                });
              }
            }
          } catch (error) {
            console.warn('[AttorneyProfileManager] Error verifying localStorage attorney:', error);

            // Use the localStorage attorney as a fallback
            attorney = storedAttorney;
          }
        }
      }

      // If still not found, create a new attorney profile
      if (!attorney && (userId || email)) {
        console.log('[AttorneyProfileManager] No attorney found, creating new attorney profile');

        const userEmail = email || `user-${userId}@example.com`;
        const name = userEmail.split('@')[0];
        const domain = userEmail.split('@')[1];
        const firmName = domain ? `${domain.split('.')[0]} Legal` : 'My Law Firm';

        // Try to create in Supabase first
        try {
          const { supabase } = await import('../lib/supabase');

          const attorneyData = {
            subdomain: `${name}-${Date.now()}`.toLowerCase().replace(/[^a-z0-9-]/g, '-'),
            firm_name: firmName,
            name: name,
            email: userEmail,
            user_id: userId,
            is_active: true,
            vapi_instructions: `You are a legal assistant for ${firmName}. Help potential clients understand their legal needs and collect relevant information for consultation.`,
            welcome_message: `Hello! I'm Scout from ${firmName}. How can I help you with your legal needs today?`,
            information_gathering: 'Tell me about your legal situation, and I\'ll help connect you with the right resources and guidance.',
            primary_color: '#4B74AA',
            secondary_color: '#2C3E50',
            background_color: '#1a1a1a',
            voice_provider: 'playht',
            voice_id: 'ranger',
            ai_model: 'gpt-4o'
          };

          const { data: newAttorney, error } = await supabase
            .from('attorneys')
            .insert([attorneyData])
            .select()
            .single();

          if (!error && newAttorney) {
            console.log('[AttorneyProfileManager] Created new attorney in Supabase:', newAttorney.id);
            attorney = newAttorney;
          } else {
            console.warn('[AttorneyProfileManager] Failed to create attorney in Supabase:', error);
            throw error;
          }
        } catch (supabaseError) {
          console.warn('[AttorneyProfileManager] Supabase creation failed, creating local attorney:', supabaseError);

          // Fallback to local attorney
          const devId = `dev-${Date.now()}`;
          attorney = {
            id: devId,
            user_id: userId,
            email: userEmail,
            firm_name: firmName,
            name: name,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            vapi_instructions: `You are a legal assistant for ${firmName}. Help potential clients understand their legal needs and collect relevant information for consultation.`,
            welcome_message: `Hello! I'm Scout from ${firmName}. How can I help you with your legal needs today?`,
            is_development: true
          };
        }

        console.log('[AttorneyProfileManager] Created development attorney:', attorney.id);
      }

      if (attorney) {
        // Store the attorney data
        this.currentAttorney = attorney;

        // Save to localStorage for offline access
        this.saveToLocalStorage(attorney);

        // Set up Supabase Realtime subscription for non-development attorneys
        if (attorney.id && typeof attorney.id === 'string' && !attorney.id.startsWith('dev-')) {
          this.setupRealtimeSubscription(attorney.id).catch(error => {
            console.warn('[AttorneyProfileManager] Failed to set up realtime subscription:', error);
          });
        } else {
          console.log('[AttorneyProfileManager] Development attorney or invalid ID, skipping Realtime subscription');
        }

        // Ensure attorney has latest data from Supabase before checking Vapi sync
        if (attorney.id && typeof attorney.id === 'string' && !attorney.id.startsWith('dev-')) {
          try {
            const latestAttorney = await this.loadAttorneyById(attorney.id);
            if (latestAttorney) {
              console.log('[AttorneyProfileManager] Refreshed attorney data before Vapi sync:', {
                id: latestAttorney.id,
                vapi_assistant_id: latestAttorney.vapi_assistant_id
              });
              attorney = latestAttorney;
              this.currentAttorney = latestAttorney;
              this.saveToLocalStorage(latestAttorney);
            }
          } catch (refreshError) {
            console.warn('[AttorneyProfileManager] Could not refresh attorney data:', refreshError);
          }
        }

        // CRITICAL MVP FIX: Skip automatic Vapi synchronization during initialization
        // Following one-way sync pattern: UI → Supabase → Vapi
        // Only sync when explicitly requested by user actions
        // This prevents duplicate assistant creation on every login
        console.log('[AttorneyProfileManager] 🛑 MVP FIX: Skipping automatic Vapi sync to prevent duplicate assistants');

        // Additional safety check: Log if attorney already has assistant
        if (attorney.vapi_assistant_id && !attorney.vapi_assistant_id.includes('mock')) {
          console.log('[AttorneyProfileManager] ✅ Attorney has valid assistant:', attorney.vapi_assistant_id);
        }

        this.isInitialized = true;
        this.notifyListeners();

        return attorney;
      }

      console.warn('[AttorneyProfileManager] No attorney profile found and could not create development attorney');
      return null;
    } catch (error) {
      console.error('[AttorneyProfileManager] Initialization error:', error);

      // Try to recover from localStorage
      const storedAttorney = this.loadFromLocalStorage();
      if (storedAttorney) {
        console.log('[AttorneyProfileManager] Recovering from localStorage');
        this.currentAttorney = storedAttorney;
        this.notifyListeners();
        return storedAttorney;
      }

      throw error;
    }
  }

  // Load attorney by user ID
  async loadAttorneyByUserId(userId) {
    try {
      console.log('[AttorneyProfileManager] Loading attorney by userId:', userId);
      const { data, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        throw error;
      }

      return data;
    } catch (error) {
      console.error('[AttorneyProfileManager] Error loading attorney by userId:', error);
      throw error;
    }
  }

  // Load attorney by email - FIXED: Prevent duplicate creation
  async loadAttorneyByEmail(email) {
    try {
      console.log('[AttorneyProfileManager] Loading attorney by email:', email);

      // CRITICAL FIX: Get the attorney with a valid vapi_assistant_id first
      const { data: attorneys, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('email', email)
        .order('updated_at', { ascending: false });

      if (error) {
        throw error;
      }

      if (attorneys && attorneys.length > 0) {
        if (attorneys.length > 1) {
          console.warn(`[AttorneyProfileManager] Found ${attorneys.length} attorneys for email ${email}`);

          // CRITICAL: Prefer attorney with valid vapi_assistant_id
          const attorneyWithAssistant = attorneys.find(a =>
            a.vapi_assistant_id &&
            !a.vapi_assistant_id.includes('mock') &&
            !a.vapi_assistant_id.includes('duplicate')
          );

          if (attorneyWithAssistant) {
            console.log(`[AttorneyProfileManager] Using attorney with valid assistant ID: ${attorneyWithAssistant.vapi_assistant_id}`);
            return attorneyWithAssistant;
          }
        }
        return attorneys[0];
      }

      return null;
    } catch (error) {
      console.error('[AttorneyProfileManager] Error loading attorney by email:', error);
      throw error;
    }
  }

  // Load attorney by ID
  async loadAttorneyById(id) {
    try {
      console.log('[AttorneyProfileManager] Loading attorney by id:', id);
      const supabase = await getSupabaseClient();
      const { data, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        throw error;
      }

      return data;
    } catch (error) {
      console.error('[AttorneyProfileManager] Error loading attorney by id:', error);
      throw error;
    }
  }

  // Set up Supabase Realtime subscription
  async setupRealtimeSubscription(attorneyId) {
    try {
      console.log('[AttorneyProfileManager] Setting up Realtime subscription for attorney:', attorneyId);

      const supabase = await getSupabaseClient();

      // Check if we got a valid Supabase client
      if (!supabase || typeof supabase.channel !== 'function') {
        console.warn('[AttorneyProfileManager] Supabase client not available or invalid, using polling instead');
        this.setupPollingFallback(attorneyId);
        return;
      }

      // Check if Supabase client has channel method (v2 API)
      if (supabase && typeof supabase.channel === 'function') {
        // Clean up existing channel if any
        if (this.channel) {
          supabase.removeChannel(this.channel);
          this.channel = null;
        }

        // Set up new channel subscription (v2 API)
        this.channel = supabase
          .channel(`attorneys:id=${attorneyId}`)
          .on('postgres_changes',
            {
              event: 'UPDATE',
              schema: 'public',
              table: 'attorneys',
              filter: `id=eq.${attorneyId}`
            },
            payload => this.handleAttorneyUpdate(payload)
          )
          .on('postgres_changes',
            {
              event: 'DELETE',
              schema: 'public',
              table: 'attorneys',
              filter: `id=eq.${attorneyId}`
            },
            payload => this.handleAttorneyDelete(payload)
          )
          .subscribe();

        console.log('[AttorneyProfileManager] Realtime subscription set up using channel API');
      }
      // Check if Supabase client has on method (v1 API)
      else if (typeof supabase.from === 'function' &&
               typeof supabase.from('attorneys').on === 'function') {
        // Clean up existing subscription if any
        if (this.subscription) {
          supabase.removeSubscription(this.subscription);
          this.subscription = null;
        }

        // Set up new subscription (v1 API)
        this.subscription = supabase
          .from(`attorneys:id=eq.${attorneyId}`)
          .on('UPDATE', payload => this.handleAttorneyUpdate(payload))
          .on('DELETE', payload => this.handleAttorneyDelete(payload))
          .subscribe();

        console.log('[AttorneyProfileManager] Realtime subscription set up using from().on() API');
      }
      else {
        console.warn('[AttorneyProfileManager] Supabase Realtime API not available, using polling instead');
        this.setupPollingFallback(attorneyId);
      }
    } catch (error) {
      console.error('[AttorneyProfileManager] Error setting up Realtime subscription:', error);

      // Fall back to polling instead of retrying
      console.log('[AttorneyProfileManager] Falling back to polling due to Realtime error');
      this.setupPollingFallback(attorneyId);
    }
  }

  // Set up polling fallback when Realtime is not available
  setupPollingFallback(attorneyId) {
    try {
      console.log('[AttorneyProfileManager] Setting up polling fallback for attorney:', attorneyId);

      // Clear any existing polling interval
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval);
      }

      this.pollingInterval = setInterval(async () => {
        try {
          if (this.currentAttorney && this.currentAttorney.id) {
            const supabase = await getSupabaseClient();

            // Check if we have a valid client
            if (!supabase || typeof supabase.from !== 'function') {
              console.warn('[AttorneyProfileManager] Supabase client not available for polling');
              return;
            }

            const { data, error } = await supabase
              .from('attorneys')
              .select('*')
              .eq('id', this.currentAttorney.id)
              .single();

            if (error) {
              console.warn('[AttorneyProfileManager] Error polling for attorney updates:', error);
              return;
            }

            // Check if data has changed
            if (data && JSON.stringify(data) !== JSON.stringify(this.currentAttorney)) {
              console.log('[AttorneyProfileManager] Attorney updated via polling');
              this.handleAttorneyUpdate({ new: data });
            }
          }
        } catch (error) {
          console.error('[AttorneyProfileManager] Error in polling fallback:', error);
        }
      }, 10000); // Poll every 10 seconds

      console.log('[AttorneyProfileManager] Polling fallback set up successfully');
    } catch (error) {
      console.error('[AttorneyProfileManager] Error setting up polling fallback:', error);
    }
  }

  // Handle attorney update from Supabase Realtime
  async handleAttorneyUpdate(payload) {
    console.log('[AttorneyProfileManager] Received attorney update from Supabase:', payload);

    try {
      const updatedAttorney = payload.new;

      // Check if this is a pending update we initiated
      const pendingUpdateId = updatedAttorney.id + '_' + updatedAttorney.updated_at;
      if (this.pendingUpdates.has(pendingUpdateId)) {
        console.log('[AttorneyProfileManager] This is a pending update we initiated, skipping sync check');
        this.pendingUpdates.delete(pendingUpdateId);
      } else {
        // This is an external update - skip automatic Vapi sync
        console.log('[AttorneyProfileManager] External update received, skipping automatic Vapi sync (following one-way sync pattern)');
      }

      // Update local state
      this.currentAttorney = updatedAttorney;

      // Save to localStorage
      this.saveToLocalStorage(updatedAttorney);

      // Notify listeners
      this.notifyListeners();
    } catch (error) {
      console.error('[AttorneyProfileManager] Error handling attorney update:', error);
    }
  }

  // Handle attorney delete from Supabase Realtime
  handleAttorneyDelete(payload) {
    console.log('[AttorneyProfileManager] Received attorney delete from Supabase:', payload);

    // Clear local state
    this.currentAttorney = null;

    // Clear localStorage
    try {
      localStorage.removeItem('attorney');
      localStorage.removeItem('attorney_last_updated');
    } catch (error) {
      console.error('[AttorneyProfileManager] Error clearing localStorage:', error);
    }

    // Notify listeners
    this.notifyListeners();
  }

  // Check if we should sync with Vapi based on changed fields
  shouldSyncWithVapi(updatedAttorney) {
    if (!this.currentAttorney) {
      // If no current attorney, only sync if the attorney has a Vapi assistant ID
      // or if it's missing one and needs to be created
      if (!updatedAttorney.vapi_assistant_id) {
        console.log('[AttorneyProfileManager] No current attorney and no assistant ID - sync needed to create assistant');
        return true;
      } else {
        console.log('[AttorneyProfileManager] No current attorney but has assistant ID - checking if assistant exists');
        return true; // We need to verify the assistant exists
      }
    }

    // Define fields that require Vapi sync (ONLY voice/AI related fields)
    const vapiRelevantFields = [
      'firm_name',
      'welcome_message',
      'vapi_instructions',
      'voice_provider',
      'voice_id',
      'ai_model',
      'vapi_assistant_id'
    ];

    // Define fields that should NEVER trigger Vapi sync
    const nonVapiFields = [
      'logo_url',
      'profile_image',
      'button_image',
      'primary_color',
      'secondary_color',
      'button_color',
      'background_color',
      'address',
      'phone',
      'practice_areas',
      'practice_description',
      'scheduling_link',
      'custom_fields',
      'summary_prompt',
      'structured_data_prompt',
      'structured_data_schema'
    ];

    // Check if any Vapi-relevant fields have changed
    for (const field of vapiRelevantFields) {
      if (this.currentAttorney[field] !== updatedAttorney[field]) {
        console.log(`[AttorneyProfileManager] Vapi-relevant field changed: ${field}`);
        console.log(`  Old: ${this.currentAttorney[field]}`);
        console.log(`  New: ${updatedAttorney[field]}`);
        return true;
      }
    }

    // Log if only non-Vapi fields changed (for debugging)
    const changedFields = [];
    for (const field in updatedAttorney) {
      if (this.currentAttorney[field] !== updatedAttorney[field]) {
        changedFields.push(field);
      }
    }

    if (changedFields.length > 0) {
      const nonVapiChanges = changedFields.filter(field => nonVapiFields.includes(field));
      if (nonVapiChanges.length > 0) {
        console.log(`[AttorneyProfileManager] Only non-Vapi fields changed: ${nonVapiChanges.join(', ')}`);
      }
    }

    // No Vapi-relevant fields changed
    return false;
  }

  // Check and fix Vapi synchronization
  async checkVapiSynchronization(attorney) {
    if (!attorney) {
      console.warn('[AttorneyProfileManager] No attorney to check Vapi synchronization');
      return;
    }

    // Handle case where attorney is passed as an array (fix for array issue)
    if (Array.isArray(attorney)) {
      console.log('[AttorneyProfileManager] Attorney passed as array, extracting first element');
      if (attorney.length > 0 && attorney[0] && attorney[0].id) {
        attorney = attorney[0];
        console.log('[AttorneyProfileManager] Using attorney from array:', attorney.id);
      } else {
        console.error('[AttorneyProfileManager] Array is empty or invalid:', attorney);
        this.syncStatus = {
          consistent: false,
          message: 'Invalid attorney array data',
          lastChecked: new Date(),
          error: 'Attorney array is empty or invalid'
        };
        return;
      }
    }

    // Validate attorney has required properties
    if (!attorney.id) {
      console.error('[AttorneyProfileManager] Attorney missing ID, cannot sync with Vapi:', attorney);
      this.syncStatus = {
        consistent: false,
        message: 'Attorney missing ID, cannot sync with voice service',
        lastChecked: new Date(),
        error: 'Invalid attorney data'
      };
      return;
    }

    console.log('[AttorneyProfileManager] Checking Vapi synchronization for attorney:', attorney.id);

    try {
      // Get the appropriate Vapi service
      const vapiService = vapiServiceManager.getMcpService();

      // Check if Vapi service is connected
      const connected = await vapiService.ensureConnection();
      if (!connected) {
        console.warn('[AttorneyProfileManager] Vapi service not connected, skipping synchronization');

        this.syncStatus = {
          consistent: true,
          message: 'Voice service not available, using local data',
          lastChecked: new Date()
        };

        this.lastSyncTime = new Date();
        return;
      }

      // Check if attorney has a mock assistant ID and fix it
      if (attorney.vapi_assistant_id && attorney.vapi_assistant_id.startsWith('mock-')) {
        console.warn('[AttorneyProfileManager] Mock assistant ID detected, attempting to fix:', attorney.vapi_assistant_id);

        // Check if we're in production environment
        const isProduction = typeof window !== 'undefined' &&
          (window.location.hostname === 'dashboard.legalscout.net' ||
           window.location.hostname.endsWith('.legalscout.net'));

        try {
          // First, try to find an existing real assistant for this firm
          const assistants = await vapiService.listAssistants();
          const firmAssistant = assistants.find(a =>
            a.name && a.name.includes(attorney.firm_name) && !a.id.startsWith('mock-')
          );

          if (firmAssistant) {
            console.log('[AttorneyProfileManager] Found existing real assistant:', firmAssistant.id);
            // Update database with real assistant ID
            await this.updateAttorneyInSupabase({
              id: attorney.id,
              vapi_assistant_id: firmAssistant.id
            });

            this.syncStatus = {
              consistent: true,
              message: 'Fixed mock assistant ID with existing real assistant',
              lastChecked: new Date()
            };
            this.lastSyncTime = new Date();
            return;
          } else if (isProduction) {
            // In production, create a new real assistant
            console.log('[AttorneyProfileManager] No existing assistant found, creating new one');
            const newAssistant = await this.createVapiAssistant(attorney);

            if (newAssistant && newAssistant.id && !newAssistant.id.startsWith('mock-')) {
              await this.updateAttorneyInSupabase({
                id: attorney.id,
                vapi_assistant_id: newAssistant.id
              });

              this.syncStatus = {
                consistent: true,
                message: 'Created new real assistant to replace mock',
                lastChecked: new Date()
              };
              this.lastSyncTime = new Date();
              return;
            }
          }
        } catch (error) {
          console.error('[AttorneyProfileManager] Error fixing mock assistant ID:', error);
        }

        // If we can't fix it, mark as inconsistent
        this.syncStatus = {
          consistent: false,
          message: 'Mock assistant ID detected - needs real assistant',
          lastChecked: new Date(),
          warning: 'Voice assistant needs to be properly configured'
        };
        this.lastSyncTime = new Date();
        return;
      }

      // Check if attorney has a Vapi assistant ID
      if (!attorney.vapi_assistant_id) {
        console.log('[AttorneyProfileManager] Attorney has no Vapi assistant ID, creating new assistant');

        // Create a new assistant
        const assistant = await this.createVapiAssistant(attorney);

        // Only update database if we got a real assistant ID (not mock)
        if (assistant && assistant.id && !assistant.id.startsWith('mock-')) {
          // Update attorney with assistant ID
          await this.updateAttorneyInSupabase({
            id: attorney.id,
            vapi_assistant_id: assistant.id
          });

          this.syncStatus = {
            consistent: true,
            message: 'Created new Vapi assistant',
            lastChecked: new Date()
          };
        } else {
          console.warn('[AttorneyProfileManager] Mock assistant created, not saving to database');
          this.syncStatus = {
            consistent: false,
            message: 'Mock assistant created, Vapi service may be unavailable',
            lastChecked: new Date(),
            warning: 'Voice assistant will be created when service is available'
          };
        }
      } else {
        // Check if the assistant exists and is up to date
        let assistant;
        try {
          assistant = await vapiServiceManager.getMcpService().getAssistant(attorney.vapi_assistant_id);
        } catch (error) {
          console.error('[AttorneyProfileManager] Error getting Vapi assistant:', error);

          // If MCP server is not available, use a mock assistant
          if (error.message.includes('MCP server') || error.message.includes('connection')) {
            console.warn('[AttorneyProfileManager] MCP server not available, using mock assistant');

            assistant = {
              id: attorney.vapi_assistant_id,
              name: `${attorney.firm_name} Assistant`,
              instructions: attorney.vapi_instructions,
              firstMessage: attorney.welcome_message,
              mock: true
            };
          } else {
            assistant = null;
          }
        }

        if (!assistant) {
          console.log('[AttorneyProfileManager] Assistant not found, creating new one');

          // Assistant doesn't exist, create a new one
          const newAssistant = await this.createVapiAssistant(attorney);

          // Update attorney with new assistant ID
          await this.updateAttorneyInSupabase({
            id: attorney.id,
            vapi_assistant_id: newAssistant.id
          });

          this.syncStatus = {
            consistent: true,
            message: 'Created replacement Vapi assistant',
            lastChecked: new Date()
          };
        } else {
          // Check for discrepancies
          const discrepancies = this.findDiscrepancies(attorney, assistant);

          if (Object.keys(discrepancies).length > 0) {
            console.log('[AttorneyProfileManager] Found discrepancies, updating assistant:', discrepancies);

            // Update the assistant
            await this.updateVapiAssistant(attorney);

            this.syncStatus = {
              consistent: true,
              message: 'Updated Vapi assistant',
              lastChecked: new Date()
            };
          } else {
            console.log('[AttorneyProfileManager] No discrepancies found, assistant is up to date');

            this.syncStatus = {
              consistent: true,
              message: 'Vapi assistant is up to date',
              lastChecked: new Date()
            };
          }
        }
      }

      this.lastSyncTime = new Date();
      this.isRecoveryMode = false;
      this.recoveryAttempts = 0;
    } catch (error) {
      console.error('[AttorneyProfileManager] Error checking Vapi synchronization:', error);

      // If MCP server is not available, mark as consistent but note the issue
      if (error.message.includes('MCP server') || error.message.includes('connection')) {
        console.warn('[AttorneyProfileManager] MCP server not available, marking as consistent but noting issue');

        this.syncStatus = {
          consistent: true,
          message: 'Vapi MCP service not available, using local data',
          lastChecked: new Date(),
          warning: error.message
        };

        this.lastSyncTime = new Date();
      } else {
        this.syncStatus = {
          consistent: false,
          message: `Sync error: ${error.message}`,
          lastChecked: new Date(),
          error: error.message
        };

        // Enter recovery mode if not already in it
        if (!this.isRecoveryMode) {
          this.isRecoveryMode = true;
          this.recoveryAttempts = 0;
          this.attemptRecovery();
        }
      }
    }
  }

  // Attempt recovery from synchronization errors
  async attemptRecovery() {
    if (this.recoveryAttempts >= this.MAX_RECOVERY_ATTEMPTS) {
      console.warn('[AttorneyProfileManager] Max recovery attempts reached, giving up');
      this.isRecoveryMode = false;
      return;
    }

    this.recoveryAttempts++;
    console.log(`[AttorneyProfileManager] Recovery attempt ${this.recoveryAttempts}/${this.MAX_RECOVERY_ATTEMPTS}`);

    try {
      // Try different recovery strategies based on the attempt number
      switch (this.recoveryAttempts) {
        case 1:
          // First attempt: Try to refresh from Supabase
          if (this.currentAttorney && this.currentAttorney.id) {
            const refreshedAttorney = await this.loadAttorneyById(this.currentAttorney.id);
            if (refreshedAttorney) {
              this.currentAttorney = refreshedAttorney;
              this.saveToLocalStorage(refreshedAttorney);
              await this.checkVapiSynchronization(refreshedAttorney);
            }
          }
          break;

        case 2:
          // Second attempt: Try to create a new Vapi assistant
          if (this.currentAttorney && this.currentAttorney.id) {
            const newAssistant = await this.createVapiAssistant(this.currentAttorney);
            await this.updateAttorneyInSupabase({
              id: this.currentAttorney.id,
              vapi_assistant_id: newAssistant.id
            });
          } else {
            console.warn('[AttorneyProfileManager] Cannot create assistant: current attorney missing or has no ID');
          }
          break;

        case 3:
          // Third attempt: Reset Vapi assistant ID and try again
          if (this.currentAttorney && this.currentAttorney.id) {
            await this.updateAttorneyInSupabase({
              id: this.currentAttorney.id,
              vapi_assistant_id: null
            });

            // The Realtime update will trigger a new check
          } else {
            console.warn('[AttorneyProfileManager] Cannot reset assistant ID: current attorney missing or has no ID');
          }
          break;
      }
    } catch (error) {
      console.error(`[AttorneyProfileManager] Recovery attempt ${this.recoveryAttempts} failed:`, error);

      // Schedule next recovery attempt
      setTimeout(() => {
        this.attemptRecovery();
      }, 5000 * this.recoveryAttempts); // Increasing backoff
    }
  }

  // Force synchronization
  async forceSynchronization() {
    console.log('[AttorneyProfileManager] Forcing synchronization');

    try {
      if (!this.currentAttorney) {
        throw new Error('No attorney profile loaded');
      }

      if (!this.currentAttorney.id) {
        throw new Error('Current attorney missing ID, cannot sync');
      }

      // Get latest from Supabase
      let supabaseData;
      try {
        console.log('[AttorneyProfileManager] Fetching latest data from Supabase for attorney:', this.currentAttorney.id);

        const supabase = await getSupabaseClient();
        const { data, error } = await supabase
          .from('attorneys')
          .select('*')
          .eq('id', this.currentAttorney.id)
          .single();

        if (error) throw error;
        supabaseData = data;

        console.log('[AttorneyProfileManager] Fetched Supabase data:', {
          id: data.id,
          firm_name: data.firm_name,
          vapi_assistant_id: data.vapi_assistant_id,
          voice_provider: data.voice_provider,
          voice_id: data.voice_id
        });

        // Update local state
        this.currentAttorney = data;

        // Save to localStorage
        this.saveToLocalStorage(data);
      } catch (supabaseError) {
        console.error('[AttorneyProfileManager] Error getting attorney from Supabase:', supabaseError);

        // Continue with current attorney data
        console.warn('[AttorneyProfileManager] Continuing with current attorney data:', {
          id: this.currentAttorney.id,
          firm_name: this.currentAttorney.firm_name,
          vapi_assistant_id: this.currentAttorney.vapi_assistant_id
        });
        supabaseData = this.currentAttorney;
      }

      // Try to update Vapi assistant (only if assistant ID is available)
      if (supabaseData.vapi_assistant_id) {
        try {
          await this.updateVapiAssistant(supabaseData);
        } catch (vapiError) {
          console.error('[AttorneyProfileManager] Error updating Vapi assistant:', vapiError);

          // If MCP server is not available, continue with warning
          if (vapiError.message.includes('MCP server') || vapiError.message.includes('connection')) {
            console.warn('[AttorneyProfileManager] MCP server not available, continuing with local data');

            this.syncStatus = {
              consistent: true,
              message: 'Voice service not available, using local data',
              lastChecked: new Date(),
              warning: vapiError.message
            };
          } else {
            // For other errors, mark as inconsistent
            this.syncStatus = {
              consistent: false,
              message: `Voice service sync error: ${vapiError.message}`,
              lastChecked: new Date(),
              error: vapiError.message
            };
          }
        }
      } else {
        console.warn('[AttorneyProfileManager] No Vapi assistant ID available, skipping Vapi sync');

        // Try to create a new assistant if none exists
        try {
          console.log('[AttorneyProfileManager] Attempting to create new Vapi assistant');
          const newAssistant = await this.createVapiAssistant(supabaseData);

          if (newAssistant && newAssistant.id) {
            // Update Supabase with the new assistant ID
            await this.updateAttorneyInSupabase({
              id: supabaseData.id,
              vapi_assistant_id: newAssistant.id
            });

            console.log('[AttorneyProfileManager] Created and saved new Vapi assistant:', newAssistant.id);

            this.syncStatus = {
              consistent: true,
              message: 'Created new voice assistant',
              lastChecked: new Date()
            };
          }
        } catch (createError) {
          console.error('[AttorneyProfileManager] Error creating new Vapi assistant:', createError);

          this.syncStatus = {
            consistent: false,
            message: 'No voice assistant available and could not create one',
            lastChecked: new Date(),
            warning: 'Voice assistant will be created on next sync attempt'
          };
        }
      }

      this.lastSyncTime = new Date();

      // If syncStatus hasn't been set by error handlers
      if (!this.syncStatus || !this.syncStatus.lastChecked ||
          this.syncStatus.lastChecked < this.lastSyncTime) {
        this.syncStatus = {
          consistent: true,
          message: 'Forced synchronization successful',
          lastChecked: new Date()
        };
      }

      // Notify listeners
      this.notifyListeners();

      return { success: true, attorney: supabaseData };
    } catch (error) {
      console.error('[AttorneyProfileManager] Error forcing synchronization:', error);

      this.syncStatus = {
        consistent: false,
        message: `Sync error: ${error.message}`,
        lastChecked: new Date(),
        error: error.message
      };

      // Notify listeners even on error
      this.notifyListeners();

      return { success: false, error: error.message };
    }
  }

  // Create a Vapi assistant
  async createVapiAssistant(attorney) {
    try {
      // Ensure attorney has required properties
      if (!attorney) {
        throw new Error('Attorney object is required');
      }

      // Log attorney info (use firm_name as fallback if id is missing)
      const attorneyIdentifier = attorney.id || attorney.firm_name || 'unknown';
      console.log('[AttorneyProfileManager] Creating Vapi assistant for attorney:', attorneyIdentifier);

      // Ensure attorney has a firm name
      if (!attorney.firm_name) {
        attorney.firm_name = 'Your Law Firm';
      }

      // Check if we're in production environment
      const isProduction = typeof window !== 'undefined' &&
        (window.location.hostname === 'dashboard.legalscout.net' ||
         window.location.hostname.endsWith('.legalscout.net'));

      // Get the appropriate Vapi service
      const vapiService = vapiServiceManager.getMcpService();

      // Ensure connection to Vapi service
      await vapiService.ensureConnection();

      const assistantConfig = {
        name: `${attorney.firm_name} Assistant`,
        firstMessage: attorney.welcome_message || `Hello, I'm Scout from ${attorney.firm_name}. How can I help you today?`,
        firstMessageMode: "assistant-speaks-first",
        model: {
          provider: "openai",
          model: attorney.ai_model || "gpt-4o",
          messages: [
            {
              role: "system",
              content: attorney.vapi_instructions || `You are a legal assistant for ${attorney.firm_name}. Help potential clients understand their legal needs and collect relevant information for consultation.`
            }
          ]
        },
        voice: {
          provider: attorney.voice_provider || "11labs",
          voiceId: attorney.voice_id || "sarah"
        },
        transcriber: {
          provider: "deepgram",
          model: "nova-3"
        }
      };

      // Create the assistant
      const assistant = await vapiService.createAssistant(assistantConfig);

      // Validate that we got a real assistant ID, not a mock one
      if (assistant && assistant.id && assistant.id.startsWith('mock-')) {
        if (isProduction) {
          throw new Error('Mock assistant created in production environment');
        }
        console.warn('[AttorneyProfileManager] Mock assistant created, this should not happen in production');
      }

      console.log('[AttorneyProfileManager] Created Vapi assistant:', assistant.id);
      return assistant;
    } catch (error) {
      console.error('[AttorneyProfileManager] Error creating Vapi assistant:', error);

      // Check if we're in production environment
      const isProduction = typeof window !== 'undefined' &&
        (window.location.hostname === 'dashboard.legalscout.net' ||
         window.location.hostname.endsWith('.legalscout.net'));

      // In production, don't fall back to mock assistants
      if (isProduction) {
        throw error;
      }

      // If MCP server is not available in development, create a mock assistant
      if (error.message.includes('MCP server') || error.message.includes('connection')) {
        console.warn('[AttorneyProfileManager] MCP server not available, creating mock assistant');

        return {
          id: 'mock-' + Date.now(),
          name: `${attorney.firm_name} Assistant`,
          instructions: attorney.vapi_instructions,
          firstMessage: attorney.welcome_message,
          mock: true
        };
      }

      throw error;
    }
  }

  // Update a Vapi assistant
  async updateVapiAssistant(attorney) {
    try {
      // Ensure attorney has required properties
      if (!attorney) {
        throw new Error('Attorney object is required');
      }

      // Log attorney info (use firm_name as fallback if id is missing)
      const attorneyIdentifier = attorney.id || attorney.firm_name || 'unknown';
      console.log('[AttorneyProfileManager] Updating Vapi assistant for attorney:', attorneyIdentifier);

      if (!attorney.vapi_assistant_id) {
        throw new Error('No Vapi assistant ID');
      }

      // Get the appropriate Vapi service
      const vapiService = vapiServiceManager.getMcpService();

      // Check if Vapi service is connected
      const connected = await vapiService.ensureConnection();
      if (!connected) {
        console.warn('[AttorneyProfileManager] Vapi service not connected, returning mock updated assistant');

        return {
          id: attorney.vapi_assistant_id,
          name: `${attorney.firm_name} Assistant`,
          instructions: attorney.vapi_instructions,
          firstMessage: attorney.welcome_message,
          mock: true
        };
      }

      // Get current assistant
      let assistant;
      try {
        assistant = await vapiService.getAssistant(attorney.vapi_assistant_id);
      } catch (assistantError) {
        console.error('[AttorneyProfileManager] Error getting assistant:', assistantError);

        // If MCP server error, return a mock assistant
        if (assistantError.message.includes('MCP server') || assistantError.message.includes('connection')) {
          console.warn('[AttorneyProfileManager] MCP server error getting assistant, returning mock assistant');

          return {
            id: attorney.vapi_assistant_id,
            name: `${attorney.firm_name} Assistant`,
            instructions: attorney.vapi_instructions,
            firstMessage: attorney.welcome_message,
            mock: true
          };
        }

        // If assistant not found, create a new one
        if (assistantError.message.includes('not found')) {
          console.warn('[AttorneyProfileManager] Assistant not found, creating a new one');
          return await this.createVapiAssistant(attorney);
        }

        throw assistantError;
      }

      if (!assistant) {
        console.warn('[AttorneyProfileManager] Assistant not found, creating a new one');
        return await this.createVapiAssistant(attorney);
      }

      // Validate and fix voice configuration
      let voiceProvider = attorney.voice_provider || (assistant.voice ? assistant.voice.provider : "11labs");
      let voiceId = attorney.voice_id || (assistant.voice ? assistant.voice.voiceId : "sarah");

      // Fix common voice/provider mismatches
      if (voiceProvider === "playht" && voiceId === "sarah") {
        console.warn('[AttorneyProfileManager] Fixing voice mismatch: sarah is not available for playht, switching to 11labs');
        voiceProvider = "11labs";
      }

      // Create update configuration
      const updateConfig = {
        name: `${attorney.firm_name} Assistant`,
        firstMessage: attorney.welcome_message || `Hello, I'm Scout from ${attorney.firm_name}. How can I help you today?`,
        firstMessageMode: "assistant-speaks-first",
        instructions: attorney.vapi_instructions || `You are a legal assistant for ${attorney.firm_name}. Help potential clients understand their legal needs and collect relevant information for consultation.`,
        llm: {
          ...(assistant.llm || assistant.model || { provider: "openai", model: "gpt-4o" }),
          model: attorney.ai_model || (assistant.llm ? assistant.llm.model : (assistant.model ? assistant.model.model : "gpt-4o"))
        },
        voice: {
          ...(assistant.voice || { provider: "11labs", voiceId: "sarah" }),
          provider: voiceProvider,
          voiceId: voiceId
        }
      };

      console.log('[AttorneyProfileManager] Sending update config to Vapi:', JSON.stringify(updateConfig, null, 2));

      // Update the assistant
      const updatedAssistant = await vapiService.updateAssistant(attorney.vapi_assistant_id, updateConfig);

      console.log('[AttorneyProfileManager] Updated Vapi assistant:', updatedAssistant.id);
      return updatedAssistant;
    } catch (error) {
      console.error('[AttorneyProfileManager] Error updating Vapi assistant:', error);

      // Log additional details for 400 errors
      if (error.message.includes('400')) {
        console.error('[AttorneyProfileManager] 400 Error Details:');
        console.error('- Assistant ID:', attorney.vapi_assistant_id);
        console.error('- Attorney Data:', JSON.stringify({
          firm_name: attorney.firm_name,
          welcome_message: attorney.welcome_message,
          vapi_instructions: attorney.vapi_instructions,
          voice_id: attorney.voice_id,
          voice_provider: attorney.voice_provider,
          ai_model: attorney.ai_model
        }, null, 2));
      }

      // If MCP server is not available, return a mock updated assistant
      if (error.message.includes('MCP server') || error.message.includes('connection')) {
        console.warn('[AttorneyProfileManager] MCP server not available, returning mock updated assistant');

        return {
          id: attorney.vapi_assistant_id,
          name: `${attorney.firm_name} Assistant`,
          instructions: attorney.vapi_instructions,
          firstMessage: attorney.welcome_message,
          mock: true
        };
      }

      throw error;
    }
  }

  // Update attorney in Supabase
  async updateAttorneyInSupabase(data) {
    try {
      // Validate input data
      if (!data) {
        throw new Error('No data provided for attorney update');
      }

      if (!data.id) {
        console.error('[AttorneyProfileManager] No ID provided in update data:', data);
        throw new Error('Attorney ID is required for Supabase update');
      }

      console.log('[AttorneyProfileManager] Updating attorney in Supabase:', data.id);

      // Check if the ID is a development ID
      if (data.id && data.id.startsWith('dev-')) {
        console.log('[AttorneyProfileManager] Development ID detected, updating locally only');

        // For development IDs, just update locally without trying Supabase
        const updateData = {
          ...this.currentAttorney,
          ...data,
          updated_at: new Date().toISOString()
        };

        // Update local state
        this.currentAttorney = updateData;

        // Save to localStorage
        this.saveToLocalStorage(updateData);

        // Notify listeners
        this.notifyListeners();

        return updateData;
      }

      // Add updated_at timestamp
      const updateData = {
        ...data,
        updated_at: new Date().toISOString()
      };

      // Add to pending updates
      const pendingUpdateId = data.id + '_' + updateData.updated_at;
      this.pendingUpdates.set(pendingUpdateId, updateData);

      const supabase = await getSupabaseClient();
      const { data: updatedData, error } = await supabase
        .from('attorneys')
        .update(updateData)
        .eq('id', data.id)
        .select()
        .single();

      if (error) throw error;

      console.log('[AttorneyProfileManager] Updated attorney in Supabase successfully');
      return updatedData;
    } catch (error) {
      console.error('[AttorneyProfileManager] Error updating attorney in Supabase:', error);

      // Remove from pending updates
      const pendingUpdateId = data.id + '_' + (data.updated_at || new Date().toISOString());
      this.pendingUpdates.delete(pendingUpdateId);

      throw error;
    }
  }

  // Save attorney to localStorage
  saveToLocalStorage(attorney) {
    if (!attorney) {
      console.warn('[AttorneyProfileManager] Cannot save undefined attorney to localStorage');
      return;
    }

    try {
      localStorage.setItem('attorney', JSON.stringify(attorney));
      localStorage.setItem('attorney_last_updated', new Date().toISOString());
      console.log('[AttorneyProfileManager] Saved attorney to localStorage:', attorney.id || 'unknown');
    } catch (error) {
      console.error('[AttorneyProfileManager] Error saving attorney to localStorage:', error);
    }
  }

  // Load attorney from localStorage
  loadFromLocalStorage() {
    try {
      const attorney = localStorage.getItem('attorney');
      if (!attorney) return null;

      const parsed = JSON.parse(attorney);
      console.log('[AttorneyProfileManager] Loaded attorney from localStorage:', parsed.id);
      return parsed;
    } catch (error) {
      console.error('[AttorneyProfileManager] Error loading attorney from localStorage:', error);
      return null;
    }
  }

  // Find discrepancies between attorney and assistant
  findDiscrepancies(attorney, assistant) {
    const discrepancies = {};

    if (assistant.name !== `${attorney.firm_name} Assistant`) {
      discrepancies.name = {
        current: assistant.name,
        expected: `${attorney.firm_name} Assistant`
      };
    }

    if (attorney.vapi_instructions && assistant.instructions !== attorney.vapi_instructions) {
      discrepancies.instructions = {
        current: assistant.instructions,
        expected: attorney.vapi_instructions
      };
    }

    if (attorney.welcome_message && assistant.firstMessage !== attorney.welcome_message) {
      discrepancies.firstMessage = {
        current: assistant.firstMessage,
        expected: attorney.welcome_message
      };
    }

    if (attorney.voice_id && assistant.voice && assistant.voice.voiceId !== attorney.voice_id) {
      discrepancies.voiceId = {
        current: assistant.voice ? assistant.voice.voiceId : null,
        expected: attorney.voice_id
      };
    }

    return discrepancies;
  }

  // Add a listener for attorney updates
  addListener(listener) {
    this.listeners.add(listener);

    // Immediately notify with current state
    if (this.currentAttorney) {
      try {
        listener(this.currentAttorney);
      } catch (error) {
        console.error('[AttorneyProfileManager] Error in initial listener notification:', error);
      }
    }
  }

  // Remove a listener
  removeListener(listener) {
    this.listeners.delete(listener);
  }

  // Notify all listeners of changes
  notifyListeners() {
    for (const listener of this.listeners) {
      try {
        listener(this.currentAttorney);
      } catch (error) {
        console.error('[AttorneyProfileManager] Error in attorney update listener:', error);
      }
    }
  }

  // Clean up resources
  cleanup() {
    // Clean up v1 API subscription
    if (this.subscription) {
      try {
        supabase.removeSubscription(this.subscription);
      } catch (error) {
        console.warn('[AttorneyProfileManager] Error removing subscription:', error);
      }
      this.subscription = null;
    }

    // Clean up v2 API channel
    if (this.channel) {
      try {
        supabase.removeChannel(this.channel);
      } catch (error) {
        console.warn('[AttorneyProfileManager] Error removing channel:', error);
      }
      this.channel = null;
    }

    // Clean up polling interval
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }

    this.listeners.clear();
    console.log('[AttorneyProfileManager] Resources cleaned up');
  }
}

// Export a singleton instance
export const attorneyProfileManager = new AttorneyProfileManager();

// Make it available globally for debugging and integration
if (typeof window !== 'undefined') {
  window.attorneyProfileManager = attorneyProfileManager;
}

export default attorneyProfileManager;
