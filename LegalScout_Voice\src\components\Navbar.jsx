import React, { useState, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { getCurrentSubdomain } from '../utils/subdomainTester';
import { useAuth } from '../contexts/AuthContext';

const Navbar = ({ isDarkTheme }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();
  const navContainerRef = useRef(null);
  const { user, signOut } = useAuth();

  // Check if we're on an attorney subdomain
  const subdomain = getCurrentSubdomain();
  const isAttorneySubdomain = subdomain !== 'default' &&
                            subdomain !== 'www' &&
                            subdomain !== '' &&
                            subdomain !== null;

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  // Removed bone cursor effect for cleaner navigation experience

  return (
    <div className={`nav-container ${isMenuOpen ? 'menu-active' : ''}`} ref={navContainerRef}>
      <button
        className={`hamburger-menu ${isMenuOpen ? 'active' : ''}`}
        onClick={toggleMenu}
        aria-label="Toggle navigation menu"
        aria-expanded={isMenuOpen}
      >
        <span></span>
        <span></span>
        <span></span>
      </button>

      <nav className={`main-nav ${isMenuOpen ? 'active' : ''}`}>
        <ul>
          {/* Only show Home and Agent links if not on an attorney subdomain */}
          {!isAttorneySubdomain && (
            <>
              <li>
                <Link
                  to="/home"
                  className={location.pathname === '/home' || location.pathname === '/' ? 'active' : ''}
                  onClick={() => setIsMenuOpen(false)}
                  data-text="Home"
                >
                  <i className="nav-icon fas fa-home"></i>
                  Home
                </Link>
              </li>
              <li>
                <Link
                  to="/demo"
                  className={location.pathname === '/demo' ? 'active' : ''}
                  onClick={() => setIsMenuOpen(false)}
                  data-text="Agent"
                >
                  <i className="nav-icon fas fa-gavel"></i>
                  Agent
                </Link>
              </li>
            </>
          )}
          {/* Always show About link */}
          <li>
            <Link
              to="/about"
              className={location.pathname === '/about' ? 'active' : ''}
              onClick={() => setIsMenuOpen(false)}
              data-text="About"
            >
              <i className="nav-icon fas fa-info-circle"></i>
              About
            </Link>
          </li>

          {/* Authentication Links */}
          {!isAttorneySubdomain && (
            <>
              {user ? (
                <>
                  <li>
                    <Link
                      to="/dashboard"
                      className={location.pathname === '/dashboard' ? 'active' : ''}
                      onClick={() => setIsMenuOpen(false)}
                      data-text="Dashboard"
                    >
                      <i className="nav-icon fas fa-tachometer-alt"></i>
                      Dashboard
                    </Link>
                  </li>
                  <li>
                    <button
                      onClick={() => {
                        signOut();
                        setIsMenuOpen(false);
                      }}
                      className="nav-button"
                      data-text="Sign Out"
                    >
                      <i className="nav-icon fas fa-sign-out-alt"></i>
                      Sign Out
                    </button>
                  </li>
                </>
              ) : (
                <>
                  {/* Sign In and Get Started buttons removed - keeping only Sign In/Sign Up button in header */}
                </>
              )}
            </>
          )}
        </ul>
      </nav>

      {/* Add overlay for mobile menu */}
      <div
        className={`nav-overlay ${isMenuOpen ? 'active' : ''}`}
        onClick={() => setIsMenuOpen(false)}
        aria-hidden="true"
      />
    </div>
  );
};

export default Navbar;