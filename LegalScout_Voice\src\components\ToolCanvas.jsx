import React, { useState, useEffect, useRef } from 'react';
import './ToolCanvas.css';

/**
 * Tool Canvas Component
 * 
 * Third column display for tool results during calls including:
 * - Legal citations
 * - Research results
 * - Attorney context actions
 * - Real-time tool execution status
 */
const ToolCanvas = ({ 
  toolResults = [], 
  isVisible = true,
  callId = null,
  assistantId = null 
}) => {
  const [activeTab, setActiveTab] = useState('all');
  const [toolHistory, setToolHistory] = useState([]);
  const [isExpanded, setIsExpanded] = useState(true);
  const canvasRef = useRef(null);

  // Tool result categories
  const toolCategories = {
    all: 'All Tools',
    dossier: 'Live Dossier',
    attorney: 'Attorney Context',
    legal: 'Legal Research',
    citations: 'Citations',
    scheduling: 'Scheduling'
  };

  // Update tool history when new results come in
  useEffect(() => {
    if (toolResults && toolResults.length > 0) {
      setToolHistory(prev => {
        const newHistory = [...prev];
        
        toolResults.forEach(result => {
          // Avoid duplicates
          if (!newHistory.find(item => item.id === result.id)) {
            newHistory.push({
              ...result,
              timestamp: result.timestamp || new Date().toISOString(),
              category: categorizeToolResult(result)
            });
          }
        });
        
        // Sort by timestamp (newest first)
        return newHistory.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
      });
    }
  }, [toolResults]);

  /**
   * Categorize tool results based on tool name/type
   */
  const categorizeToolResult = (result) => {
    const toolName = result.toolName || result.name || '';
    
    if (toolName.includes('dossier')) return 'dossier';
    if (toolName.includes('attorney') || toolName.includes('context')) return 'attorney';
    if (toolName.includes('legal') || toolName.includes('research')) return 'legal';
    if (toolName.includes('citation')) return 'citations';
    if (toolName.includes('schedule') || toolName.includes('consultation')) return 'scheduling';
    
    return 'all';
  };

  /**
   * Filter tool history based on active tab
   */
  const getFilteredResults = () => {
    if (activeTab === 'all') return toolHistory;
    return toolHistory.filter(result => result.category === activeTab);
  };

  /**
   * Render individual tool result
   */
  const renderToolResult = (result, index) => {
    const isRecent = Date.now() - new Date(result.timestamp).getTime() < 5000; // 5 seconds
    
    return (
      <div 
        key={result.id || index} 
        className={`tool-result-item ${result.category} ${isRecent ? 'recent' : ''}`}
      >
        <div className="tool-result-header">
          <div className="tool-info">
            <span className="tool-name">{result.toolName || result.name || 'Unknown Tool'}</span>
            <span className="tool-timestamp">
              {new Date(result.timestamp).toLocaleTimeString()}
            </span>
          </div>
          <div className={`tool-status ${result.status || 'completed'}`}>
            {result.status === 'running' && <div className="spinner"></div>}
            {result.status === 'completed' && '✅'}
            {result.status === 'failed' && '❌'}
            {!result.status && '✅'}
          </div>
        </div>
        
        <div className="tool-result-content">
          {renderToolContent(result)}
        </div>
      </div>
    );
  };

  /**
   * Render tool-specific content
   */
  const renderToolContent = (result) => {
    switch (result.category) {
      case 'dossier':
        return renderDossierContent(result);
      case 'attorney':
        return renderAttorneyContent(result);
      case 'legal':
        return renderLegalContent(result);
      case 'citations':
        return renderCitationsContent(result);
      case 'scheduling':
        return renderSchedulingContent(result);
      default:
        return renderGenericContent(result);
    }
  };

  /**
   * Render dossier update content
   */
  const renderDossierContent = (result) => (
    <div className="dossier-content">
      <div className="content-summary">
        📋 Case information updated
      </div>
      {result.data && (
        <div className="dossier-details">
          {result.data.status && (
            <div className="detail-item">
              <strong>Status:</strong> {result.data.status}
            </div>
          )}
          {result.data.jurisdiction && (
            <div className="detail-item">
              <strong>Location:</strong> {result.data.jurisdiction.address || result.data.jurisdiction}
            </div>
          )}
          {result.data.legalIssues && (
            <div className="detail-item">
              <strong>Legal Issues:</strong> {result.data.legalIssues}
            </div>
          )}
        </div>
      )}
    </div>
  );

  /**
   * Render attorney context content
   */
  const renderAttorneyContent = (result) => (
    <div className="attorney-content">
      <div className="content-summary">
        👨‍💼 {result.action || 'Attorney action performed'}
      </div>
      {result.message && (
        <div className="attorney-message">
          {result.message}
        </div>
      )}
    </div>
  );

  /**
   * Render legal research content
   */
  const renderLegalContent = (result) => (
    <div className="legal-content">
      <div className="content-summary">
        ⚖️ Legal research completed
      </div>
      {result.findings && (
        <div className="legal-findings">
          {result.findings.map((finding, index) => (
            <div key={index} className="finding-item">
              {finding}
            </div>
          ))}
        </div>
      )}
    </div>
  );

  /**
   * Render citations content
   */
  const renderCitationsContent = (result) => (
    <div className="citations-content">
      <div className="content-summary">
        📚 {result.citations?.length || 0} citation(s) found
      </div>
      {result.citations && (
        <div className="citations-list">
          {result.citations.map((citation, index) => (
            <div key={index} className="citation-item">
              <div className="citation-title">{citation.title}</div>
              <div className="citation-source">{citation.source}</div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  /**
   * Render scheduling content
   */
  const renderSchedulingContent = (result) => (
    <div className="scheduling-content">
      <div className="content-summary">
        📅 Scheduling action completed
      </div>
      {result.appointmentDetails && (
        <div className="appointment-details">
          <div><strong>Client:</strong> {result.appointmentDetails.clientName}</div>
          <div><strong>Type:</strong> {result.appointmentDetails.type}</div>
        </div>
      )}
    </div>
  );

  /**
   * Render generic content
   */
  const renderGenericContent = (result) => (
    <div className="generic-content">
      <div className="content-summary">
        🔧 Tool executed
      </div>
      {result.result && (
        <div className="tool-result-text">
          {typeof result.result === 'string' ? result.result : JSON.stringify(result.result)}
        </div>
      )}
    </div>
  );

  if (!isVisible) {
    return null;
  }

  const filteredResults = getFilteredResults();

  return (
    <div className={`tool-canvas ${isExpanded ? 'expanded' : 'collapsed'}`} ref={canvasRef}>
      <div className="tool-canvas-header">
        <div className="header-title">
          <h3>Tool Canvas</h3>
          <span className="result-count">
            {filteredResults.length} result{filteredResults.length !== 1 ? 's' : ''}
          </span>
        </div>
        <div className="header-controls">
          <button 
            className="expand-toggle"
            onClick={() => setIsExpanded(!isExpanded)}
            title={isExpanded ? 'Collapse' : 'Expand'}
          >
            {isExpanded ? '⬅️' : '➡️'}
          </button>
        </div>
      </div>

      {isExpanded && (
        <>
          <div className="tool-canvas-tabs">
            {Object.entries(toolCategories).map(([key, label]) => (
              <button
                key={key}
                className={`tab-button ${activeTab === key ? 'active' : ''}`}
                onClick={() => setActiveTab(key)}
              >
                {label}
                {key !== 'all' && (
                  <span className="tab-count">
                    {toolHistory.filter(r => r.category === key).length}
                  </span>
                )}
              </button>
            ))}
          </div>

          <div className="tool-canvas-content">
            {filteredResults.length === 0 ? (
              <div className="empty-state">
                <div className="empty-icon">🔧</div>
                <div className="empty-message">
                  {activeTab === 'all' 
                    ? 'No tool results yet. Tools will appear here as they execute during the call.'
                    : `No ${toolCategories[activeTab].toLowerCase()} results yet.`
                  }
                </div>
              </div>
            ) : (
              <div className="tool-results-list">
                {filteredResults.map((result, index) => renderToolResult(result, index))}
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default ToolCanvas;
