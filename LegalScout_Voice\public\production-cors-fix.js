// Production CORS Fix
(function() {
  console.log('🌐 [CORS] Applying production CORS fixes...');
  
  // Fix CORS issues in production
  if (typeof window !== 'undefined') {
    const originalFetch = window.fetch;
    
    window.fetch = function(url, options = {}) {
      // Add CORS headers for production
      const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, apikey, X-Client-Info',
        ...options.headers
      };
      
      return originalFetch(url, { ...options, headers });
    };
    
    console.log('✅ [CORS] Production CORS fixes applied');
  }
})();